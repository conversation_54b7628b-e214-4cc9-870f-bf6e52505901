version: 2.1

executors:
  node:
    docker:
      - image: 'cimg/node:20.11-browsers'
  playwright:
    docker:
      - image: mcr.microsoft.com/playwright:v1.50.1-jammy    

orbs:
  node: circleci/node@5.2.0
  hal: rocket-technology/hal@1.5.0 # https://git.rockfin.com/RocketTechnology/hal-orb
  browser-tools: circleci/browser-tools@1.4.8

commands:
  attach:
    description: 'Attach'
    steps:
      - attach_workspace:
          at: '.'

  build:
    description: Run build for given environment
    parameters:
      env:
        type: enum
        enum: ["test", "beta", "prod", "train"]
    steps:
      - run: npm run build:<< parameters.env >>
      - run: cp -R ./dist/rl-xp-ui/browser ./package-<< parameters.env >>
      - run: du -sh ./package-<< parameters.env >>/*
      - persist_to_workspace:
          root: '.'
          paths: ['package-<< parameters.env >>']

#############################################################
# Shared jobs and configuration
#############################################################

run_on_pull_request: &run_on_pull_request
  filters:
    branches:
      ignore: [main, /feature\/.*/, /hotfix\/.*/]
    tags:
      ignore: /.*/

run_on_main: &run_on_main
  filters:
    branches:
      only: main

run_on_feature: &run_on_feature
  filters:
    branches:
      only: /feature\/.*/

run_on_prod_deploy: &run_on_prod_deploy
  filters:
    branches:
      ignore: /.*/
    tags:
      only: /^release\/\d{4}-\d{2}-\d{2}(-.+)?$/

run_on_hotfix: &run_on_hotfix
  filters:
    branches:
      only: /hotfix\/.*/

persist_workspace: &persist_workspace
  persist_to_workspace:
    root: '.'
    paths: ['.']

bypass_yesno_ssh: &bypass_yesno_ssh
  run:
    name: 'Bypassing entering yes/no for ssh'
    command: mkdir -p ~/.ssh/ && echo -e "Host git.rockfin.com\n\tStrictHostKeyChecking no\n" > ~/.ssh/config

clone_test_repo: &clone_test_repo
  run:
    name: 'Cloning the project'
    command: |
      ssh-keyscan -H git.rockfin.com >> ~/.ssh/known_hosts
      git clone '*******************:Rocket-Logic/rl-xp-ui-playwrightautomation'    
#############################################################

workflows:
  version: 2

  pull_request:
    jobs:
      - install_dependencies:
          <<: *run_on_pull_request   
      - unit_tests:
          requires: [install_dependencies]
      - linting:
          requires: [install_dependencies]
      - build_test:
          requires: [install_dependencies]     

  feature:
    jobs:
      - install_dependencies:
          <<: *run_on_feature
      - unit_tests:
          requires: [install_dependencies]
      - linting:
          requires: [install_dependencies]
      - build_test:
          requires: [install_dependencies]
      - approval_for_test:
          requires: [unit_tests, linting, build_test]
          type: approval
      - hal/publish:
          name: publish_test
          context: rl-hal-push
          hal-appid: '17875'
          artifact-path: './package-test'
          requires: [approval_for_test]
      - hal/deploy:
          name: deploy_test
          context: rl-hal-push
          requires: [publish_test]
          deploy-setting-id: '117523'
          persist-job-to-workspace: false
      - build_beta:
          requires: [install_dependencies]
      - approval_for_beta:
          requires: [unit_tests, linting, build_beta]
          type: approval
      - hal/publish:
          name: publish_beta
          context: rl-hal-push
          hal-appid: '17875'
          artifact-path: 'package-beta'
          requires: [approval_for_beta]
      - approval_feature_1:
          requires: [publish_beta]
          type: approval
      - hal/deploy:
          name: deploy_feature_1
          context: rl-hal-push
          requires: [approval_feature_1]
          deploy-setting-id: '126149'
          persist-job-to-workspace: false
      - approval_feature_2:
          requires: [publish_beta]
          type: approval
      - hal/deploy:
          name: deploy_feature_2
          context: rl-hal-push
          requires: [approval_feature_2]
          deploy-setting-id: '126151'
          persist-job-to-workspace: false

  merge:
    jobs:
      - install_dependencies:
          <<: *run_on_main
      - unit_tests:
          requires: [install_dependencies]
      - linting:
          requires: [install_dependencies]
      - build_test:
          requires: [install_dependencies]
      - hal/publish:
          name: publish_test
          context: rl-hal-push
          hal-appid: '17875'
          artifact-path: './package-test'
          requires: [unit_tests, linting, build_test]
      - hal/deploy:
          name: deploy_test
          context: rl-hal-push
          deploy-setting-id: '117523'
          persist-job-to-workspace: false
          requires: [publish_test]
      - approval_for_beta:
          requires: [deploy_test]
          type: approval
      - build_beta:
          requires: [approval_for_beta]
      - hal/publish:
          name: publish_beta
          context: rl-hal-push
          hal-appid: '17875'
          artifact-path: 'package-beta'
          requires: [build_beta]
      - hal/deploy:
          name: deploy_beta
          context: rl-hal-push
          requires: [publish_beta]
          deploy-setting-id: '117524'
          persist-job-to-workspace: false
      - beta_regression_tests:
          requires: [deploy_beta]

  prod_deploy:
    jobs:
      - install_dependencies:
          <<: *run_on_prod_deploy
      - unit_tests:
          <<: *run_on_prod_deploy
          requires: [install_dependencies]
      - linting:
          <<: *run_on_prod_deploy
          requires: [install_dependencies]
      - build_prod:
          <<: *run_on_prod_deploy
          requires: [install_dependencies]
      - build_train:
          <<: *run_on_prod_deploy
          requires: [install_dependencies]
      - approval_for_prod:
          <<: *run_on_prod_deploy
          requires: [unit_tests, linting, build_prod]
          type: approval
      - hal/publish:
          <<: *run_on_prod_deploy
          name: publish_prod
          context: rl-hal-push
          hal-appid: '17875'
          artifact-path: 'package-prod'
          requires: [approval_for_prod]
      - hal/deploy:
          <<: *run_on_prod_deploy
          name: deploy_prod
          context: rl-hal-push
          requires: [publish_prod]
          deploy-setting-id: '117525'
          persist-job-to-workspace: false
      - hal/publish:
          <<: *run_on_prod_deploy
          name: publish_train
          context: rl-hal-push
          hal-appid: '17875'
          artifact-path: 'package-train'
          requires: [approval_for_prod, build_train]
      - hal/deploy:
          <<: *run_on_prod_deploy
          name: deploy_train
          context: rl-hal-push
          requires: [publish_train]
          deploy-setting-id: '117526'
          persist-job-to-workspace: false
      - prod_regression_tests:
          <<: *run_on_prod_deploy
          requires: [deploy_prod]
      - send_slack_notification:
          requires: [prod_regression_tests]        

  hotfix:
    jobs:
      - install_dependencies:
          <<: *run_on_hotfix
      - unit_tests:
          requires: [install_dependencies]
      - linting:
          requires: [install_dependencies]
      - build_beta:
          requires: [install_dependencies]
      - approval_for_beta:
          requires: [build_beta]
          type: approval
      - hal/publish:
          name: publish_beta
          context: rl-hal-push
          hal-appid: '17875'
          artifact-path: 'package-beta'
          requires: [approval_for_beta]
      - hal/deploy:
          name: deploy_beta
          context: rl-hal-push
          requires: [publish_beta]
          deploy-setting-id: '117524'
          persist-job-to-workspace: false
      - approval_for_prod:
          requires: [deploy_beta]
          type: approval
      - build_prod:
          requires: [approval_for_prod]
      - hal/publish:
          name: publish_prod
          context: rl-hal-push
          hal-appid: '17875'
          artifact-path: 'package-prod'
          requires: [build_prod]
      - hal/deploy:
          name: deploy_prod
          context: rl-hal-push
          requires: [publish_prod]
          deploy-setting-id: '117525'
          persist-job-to-workspace: false
      - build_train:
          requires: [approval_for_prod]
      - hal/publish:
          name: publish_train
          context: rl-hal-push
          hal-appid: '17875'
          artifact-path: 'package-train'
          requires: [build_train]
      - hal/deploy:
          name: deploy_train
          context: rl-hal-push
          requires: [publish_train]
          deploy-setting-id: '117526'
          persist-job-to-workspace: false

############################################################

jobs:
  build_test:
    executor: node
    steps:
      - attach
      - build:
          env: test

  build_beta:
    executor: node
    steps:
      - attach
      - build:
          env: beta

  build_prod:
    executor: node
    steps:
      - attach
      - build:
          env: prod

  build_train:
    executor: node
    steps:
      - attach
      - build:
          env: train

  unit_tests:
    executor: node
    environment:
      CHROME_BIN: /usr/bin/google-chrome
    steps:
      - attach
      - browser-tools/install-chrome
      - run:
          command: google-chrome --version
          name: Check install
      - run: npm run test:ci

  linting:
    executor: node
    steps:
      - attach
      - run: npm run lint

  install_dependencies:
    executor: node
    steps:
      - checkout
      - hal/install-certificates
      - run: npm --version
      - node/install-packages:
          with-cache: false
      - persist_to_workspace:
          root: '.'
          paths: ['.']

  beta_regression_tests:
    executor: playwright
    parallelism: 4
    resource_class: xlarge
    environment:
      NODE_ENV: "beta"
    steps:  
      - attach
      - run:  apt-get update &&  apt-get -y install ssh
      - *bypass_yesno_ssh
      - add_ssh_keys:
          fingerprints:
            - "dc:bd:ec:60:76:9e:8f:8b:53:4b:68:13:fd:b2:de:cc"
      - *clone_test_repo
      - run:
          name: Install Node.js Dependencies
          command: cd rl-xp-ui-playwrightautomation && npm install     
      - run:
          name: Inject secrets into env.beta.yml
          command: |
            cd rl-xp-ui-playwrightautomation
            SHARD="$((${CIRCLE_NODE_INDEX}+1))"
            sed -i \
              -e "s|\$LEAD_PASSWORD|$Lead_Password|g" \
              -e "s|\$RP_API_KEY|$RP_API_KEY|g" \
              ./configs/env.beta.yml
      - run:
          name: Run  Beta Regression Tests
          command: |
            cd rl-xp-ui-playwrightautomation
            export NODE_ENV=beta
            npx playwright test --project=chromium
            exit 0
          no_output_timeout: 10m           
      - store_artifacts:
          path: rl-xp-ui-playwrightautomation/beta-reports/html-report
          destination: beta-test-results 

  prod_regression_tests:
    executor: playwright
    parallelism: 4
    resource_class: xlarge
    environment:
      NODE_ENV: "prod"
    steps:  
      - attach
      - run:  apt-get update &&  apt-get -y install ssh
      - *bypass_yesno_ssh
      - add_ssh_keys:
          fingerprints:
            - "dc:bd:ec:60:76:9e:8f:8b:53:4b:68:13:fd:b2:de:cc"
      - *clone_test_repo
      - run:
          name: Install Node.js Dependencies
          command: cd rl-xp-ui-playwrightautomation && npm install
      - run:
          name: Inject secrets into env.prod.yml
          command: |
            cd rl-xp-ui-playwrightautomation
            SHARD="$((${CIRCLE_NODE_INDEX}+1))"
            sed -i \
              -e "s|\$LEAD_PASSWORD|$Lead_Password_prod|g" \
              -e "s|\$RP_API_KEY|$RP_API_KEY_PROD|g" \
              ./configs/env.prod.yml
      - run:
          name: Run Regression Tests
          command: |
            cd rl-xp-ui-playwrightautomation
            export NODE_ENV=prod
            npx playwright test --project=chromium
            exit 0
          no_output_timeout: 10m         
      - store_artifacts:
          path: rl-xp-ui-playwrightautomation/prod-reports/html-report
          destination: prod-test-results     
     
  send_slack_notification:
    docker:
      - image: cimg/base:stable
    steps:
      - run:
          name: Send Slack Notification using Webhook
          command: |
            MESSAGE="*✅ Prod Regression Tests Complete*\nBranch: \`${CIRCLE_BRANCH}\`\nJob: \`${CIRCLE_JOB}\`\n🔗 <${CIRCLE_BUILD_URL}|View build in CircleCI>"
            curl -X POST -H 'Content-type: application/json' \
            --data "{\"message\": \"${MESSAGE}\"}" \
            $SLACK_WEBHOOK