locals {
  app_name = "rl-xp-ui-site"
  app_id = "214288"
  hal_app_id = "17876"
  contact_email = "<EMAIL>"
  rl_hostname = var.environment == "prod" ? "rocket-logic.foc.zone" : "${var.environment}.np.rocket-logic.foc.zone"
  rl_origin = "https://${var.rl_xp_ui_domain_name}"
  acm_certificate_domain_name = var.acm_certificate_domain_name != null ? var.acm_certificate_domain_name : var.rl_xp_ui_domain_name
}

module "rl-xp-ui-site" {
  source = "git::https://git.rockfin.com/terraform/aws-s3-cloudfront-waf-tf.git?ref=5.2.0"

  # ---------------------------------------------------------------------------------------------------------------------
  # Required variables for AWS
  # ---------------------------------------------------------------------------------------------------------------------

  aws_region = var.aws_region
  aws_account_id = var.aws_account_id

  # ---------------------------------------------------------------------------------------------------------------------
  # Standard Module Required Variables
  # ---------------------------------------------------------------------------------------------------------------------

  app_id = local.app_id
  application_name = local.app_name
  environment = var.environment

  development_team_email = local.contact_email
  infrastructure_team_email = local.contact_email
  infrastructure_engineer_email = local.contact_email

  # ---------------------------------------------------------------------------------------------------------------------
  # Infrastructure Tags
  # ---------------------------------------------------------------------------------------------------------------------

  app_tags = {
    hal-app-id = local.hal_app_id
  }

  # ---------------------------------------------------------------------------------------------------------------------
  # Infrastructure Variables
  # ---------------------------------------------------------------------------------------------------------------------

  website_domain_name = var.rl_xp_ui_domain_name

  create_route53_entry = true
  hosted_zone_name = var.route53_zone_name
  # hosted zone that the website_domain_name will be created in

  acm_certificate_domain_name = local.acm_certificate_domain_name

  min_ttl     = 0
  max_ttl     = 60
  default_ttl = 30

  index_document     = var.index_document
  error_document_404 = var.error_document

  error_404_response_code = 200

  cors_rules = [
    {
      allowed_headers = ["*"]
      allowed_methods = ["GET"]
      allowed_origins = [local.rl_origin]
      max_age_seconds = 3600
      expose_headers  = []
    }
  ]

  cloudfront_forward_headers = [
    "Access-Control-Request-Headers",
    "Access-Control-Request-Method",
    "Origin",
  ]

  allowed_external_ips = [
    "************/24",    # QL IPs
    "*************/21",   # QL IPs
    "***********/24",     # QL web range
    "************/27",    # QL web range
    "**************/32",  # Circle CI
    "**************/32",  # Circle CI
    "************/32",    # Circle CI
    "************/32",    # Dynatrace Synthetics - rcd.michigan.dc1.secure - Detroit (RKT)
    "************/32",    # Dynatrace Synthetics - rcd.michigan.dc1.secure - Detroit (RKT)
    "*************/32",   # Dynatrace Synthetics -  rcd.michigan.dc2.secure - Detroit (RKT)
    "*************/32",   # Dynatrace Synthetics -  rcd.michigan.dc2.secure - Detroit (RKT)
    "************/32",    # Dynatrace Synthetics -  aws.us-east-2.secure - Ohio (All FOC's)
    "************/32",    # Dynatrace Synthetics -  aws.us-east-2.secure - Ohio (All FOC's)
    "************/32",    # Dynatrace Synthetics -  aws.us-east-2.secure - Ohio (All FOC's)
    "************/32",    # Dynatrace Synthetics -  aws.us-east-2.secure - Ohio (All FOC's) (PROD)
    "**********/32",      # Dynatrace Synthetics -  aws.us-east-2.secure - Ohio (All FOC's) (PROD)
    "3.13.41.89/32",      # Dynatrace Synthetics -  aws.us-east-2.secure - Ohio (All FOC's) (PROD)
    "3.212.90.190/32",    # Dynatrace Synthetics -  aws.us-east-1.secure - N. Virginia (All FOC's)
    "3.221.141.41/32",    # Dynatrace Synthetics -  aws.us-east-1.secure - N. Virginia (All FOC's)
    "54.236.197.250/32",  # Dynatrace Synthetics -  aws.us-east-1.secure - N. Virginia (All FOC's)
    "3.92.35.156/32",     # Dynatrace Synthetics -  aws.us-east-1.secure - N. Virginia (All FOC's) (PROD)
    "34.232.173.189/32",  # Dynatrace Synthetics -  aws.us-east-1.secure - N. Virginia (All FOC's) (PROD)
    "52.71.1.181/32",     # Dynatrace Synthetics -  aws.us-east-1.secure - N. Virginia (All FOC's) (PROD)
    "100.21.181.234/32",  # Dynatrace Synthetics -  aws.us-west-2.secure - Oregon (All FOC's)
    "44.230.220.61/32",   # Dynatrace Synthetics -  aws.us-west-2.secure - Oregon (All FOC's)
    "44.241.127.57/32",   # Dynatrace Synthetics -  aws.us-west-2.secure - Oregon (All FOC's)
    "35.167.122.60/32",   # Dynatrace Synthetics -  aws.us-west-2.secure - Oregon (All FOC's) (PROD)
    "35.84.161.167/32",   # Dynatrace Synthetics -  aws.us-west-2.secure - Oregon (All FOC's) (PROD)
    "52.43.251.93/32",    # Dynatrace Synthetics -  aws.us-west-2.secure - Oregon (All FOC's) (PROD)
  ]

  # ---------------------------------------------------------------------------------------------------------------------
  # Optional
  # ---------------------------------------------------------------------------------------------------------------------

  prefix = var.prefix
  suffix = var.suffix
}
