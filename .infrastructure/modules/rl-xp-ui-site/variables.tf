variable "aws_region" {}
variable "aws_account_id" {}
variable "environment" {}
variable "rl_xp_ui_domain_name" {}
variable "route53_zone_name" {}
variable "index_document" {
  description = "The path to the index document in the S3 bucket (e.g. index.html)."
  default     = "index.html"
}

variable "error_document" {
  description = "The path to the error document in the S3 bucket (e.g. error.html)."
  default     = "error.html"
}

variable acm_certificate_domain_name {
  description = "The domain name for which an ACM cert has been issued (e.g. *.foo.com). Only used if var.create_route53_entry is true. Set to blank otherwise."
  type        = string
  default     = null
}

variable "prefix" {
  description = "Add additional prefix to beginning of resource names."
  type        = string
  default     = ""
}

variable "suffix" {
  description = "Add additional suffix to end of resource names."
  type        = string
  default     = ""
}