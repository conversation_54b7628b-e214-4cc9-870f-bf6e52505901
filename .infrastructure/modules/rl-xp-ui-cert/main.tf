locals {
  contact_email = "<EMAIL>"
  app_id = "214288"
  hal_app_id = "17876"
}

module "cert" {
  source = "git::https://git.rockfin.com/terraform/aws-acm-certificate-tf?ref=4.0.0"

  # ---------------------------------------------------------------------------------------------------------------------
  # Required variables for AWS
  # ---------------------------------------------------------------------------------------------------------------------

  # aws_region     = var.aws_region
  # aws_account_id = var.aws_account_id

  # ---------------------------------------------------------------------------------------------------------------------
  # Standard Module Required Variables
  # ---------------------------------------------------------------------------------------------------------------------

  app_id           = local.app_id
  environment      = var.environment

  development_team_email = local.contact_email
  infrastructure_team_email = local.contact_email
  infrastructure_engineer_email = local.contact_email
  # ---------------------------------------------------------------------------------------------------------------------
  # Infrastructure Tags
  # ---------------------------------------------------------------------------------------------------------------------

  app_tags = {
    hal-app-id = local.hal_app_id
  }

  # ---------------------------------------------------------------------------------------------------------------------
  # Infrastructure Variables
  # ---------------------------------------------------------------------------------------------------------------------

  route53_zone = var.route53_zone_name

  certificate_domains = concat([var.rl_xp_ui_domain_name], var.rl_xp_ui_additional_domain_names)
}