/**
*  RLXP STYLING
*  This file is the entry point for the application's styles and should be the only file added to the angular.json styles array.
*  It imports all the necessary styles for the application and sets up the theme for the application.
*  It also includes global styles that are used throughout the application.
*  RLXP will have its own layer of styles that will be imported here, most of which will map to the Rocket Design System but will allow for overrides.
*  Figma remains the source of truth and ideally the Figma spec will follow RDS closely.
*  Theme overrides will require css variables and be used as var() references within components.
*  Legacy theme overrides are Sass Mixins following the Angular Material style.
*  Angular Material theme overrides will continue to be the theme mixin format.  
**/

@use 'sass:map';
/**
* Angular Material + Angular Custom Themes(Dark Mode, Light Mode)
**/
@use '@angular/material' as mat;
@use './styling/theme/angular-material-themes' as angular-material;

/**
* Rocket Design System v17
**/
@use './styling/theme/overrides/_rds-overrides.scss' as *;
@use '@rocketcentral/rocket-design-system-styles/rocket' as rocket;
@use '@rocketcentral/rocket-design-system-styles/theme-dark' as rocket-dark;
@use '@rocketcentral/rocket-design-system-styles/theme-enterprise' as rocket-enterprise;
@use '@rocketcentral/rocket-design-system-styles/theme-dark-enterprise' as rocket-enterprise-dark;

/**
* RLXP Theme 
**/
@use './styling/theme/rlxp-theme' as rlxp-theme;
@use './styling/theme/aggregate-by-value/index' as aggregate-by-value;

/**
* Theme Overrides for Angular Material Components 
**/
@use 'styling/theme/overrides/rlxp-form-field.theme' as rlxp-form-field;
@use 'styling/theme/overrides/rlxp-radio-huge.theme' as rlxp-radio-huge;
@use 'styling/theme/overrides/rlxp-app-island.theme' as rlxp-app-island;
@use 'styling/theme/overrides/rlxp-form-section.theme' as rlxp-form-section;
@use 'styling/theme/overrides/rlxp-loan-form.theme' as rlxp-loan-form;
@use 'styling/theme/overrides/rlxp-vertical-navbar.theme' as rlxp-vertical-navbar;
@use './app/tile/tile.component-theme' as rlxp-tile;
@use './app/header/_header.theme' as rlxp-header;
@use 'styling/theme/overrides/_rl-xp-error-button.theme' as rlxp-error-button;

/**
* Custom Styles
**/
@use './styling/skeleton-common' as skeleton-common;
@use './styling/theme/overrides/form-field-overrides' as form-field-overrides;
@use './styling/accordion-panel-outlined.scss' as accordion-panel-outlined;

/* Orphaned global styles */
@use './styling/theme/overrides/global' as global;
@use './app/assistant/rla-highlight-mat-form-field.scss';

/* Angular Material core setup */
@include mat.core();

/* Apply our custom theme to all Angular Material components */
@include mat.all-component-themes(angular-material.$rlxp-light-theme);

body {
  color: var(--Mapped-Text-text-primary);
  background-color: var(--rlxp-application-background-color);
}

.rkt-LightMode {
  $theme: angular-material.$rlxp-light-theme;
  // Overrides
  @include rlxp-tile.theme($theme);
  @include rlxp-app-island.theme($theme);
  @include rlxp-form-section.theme($theme);
  @include rlxp-vertical-navbar.theme($theme);
  @include rlxp-header.theme($theme);
  @include rlxp-loan-form.theme($theme);
  @include rlxp-form-field.theme($theme);
  @include rlxp-radio-huge.theme($theme);
  @include rlxp-error-button.theme($theme);
}

.rkt-DarkMode {
  @include mat.all-component-colors(angular-material.$rlxp-dark-theme);

  $theme: angular-material.$rlxp-dark-theme;
  // Overrides
  @include rlxp-radio-huge.theme($theme);
  @include rlxp-tile.theme($theme);
  @include rlxp-app-island.theme($theme);
  @include rlxp-form-section.theme($theme);
  @include rlxp-vertical-navbar.theme($theme);
  @include rlxp-header.theme($theme);
  @include rlxp-loan-form.theme($theme);
  @include rlxp-form-field.theme($theme);
  @include rlxp-radio-huge.theme($theme);
  @include rlxp-error-button.theme($theme);
}
