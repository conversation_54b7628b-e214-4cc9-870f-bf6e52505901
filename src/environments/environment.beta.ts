import { EnvironmentConfig } from './environment-config';
import { environmentCommon } from './environment.common';

export const environment: EnvironmentConfig = {
  ...environmentCommon,
  appEnvironment: 'beta',
  dataProviderUrl: 'https://beta-rl-xp-bff.np.rocket-logic.foc.zone',
  assistantUrl: 'https://beta-rl-xp-bff.np.rocket-logic.foc.zone',
  splunkForwarderUrl: 'https://splunk-log-forwarder.beta.assets-np.foc.zone/log/messages',
  productsUrl: 'https://beta.np.rocket-logic.foc.zone/einstein-mortgage-qualifier',
  legacyBankingUrl: 'https://beta.np.rocket-logic.foc.zone/quick-app-dynamic',
  creditReportUrl: 'https://beta.np.rocket-logic.foc.zone/quick-app-dynamic/credit-report/',
  auth0: {
    domain: 'sso.beta.authrock.com',
    clientId: 'i0PuCcm4GvcmVpRhaI05p1P3291xCt1a',
    audience: 'urn:ql-api:rocket_logic_experience_bff-214291:Beta',
  },
  support: {
    stage: 'beta',
    definitionOrchUrl: 'https://beta-rl-xp-bff.np.rocket-logic.foc.zone',
  },
  segmentWriteKey: 'qYEeZlZZYV6AQvJKyt9pToYsm2h6Bf1x',
  amplitudeApiKey: 'cff030895ca40f9822706e975b38c6bd',
  featureFlags: {
    allowRefiAccess: true,
    enableSchwab: true,
    enableSpouseFlow: true,
    allowAmeripriseHeloc: true,
  },
  avoWebDebugger: false,
  estimator: {
    enabled: true
  },
  rla: {
    rlaFeedbackUrl: 'https://rla-feedback-beta.np.rocket-logic.foc.zone',
  },
};
