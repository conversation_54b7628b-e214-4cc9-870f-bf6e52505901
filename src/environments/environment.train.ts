import { EnvironmentConfig } from './environment-config';
import { environmentCommon } from './environment.common';

export const environment: EnvironmentConfig = {
  ...environmentCommon,
  appEnvironment: 'train',
  dataProviderUrl: 'https://train-rl-xp-bff.np.rocket-logic.foc.zone',
  assistantUrl: 'https://train-rl-xp-bff.np.rocket-logic.foc.zone',
  splunkForwarderUrl: 'https://splunk-log-forwarder.train.assets-np.foc.zone/log/messages',
  productsUrl: 'https://train.np.rocket-logic.foc.zone/einstein-mortgage-qualifier',
  legacyBankingUrl: 'https://train.np.rocket-logic.foc.zone/quick-app-dynamic',
  creditReportUrl: 'https://train.np.rocket-logic.foc.zone/quick-app-dynamic/credit-report/',
  auth0: {
    domain: 'sso.beta.authrock.com',
    clientId: 'i0PuCcm4GvcmVpRhaI05p1P3291xCt1a',
    audience: 'urn:ql-api:rocket_logic_experience_bff-214291:Beta',
  },
  support: {
    stage: 'train',
    definitionOrchUrl: 'https://train-rl-xp-bff.np.rocket-logic.foc.zone',
  },
  featureFlags: {
    allowRefiAccess: false,
    enableSchwab: true,
    enableSpouseFlow: true,
    allowAmeripriseHeloc: true,
  },
  avoWebDebugger: false,
  estimator: {
    enabled: false
  },
  rla: {
    rlaFeedbackUrl: 'https://rla-feedback-beta.np.rocket-logic.foc.zone',
  },
};
