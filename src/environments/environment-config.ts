import { SupportEnvironment } from '@rocket-logic/support';
import { TranscriptionExtractedInfo } from '../app/assistant/models/transcription-extracted-info';

export interface EnvironmentConfig {
  appName: string;
  appEnvironment: string;
  dataProviderUrl: string;
  assistantUrl: string;
  splunkForwarderUrl: string;
  productsUrl: string;
  legacyBankingUrl: string;
  creditReportUrl: string;
  auth0: {
    domain: string;
    clientId: string;
    audience: string;
  };
  support: SupportEnvironment;
  scrollBehavior?: ScrollBehavior;
  segmentWriteKey?: string;
  amplitudeApiKey?: string;
  featureFlags?: {
    allowRefiAccess: boolean;
    enableSchwab?: boolean;
    enableSpouseFlow?: boolean;
    allowAmeripriseHeloc: boolean;
  };
  tabLimit: number;
  avoWebDebugger: boolean;
  enableAvo: boolean;
  estimator?: {
    useMockData?: boolean;
    enabled?: boolean;
  };
  rla: {
    rlaFeedbackUrl: string;
    mockExtractedData?: TranscriptionExtractedInfo;
    mockOverallSummary?: string;
    mockCombinedTranscript?: string;
    enableSidebarAutoOpen?: boolean;
  };
}
