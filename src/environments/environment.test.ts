import { EnvironmentConfig } from './environment-config';
import { environmentCommon } from './environment.common';

export const environment: EnvironmentConfig = {
  ...environmentCommon,
  appEnvironment: 'test',
  dataProviderUrl: 'https://test-rl-xp-bff.np.rocket-logic.foc.zone',
  splunkForwarderUrl: 'https://splunk-log-forwarder.test.assets-np.foc.zone/log/messages',
  productsUrl: 'https://test.np.rocket-logic.foc.zone/einstein-mortgage-qualifier',
  legacyBankingUrl: 'https://test.np.rocket-logic.foc.zone/quick-app-dynamic',
  creditReportUrl: 'https://test.np.rocket-logic.foc.zone/quick-app-dynamic/credit-report/',
  assistantUrl: 'https://test-rl-xp-bff.np.rocket-logic.foc.zone',
  auth0: {
    domain: 'sso.test.authrock.com',
    clientId: 'P7k0AzOUDFbxO2GC2L02y7AS1debOZ8f',
    audience: 'urn:ql-api:rocket_logic_experience_bff-214291:Test',
  },
  support: {
    stage: 'test',
    definitionOrchUrl: 'https://test-rl-xp-bff.np.rocket-logic.foc.zone',
  },
  segmentWriteKey: 'qYEeZlZZYV6AQvJKyt9pToYsm2h6Bf1x',
  amplitudeApiKey: 'cff030895ca40f9822706e975b38c6bd',
  featureFlags: {
    allowRefiAccess: true,
    enableSchwab: true,
    enableSpouseFlow: true,
    allowAmeripriseHeloc: true,
  },
  avoWebDebugger: false,
  estimator: {
    enabled: true
  },
  rla: {
    rlaFeedbackUrl: 'https://rla-feedback-test.np.rocket-logic.foc.zone',
  },
};
