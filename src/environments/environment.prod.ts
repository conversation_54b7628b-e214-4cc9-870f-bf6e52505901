import { EnvironmentConfig } from './environment-config';
import { environmentCommon } from './environment.common';

export const environment: EnvironmentConfig = {
  ...environmentCommon,
  appEnvironment: 'prod',
  dataProviderUrl: 'https://rl-xp-bff.rocket-logic.foc.zone',
  assistantUrl: 'https://rl-xp-bff.rocket-logic.foc.zone',
  splunkForwarderUrl: 'https://splunk-log-forwarder.assets.foc.zone/log/messages',
  productsUrl: 'https://rocket-logic.foc.zone/einstein-mortgage-qualifier',
  legacyBankingUrl: 'https://rocket-logic.foc.zone/quick-app-dynamic',
  creditReportUrl: 'https://rocket-logic.foc.zone/quick-app-dynamic/credit-report/',
  auth0: {
    domain: 'sso.authrock.com',
    clientId: 'pdwkOmU1NUZeezBksB3OCWAWmkc0z6mh',
    audience: 'urn:ql-api:rocket_logic_experience_bff-214291:Prod',
  },
  support: {
    stage: 'prod',
    definitionOrchUrl: 'https://rl-xp-bff.rocket-logic.foc.zone',
  },
  segmentWriteKey: 'BAd92ufLP2nNFFzA8txkaFZS9Khei6Je',
  amplitudeApiKey: '822804a5db38a6281ea13b8a56874244',
  featureFlags: {
    allowRefiAccess: true,
    enableSchwab: true,
    enableSpouseFlow: true,
    allowAmeripriseHeloc: true,
  },
  avoWebDebugger: false,
  enableAvo: true,
  estimator: {
    enabled: true,
  },
  rla: {
    rlaFeedbackUrl: 'https://rla-feedback.rocket-logic.foc.zone',
  },
};
