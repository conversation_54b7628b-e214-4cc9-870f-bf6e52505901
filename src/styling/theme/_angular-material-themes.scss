@use '@angular/material' as mat;
@use './typography' as typography;
@use './palettes' as palettes;

$rlxp-material-typography: mat.define-typography-config($font-family: typography.$rlxp-typography-font-family-rocket,
        $headline-1: mat.define-typography-level($font-family: typography.$rlxp-typography-font-family-heading-28,
            $font-weight: typography.$rlxp-typography-font-weight-heading-28,
            $font-size: typography.$rlxp-typography-font-size-heading-28,
            $line-height: typography.$rlxp-typography-line-height-heading-28,
            $letter-spacing: normal,
        ),
        $headline-2: mat.define-typography-level($font-family: typography.$rlxp-typography-font-family-heading-36,
            $font-weight: typography.$rlxp-typography-font-weight-500,
            $font-size: typography.$rlxp-typography-font-size-heading-36,
            $line-height: typography.$rlxp-typography-line-height-heading-36,
            $letter-spacing: normal,
        ),
        $headline-3: mat.define-typography-level($font-family: typography.$rlxp-typography-font-family-heading-24,
            $font-weight: typography.$rlxp-typography-font-weight-heading-24,
            $font-size: typography.$rlxp-typography-font-size-heading-24,
            $line-height: typography.$rlxp-typography-line-height-heading-24,
            $letter-spacing: normal,
        ),
        $headline-4: mat.define-typography-level($font-family: typography.$rlxp-typography-font-family-heading-24,
            $font-weight: typography.$rlxp-typography-font-weight-heading-24,
            $font-size: typography.$rlxp-typography-font-size-heading-24,
            $line-height: typography.$rlxp-typography-line-height-heading-24,
            $letter-spacing: normal,
        ),
        $body-1: mat.define-typography-level($font-family: typography.$rlxp-typography-font-family-body-16,
            $font-weight: typography.$rlxp-typography-font-weight-500,
            $font-size: typography.$rlxp-typography-font-size-body-16,
            $line-height: typography.$rlxp-typography-line-height-body-16,
            $letter-spacing: normal,
        ),
        $body-2: mat.define-typography-level($font-family: typography.$rlxp-typography-font-family-body-16,
            $font-weight: typography.$rlxp-typography-font-weight-700,
            $font-size: typography.$rlxp-typography-font-size-body-16,
            $line-height: typography.$rlxp-typography-line-height-body-16,
            $letter-spacing: normal,
        ),
        $caption: mat.define-typography-level($font-family: typography.$rlxp-typography-font-family-caption,
            $font-weight: typography.$rlxp-typography-font-weight-caption,
            $font-size: typography.$rlxp-typography-font-size-caption,
            $line-height: typography.$rlxp-typography-line-height-caption,
            $letter-spacing: normal,
        ),
        $button: mat.define-typography-level($font-family: typography.$rlxp-typography-font-family-button,
            $font-weight: typography.$rlxp-typography-font-weight-button,
            $font-size: typography.$rlxp-typography-font-size-button,
            $line-height: typography.$rlxp-typography-line-height-button,
            $letter-spacing: normal,
        ),
        $subtitle-1: mat.define-typography-level($font-family: typography.$rlxp-typography-font-family-subheading-one,
            $font-weight: typography.$rlxp-typography-font-weight-subheading-one,
            $font-size: typography.$rlxp-typography-font-size-subheading-one,
            $line-height: typography.$rlxp-typography-line-height-subheading-one,
            $letter-spacing: normal,
        ),
        $subtitle-2: mat.define-typography-level($font-family: typography.$rlxp-typography-font-family-subheading-two,
            $font-weight: typography.$rlxp-typography-font-weight-subheading-two,
            $font-size: typography.$rlxp-typography-font-size-subheading-two,
            $line-height: typography.$rlxp-typography-line-height-subheading-two,
            $letter-spacing: normal,
        ),
    );




$rlxp-light-theme: mat.define-light-theme((color: (primary: mat.define-palette(palettes.$rlxp-red, 700),
                accent: mat.define-palette(palettes.$rlxp-gray, 800),
                warn: mat.define-palette(palettes.$rlxp-rose, 600),
            ),
            typography: $rlxp-material-typography,
        ));

$rlxp-dark-theme: mat.define-dark-theme((color: (primary: mat.define-palette(palettes.$rlxp-dark-red, 700),
                accent: mat.define-palette(palettes.$rlxp-dark-gray, 800),
                warn: mat.define-palette(palettes.$rlxp-rose, 500),
            ),
            typography: $rlxp-material-typography,
        ));


/**
* Determine theme version (M2 or M3) for Angular Material (Will likely change to M3 around Angular 19, currently in preview for Angular 18)
**/
@if (mat.get-theme-version($rlxp-dark-theme)==1) {
    @debug 'M3 theme';
}

@else {
    @debug 'M2 theme';
}