// @use '@angular/material' as mat;
@use 'sass:map';
@use '@angular/material' as mat;
@use '@rocketcentral/rocket-design-system-styles/web/scss/_color.scss' as rkt-colors;


@mixin theme($theme) {
  mat-form-field.mat-mdc-form-field.mat-form-field-appearance-outline {

    .mat-mdc-text-field-wrapper {
      background: var(--rlxp-form-field-background-color);

      &.mdc-text-field--disabled {
        background: var(--rlxp-form-field-disabled-background-color);
      }
    }

  }

  mat-form-field.mat-form-field-appearance-outline.active-input-section {
    --rlxp-form-field-background-color: var(--rlxp-form-field-active-milestone-background-color);
    @if mat.get-theme-type($theme) == dark {
      --mdc-outlined-text-field-focus-outline-color: var(--rlxp-tangerine-dark-500);
    }
  }

  mat-form-field.rlxp-FormField--read-only {
    outline-color: mat.get-theme-color($theme, accent, 50);
  }
}
