:root {
  --mat-form-field-container-height: 40px;
  --mat-form-field-container-vertical-padding: 8px;
}

label.mdc-floating-label.mat-mdc-floating-label {
  padding: 5px;
}

div.mat-mdc-form-field-icon-prefix span {
  padding-left: 5px;
}

mat-card.mdc-card.mat-mdc-card {
  max-width: unset;
}

label.mdc-floating-label.mat-mdc-floating-label {
  padding: unset;
}

button.mat-mdc-icon-button.mat-mdc-button-base,
.mat-datepicker-toggle > button.mdc-icon-button {
  margin: 0;
  padding: 4px;
  --mdc-icon-button-state-layer-size: 32px;
}

mat-form-field.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper {
  padding-right: 16px;
}

mat-form-field.mat-mdc-form-field.mat-form-field-appearance-outline
  .mat-mdc-text-field-wrapper {
  border-radius: var(--mdc-outlined-text-field-container-shape);
}
