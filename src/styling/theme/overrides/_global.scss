/**
* File for global overrides without a home. This should be a last resort for overrides. Typically what you would find in styles.scss.
* This file is imported into styles.scss 
*/

body,
html {
  height: 100%;
  width: 100%;
  scrollbar-color: rgb(217, 217, 217) transparent;
}

.row {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 1rem;
}

.flex-wrap {
  display: flex;
  flex-wrap: wrap;
}

input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.app-Icon--success,
.rkt-DarkMode
  .app-Icon--success:not(
    .rkt-AccordionPanel--on-dark .app-Icon--success,
    .rkt-ButtonToggle--on-dark .app-Icon--success,
    .rkt-Button--on-dark .app-Icon--success,
    .rkt-Card--on-dark .app-Icon--success,
    .rkt-Chip--on-dark .app-Icon--success,
    .rkt-Link--on-dark .app-Icon--success,
    .rkt-Tag--on-dark .app-Icon--success,
    .rkt-Toggle--on-dark .app-Icon--success,
    .rkt-Tabs--on-dark .app-Icon--success,
    .rkt-Tooltip--on-dark .app-Icon--success
  ):not(.app-Icon--disabled) {
  color: var(--rlxp-green-500);
}

.app-Icon--error,
.rkt-DarkMode
  .app-Icon--error:not(
    .rkt-AccordionPanel--on-dark .app-Icon--error,
    .rkt-ButtonToggle--on-dark .app-Icon--error,
    .rkt-Button--on-dark .app-Icon--error,
    .rkt-Card--on-dark .app-Icon--error,
    .rkt-Chip--on-dark .app-Icon--error,
    .rkt-Link--on-dark .app-Icon--error,
    .rkt-Tag--on-dark .app-Icon--error,
    .rkt-Toggle--on-dark .app-Icon--error,
    .rkt-Tabs--on-dark .app-Icon--error,
    .rkt-Tooltip--on-dark .app-Icon--error
  ):not(.app-Icon--disabled) {
  color: var(--rlxp-icon-warn-color);
}

.active-form-section {
  app-form-section {
    .section-container {
      outline-width: 2px;
    }
  }
}

.rkt-Alert {
  padding: 12px 12px 12px 12px;
}

p.rkt-Alert__text {
  margin: 0px;
}

/**
  * Mat Icon overrides for Main Logo
  */

.logo {
  mat-icon svg {
    height: 100%;
    width: 100%;
  }

  .mat-mdc-icon-button:hover .mat-mdc-button-persistent-ripple::before {
    opacity: 0;
    background-color: transparent;
  }
}

.rkt-Button.rkt-Button--large {
  height: 40px;
  padding: 8px 16px;
  font-size: 0.875rem;
  line-height: 1.25rem;

  &.mat-mdc-button > .mat-icon,
  &.mat-mdc-unelevated-button > .mat-icon,
  &.mat-mdc-raised-button > .mat-icon,
  &.mat-mdc-outlined-button > .mat-icon {
    font-size: 1.5rem;
    height: 1.5rem;
    width: 1.5rem;
  }
}

.rkt-Button--tertiary.rkt-Button--has-icon:not(
    .rkt-Button--is-spinning,
    .rkt-AccordionPanel--on-dark .rkt-Button,
    .rkt-Button--on-dark,
    .rkt-Card--on-dark .rkt-Button,
    .rkt-Toggle--on-dark .rkt-Button,
    .rkt-Tabs--on-dark .rkt-Button
  ).rkt-Button--is-disabled
  .rkt-Icon {
  color: var(--rlxp-tertiary-button-disabled);
}

.rkt-Chip.mat-mdc-chip.mat-mdc-standard-chip {
  --mdc-chip-container-height: 24px;
  margin-top: 0;
  margin-bottom: 0;
}

.mat-body p,
.mat-body-2 p,
.mat-typography .mat-body p,
.mat-typography .mat-body-2 p,
.mat-typography p {
  margin: 0 0 12px;
}

.rkt-DarkMode .rkt-Menu__item-text {
  color: white;
}

// Angular 15 makes the text visually smaller
// so we unset to correct the visual difference
.mdc-button,
.mat-mdc-standard-chip,
.mdc-form-field,
.mat-mdc-form-field,
.mat-mdc-floating-label,
.mdc-text-field__input,
.mdc-floating-label,
.mat-mdc-menu-content,
.mat-mdc-menu-content .mat-mdc-menu-item .mdc-list-item__primary-text,
.mat-mdc-paginator,
.mat-mdc-select,
.mat-mdc-option,
.mat-mdc-select-panel,
.mdc-data-table__header-cell,
.mdc-data-table__cell,
.mdc-tab {
  -webkit-font-smoothing: unset;
  -moz-osx-font-smoothing: unset;
}

// rkt-Dialog mat-dialog-content element cuts off floating labels by default
.rkt-Dialog mat-dialog-content.mat-mdc-dialog-content.mdc-dialog__content {
  padding: 6px 0 0 0;
}

.left-panel-overlay-container.cdk-overlay-container {
  z-index: 3;
}

mat-icon.mat-icon.mat-icon.mat-icon.mat-icon.rkt-Icon:not(.mat-icon-no-color) {
  color: var(--mat-icon-color);
}

.dark-mode .body-wrapper lib-icon-button button[type='button'] {
  background: var(--rlxp-white);
}
