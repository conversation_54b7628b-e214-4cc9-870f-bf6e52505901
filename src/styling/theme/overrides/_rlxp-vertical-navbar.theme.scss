@use '@angular/material' as mat;

@mixin theme($theme) {

    // Dark version
    @if mat.get-theme-type($theme)==dark {
        .vertical-navbar {
            background-color: mat.get-theme-color($theme, background, app-bar);
            border-right: 1px solid mat.get-theme-color($theme, foreground, divider);

            app-form-nav {
                app-nav-item {
                    border-color: mat.get-theme-color($theme, foreground, divider);
                    border-left-color: mat.get-theme-color($theme, primary, 600);

                    &:hover {
                        background-color: mat.get-theme-color($theme, background, hover);
                    }
                }
            }
        }
    }

    // Light version
    @else {
        .vertical-navbar {
            background-color: var(--rlxp-vertical-navbar-background-color);
            border-right: 1px solid var(--rlxp-gray-200);

            app-form-nav {
                app-nav-item {
                    border-color: mat.get-theme-color($theme, foreground, divider);
                    border-left-color: mat.get-theme-color($theme, primary, 600);

                    &:hover {
                        background-color: mat.get-theme-color($theme, background, hover);
                    }
                }
            }


        }
    }

}