@use '@angular/material' as mat;

@mixin theme($theme) {
  app-form-section {
    // Any Theme
    mat-card.mat-mdc-card.mdc-card.section-container {
      background-color: var(--rlxp-form-section-background-color);
      outline-style: solid;
      outline-width: 1px;
    }

    // Dark Theme
    @if mat.get-theme-type($theme) ==dark {
      .section-container.active-form-section {
        outline-width: 2px;
        outline-color: var(--rlxp-gray-200);
      }

      .section-container {
        outline-color: var(--rlxp-gray-600);
      }
    }

    // Light Theme
    @else {
      .section-container.active-form-section {
        outline-width: 2px;
        outline-color: #545453;
      }

      .section-container {
        outline-color: rgb(222, 222, 222);
      }
    }
  }
}
