/*
 *_var-overrides.scss

* Overrides for RDS variables
*/
@use '../colors-dark' as rlxp-colors-dark;
@use '@rocketcentral/rocket-design-system-styles/web/scss/_color.scss' as *;
@use '@rocketcentral/rocket-design-system-styles/web/scss/_color-dark.scss' as *;

@use '@rocketcentral/rocket-design-system-styles/web/scss/_vars.scss' as * with (
    $rkt-alert-icon-color-warn: $rkt-red-800,
    $rkt-alert-border-warn: 1px solid $rkt-red-800,
    $rkt-alert-bg-color-warn: $rkt-red-50,
    $rkt-button-warning-outlined-border: solid 1px $rkt-red-800,
    $rkt-button-warning-outlined-color: $rkt-red-800,
    $rkt-button-warning-outlined-spinning-border-color: $rkt-red-800,
    $rkt-button-warning-outlined-spinning-color: $rkt-red-800,
    $rkt-button-warning-outlined-spinning-spinner-color: $rkt-red-800,
    $rkt-button-warning-outlined-hover-background-color: $rkt-red-800,
    $rkt-button-warning-outlined-focus-background-color: $rkt-red-500,
    $rkt-button-warning-outlined-active-background-color: $rkt-red-50,
    $rkt-button-warning-outlined-active-color: $rkt-red-800,
    $rkt-button-warning-background-color: $rkt-red-800,
    $rkt-button-warning-hover-color: $rkt-red-800,
    $rkt-button-warning-hover-box-shadow: inset 0 0 0 1px $rkt-red-800,
    $rkt-button-warning-focus-background-color: $rkt-red-50,
    $rkt-button-warning-focus-color: $rkt-red-800,
    $rkt-button-warning-focus-box-shadow: inset 0 0 0 2px $rkt-red-800,
    $rkt-button-warning-active-background-color: $rkt-red-800,
    $rkt-button-warning-spinning-background-color: $rkt-red-50,
    $rkt-button-warning-spinning-color: $rkt-red-800,
    $rkt-button-warning-spinning-spinner-color: $rkt-red-800,
    $rkt-credit-score-chart-change-indicator-icon-decrease-color: $rkt-red-800,
    $rkt-file-uploader-list-item-status-icon-error-color: $rkt-red-800,
    $rkt-input-color-error: var(--mat-form-field-error-text-color),
 );


 @use '@rocketcentral/rocket-design-system-styles/web/scss/_dark-settings' as * with (
    $rkt-alert-dark-warn-background-color: $rkt-red-100,
    $rkt-alert-dark-warn-border-color: $rkt-red-500,
    $rkt-alert-dark-warn-icon-color: $rkt-red-500,
    $rkt-button-dark-icon-color: $rkt-red-500,
    $rkt-button-dark-warning-background-color: $rkt-red-500,
    $rkt-button-dark-warning-focus-background-color: $rkt-red-100,
    $rkt-button-dark-warning-focus-box-shadow: inset 0 0 0 2px $rkt-red-500,
    $rkt-button-dark-warning-focus-color: $rkt-red-500,
    $rkt-button-dark-warning-hover-box-shadow: inset 0 0 0 1px $rkt-red-500,
    $rkt-button-dark-warning-hover-color: $rkt-red-500,
    $rkt-button-dark-warning-outlined-active-background-color: $rkt-red-100,
    $rkt-button-dark-warning-outlined-active-color: $rkt-red-500,
    $rkt-button-dark-warning-outlined-border: solid 1px $rkt-red-500,
    $rkt-button-dark-warning-outlined-color: $rkt-red-500,
    $rkt-button-dark-warning-outlined-focus-background-color: $rkt-red-500,
    $rkt-button-dark-warning-outlined-hover-background-color: $rkt-red-500,
    $rkt-button-dark-warning-outlined-spinning-border: solid 1px $rkt-red-500,
    $rkt-button-dark-warning-outlined-spinning-color: $rkt-red-500,
    $rkt-button-dark-warning-outlined-spinning-spinner-color: $rkt-red-500,
    $rkt-button-dark-warning-spinning-background-color: $rkt-red-100,
    $rkt-button-dark-warning-spinning-color: $rkt-red-500,
    $rkt-button-dark-warning-spinning-spinner-color: $rkt-red-500,
    $rkt-card-dark-link-error-border: solid 2px $rkt-red-500,
    $rkt-credit-score-chart-dark-change-indicator-icon-decrease-color: $rkt-red-500,
    $rkt-file-uploader-dark-action-item-invalid-border-color: $rkt-red-500,
    $rkt-file-uploader-dark-drop-area-invalid-border-color: $rkt-red-500,
    $rkt-file-uploader-dark-list-error-color: $rkt-red-500,
    $rkt-file-uploader-dark-list-item-error-background: $rkt-red-100,
    $rkt-input-dark-error-color: rlxp-colors-dark.$rlxp-input-error-color,
    $rkt-menu-dark-item-notification-icon-red-background-color: $rkt-red-500,
    $rkt-stepper-dark-dot-error-color: $rkt-red-500,
    $rkt-stepper-dark-label-error-color: $rkt-red-500,
    $rkt-tag-dark-warn-background-color: $rkt-red-100,
    $rkt-tag-dark-warn-icon-color: $rkt-red-500,
    $rkt-tag-dark-warn-text-color: $rkt-red-500,
    $rkt-huge-radio-dark-disabled-border-color: rgba(255, 255, 255, 0.38),
);


