@use '@rocketcentral/rocket-design-system-styles/web/scss/_color-dark.scss' as *;

:root {
    --rlxp-red-dark-50: #{$rkt-red-dark-50};
    --rlxp-red-dark-100: #{$rkt-red-dark-100};
    --rlxp-red-dark-200: #{$rkt-red-dark-200};
    --rlxp-red-dark-300: #{$rkt-red-dark-300};
    --rlxp-red-dark-400: #{$rkt-red-dark-400};
    --rlxp-red-dark-600: #{$rkt-red-dark-600};
    --rlxp-red-dark-700: #{$rkt-red-dark-700};
    --rlxp-red-dark-800: #{$rkt-red-dark-800};
    --rlxp-red-dark-900: #{$rkt-red-dark-900};
  
    --rlxp-blue-dark-50: #{$rkt-blue-dark-50};
    --rlxp-blue-dark-100: #{$rkt-blue-dark-100};
    --rlxp-blue-dark-200: #{$rkt-blue-dark-200};
    --rlxp-blue-dark-300: #{$rkt-blue-dark-300};
    --rlxp-blue-dark-400: #{$rkt-blue-dark-400};
    --rlxp-blue-dark-500: #{$rkt-blue-dark-500};
    --rlxp-blue-dark-600: #{$rkt-blue-dark-600};
    --rlxp-blue-dark-700: #{$rkt-blue-dark-700};
    --rlxp-blue-dark-800: #{$rkt-blue-dark-800};
    --rlxp-blue-dark-900: #{$rkt-blue-dark-900};
  
    --rlxp-orange-dark-50: #{$rkt-orange-dark-50};
    --rlxp-orange-dark-100: #{$rkt-orange-dark-100};
    --rlxp-orange-dark-200: #{$rkt-orange-dark-200};
    --rlxp-orange-dark-300: #{$rkt-orange-dark-300};
    --rlxp-orange-dark-400: #{$rkt-orange-dark-400};
    --rlxp-orange-dark-500: #{$rkt-orange-dark-500};
    --rlxp-orange-dark-600: #{$rkt-orange-dark-600};
    --rlxp-orange-dark-700: #{$rkt-orange-dark-700};
    --rlxp-orange-dark-800: #{$rkt-orange-dark-800};
    --rlxp-orange-dark-900: #{$rkt-orange-dark-900};
  
    --rlxp-green-dark-50: #{$rkt-green-dark-50};
    --rlxp-green-dark-100: #{$rkt-green-dark-100};
    --rlxp-green-dark-200: #{$rkt-green-dark-200};
    --rlxp-green-dark-300: #{$rkt-green-dark-300};
    --rlxp-green-dark-400: #{$rkt-green-dark-400};
    --rlxp-green-dark-500: #{$rkt-green-dark-500};
    --rlxp-green-dark-600: #{$rkt-green-dark-600};
    --rlxp-green-dark-700: #{$rkt-green-dark-700};
    --rlxp-green-dark-800: #{$rkt-green-dark-800};
    --rlxp-green-dark-900: #{$rkt-green-dark-900};
  
    --rlxp-purple-dark-bright-50: #{$rkt-purple-dark-bright-50};
    --rlxp-purple-dark-bright-500: #{$rkt-purple-dark-bright-500};
    --rlxp-teal-dark-500: #{$rkt-teal-dark-500};
    --rlxp-lime-dark-500: #{$rkt-lime-dark-500};
    --rlxp-tangerine-dark-50: #{$rkt-tangerine-dark-50};
    --rlxp-tangerine-dark-500: #{$rkt-tangerine-dark-500};
    --rlxp-rose-dark-50: #{$rkt-rose-dark-50};
    --rlxp-rose-dark-500: #{$rkt-rose-dark-500};
  
    --rlxp-gray-dark-50: #{$rkt-gray-dark-50};
    --rlxp-gray-dark-100: #{$rkt-gray-dark-100};
    --rlxp-gray-dark-200: #{$rkt-gray-dark-200};
    --rlxp-gray-dark-300: #{$rkt-gray-dark-300};
    --rlxp-gray-dark-400: #{$rkt-gray-dark-400};
    --rlxp-gray-dark-500: #{$rkt-gray-dark-500};
    --rlxp-gray-dark-600: #{$rkt-gray-dark-600};
    --rlxp-gray-dark-700: #{$rkt-gray-dark-700};
    --rlxp-gray-dark-800: #{$rkt-gray-dark-800};
    --rlxp-gray-dark-900: #{$rkt-gray-dark-900};
  
    --rlxp-gradient-dark-1: #{$rkt-gradient-dark-1};
    --rlxp-gradient-dark-2: #{$rkt-gradient-dark-2};
    --rlxp-gradient-dark-3: #{$rkt-gradient-dark-3};
  
    --rlxp-gradient-dark-border-1-border: #{$rkt-gradient-dark-border-1-border};
    --rlxp-gradient-dark-border-1-background-content-area-color: #{$rkt-gradient-dark-border-1-background-content-area-color};
    --rlxp-gradient-dark-border-1-background-border-area-color: #{$rkt-gradient-dark-border-1-background-border-area-color};
    --rlxp-gradient-dark-border-1-background-clip: #{$rkt-gradient-dark-border-1-background-clip};
    --rlxp-gradient-dark-border-1-background-origin: #{$rkt-gradient-dark-border-1-background-origin};
  }
  

  $rlxp-input-error-color: #F77CA9;
  $rlxp-input-warn-color: #F77CA9;
  $rlxp-lt-tangerine: #271F13;