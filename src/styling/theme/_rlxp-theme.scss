@use './colors' as colors;
@use './colors-dark' as colors-dark;

:root {
  --rlxp-default-border-radius: 12px;
  --rlxp-default-divider-color: #c6c5c5;
  --rlxp-primary-icon-color: var(--rlxp-red-700);
  --mat-sidenav-container-shape: 12px;
  --rlxp-warning-icon-color: #dd781b;
}

/**
* Format: {domain}-{optional variant}-{component/container/element/semantic identifier}-{optional variant}-{optional non default state}-{css property}
* For styles that are not theme specific, hoist to :root variables
**/
body.rkt-DarkMode {
  --Mapped-Surface-bg-elevation-default: #303030;
  --Mapped-Text-text-primary: #fff;
  --mat-expansion-panel-background-color: rgba(186, 186, 186, 0.1);
  --mat-expansion-panel-hover-background-color: rgba(255, 255, 255, 0.1);
  --mat-radio-checked-ripple-color: #9e9e9e;
  --mat-radio-disabled-label-color: rgba(255, 255, 255, 0.5);
  --mat-radio-ripple-color: white;
  --mat-sidenav-content-background-color: var(--Background-bg-primary, #191919);
  --mdc-filled-button-label-text-color: white;
  --mdc-form-field-label-text-color: #fff;
  --mdc-form-field-label-text-color: white;
  --mdc-outlined-text-field-disabled-outline-color: #7d7d7d;
  --mdc-radio-disabled-selected-icon-color: white;
  --mdc-radio-disabled-unselected-icon-color: white;
  --mdc-radio-selected-focus-icon-color: #9e9e9e;
  --mdc-radio-selected-hover-icon-color: #9e9e9e;
  --mdc-radio-selected-icon-color: #9e9e9e;
  --mdc-radio-selected-icon-color: white;
  --mdc-radio-selected-pressed-icon-color: #9e9e9e;
  --mdc-radio-unselected-hover-icon-color: #eeeeee;
  --mdc-radio-unselected-icon-color: rgba(255, 255, 255, 0.54);
  --mdc-radio-unselected-pressed-icon-color: rgba(255, 255, 255, 0.54);
  --rkt-huge-radio-dark-background-color: black;
  --rlxp-application-background-color: var(--Background-bg-primary, #0a0a0a);
  --rlxp-application-island-background-color: var(--Background-bg-secondary, #191919);
  --rlxp-button-group-disabled-divider-color: #444444;
  --rlxp-button-group-divider-color: #212121;
  --rlxp-card-credit-report-background-color: #0a0a0a;
  --rlxp-default-button-disabled-primary-text-color: rgb(138 138 138);
  --rlxp-default-button-primary-text-color: white;
  --rlxp-form-field-active-milestone-background-color: #271f13;
  --rlxp-form-field-background-color: black;
  --rlxp-form-section-background-color: #191919;
  --rlxp-green-icon-bg-color: #1c271f;
  --rlxp-green-icon-bg-color-hover: #1e3324;
  --rlxp-green-icon-bg-color-disabled: #161616;
  --rlxp-green-icon-color-disabled: #444444;
  --rlxp-icon-dropdown-color: white;
  --rlxp-icon-label: white;
  --rlxp-icon-label-disabled: #444444;
  --rlxp-icon-warn-color: #f77ca9;
  --rlxp-input-error-color: #f77ca9;
  --rlxp-long-form-background-color: var(--Background-bg-primary, #0a0a0a);
  --rlxp-nav-divider-color: #d3d3d3;
  --rlxp-progress-bar-container: black;
  --rlxp-recent-loan-list-item-background-color: #2d2d2d;
  --rlxp-recent-loan-list-item-hover-background-color: #3d3d3d;
  --rlxp-section-header-color: #fff;
  --rlxp-term-title-color: #d3d3d3;
  --rlxp-term-value-color: white;
  --rlxp-tile-background-color: var(--Background-bg-tertiary, #191919);
  --rlxp-tile-background-color-hover: #3d3d3d;
  --rlxp-tile-background-color-active: rgba(255, 255, 255, 0.2);
  --rlxp-tile-background-color-matched: #1c271f;
  --rlxp-tile-border-color: #7d7d7d;
  --rlxp-data-tile-icon-color: var(--rlxp-gray-500, #7d7d7d);
  --rlxp-tile-border-color-matched: #32a14e;
  --rlxp-tile-icon-background-color: #32a14e;
  --rlxp-tangerine-dark: #271f13;
  --rlxp-rose-dark: #2e0403;
  --rlxp-info-dark: #1d2530;
  --rlxp-data-tile-icon-color-matched: #32a14e;
  --rlxp-accordion-outlined-border-color: #444;
  --rlxp-accordion-outlined-background-color: var(--Background-bg-primary, #0a0a0a);
  --rlxp-settings-card-background-color: rgba(255, 255, 255, 0.1);
  --rlxp-form-field-disabled-background-color: #252525;
}

body.rkt-LightMode {
  --Mapped-Surface-bg-elevation-default: #fff;
  --Mapped-Text-text-primary: #000;
  --mat-expansion-panel-background-color: white;
  --mat-expansion-panel-header-hover: white;
  --mat-expansion-panel-header: white;
  --mat-expansion-panel-hover-background-color: white;
  --mat-radio-checked-ripple-color: #9e9e9e;
  --mat-radio-disabled-label-color: rgba(255, 255, 255, 0.5);
  --mat-radio-ripple-color: white;
  --mat-sidenav-container-background-color: #f8f8f8;
  --mat-sidenav-content-background-color: #f8f8f8;
  --mdc-filled-button-label-text-color: #707070;
  --mdc-form-field-label-text-color: #000;
  --mdc-form-field-label-text-color: black;
  --mdc-radio-disabled-selected-icon-color: white;
  --mdc-radio-disabled-unselected-icon-color: white;
  --mdc-radio-selected-focus-icon-color: #9e9e9e;
  --mdc-radio-selected-hover-icon-color: #9e9e9e;
  --mdc-radio-selected-icon-color: #9e9e9e;
  --mdc-radio-selected-icon-color: black;
  --mdc-radio-selected-pressed-icon-color: #9e9e9e;
  --mdc-radio-unselected-hover-icon-color: #eeeeee;
  --mdc-radio-unselected-icon-color: rgba(255, 255, 255, 0.54);
  --mdc-radio-unselected-pressed-icon-color: rgba(255, 255, 255, 0.54);
  --rlxp-application-background-color: #f2f2f2;
  --rlxp-application-island-background-color: var(--Background-bg-primary, white);
  --rlxp-button-group-disabled-divider-color: #949494;
  --rlxp-button-group-divider-color: white;
  --rlxp-card-credit-report-background-color: #fff;
  --rlxp-default-button-disabled-primary-text-color: rgb(138 138 138);
  --rlxp-default-button-primary-text-color: white;
  --rlxp-form-card-background-color: white;
  --rlxp-form-field-active-milestone-background-color: var(--rlxp-tangerine-50, #fff2d7);
  --rlxp-form-field-background-color: white;
  --rlxp-form-field-disabled-background-color: #d3d3d3;
  --rlxp-form-section-background-color: #f8f8f8;
  --rlxp-green-icon-bg-color: #eaf3ec;
  --rlxp-green-icon-bg-color-hover: #daecde;
  --rlxp-green-icon-bg-color-disabled: #f2f2f2;
  --rlxp-green-icon-color-disabled: #949494;
  --rlxp-header-background-color: var(--Background-bg-primary, white);
  --rlxp-icon-dropdown-color: black;
  --rlxp-icon-label: black;
  --rlxp-icon-label-disabled: #949494;
  --rlxp-icon-warn-color: #f77ca9;
  --rlxp-input-error-color: #f77ca9;
  --rlxp-long-form-background-color: #f2f2f2;
  --rlxp-nav-divider-color: #d3d3d3;
  --rlxp-progress-bar-container: white;
  --rlxp-purple-icon-color: #5f00a0;
  --rlxp-recent-loan-list-item-hover-background-color: #f8f8f8;
  --rlxp-section-header-color: #000;
  --rlxp-term-title-color: #545453;
  --rlxp-term-value-color: #545453;
  --rlxp-tile-background-color: #fff;
  --rlxp-tile-background-color-hover: #f2f2f2;
  --rlxp-tile-background-color-active: #b1b1b0;
  --rlxp-tile-background-color-matched: #eaf3ec;
  --rlxp-tile-border-color: #7d7d7d;
  --rlxp-data-tile-icon-color: var(--rlxp-gray-500, #7d7d7d);
  --rlxp-tile-border-color-matched: #32a14e;
  --rlxp-data-tile-icon-color-matched: #32a14e;
  --rlxp-vertical-navbar-background-color: var(--Background-bg-primary, white);
  --rlxp-tertiary-button-icon-disabled: #949494;
  --rlxp-mat-button-error: #d81b60;
  --rlxp-mat-button-error-label-text: #fff;
  --rlxp-accordion-outlined-border-color: var(--rlxp-gray-200);
  --rlxp-accordion-outlined-background-color: var(--Background-bg-secondary, #f8f8f8);
  --rlxp-settings-card-background-color: #fff;
}
