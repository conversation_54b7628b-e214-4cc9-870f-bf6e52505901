@use '@rocketcentral/rocket-design-system-styles/web/scss/_color.scss' as color;
@use '@rocketcentral/rocket-design-system-styles/web/scss/_color-dark.scss' as color-dark;

/** Primary **/
$rlxp-red: (
  50: color.$rkt-red-50,
  100: color.$rkt-red-100,
  200: color.$rkt-red-200,
  300: color.$rkt-red-300,
  400: color.$rkt-red-400,
  500: color.$rkt-red-500,
  600: color.$rkt-red-600,
  700: color.$rkt-red-700,
  800: color.$rkt-red-800,
  900: color.$rkt-red-900,
  contrast: (50: color.$rkt-white,
    100: color.$rkt-white,
    200: color.$rkt-white,
    300: color.$rkt-white,
    400: color.$rkt-white,
    500: color.$rkt-white,
    600: color.$rkt-white,
    700: color.$rkt-white,
    800: color.$rkt-white,
    900: color.$rkt-white,
    A100: color.$rkt-white,
    A200: color.$rkt-white,
    A400: color.$rkt-white,
    A700: color.$rkt-white,
  ),
);

/** Secondary **/
$rlxp-gray: (
  50: color.$rkt-gray-50,
  100: color.$rkt-gray-100,
  200: color.$rkt-gray-200,
  300: color.$rkt-gray-300,
  400: color.$rkt-gray-400,
  500: color.$rkt-gray-500,
  600: color.$rkt-gray-600,
  700: color.$rkt-gray-700,
  800: color.$rkt-gray-800,
  900: color.$rkt-gray-900,
  contrast: (50: color.$rkt-white,
    100: color.$rkt-white,
    200: color.$rkt-white,
    300: color.$rkt-white,
    400: color.$rkt-white,
    500: color.$rkt-white,
    600: color.$rkt-white,
    700: color.$rkt-white,
    800: color.$rkt-white,
    900: color.$rkt-white,
    A100: color.$rkt-white,
    A200: color.$rkt-white,
    A400: color.$rkt-white,
    A700: color.$rkt-white,
  ),
);

/** Tertiary **/
$rlxp-orange: (
  50: color.$rkt-orange-50,
  100: color.$rkt-orange-100,
  200: color.$rkt-orange-200,
  300: color.$rkt-orange-300,
  400: color.$rkt-orange-400,
  500: color.$rkt-orange-500,
  600: color.$rkt-orange-600,
  700: color.$rkt-orange-700,
  800: color.$rkt-orange-800,
  900: color.$rkt-orange-900,
  A100: color.$rkt-orange-a100,
  A200: color.$rkt-orange-a200,
  A300: color.$rkt-orange-a300,
  A400: color.$rkt-orange-a400,
  contrast: (
    50: color.$rkt-white,
    100: color.$rkt-white,
    200: color.$rkt-white,
    300: color.$rkt-white,
    400: color.$rkt-white,
    500: color.$rkt-white,
    600: color.$rkt-white,
    700: color.$rkt-white,
    800: color.$rkt-white,
    900: color.$rkt-white,
    A100: color.$rkt-white,
    A200: color.$rkt-white,
    A400: color.$rkt-white,
    A700: color.$rkt-white,
  ),
);


// Note: RDS dark missing 500 entry in @rocketcentral/rocket-design-system-styles/web/scss/_color-dark.scss
$rkt-red-dark-500: #b61e30 !default;

$rlxp-dark-red: (
  50: color-dark.$rkt-red-dark-50,
  100: color-dark.$rkt-red-dark-100,
  200: color-dark.$rkt-red-dark-200,
  300: color-dark.$rkt-red-dark-300,
  400: color-dark.$rkt-red-dark-400,
  500: $rkt-red-dark-500,
  600: color-dark.$rkt-red-dark-600,
  700: color-dark.$rkt-red-dark-700,
  800: color-dark.$rkt-red-dark-800,
  900: color-dark.$rkt-red-dark-900,
  contrast:(
    50: color.$rkt-white,
    100: color.$rkt-white,
    200: color.$rkt-white,
    300: color.$rkt-white,
    400: color.$rkt-white,
    500: color.$rkt-white,
    600: color.$rkt-white,
    700: color.$rkt-white,
    800: color.$rkt-white,
    900: color.$rkt-white,
  )
);

$rlxp-dark-gray: (
  50: color-dark.$rkt-gray-dark-50,
  100: color-dark.$rkt-gray-dark-100,
  200: color-dark.$rkt-gray-dark-200,
  300: color-dark.$rkt-gray-dark-300,
  400: color-dark.$rkt-gray-dark-400,
  500: color-dark.$rkt-gray-dark-500,
  600: color-dark.$rkt-gray-dark-600,
  700: color-dark.$rkt-gray-dark-700,
  800: color-dark.$rkt-gray-dark-800,
  900: color-dark.$rkt-gray-dark-900,
  contrast: (
    50: color.$rkt-white,
    100: color.$rkt-white,
    200: color.$rkt-white,
    300: color.$rkt-white,
    400: color.$rkt-white,
    500: color.$rkt-white,
    600: color.$rkt-white,
    700: color.$rkt-white,
    800: color.$rkt-white,
    900: color.$rkt-white,
  ),
);

$rlxp-rose-50: #FCE4EC;
$rlxp-rose-100: #F8BBD0;
$rlxp-rose-200: #F48FB1;
$rlxp-rose-300: #F06292;
$rlxp-rose-400: #EC407A;
$rlxp-rose-500: #F77CA9;
$rlxp-rose-600: #E91E63;
$rlxp-rose-700: #D81B60;
$rlxp-rose-800: #C2185B;
$rlxp-rose-900: #AD1457;



$rlxp-rose: (
  50: $rlxp-rose-50,
  100: $rlxp-rose-100,
  200: $rlxp-rose-200,
  300: $rlxp-rose-300,
  400: $rlxp-rose-400,
  500: $rlxp-rose-500,
  600: $rlxp-rose-600,
  700: $rlxp-rose-700,
  800: $rlxp-rose-800,
  900: $rlxp-rose-900,
  contrast: (
    50: color.$rkt-white,
    100: color.$rkt-white,
    200: color.$rkt-white,
    300: color.$rkt-white,
    400: color.$rkt-white,
    500: color.$rkt-white,
    600: color.$rkt-white,
    700: color.$rkt-white,
    800: color.$rkt-white,
    900: color.$rkt-white,
  ),
);