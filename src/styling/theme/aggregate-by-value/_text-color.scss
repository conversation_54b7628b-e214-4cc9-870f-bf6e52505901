/*
* 
*/


@mixin text-color($color, $selectors: ()) {
    $selectors-string: '';

    // Build the selector string
    @each $selector in $selectors {
        $selectors-string: '#{$selectors-string}#{$selector}, ';
    }

    // Remove the trailing comma and space
    $selectors-string: unquote(trim($selectors-string, ', '));

    // Apply the background color to the built selector string
    #{$selectors-string} {
        color: $color;
    }
}

// Helper function to trim trailing characters
@function trim($string, $characters) {
    $length: str-length($string);

    @if $length ==0 {
        @return '';
    }

    @while str-slice($string, -1)==$characters {
        $string: str-slice($string, 1, $length - 1);
        $length: str-length($string);
    }

    @return $string;
}

// Example Usage
//   @include background-color(var(--main-color), $selectors);
//   @include background-color(red, $selectors);
//   @include background-color(blue, $selectors);

$inherit: (
);


body.rkt-DarkMode {

    $hex-ffffff: (
        '.rkt-Menu__item-text'
    );

@include text-color(#ffffff, $hex-ffffff);
}

body.rkt-LightMode {

    $hex-000000: (
        // 'cdk-accordion-item',
        // 'cdk-accordion-item:hover',
        // '.cdk-accordion-item',
        // '.cdk-accordion-item:hover',
        // '.accordion-item:not(.accordion-item--disabled) .accordion-item-header:hover',
        // 'rkt-Menu__item-text'
    );

// @include text-color(#000000, $hex-000000);
}