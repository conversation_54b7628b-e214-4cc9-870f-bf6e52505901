@mixin border-color($color, $selectors: ()) {
    $selectors-string: '';

    // Build the selector string
    @each $selector in $selectors {
        $selectors-string: '#{$selectors-string}#{$selector}, ';
    }

    // Remove the trailing comma and space
    $selectors-string: unquote(trim($selectors-string, ', '));

    // Apply the background color to the built selector string
    #{$selectors-string} {
        border-color: $color;
    }
}

// Helper function to trim trailing characters
@function trim($string, $characters) {
    $length: str-length($string);

    @if $length ==0 {
        @return '';
    }

    @while str-slice($string, -1)==$characters {
        $string: str-slice($string, 1, $length - 1);
        $length: str-length($string);
    }

    @return $string;
}



body.rkt-DarkMode {
    $rlxp-gray-dark-500-selectors : ();

    $hex-191919 : (
        '.rkt-Alert--warn'
    );

    $hex-303030: (
    );

    $hex-484848: (
    );

    $rlxp-red-600 : (
        '.rkt-Alert:not(.rkt-AccordionPanel--on-dark .rkt-Alert, .rkt-Card--on-dark .rkt-Alert, .rkt-Toggle--on-dark .rkt-Alert, .rkt-Tabs--on-dark .rkt-Alert).rkt-Alert--warn'
    );


    @include border-color(#191919, $hex-191919);
    @include border-color(var(--rlxp-red-600), $rlxp-red-600);

}

body.rkt-LightMode {
    $rlxp-red-50: (
    );
$rlxp-red-100: (
);
$rlxp-red-200: (
);
$rlxp-red-300: (
);
$rlxp-red-400: (
);
$rlxp-red-500: (
);
$rlxp-red-600: (
);
$rlxp-red-700: (
);
$rlxp-red-800: (
);
$rlxp-red-900: (
);


$rlxp-gray-50: (
);
$rlxp-gray-100: (
);
$rlxp-gray-200: (
);
$rlxp-gray-300: (
);
$rlxp-gray-400: (
);
$rlxp-gray-500: (
);
$rlxp-gray-600: (
);
$rlxp-gray-700: (
);
$rlxp-gray-800: (
);
$rlxp-gray-900: (
);
}