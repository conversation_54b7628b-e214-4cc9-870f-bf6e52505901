@mixin background-color($color, $selectors: ()) {
    $selectors-string: '';

    // Build the selector string
    @each $selector in $selectors {
        $selectors-string: '#{$selectors-string}#{$selector}, ';
    }

    // Remove the trailing comma and space
    $selectors-string: unquote(trim($selectors-string, ', '));

    // Apply the background color to the built selector string
    #{$selectors-string} {
        background-color: $color;
    }
}

// Helper function to trim trailing characters
@function trim($string, $characters) {
    $length: str-length($string);

    @if $length ==0 {
        @return '';
    }

    @while str-slice($string, -1)==$characters {
        $string: str-slice($string, 1, $length - 1);
        $length: str-length($string);
    }

    @return $string;
}


body.rkt-DarkMode {

    $hex-191919 : (
        'mat-drawer',
        'body > app-root > app-loan-wrapper > div > mat-sidenav-container > mat-sidenav > div > div > app-support > div > lib-content > cdk-accordion > lib-question-factory.ng-tns-c959433072-56.ng-star-inserted > cdk-accordion-item'
    );

    $hex-a30e2633: (
        '.rkt-Alert:not(.rkt-AccordionPanel--on-dark .rkt-Alert, .rkt-Card--on-dark .rkt-Alert, .rkt-Toggle--on-dark .rkt-Alert, .rkt-Tabs--on-dark .rkt-Alert).rkt-Alert--warn'
    );

@include background-color(#191919, $hex-191919);
@include background-color(#a30e2633, $hex-a30e2633);

}

body.rkt-LightMode {}