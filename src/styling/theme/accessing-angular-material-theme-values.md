Color Palette Values in Angular Material SCSS
Primary, Accent, and Warn Color Palettes
Each of the color palettes (primary, accent, and warn) in Angular Material supports reading the following named colors:

default: The default color from this palette.
lighter: A lighter version of the color for this palette.
darker: A darker version of the color for this palette.
text: The text color for this palette.
default-contrast: A color that stands out against this palette's default color.
lighter-contrast: A color that stands out against this palette's lighter color.
darker-contrast: A color that stands out against this palette's darker color.
[hue]: The [hue] color for the palette. Possible values for [hue] are: 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, A100, A200, A400, A700.
[hue]-contrast: A color that stands out against the [hue] color for the palette. Possible values for [hue] are: 50, 100, 200, 300, 400, 500, 600, 700, 800, 900, A100, A200, A400, A700.
Background Palette
The background palette supports reading the following named colors:

status-bar: The background color for a status bar.
app-bar: The background color for an app bar.
background: The background color for the app.
hover: The background color of a hover overlay.
card: The background color of a card.
dialog: The background color of a dialog.
raised-button: The background color of a raised button.
selected-button: The background color of a selected button.
selected-disabled-button: The background color of a selected disabled button.
disabled-button: The background color of a disabled button.
focused-button: The background color of a focused button.
disabled-button-toggle: The background color of a disabled button toggle.
unselected-chip: The background color of an unselected chip.
disabled-list-option: The background color of a disabled list option.
tooltip: The background color of a tooltip.
Foreground Palette
The foreground palette supports reading the following named colors:

base: The base foreground color, can be used for color mixing or creating a custom opacity foreground color.
divider: The color of a divider.
dividers: (Alternate name for divider).
disabled-text: The color for disabled text.
disabled: (Alternate name for disabled-text).
disabled-button: The color for disabled button text.
elevation: The color elevation shadows.
hint-text: The color for hint text.
secondary-text: The color for secondary text.
icon: The color for icons.
icons: (Alternate name for icon).
text: The color for text.