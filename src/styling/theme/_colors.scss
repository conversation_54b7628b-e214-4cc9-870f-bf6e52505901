@use '@rocketcentral/rocket-design-system-styles/web/scss/_color.scss' as *;
@use '@rocketcentral/rocket-design-system-styles/web/scss/_color-dark.scss' as *;

:root {
  --rlxp-white: #{$rkt-white};
  --rlxp-black: #{$rkt-black};

  --rlxp-red-50: #{$rkt-red-50};
  --rlxp-red-100: #{$rkt-red-100};
  --rlxp-red-200: #{$rkt-red-200};
  --rlxp-red-300: #{$rkt-red-300};
  --rlxp-red-400: #{$rkt-red-400};
  --rlxp-red-500: #{$rkt-red-500};
  --rlxp-red-600: #{$rkt-red-600};
  --rlxp-red-700: #{$rkt-red-700};
  --rlxp-red-800: #{$rkt-red-800};
  --rlxp-red-900: #{$rkt-red-900};

  --rlxp-purple-50: #{$rkt-purple-50};
  --rlxp-purple-100: #{$rkt-purple-100};
  --rlxp-purple-200: #{$rkt-purple-200};
  --rlxp-purple-300: #{$rkt-purple-300};
  --rlxp-purple-400: #{$rkt-purple-400};
  --rlxp-purple-500: #{$rkt-purple-500};
  --rlxp-purple-600: #{$rkt-purple-600};
  --rlxp-purple-700: #{$rkt-purple-700};
  --rlxp-purple-800: #{$rkt-purple-800};
  --rlxp-purple-900: #{$rkt-purple-900};
  --rlxp-purple-a100: #{$rkt-purple-a100};
  --rlxp-purple-a200: #{$rkt-purple-a200};
  --rlxp-purple-a300: #{$rkt-purple-a300};
  --rlxp-purple-a400: #{$rkt-purple-a400};
  --rlxp-purple-bright-500: #{$rkt-purple-bright-500};

  --rlxp-orange-50: #{$rkt-orange-50};
  --rlxp-orange-100: #{$rkt-orange-100};
  --rlxp-orange-200: #{$rkt-orange-200};
  --rlxp-orange-300: #{$rkt-orange-300};
  --rlxp-orange-400: #{$rkt-orange-400};
  --rlxp-orange-500: #{$rkt-orange-500};
  --rlxp-orange-600: #{$rkt-orange-600};
  --rlxp-orange-700: #{$rkt-orange-700};
  --rlxp-orange-800: #{$rkt-orange-800};
  --rlxp-orange-900: #{$rkt-orange-900};
  --rlxp-orange-a100: #{$rkt-orange-a100};
  --rlxp-orange-a200: #{$rkt-orange-a200};
  --rlxp-orange-a300: #{$rkt-orange-a300};
  --rlxp-orange-a400: #{$rkt-orange-a400};

  --rlxp-blue-50: #{$rkt-blue-50};
  --rlxp-blue-100: #{$rkt-blue-100};
  --rlxp-blue-200: #{$rkt-blue-200};
  --rlxp-blue-300: #{$rkt-blue-300};
  --rlxp-blue-400: #{$rkt-blue-400};
  --rlxp-blue-500: #{$rkt-blue-500};
  --rlxp-blue-600: #{$rkt-blue-600};
  --rlxp-blue-700: #{$rkt-blue-700};
  --rlxp-blue-800: #{$rkt-blue-800};
  --rlxp-blue-900: #{$rkt-blue-900};
  --rlxp-blue-a100: #{$rkt-blue-a100};
  --rlxp-blue-a200: #{$rkt-blue-a200};
  --rlxp-blue-a300: #{$rkt-blue-a300};
  --rlxp-blue-a400: #{$rkt-blue-a400};

  --rlxp-green-50: #{$rkt-green-50};
  --rlxp-green-100: #{$rkt-green-100};
  --rlxp-green-200: #{$rkt-green-200};
  --rlxp-green-300: #{$rkt-green-300};
  --rlxp-green-400: #{$rkt-green-400};
  --rlxp-green-500: #{$rkt-green-500};
  --rlxp-green-600: #{$rkt-green-600};
  --rlxp-green-700: #{$rkt-green-700};
  --rlxp-green-800: #{$rkt-green-800};
  --rlxp-green-900: #{$rkt-green-900};
  --rlxp-green-a100: #{$rkt-green-a100};
  --rlxp-green-a200: #{$rkt-green-a200};
  --rlxp-green-a300: #{$rkt-green-a300};
  --rlxp-green-a400: #{$rkt-green-a400};

  --rlxp-gray-50: #{$rkt-gray-50};
  --rlxp-gray-100: #{$rkt-gray-100};
  --rlxp-gray-200: #{$rkt-gray-200};
  --rlxp-gray-300: #{$rkt-gray-300};
  --rlxp-gray-400: #{$rkt-gray-400};
  --rlxp-gray-500: #{$rkt-gray-500};
  --rlxp-gray-600: #{$rkt-gray-600};
  --rlxp-gray-700: #{$rkt-gray-700};
  --rlxp-gray-800: #{$rkt-gray-800};
  --rlxp-gray-900: #{$rkt-gray-900};

  --rlxp-white: #{$rkt-white};
  --rlxp-white-opacity-50: #{$rkt-white-opacity-50};
  --rlxp-white-opacity-100: #{$rkt-white-opacity-100};
  --rlxp-white-opacity-200: #{$rkt-white-opacity-200};
  --rlxp-white-opacity-300: #{$rkt-white-opacity-300};
  --rlxp-white-opacity-400: #{$rkt-white-opacity-400};
  --rlxp-white-opacity-500: #{$rkt-white-opacity-500};
  --rlxp-white-opacity-600: #{$rkt-white-opacity-600};
  --rlxp-white-opacity-700: #{$rkt-white-opacity-700};

  --rlxp-black: #{$rkt-black};
  --rlxp-rose-500: #{$rkt-rose-500};
  --rlxp-tangerine-50: #{$rkt-tangerine-50};
  --rlxp-tangerine-500: #{$rkt-tangerine-500};
  --rlxp-lime-500: #{$rkt-lime-500};
  --rlxp-teal-500: #{$rkt-teal-500};

  --rlxp-gradient-1: #{$rkt-gradient-1};
  --rlxp-gradient-2: #{$rkt-gradient-2};
  --rlxp-gradient-3: #{$rkt-gradient-3};

  --rlxp-gradient-border-1-border: #{$rkt-gradient-border-1-border};
  --rlxp-gradient-border-1-background-content-area-color: #{$rkt-gradient-border-1-background-content-area-color};
  --rlxp-gradient-border-1-background-border-area-color: #{$rkt-gradient-border-1-background-border-area-color};
  --rlxp-gradient-border-1-background-clip: #{$rkt-gradient-border-1-background-clip};
  --rlxp-gradient-border-1-background-origin: #{$rkt-gradient-border-1-background-origin};

  /* Dark*/
  --rlxp-red-dark-50: #{$rkt-red-dark-50};
  --rlxp-red-dark-100: #{$rkt-red-dark-100};
  --rlxp-red-dark-200: #{$rkt-red-dark-200};
  --rlxp-red-dark-300: #{$rkt-red-dark-300};
  --rlxp-red-dark-400: #{$rkt-red-dark-400};
  --rlxp-red-dark-600: #{$rkt-red-dark-600};
  --rlxp-red-dark-700: #{$rkt-red-dark-700};
  --rlxp-red-dark-800: #{$rkt-red-dark-800};
  --rlxp-red-dark-900: #{$rkt-red-dark-900};

  --rlxp-blue-dark-50: #{$rkt-blue-dark-50};
  --rlxp-blue-dark-100: #{$rkt-blue-dark-100};
  --rlxp-blue-dark-200: #{$rkt-blue-dark-200};
  --rlxp-blue-dark-300: #{$rkt-blue-dark-300};
  --rlxp-blue-dark-400: #{$rkt-blue-dark-400};
  --rlxp-blue-dark-500: #{$rkt-blue-dark-500};
  --rlxp-blue-dark-600: #{$rkt-blue-dark-600};
  --rlxp-blue-dark-700: #{$rkt-blue-dark-700};
  --rlxp-blue-dark-800: #{$rkt-blue-dark-800};
  --rlxp-blue-dark-900: #{$rkt-blue-dark-900};

  --rlxp-orange-dark-50: #{$rkt-orange-dark-50};
  --rlxp-orange-dark-100: #{$rkt-orange-dark-100};
  --rlxp-orange-dark-200: #{$rkt-orange-dark-200};
  --rlxp-orange-dark-300: #{$rkt-orange-dark-300};
  --rlxp-orange-dark-400: #{$rkt-orange-dark-400};
  --rlxp-orange-dark-500: #{$rkt-orange-dark-500};
  --rlxp-orange-dark-600: #{$rkt-orange-dark-600};
  --rlxp-orange-dark-700: #{$rkt-orange-dark-700};
  --rlxp-orange-dark-800: #{$rkt-orange-dark-800};
  --rlxp-orange-dark-900: #{$rkt-orange-dark-900};

  --rlxp-green-dark-50: #{$rkt-green-dark-50};
  --rlxp-green-dark-100: #{$rkt-green-dark-100};
  --rlxp-green-dark-200: #{$rkt-green-dark-200};
  --rlxp-green-dark-300: #{$rkt-green-dark-300};
  --rlxp-green-dark-400: #{$rkt-green-dark-400};
  --rlxp-green-dark-500: #{$rkt-green-dark-500};
  --rlxp-green-dark-600: #{$rkt-green-dark-600};
  --rlxp-green-dark-700: #{$rkt-green-dark-700};
  --rlxp-green-dark-800: #{$rkt-green-dark-800};
  --rlxp-green-dark-900: #{$rkt-green-dark-900};

  --rlxp-purple-dark-bright-50: #{$rkt-purple-dark-bright-50};
  --rlxp-purple-dark-bright-500: #{$rkt-purple-dark-bright-500};

  --rlxp-teal-dark-500: #{$rkt-teal-dark-500};
  --rlxp-lime-dark-500: #{$rkt-lime-dark-500};
  --rlxp-tangerine-dark-50: #{$rkt-tangerine-dark-50};
  --rlxp-tangerine-dark-500: #{$rkt-tangerine-dark-500};
  --rlxp-rose-dark-50: #{$rkt-rose-dark-50};
  --rlxp-rose-dark-500: #{$rkt-rose-dark-500};

  --rlxp-gray-dark-50: #{$rkt-gray-dark-50};
  --rlxp-gray-dark-100: #{$rkt-gray-dark-100};
  --rlxp-gray-dark-200: #{$rkt-gray-dark-200};
  --rlxp-gray-dark-300: #{$rkt-gray-dark-300};
  --rlxp-gray-dark-400: #{$rkt-gray-dark-400};
  --rlxp-gray-dark-500: #{$rkt-gray-dark-500};
  --rlxp-gray-dark-600: #{$rkt-gray-dark-600};
  --rlxp-gray-dark-700: #{$rkt-gray-dark-700};
  --rlxp-gray-dark-800: #{$rkt-gray-dark-800};
  --rlxp-gray-dark-900: #{$rkt-gray-dark-900};

  --rlxp-gradient-dark-1: #{$rkt-gradient-dark-1};
  --rlxp-gradient-dark-2: #{$rkt-gradient-dark-2};
  --rlxp-gradient-dark-3: #{$rkt-gradient-dark-3};

  --rlxp-gradient-dark-border-1-border: #{$rkt-gradient-dark-border-1-border};
  --rlxp-gradient-dark-border-1-background-content-area-color: #{$rkt-gradient-dark-border-1-background-content-area-color};
  --rlxp-gradient-dark-border-1-background-border-area-color: #{$rkt-gradient-dark-border-1-background-border-area-color};
  --rlxp-gradient-dark-border-1-background-clip: #{$rkt-gradient-dark-border-1-background-clip};
  --rlxp-gradient-dark-border-1-background-origin: #{$rkt-gradient-dark-border-1-background-origin};
}
