.skeleton-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;

  border-bottom: 1px solid var(--rlxp-default-divider-color);
  padding: 22px 0;

  &:first-child {
    padding-top: 0;
  }
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.title-skeleton {
  height: 1.5rem;
}

.subtitle-skeleton {
  height: 1.25rem;
}

.skeleton-grid {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  column-gap: 1rem;
  row-gap: 2rem;
  align-items: center;

  .form-field-skeleton,
  .slide-toggle-skeleton {
    width: unset;
  }
}

.form-field-skeleton {
  height: 2.5rem;
  border-radius: 4px;
}

.slide-toggle-skeleton {
  height: 1.5625rem;
}

.button-skeleton {
  height: 2rem;
}

.button-large-skeleton {
  height: 3rem;
}

.button-skeleton,
.button-large-skeleton {
  border-radius: 8px;
}

.huge-radio-skeleton {
  height: 3.25rem;
  width: 100%;
  border-radius: 12px;
}

.title-skeleton {
  height: 4rem;
  border-radius: 12px;
}

@for $i from 2 through 10 {
  .col-span-#{$i} {
    grid-column: span $i;
  }
}
