mat-expansion-panel.app-AccordionPanel-outlined {
  background-color: var(--rlxp-accordion-outlined-background-color);
  .mat-expansion-panel-header {
    padding: 12px 16px;
  }

  .mat-expansion-panel-body {
    padding: 12px;
  }

  &.mat-expanded {
    .mat-expansion-panel-header {
      border-bottom: 0.5px solid var(--rlxp-accordion-outlined-border-color);
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
    }
  }

  .mat-content.mat-content-hide-toggle {
    margin-right: 0;
  }
}

.app-AccordionPanel-outlined,
.app-AccordionPanel-outlined.mat-expansion-panel,
.app-AccordionPanel-outlined.mat-expansion-panel:not([class*='mat-elevation-z']) {
  box-shadow: none;
  border: 0.5px solid var(--rlxp-accordion-outlined-border-color);
}

.app-AccordionPanel-outlined.mat-expansion-panel
  .mat-expansion-panel-header.cdk-keyboard-focused:not([aria-disabled='true']),
.app-AccordionPanel-outlined.mat-expansion-panel
  .mat-expansion-panel-header.cdk-program-focused:not([aria-disabled='true']),
.app-AccordionPanel-outlined.mat-expansion-panel:not(.mat-expanded)
  .mat-expansion-panel-header:hover:not([aria-disabled='true']),
.app-AccordionPanel-outlined:hover:not(.Mui-expanded),
.app-AccordionPanel-outlined:hover:not([aria-disabled='true']) {
  background: var(--rlxp-accordion-outlined-background-color);
}

.rkt-DarkMode
  .app-AccordionPanel-outlined:not(
    .app-AccordionPanel-outlined--on-dark .app-AccordionPanel-outlined,
    .app-AccordionPanel-outlined--on-dark,
    .rkt-Card--on-dark .app-AccordionPanel-outlined,
    .rkt-Toggle--on-dark .app-AccordionPanel-outlined,
    .rkt-Tabs--on-dark .app-AccordionPanel-outlined
  ):not(.app-AccordionPanel-outlined--nav) {
  background-color: var(--rlxp-accordion-outlined-background-color);
}

.rkt-DarkMode
  .app-AccordionPanel-outlined:not(
    .app-AccordionPanel-outlined--on-dark .app-AccordionPanel-outlined,
    .app-AccordionPanel-outlined--on-dark,
    .rkt-Card--on-dark .app-AccordionPanel-outlined,
    .rkt-Toggle--on-dark .app-AccordionPanel-outlined,
    .rkt-Tabs--on-dark .app-AccordionPanel-outlined
  ).mat-expansion-panel:not(.mat-expanded)
  .mat-expansion-panel-header:hover:not([aria-disabled='true']),
.rkt-DarkMode
  .app-AccordionPanel-outlined:not(
    .app-AccordionPanel-outlined--on-dark .app-AccordionPanel-outlined,
    .app-AccordionPanel-outlined--on-dark,
    .rkt-Card--on-dark .app-AccordionPanel-outlined,
    .rkt-Toggle--on-dark .app-AccordionPanel-outlined,
    .rkt-Tabs--on-dark .app-AccordionPanel-outlined
  ):hover:not(.Mui-expanded),
.rkt-DarkMode
  .app-AccordionPanel-outlined:not(
    .app-AccordionPanel-outlined--on-dark .app-AccordionPanel-outlined,
    .app-AccordionPanel-outlined--on-dark,
    .rkt-Card--on-dark .app-AccordionPanel-outlined,
    .rkt-Toggle--on-dark .app-AccordionPanel-outlined,
    .rkt-Tabs--on-dark .app-AccordionPanel-outlined
  ):hover:not([aria-disabled='true']),
.rkt-DarkMode
  .app-AccordionPanel-outlined:not(
    .app-AccordionPanel-outlined--on-dark .app-AccordionPanel-outlined,
    .app-AccordionPanel-outlined--on-dark,
    .rkt-Card--on-dark .app-AccordionPanel-outlined,
    .rkt-Toggle--on-dark .app-AccordionPanel-outlined,
    .rkt-Tabs--on-dark .app-AccordionPanel-outlined
  ):focus-within:not([aria-disabled='true']) {
  background-color: var(--rlxp-accordion-outlined-background-color);
}
