import { Component, input, output } from '@angular/core';
import { TileSkeletonComponent } from './tile-skeleton/tile-skeleton.component';

@Component({
  selector: 'app-tile',
  standalone: true,
  imports: [TileSkeletonComponent],
  templateUrl: './tile.component.html',
  styleUrl: './tile.component.scss',
})
export class TileComponent {
  label = input<string>();
  content = input<string | null>();
  tileClick = output<void>();
}
