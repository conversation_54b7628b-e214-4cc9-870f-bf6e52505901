import { Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { BehaviorSubject, map, Observable, shareReplay } from 'rxjs';

export enum SuggestionGroup {
  Rla = 'RLA',
}

export type Suggestion = {
  value: any;
  formattedValue?: string;
  group: SuggestionGroup;
  isEqualToControl: boolean;
};

@Injectable()
export class SuggestionsService {
  private suggestionsSubject = new BehaviorSubject<Record<string, Array<{ control: FormControl, suggestions: Suggestion[] }>>>({});

  suggestions$ = this.suggestionsSubject.pipe(
    map(suggestionsMap => {
      const entries = Object.values(suggestionsMap).flat()

      const merged = new Map<FormControl, Suggestion[]>();
      for (const { control, suggestions: incomingSuggestions } of entries) {
        if (!merged.has(control)) {
          merged.set(control, incomingSuggestions);
          continue;
        }
        merged.set(control, merged.get(control)!.concat(incomingSuggestions));
      }
      return merged;
    }),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  suggestionsForControl$(control: FormControl): Observable<Suggestion[]> {
    return this.suggestions$.pipe(
      map(suggestions => suggestions.get(control) ?? []),
    );
  }

  /**
   * @returns A callback to unregister the suggestions.
   */
  register(suggestionsGroupKey: SuggestionGroup, suggestions$: Observable<Array<{ control: FormControl, suggestions: Suggestion[] }>>) {
    const subscription = suggestions$.subscribe(suggestions => {
      this.suggestionsSubject.next({
        ...this.suggestionsSubject.value,
        [suggestionsGroupKey]: suggestions,
      });
    });

    return () => {
      subscription.unsubscribe();
      const newValue = { ...this.suggestionsSubject.value };
      if (newValue[suggestionsGroupKey] !== undefined) {
        delete newValue[suggestionsGroupKey];
      }
      this.suggestionsSubject.next(newValue);
    };
  }
}
