import { Logger, LogLevel } from '@amplitude/analytics-types';
import { inject, Injectable } from '@angular/core';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';

@Injectable()
export class AmplitudeLoggerService implements Logger {
  private splunkLogger = inject(SplunkLoggerService);

  private isDisabled = false;
  private logLevel = LogLevel.Verbose;

  disable(): void {
    this.isDisabled = true;
  }
  enable(logLevel: LogLevel): void {
    this.isDisabled = false;
    this.logLevel = logLevel;
  }
  log(...args: any[]): void {
    if (this.isDisabled || this.logLevel < LogLevel.Verbose) {
      return;
    }
    console.log(...args);
    this.splunkLogger.info('Amplitude log', { args });
  }
  warn(...args: any[]): void {
    if (this.isDisabled || this.logLevel < LogLevel.Warn) {
      return;
    }
    console.warn(...args);
    this.splunkLogger.warning('Amplitude log', { args });
  }
  error(...args: any[]): void {
    if (this.isDisabled || this.logLevel < LogLevel.Error) {
      return;
    }
    console.error(...args);
    this.splunkLogger.error('Amplitude log', undefined, { args });
  }
  debug(...args: any[]): void {
    if (this.isDisabled || this.logLevel < LogLevel.Debug) {
      return;
    }
    console.debug(...args);
    this.splunkLogger.detailed('Amplitude log', { args });
  }
}
