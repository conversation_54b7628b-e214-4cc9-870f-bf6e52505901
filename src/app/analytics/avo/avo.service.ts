import { inject, Injectable } from '@angular/core';
import { Client } from '@rocket-logic/rl-xp-bff-models';
import { filter, Subject, withLatestFrom } from 'rxjs';
import Avo from '../../../Avo';
import { environment } from '../../../environments/environment';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import { LoanIdService } from '../../services/loan-id/loan-id.service';

@Injectable()
export class AvoService {
  private readonly emailReg = new RegExp('^[^@\\s]+@[^@\\s]+\\.[^@\\s]+$'); // TODO: centralize this regex
  loanStateService = inject(LoanStateService);
  loanIdService = inject(LoanIdService);
  clientUpdate$ = new Subject<Client>();
  creditPull$ = new Subject<{ gcids: string[] }>();
  pricingRequested$ = new Subject<{ gcids: string[] }>();

  private enableAvo = environment.enableAvo;

  constructor() {
    /**
     * Eventually it would be better to pull in dependencies and run effects for analytics but for now going with subject triggers
     * All these subscriptions run for the lifetime of the application, no cleanup required
     */
    this.clientUpdate$
      .pipe(
        filter(() => this.enableAvo),
        withLatestFrom(this.loanIdService.loanId$),
      )
      .subscribe(([client, loanId]) => {
        if (loanId && client?.gcid) {
          this.providedPersonalInfo(
            loanId!,
            client?.contactInformation?.emailAddress &&
              this.emailReg.test(client.contactInformation.emailAddress)
              ? true
              : false,
            client?.personalInformation?.firstName && client.personalInformation.lastName
              ? true
              : false,
            client?.contactInformation?.phoneNumbers![0].number ? true : false,
            client.gcid!,
          );
        }
      });

    this.creditPull$
      .pipe(
        filter(() => this.enableAvo),
        withLatestFrom(this.loanIdService.loanId$),
      )
      .subscribe(([creditPull, loanId]) => {
        if (loanId && creditPull?.gcids) {
          this.requestedCreditPull(loanId!, creditPull.gcids!);
        }
      });

    this.pricingRequested$
      .pipe(
        filter(() => this.enableAvo),
        withLatestFrom(this.loanIdService.loanId$),
      )
      .subscribe(([pricingRequested, loanId]) => {
        if (loanId && pricingRequested?.gcids) {
          this.requestedPricing(loanId!, pricingRequested.gcids!);
        }
      });
  }

  providedPersonalInfo(
    loanNumber: string,
    enteredEmail: boolean,
    enteredName: boolean,
    enteredPhone: boolean,
    gcid: string,
  ) {
    if (!this.enableAvo) {
      return;
    }

    Avo.providedPersonalInfo({
      loanNumber: loanNumber,
      enteredEmail: enteredEmail,
      enteredName: enteredName,
      enteredPhone: enteredPhone,
      gcid: gcid,
    });
  }

  requestedCreditPull(loanNumber: string, gcids: string[]) {
    if (!this.enableAvo) {
      return;
    }
    Avo.completedCreditPull({
      loanNumber: loanNumber,
      gcid: gcids.join(','),
    });
  }

  requestedPricing(loanNumber: string, gcids: string[]) {
    if (!this.enableAvo) {
      return;
    }

    Avo.completedPricingScenario({
      loanNumber: loanNumber,
      gcid: gcids.join(','),
    });
  }
}
