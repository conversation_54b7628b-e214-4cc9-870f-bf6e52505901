import { Directive, ElementRef, inject, input } from '@angular/core';
import { AnalyticsBrowser } from '@segment/analytics-next';
import { LoanIdService } from '../services/loan-id/loan-id.service';

@Directive({
  selector: '[appTrackClick]',
  standalone: true,
})
export class TrackClickDirective {
  private analyticsBrowser = inject(AnalyticsBrowser, { optional: true });
  private elementRef = inject(ElementRef);
  private loanIdService = inject(LoanIdService, { optional: true });

  description = input.required<string>();

  ngOnInit() {
    this.analyticsBrowser?.trackClick(this.elementRef.nativeElement, this.description, {
      loanNumber: this.loanIdService?.loanId(),
    });
  }
}
