import { ENVIRONMENT_INITIALIZER, NgZ<PERSON>, Provider } from '@angular/core';
import { AnalyticsBrowser } from '@segment/analytics-next';
import { analyticsInitFn } from '../env-initializers/analytics.init';
import { AmplitudeLoggerService } from './amplitude-logger.service';
import { AvoService } from './avo/avo.service';
import { AvoProvider } from './avo/avo.token';
import { EventSequenceMiddleware } from './middleware/event-sequence.middleware';
import {
  SEGMENT_SOURCE_MIDDLEWARE,
  SegmentSourceMiddleware,
} from './middleware/segment-middleware';

export function provideAnalytics(segmentWriteKey?: string): Provider[] {
  return [
    { provide: AvoProvider, useClass: AvoService },
    AmplitudeLoggerService,
    { provide: SEGMENT_SOURCE_MIDDLEWARE, multi: true, useClass: EventSequenceMiddleware },
    {
      provide: AnalyticsBrowser,
      deps: [SEGMENT_SOURCE_MIDDLEWARE, NgZ<PERSON>],
      useFactory: (sourceMiddleware: SegmentSourceMiddleware[], zone: NgZone) => {
        if (segmentWriteKey) {
          let browser: AnalyticsBrowser | null = null;
          zone.runOutsideAngular(() => {
            browser = AnalyticsBrowser.load({ writeKey: segmentWriteKey });
            sourceMiddleware.forEach((m) => browser?.addSourceMiddleware(m.middlewareFn));
          });
          return browser;
        }
        return null;
      },
    },
    { provide: ENVIRONMENT_INITIALIZER, multi: true, useValue: analyticsInitFn },
  ];
}
