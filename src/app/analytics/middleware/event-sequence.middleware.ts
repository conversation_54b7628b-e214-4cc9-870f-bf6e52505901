import { Injectable } from '@angular/core';
import { MiddlewareFunction } from '@segment/analytics-next';
import { SegmentSourceMiddleware } from './segment-middleware';

@Injectable()
export class EventSequenceMiddleware extends SegmentSourceMiddleware {
  private sequenceRecord: Record<string, number> = {};

  override middlewareFn: MiddlewareFunction = ({ payload, next }) => {
    let eventName: string | undefined;
    if (payload.type() === 'track') {
      eventName = payload.obj.event;
    } else if (payload.type() === 'page') {
      eventName = payload.obj.name;
    }

    if (eventName) {
      const currentSequence = (this.sequenceRecord[eventName] ?? 0) + 1;
      this.sequenceRecord[eventName] = currentSequence;
      payload.obj.properties = {
        ...payload.obj.properties,
        eventSequence: currentSequence,
      };
    }

    next(payload);
  };
}
