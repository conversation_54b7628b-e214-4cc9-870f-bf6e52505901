import { <PERSON><PERSON>rencyPipe } from '@angular/common';
import { Component, computed, inject } from "@angular/core";
import { takeUntilDestroyed, toSignal } from "@angular/core/rxjs-interop";
import { AbstractControl, NonNullableFormBuilder, ReactiveFormsModule, ValidationErrors, ValidatorFn } from "@angular/forms";
import { MatDivider } from "@angular/material/divider";
import { MatFormField } from "@angular/material/form-field";
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from "@angular/material/input";
import { MatSidenavModule } from "@angular/material/sidenav";
import { MatTabsModule } from "@angular/material/tabs";
import { LoanPurpose } from '@rocket-logic/rl-xp-bff-models';
import { RktSkeletonModule } from '@rocketcentral/rocket-design-system-angular';
import { combineLatest, filter, map, Observable, shareReplay, switchMap } from "rxjs";
import { getErrorMessage } from '../../../../app/util/get-error-message';
import { FormattedNumberInputDirective } from "../../../question-input/formatted-number-input/formatted-number-input.directive";
import { TeamMemberDataService } from '../../../services/team-member-data/team-member-data.serivce';
import { loadable, Loadable, mapLoadableData } from "../../../util/loadable";
import { AprFee, EstimateAddress, Fee, FeeAdjustments, PricingOption, Product } from "../../models/products";
import { EstimatorService, EstimatorStep, SelectedPricingOption } from "../../services/estimator.service";
import { FeeLabelMap } from '../../shared/fee-label-map';

export interface ProductFees {
  loanDiscountFee?: AprFee | null;
  originationFee?: AprFee | null;
  processingFee?: AprFee | null;
}

@Component({
  selector: 'app-estimator-sidebar',
  standalone: true,
  templateUrl: './estimator-sidebar.component.html',
  styleUrl: './estimator-sidebar.component.scss',
  imports: [
    MatTabsModule,
    RktSkeletonModule,
    MatSidenavModule,
    CurrencyPipe,
    MatFormField,
    MatDivider,
    MatInputModule,
    ReactiveFormsModule,
    FormattedNumberInputDirective,
    MatIconModule
  ],
})
export class EstimatorSidebarComponent {
  private fb = inject(NonNullableFormBuilder);
  private estimatorService = inject(EstimatorService);
  private teamMemberDataService = inject(TeamMemberDataService);
  EstimatorStep = EstimatorStep;

  getDisplayLabel(label: string): string {
    return FeeLabelMap[label] || label;
  }

  loanAmount = toSignal(this.estimatorService.loanAmount$);

  selectedPricingOption$: Observable<{pricingOption: PricingOption, product: Product} | null> =
    this.estimatorService.selectedPricingOption$;
  selectedPricingOption = toSignal(this.selectedPricingOption$);

  taxAndInsuranceAmount = computed(() => {
    const selectedPricingOption = this.selectedPricingOption();
    if (selectedPricingOption) {
      const taxAmount = selectedPricingOption.pricingOption.PaymentInformation.PaymentInformationDetails.MonthlyTaxPayment;
      const insuranceAmount = selectedPricingOption.pricingOption.PaymentInformation.PaymentInformationDetails.MonthlyInsurancePayment;
      return taxAmount + insuranceAmount;
    }
    return null;
  });

  ltvPercentageAmount = computed(() => {
    const selectedPricingOption = this.selectedPricingOption();
    if (selectedPricingOption) {
      return selectedPricingOption.pricingOption.LtvInformation.Rounded * 100;
    }
    return null;
  });

  getErrorMessage() {
    return getErrorMessage(this.premiumCtrl, [['maxAmountExceeded', () => 'Discount Points (802) cannot exceed required amount.']]);
  }

  errorMessage = computed(() => {
    return this.getErrorMessage();
  });

  closingFees$: Observable<Loadable<Fee[]>> = this.selectedPricingOption$.pipe(
    filter((option): option is SelectedPricingOption => option !== null),
    switchMap(option => {
      return this.estimatorService.getClosingFees$(option.product, option.pricingOption).pipe(
        loadable(),
      );
    }),
    takeUntilDestroyed(),
    shareReplay(1),
  );
  feeDetails = toSignal(this.closingFees$);

  productFees$: Observable<AprFee[]> = combineLatest([
    this.selectedPricingOption$,
    this.estimatorService.feeAdjustments$,
    this.teamMemberDataService.teamMemberRoles$
  ]).pipe(
    map(([option, feeAdjustments, teamMemberRoles]) => {
      if (option === null) return [];
      const adjustments = feeAdjustments.find(adj => adj.optionId === option.pricingOption.Id);
      const pricingOptionFees = this.estimatorService.getProductFees(option.product.ParentEligibilityGroups, option.pricingOption.FundsToClose.Details, teamMemberRoles);
      if (pricingOptionFees || adjustments) {
        const productFees = {
          ...pricingOptionFees,
          ...adjustments?.productFees,
        }
        return Object.values(productFees).filter((fee): fee is AprFee => fee !== null);
      }
      return [];
    }),
    takeUntilDestroyed(),
    shareReplay(1),
  );
  productFees = toSignal(this.productFees$);

  totalFees = toSignal(combineLatest([
    this.productFees$,
    this.closingFees$,
  ]).pipe(
    map(([productFees, closingFees]) => this.estimatorService.getTotalFees(productFees, closingFees.data))
  ));

  estimateAddress = toSignal<Loadable<EstimateAddress>>(this.estimatorService.productsResponse$.pipe(
    mapLoadableData((response) => response.Property.Address)
  ));

  premiumCtrl = this.fb.control<number>(0, {
    validators: [this.maxRequiredAmountValidator()],
  });
  originationCtrl = this.fb.control<number>(0);

  hasCombinedFeeError = toSignal(combineLatest([
    this.premiumCtrl.valueChanges,
    this.originationCtrl.valueChanges,
  ]).pipe(
    map(([premium, origination]) => {
      const combinedFees = premium + origination;
      return combinedFees >= 10;
    })
  ));

  hasValidFees = toSignal(combineLatest([
    this.premiumCtrl.statusChanges,
    this.originationCtrl.statusChanges,
  ]).pipe(
    map(([premium, origination]) => {
      return premium === 'VALID' && origination === 'VALID';
    })
  ));

  constructor() {
    this.selectedPricingOption$.pipe(takeUntilDestroyed()).subscribe(() => {
      this.premiumCtrl.reset();
      this.originationCtrl.reset();
    });
  }

  recalculateClosingFees() {
    const originationFeePercent = this.originationCtrl.value;
    const premiumFeePercent = this.premiumCtrl.value;
    this.recalculateFees(originationFeePercent, premiumFeePercent);
  }

  private maxRequiredAmountValidator(): ValidatorFn {
    return (control:AbstractControl): ValidationErrors | null => {
      const requiredAmount = this.selectedPricingOption()?.pricingOption.Points.Base;
      if (requiredAmount !== undefined && control.value > requiredAmount) {
        return {maxAmountExceeded: {requiredAmount}};
      }
      return null;
    };
  }

  recalculateFees(originationFeePercent: number, premiumFeePercent: number) {
    const loanPurpose = this.estimatorService.loanPurposeControl.value;
    const isCashoutValue = this.estimatorService.calcForm.controls.isCashout.value;
    const desiredCashoutAmountValue = this.estimatorService.calcForm.controls.desiredCashoutAmount.value;
    const currentLoanAmountValue = this.estimatorService.calcForm.controls.currentLoanAmount.value;

    const refinanceLoanAmount = loanPurpose === LoanPurpose.Refinance && isCashoutValue ? desiredCashoutAmountValue : currentLoanAmountValue;
    const loanAmount = loanPurpose === LoanPurpose.Purchase
      ? this.estimatorService.calcForm.controls.loanAmount.value : refinanceLoanAmount;

    if (!loanAmount) return;
    const originationFeeAmount = (loanAmount * originationFeePercent) / 100;
    const premiumFeeAmount = (loanAmount * premiumFeePercent) / 100;
    const originationFee = {
      Amount: originationFeeAmount,
      Code: '801',
      Name: 'Loan Origination',
    } satisfies AprFee;
    const premiumFee = {
      Amount: premiumFeeAmount,
      Code: '802',
      Name: 'Points',
    } satisfies AprFee;
    const newProductFees: ProductFees = {
      loanDiscountFee: premiumFee,
      originationFee: originationFee,
    };
    const selectedPricingOption = this.selectedPricingOption();
    if (selectedPricingOption?.pricingOption.Id && newProductFees) {
      const feeAdjustmentsToSave = {
        optionId: selectedPricingOption.pricingOption.Id,
        productFees: newProductFees,
      } satisfies FeeAdjustments;
      this.estimatorService.addFeesWithAdjustments(feeAdjustmentsToSave);
    }
  }
}
