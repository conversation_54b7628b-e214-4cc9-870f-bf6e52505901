import { NgClass } from "@angular/common";
import { Component, input } from "@angular/core";
import { MatExpansionModule } from "@angular/material/expansion";
import { MatIconModule } from "@angular/material/icon";
import { MatTableModule } from "@angular/material/table";
import { Product } from "../../models/products";

@Component({
  selector: 'app-estimator-ineligible-table',
  standalone: true,
  templateUrl: './estimator-ineligible-table.component.html',
  styleUrl: './estimator-ineligible-table.component.scss',
  imports: [
    MatTableModule,
    MatIconModule,
    NgClass,
    MatExpansionModule,
  ],
  providers: [],
})
export class EstimatorIneligibleTableComponent { 
  ineligibleProducts = input<Product[]>([]);

  displayedColumns: string[] = ['ProductCode', 'ProductName', 'Ineligibilites'];
}
