<mat-accordion multi displayMode="flat">
  <mat-expansion-panel class="dark-bg">
    <mat-expansion-panel-header>
      <mat-panel-title>
        <div class="flex items-center">
          <mat-icon class="rkt-Icon icon--task" svgIcon="error-outlined"></mat-icon>
          <div id="product-name" class="rkt-Label-14 rkt-FontWeight--100 rkt-Spacing--ml8">INELIGIBLE PRODUCTS</div>
        </div>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="card rounded-xl rkt-Elevation-6 overflow-hidden">
      <table mat-table [dataSource]="ineligibleProducts()">
        <ng-container matColumnDef="ProductCode">
          <th mat-header-cell *matHeaderCellDef>Product Code</th>
          <td mat-cell *matCellDef="let product">
            {{ product.ProductCode }}
          </td>
        </ng-container>
        <ng-container matColumnDef="ProductName">
          <th mat-header-cell *matHeaderCellDef>Product Name</th>
          <td mat-cell *matCellDef="let product">
            {{ product.ProductName }}
          </td>
        </ng-container>
        <ng-container matColumnDef="Ineligibilites">
          <th mat-header-cell *matHeaderCellDef>Number of ineligibility failures</th>
          <td mat-cell *matCellDef="let product" class="tooltip">
            {{ product.Ineligibilites.length }}
            <div class="tooltiptext">
              <ul>
                @for (ineligibility of product.Ineligibilites; track ineligibility) {
                  <li> {{ ineligibility.Message }}</li>}
              </ul>
            </div>
          </td>
        </ng-container>
        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>
  </mat-expansion-panel>
</mat-accordion>