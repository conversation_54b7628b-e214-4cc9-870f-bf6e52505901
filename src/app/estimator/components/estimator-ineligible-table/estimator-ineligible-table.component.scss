.card {
  border: 1px solid var(--mdc-outlined-card-container-color);
}

#product-name {
  margin-right: 0.5rem;
}

:host-context(.rkt-DarkMode) {
  .dark-bg {
    background-color: var(--rlxp-application-island-background-color);
  }
  .mat-mdc-row:nth-child(odd) {
    background-color: var(--rlxp-application-island-background-color);
  }
  .mat-mdc-row:nth-child(even) {
    background-color: var(--rlxp-application-background-color);
  }

  .tooltip .tooltiptext {
    background-color: var(--rlxp-application-island-background-color);
    color: #fff;
  }
}

.mat-mdc-row:nth-child(even) {
  background-color: #F2F2F2;
}

mat-expansion-panel:not([class*=mat-elevation-z]) {
  box-shadow: none;
}

.mat-icon.icon--task ::ng-deep svg {
  color: #ce2742;
}

.tooltip .tooltiptext {
  visibility: hidden;
  background-color: #f9f9f9;
  color: #000;
  text-align: left;
  font-size: 0.8rem;
  border-radius: 5px;
  position: absolute;
  margin-top: -44px;
  margin-left: 15px;
  box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
}
.tooltip:hover .tooltiptext {
  visibility: visible;
}

.tooltip .tooltiptext ul {
  list-style-type: disc; 
  padding-left: 20px;
}