<div #printEstimateSection>
  <div class="flex flex-col items-center mt-5">
    @if (estimatesToShare(); as estimates) {
      @if (estimateAddress()?.data; as address) {
        @for (option of estimates; track option) {
          <mat-card class="rkt-Card rkt-Card--enterprise section-container rkt-Spacing--mb16">
            <mat-card class="notifications-tile">
              <div class="flex m-[5px]">
                <mat-icon
                  svgIcon="notifications-outlined"
                  class="rkt-Icon icon--notifications"
                ></mat-icon>
                <div>Your actual rate, payment, and costs could be higher. Get an official Loan Estimate before choosing a loan.</div>
              </div>
            </mat-card>
            <app-estimator-product-option [option]="option" [estimateAddress]="address"/>
          </mat-card>
        }
      }
    }
    <mat-card class="rkt-Card rkt-Card--enterprise section-container rkt-Spacing--mb16 w-2/3">
      <div class="flex justify-between" data-html2canvas-ignore="true">
        <button (click)="onNavigateBack()" class="rkt-Button rkt-Button--secondary rkt-Button--large mr-2" mat-flat-button>Back</button>
        <button
          (click)="generatePdf()"
          class="rkt-Button rkt-Button--large flex items-center"
          mat-flat-button color="primary">
          <mat-icon class="rkt-Icon">file_upload</mat-icon>
          Rocket Logic Export
        </button>
      </div>
    </mat-card>
  </div>
</div>