import { CommonModule, formatDate } from '@angular/common';
import { Component, ElementRef, inject, viewChild } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { Observable } from 'rxjs';
import { Loadable, mapLoadableData } from '../../../util/loadable';
import { EstimateAddress } from '../../models/products';
import { EstimatorService, EstimatorStep } from '../../services/estimator.service';
import { EstimatorProductOptionComponent } from "../estimator-product-option/estimator-product-option.component";

@Component({
  selector: 'app-products-estimate',
  standalone: true,
  imports: [
    MatIconModule,
    MatCardModule,
    MatDividerModule,
    CommonModule,
    EstimatorProductOptionComponent
],
  templateUrl: './products-estimate.component.html',
  styleUrl: './products-estimate.component.scss',
})
export class ProductsEstimateComponent {
  private estimatorService = inject(EstimatorService);
  private logger = inject(SplunkLoggerService);

  onNavigateBack() {
    this.estimatorService.navigate(EstimatorStep.Calculator);
  }

  estimatesToShare = toSignal(this.estimatorService.estimatesToShare$);

  estimateAddress$: Observable<Loadable<EstimateAddress>> =
    this.estimatorService.productsResponse$.pipe(
      mapLoadableData((response) => response.Property.Address),
    );
  estimateAddress = toSignal<Loadable<EstimateAddress>>(this.estimateAddress$);

  printSectionEl = viewChild.required<ElementRef>('printEstimateSection');
  
  async generatePdf() {
    const data = this.printSectionEl().nativeElement;
    if (data) {
      try {
        const canvas = await html2canvas(data, {scale: 2});
        const imgWidth = 210;
        const pageHeight = 297;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;
        const contentDataURL = canvas.toDataURL('image/png');
        
        const pdf = new jsPDF('p', 'mm', 'a4');
        let yPosition = 0;

        while (yPosition < imgHeight) {
          pdf.addImage(contentDataURL, 'PNG', 0, yPosition > 0 ? -yPosition: 0, imgWidth, imgHeight);
          yPosition += pageHeight;

          if (yPosition < imgHeight) {
            pdf.addPage();
          }
        }

        const date = formatDate(new Date(), 'MM/dd/yyyy/HH:mm:ss', 'en-US');
        pdf.save(`estimates_${date}.pdf`);
      } catch (error) {
        this.logger.error('Error generating canvas:', error as Error);
      }
    } else {
      this.logger.error('Element not found to convert to pdf.');
    }
  }
}