import { Component, inject } from "@angular/core";
import { toSignal } from "@angular/core/rxjs-interop";
import { MatIconModule } from "@angular/material/icon";
import { MatTabsModule } from "@angular/material/tabs";
import { ActivatedRoute } from "@angular/router";
import { RktSkeletonModule } from '@rocketcentral/rocket-design-system-angular';
import { Observable } from "rxjs";
import { TrackClickDirective } from "../../../analytics/track-click.directive";
import { Loadable, mapLoadableData } from "../../../util/loadable";
import { AmortizationType, GroupedProducts, ParentEligibilityGroup, Product, ProductsResponse, Tag } from "../../models/products";
import { EstimatorService, EstimatorStep } from "../../services/estimator.service";
import { EstimatorIneligibleTableComponent } from "../estimator-ineligible-table/estimator-ineligible-table.component";
import { EstimatorProductTableComponent } from "../estimator-product-table/estimator-product-table.component";

@Component({
  selector: 'app-estimator-product-page',
  standalone: true,
  templateUrl: './estimator-product-page.component.html',
  styleUrl: './estimator-product-page.component.scss',
  imports: [
    MatTabsModule,
    EstimatorProductTableComponent,
    EstimatorIneligibleTableComponent,
    RktSkeletonModule,
    MatIconModule,
    TrackClickDirective
],
  providers: [],
})
export class EstimatorProductPageComponent {
  private estimatorService = inject(EstimatorService);
  private route = inject(ActivatedRoute);
  productsResponse$: Observable<Loadable<ProductsResponse>> = this.estimatorService.productsResponse$;

  async onShareEstimate() {
    const opportunityId = this.route.snapshot.paramMap.get('opportunityId');
    if (opportunityId) {
      await this.estimatorService.saveEstimateDetails(opportunityId);
    }
    this.estimatorService.navigate(EstimatorStep.Estimate);
  }

  canShareEstimate = toSignal(this.estimatorService.canShareEstimate$);

  groupedProducts = toSignal<Loadable<GroupedProducts>>(this.productsResponse$.pipe(
    mapLoadableData(response => {
      const sortedProducts = this.sortProducts(
        response.ProductGroups.flatMap(group => group.Products)
          .reduce((uniqueProducts, product) => {
            const seenCodes = new Set(uniqueProducts.map(p => p.ProductCode));
            if (!seenCodes.has(product.ProductCode)) {
              uniqueProducts.push(product);
            }
            return uniqueProducts;
          }, [] as Product[])
      );

      const eligibleProducts = sortedProducts
      .filter(product => product.IsEligible)
      .map(product => ({
          ...product,
          PricingOptions: product.PricingOptions.filter(o => o.PaymentInformation?.TotalPayment !== undefined)
      }));

      const ineligibleProducts = sortedProducts
        .filter(product => !product.IsEligible)
        .sort((a, b) => a.ProductCode.localeCompare(b.ProductCode));

      const fixedProducts = eligibleProducts.filter(
        product => product.AmortizationType === AmortizationType.Fixed &&
        !product.ParentEligibilityGroups.includes(ParentEligibilityGroup.InterestOnly) &&
        !product.Tags.includes(Tag.Yourgage)
      );
      const fixedIneligibleProducts = ineligibleProducts.filter(
        product => product.AmortizationType === AmortizationType.Fixed &&
        !product.ParentEligibilityGroups.includes(ParentEligibilityGroup.InterestOnly) &&
        !product.Tags.includes(Tag.Yourgage)
      );

      const adjustableProducts = eligibleProducts.filter(
        product => product.AmortizationType === AmortizationType.ARM &&
        !product.ParentEligibilityGroups.includes(ParentEligibilityGroup.InterestOnly) &&
        !product.Tags.includes(Tag.Yourgage)
      );      
      const adjustableIneligibleProducts = ineligibleProducts.filter(
        product => product.AmortizationType === AmortizationType.ARM &&
        !product.ParentEligibilityGroups.includes(ParentEligibilityGroup.InterestOnly) &&
        !product.Tags.includes(Tag.Yourgage)
      );

      const interestOnlyProducts = eligibleProducts.filter(
        product => product.ParentEligibilityGroups.includes(ParentEligibilityGroup.InterestOnly) &&
        !product.Tags.includes(Tag.Yourgage)
      );
      const interestOnlyIneligibleProducts = ineligibleProducts.filter(
        product => product.ParentEligibilityGroups.includes(ParentEligibilityGroup.InterestOnly) &&
        !product.Tags.includes(Tag.Yourgage)
      );

      const yourgageProducts = eligibleProducts.filter(
        product => product.Tags.includes(Tag.Yourgage)
      );
      const yourgageIneligibleProducts = ineligibleProducts.filter(
        product => product.Tags.includes(Tag.Yourgage)
      );

      const groupedProducts: GroupedProducts = {
        fixedProducts,
        adjustableProducts,
        interestOnlyProducts,
        yourgageProducts,
        fixedIneligibleProducts,
        adjustableIneligibleProducts,
        interestOnlyIneligibleProducts,
        yourgageIneligibleProducts
      };

      return groupedProducts;
    }),
  ));

  sortProducts(products: Product[]): Product[] {
    return products.sort((a, b) => {
      const aIsConventional = a.ParentEligibilityGroups.includes(ParentEligibilityGroup.Conventional);
      const bIsConventional = b.ParentEligibilityGroups.includes(ParentEligibilityGroup.Conventional);

      const aIsVa = a.ParentEligibilityGroups.includes(ParentEligibilityGroup.VA);
      const bIsVa = b.ParentEligibilityGroups.includes(ParentEligibilityGroup.VA);

      // Prioritize conventional products
      if (aIsConventional && !bIsConventional) return -1;
      if (!aIsConventional && bIsConventional) return 1;

      // Prioritize VA over FHA
      if (aIsVa && !bIsVa) return -1;
      if (!aIsVa && bIsVa) return 1;

      // Sort products by term months in descending order
      return b.TermMonths - a.TermMonths;
    });
  }
}