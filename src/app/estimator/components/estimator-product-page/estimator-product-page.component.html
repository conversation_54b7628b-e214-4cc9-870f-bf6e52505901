<div class="flex flex-col pt-6">
  <div mat-card class="card p-3 rounded-xl rkt-Elevation-6 self-center w-full max-w-3xl">
    @if(groupedProducts()?.loading) {
      <div rktSkeleton></div>
      <div rktSkeleton></div>
      <div rktSkeleton></div>
      <div rktSkeleton></div>
      <div rktSkeleton></div>
      <div rktSkeleton></div>
    } 
    @if (groupedProducts()?.data; as data) {
      <mat-tab-group>
        <mat-tab label="Fixed Rate">
          @if (data.fixedProducts && data.fixedProducts.length > 0) {
            @for (product of data.fixedProducts; track product) {
              <app-estimator-product-table [product]="product"/>
            }
          } @if (data.fixedIneligibleProducts && data.fixedIneligibleProducts.length > 0){
              <app-estimator-ineligible-table [ineligibleProducts]="data.fixedIneligibleProducts"/>
          } @if ((!data.fixedProducts || data.fixedProducts.length === 0) && (!data.fixedIneligibleProducts || data.fixedIneligibleProducts.length === 0)){
              <div class="rkt-Caption-12 text-center">No products available.</div>
          }
        </mat-tab>
        <mat-tab label="Adjustable Rate">
          @if (data.adjustableProducts && data.adjustableProducts.length > 0) {
            @for (product of data.adjustableProducts; track product) {
              <app-estimator-product-table [product]="product"/>
            }
          } @if (data.adjustableIneligibleProducts && data.adjustableIneligibleProducts.length > 0) {
              <app-estimator-ineligible-table [ineligibleProducts]="data.adjustableIneligibleProducts"/>
          } @if ((!data.adjustableProducts || data.adjustableProducts.length === 0) && (!data.adjustableIneligibleProducts || data.adjustableIneligibleProducts.length === 0)){
              <div class="rkt-Caption-12 text-center">No products available.</div>
          }
        </mat-tab>
        <mat-tab label="Interest Only">
          @if (data.interestOnlyProducts && data.interestOnlyProducts.length > 0) {
            @for (product of data.interestOnlyProducts; track product) {
              <app-estimator-product-table [product]="product"/>
            }
          } @if (data.interestOnlyIneligibleProducts && data.interestOnlyIneligibleProducts.length > 0){
              <app-estimator-ineligible-table [ineligibleProducts]="data.interestOnlyIneligibleProducts"/>
          } @if ((!data.interestOnlyProducts || data.interestOnlyProducts.length === 0) && (!data.interestOnlyIneligibleProducts || data.interestOnlyIneligibleProducts.length === 0)){
              <div class="rkt-Caption-12 text-center">No products available.</div>
          }
        </mat-tab>
        <mat-tab label="YOURgage">
          @if (data.yourgageProducts && data.yourgageProducts.length > 0) {
            @for (product of data.yourgageProducts; track product) {
              <app-estimator-product-table [product]="product"/>
            }
          } @if (data.yourgageIneligibleProducts && data.yourgageIneligibleProducts.length > 0){
              <app-estimator-ineligible-table [ineligibleProducts]="data.yourgageIneligibleProducts"/>
          } @if ((!data.yourgageProducts || data.yourgageProducts.length === 0) && (!data.yourgageIneligibleProducts || data.yourgageIneligibleProducts.length === 0)){
              <div class="rkt-Caption-12 text-center">No products available.</div>
          }
        </mat-tab>
      </mat-tab-group>
    }
    <div class="flex justify-end mt-5">
      <button
        appTrackClick
        description="Share Estimate"
        class="rkt-Button rkt-Button--large flex items-center justify-center"
        [disabled]="!canShareEstimate()"
        [class.rkt-Button--is-disabled]="!canShareEstimate()"
        mat-flat-button color="primary"
        (click)="onShareEstimate()">
        <mat-icon class="rkt-Icon mr-1">send</mat-icon>
        Share Estimate
      </button>
    </div>
  </div>
</div>