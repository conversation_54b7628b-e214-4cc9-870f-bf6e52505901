:host {
  --estimator-product-card-bg: #ffffff;
}
.card {
  border: 1px solid var(--mdc-outlined-card-container-color);
  background-color: var(--estimator-product-card-bg);
}

.rkt-Skeleton--text {
  width: 100%;
  height: 2.5rem;
  margin-top: 0.5rem;
}

.mat-icon.icon--task {
  color: #ce2742;
}

:host-context(.rkt-DarkMode) {
  --estimator-product-card-bg: var(--rlxp-application-island-background-color);

  .dark-bg {
    background-color: var(--rlxp-application-island-background-color);
  }
}