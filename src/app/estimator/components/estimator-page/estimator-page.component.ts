import { Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSidenavModule } from '@angular/material/sidenav';
import { RktThemeToggleModule } from '@rocketcentral/rocket-design-system-angular';
import { combineLatest, map } from 'rxjs';
import { HeaderComponent } from "../../../header/header.component";
import { DarkModePreference, DarkModeService } from '../../../services/dark-mode/dark-mode.service';
import { EstimatorService, EstimatorStep } from '../../services/estimator.service';
import { EstimatorActionBarComponent } from '../estimator-action-bar/estimator-action-bar.component';
import { EstimatorCalculatorComponent } from '../estimator-calculator/estimator-calculator.component';
import { EstimatorProductPageComponent } from '../estimator-product-page/estimator-product-page.component';
import { EstimatorSidebarComponent } from '../estimator-sidebar/estimator-sidebar.component';
import { ProductsEstimateComponent } from '../products-estimate/products-estimate.component';

@Component({
  selector: 'app-estimator-page',
  standalone: true,
  imports: [
    HeaderComponent,
    RktThemeToggleModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    EstimatorActionBarComponent,
    EstimatorProductPageComponent,
    EstimatorCalculatorComponent,
    MatSidenavModule,
    MatDividerModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatIconModule,
    ProductsEstimateComponent,
    MatSidenavModule,
    EstimatorSidebarComponent
  ],
  templateUrl: './estimator-page.component.html',
  styleUrl: './estimator-page.component.scss',
})
export class EstimatorPageComponent {
  darkModeService = inject(DarkModeService);
  readonly DarkModePreference = DarkModePreference;
  private estimatorService = inject(EstimatorService);
  EstimatorStep = EstimatorStep;

  activePage$ = this.estimatorService.activePage$;
  activePage = toSignal(this.activePage$);
  shouldShowProducts = toSignal(this.estimatorService.productsRequest$);

  isOpenFeesSidebar = toSignal(combineLatest([this.activePage$, this.estimatorService.isOpenFeesSidebar$])
    .pipe(
      map(([activePage, isOpenFeesSidebar]) => activePage === EstimatorStep.Calculator && isOpenFeesSidebar)
    ));
}
