:host {
  height: 100%;
}

.logo {
  font-family: "Rocket Sans";
  font-size: 1.5625rem;
  font-weight: 400;
  line-height: 1.25rem; /* 80% */
  color: var(--logo-color, #000);

  &__rocket {
    font-weight: 800;
  }

  &__estimator {
    color: #E02B52;
  }
}

mat-sidenav {
  --mat-sidenav-container-shape: 1rem;
}

.total-caption {
  color: var(--total-caption-color, inherit);
}

app-estimator-action-bar {
  position: sticky;
  top: 1.5rem;
  z-index: 2;
  display: block;
}

:host-context(.rkt-DarkMode) {
  .logo {
    --logo-color: #fff;
  }

  mat-sidenav-container {
    --mat-sidenav-content-background-color: #0a0a0a;
  }

  mat-sidenav {
    --mat-sidenav-container-background-color: #191919;
  }

  .total-caption {
    --total-caption-color: #D3D3D3;
  }
}