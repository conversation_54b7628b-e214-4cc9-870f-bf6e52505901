<div class="h-full flex flex-col">
  <app-header [iconTemplate]="iconTemplate" [actionsTemplate]="actionsTemplate" />

  <ng-template #iconTemplate>
    <div class="logo">
      <span class="logo__rocket">ROCKET</span> LOGIC <span class="logo__estimator">ESTIMATOR</span>
    </div>
  </ng-template>

  <ng-template #actionsTemplate>
    <rkt-theme-toggle [isDark]="darkModeService.isDarkMode()"
      (onToggleChangeEvent)="darkModeService.darkModePreference.set($event ? DarkModePreference.Dark : DarkModePreference.Light)"
      class="p-3" />
  </ng-template>
  <mat-sidenav-container class="h-full">
    <mat-sidenav-content class="flex flex-col">
      <div class="pt-3">
        @switch (activePage()) {
          @case (EstimatorStep.Calculator) {
            <app-estimator-action-bar/>
            <div class="mt-6">
              <app-estimator-calculator />
              @if (shouldShowProducts()) {
                <app-estimator-product-page/>
              }
            </div>
          }
          @case(EstimatorStep.Estimate) {
            <app-products-estimate/>
          }
        }
      </div>
    </mat-sidenav-content>
    <mat-sidenav [opened]="isOpenFeesSidebar()" position="end" mode="side" class="max-w-96">
      <app-estimator-sidebar/>
    </mat-sidenav>
  </mat-sidenav-container>
</div>