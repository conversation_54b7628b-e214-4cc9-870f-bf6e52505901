.form-grid {
  display: grid;
  grid-template: repeat(2, 1fr) / repeat(4, 1fr);
}

.inner-card-section {
  border: 1.5px solid;
  border-color: #a8a8a8;
  background-color: transparent;
}

.fee-details {
  table {
    th, td {
      padding: 0.5rem;
      border-bottom: 1px solid var(--fee-details-row-border, #a8a8a8);
    }

    tbody tr:nth-child(odd) {
      background: var(--fee-details-row-alt-bg, #dbdbdb);
    }

    tr.no-bottom-border td {
      border-bottom: none;
    }
  }
}

:host-context(.rkt-DarkMode) {
  .fee-details {
    --fee-details-row-alt-bg: #303030;
    --fee-details-row-border: #444444;
    --fee-details-row-last-bg: #000;
  }
}
  