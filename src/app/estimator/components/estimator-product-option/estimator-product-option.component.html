<div class="rkt-Label-16 rkt-Spacing--mt16 rkt-Spacing--mb16 font-bold">Pre Loan Estimate: {{ option().productName }}</div>
<mat-card class="inner-card-section">
  <mat-card-content>
    <div class="rkt-Caption-14 rkt-Spacing--mb16">
      <div>Loan Amount</div>
      <div class="rkt-Label-16">{{option().loanAmount| currency}}</div>
    </div>
    <div class="form-grid mb-2.5">
      <div class="rkt-Caption-14 rkt-Spacing--mb16">
        <div>Monthly Payment</div>
        <div class="rkt-Label-16">{{option().pricingOption.PaymentInformation.TotalPayment | currency}}</div>
      </div>
      <div class="rkt-Caption-14">
        <div>Interest Rate</div>
        <div class="rkt-Label-16">{{option().pricingOption.Rate.Adjusted}} %</div>
      </div>
      <div class="rkt-Caption-14">
        <div>APR</div>
        <div class="rkt-Label-16">{{option().pricingOption.APR}} %</div>
      </div>
      @if (option().totalFees; as totalFees) {
        <div class="rkt-Caption-14">
          <div>Closing Costs</div>
          <div class="rkt-Label-16">{{ totalFees | currency }}</div>
        </div>
      }
      @if (estimateAddress(); as estimateLocation) {
        <div class="rkt-Caption-14">
          <div>Location</div>
          <div class="rkt-Label-16">{{estimateLocation.County.Name}}, {{estimateLocation.State}}</div>
        </div>
      }
      @if (taxAndInsuranceAmount(); as taxAndInsuranceAmount) {
        <div class="rkt-Caption-14">
          <div>Tax & Insurance</div>
          <div class="rkt-Label-16">{{taxAndInsuranceAmount | currency}}</div>
        </div>
      }
      @if (ltvPercentageAmount(); as ltvPercentageAmount) {
        <div class="rkt-Caption-14">
          <div>LTV</div>
          <div class="rkt-Label-16">{{ltvPercentageAmount}}%</div>
        </div>
      }
      <div class="rkt-Caption-14">
        <div>Points</div>
        <div class="rkt-Label-16">{{option().pricingOption.Points.Final}}</div>
      </div>
    </div>

    <mat-divider class="rkt-HorizontalDivider"/>

    @if (option().feeDetails; as fees) {
      <div class="rkt-Caption-16 mt-5">Closing Cost Overview</div>
      <div class="fee-details">
        <table class="table-auto w-full">
          <thead>
            <tr class="rkt-Caption-14">
              <th class="text-left">Fee</th>
              <th class="text-left">Payment ($)</th>
            </tr>
          </thead>
          <tbody>
            @if (option().productFees; as productFees) {
              @for (fee of productFees; track fee) {
                <tr class="rkt-Caption-14">
                  <td>{{ getDisplayLabel(fee.Name) }}</td>
                  @if (fee.Amount) {
                    <td>{{ fee.Amount | currency }}</td>
                  } @else {
                    <td>-</td>
                  }
                </tr>
              }
            }
            @for (fee of fees; track fee) {
              <tr class="rkt-Caption-14">
                <td>{{ fee.name }}</td>
                <td>{{ fee.value | currency }}</td>
              </tr>
            }
            @if (option().totalFees; as totalFees) {
            <tr class="rkt-Caption-14 font-bold no-bottom-border">
              <td>Total Closing Fees</td>
              <td>{{ totalFees | currency }}</td>
            </tr>
            }
          </tbody>
        </table>
      </div>
    }
  </mat-card-content>
</mat-card>