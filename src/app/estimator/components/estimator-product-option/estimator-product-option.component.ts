import { CommonModule } from '@angular/common';
import { Component, computed, input } from "@angular/core";
import { MatCardModule } from "@angular/material/card";
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { EstimateAddress, EstimateToShare } from "../../models/products";
import { FeeLabelMap } from '../../shared/fee-label-map';

@Component({
  selector: 'app-estimator-product-option',
  standalone: true,
  templateUrl: './estimator-product-option.component.html',
  styleUrl: './estimator-product-option.component.scss',
  imports: [
    MatCardModule,
    MatDividerModule,
    MatIconModule,
    CommonModule
  ],
})
export class EstimatorProductOptionComponent {
  option = input.required<EstimateToShare>();
  estimateAddress = input.required<EstimateAddress>();

  getDisplayLabel(label: string): string {
    return FeeLabelMap[label] || label;
  }

  taxAndInsuranceAmount = computed(() => {
    const taxAmount = this.option().pricingOption.PaymentInformation.PaymentInformationDetails.MonthlyTaxPayment;
    const insuranceAmount = this.option().pricingOption.PaymentInformation.PaymentInformationDetails.MonthlyInsurancePayment;
    return taxAmount + insuranceAmount;

  });

  ltvPercentageAmount = computed(() => {
    return this.option().pricingOption.LtvInformation.Rounded * 100;
  });
}