.container {
  gap: 1rem;
  max-width: 768px;
  width: 100%;
}

.card {
  border: 1px solid var(--mdc-outlined-card-container-color);
}

.page-wrapper {
  display: flex;
  justify-content: center;
}

.header-spacing {
  display: flex;
}

.saved-pill {
  border-radius: 12px;
  background: var(--Semantics-info-secondary, #E5EEFA);
  height: 24px;
  padding: 4px 12px 4px 10px;
  gap: 4px;
}

.saved-text {
  font-family: "Rocket Sans";
  font-size: 12px;
  font-style: normal;
  line-height: 16px;
}

.bookmark-color {
  color: #0438B8;
}

.last-saved {
  display: flex;
  width: 206px;
  height: 21px;
  flex-direction: column;
  justify-content: center;

  color: #646464;
  font-family: "Rocket Sans";
  font-size: 12px;
  font-style: normal;
  font-weight: 500;
  line-height: 16px;
}

.form-grid {
  display: grid;
  grid-template: repeat(3, 1fr) / repeat(10, 1fr);
  gap: 0.625rem;
}

mat-divider {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}

:host :ng-deep {
  input[type=number] {
    -moz-appearance: textfield;
    appearance: textfield;
    margin: 0;
  }
  input[type=number]::-webkit-inner-spin-button,
  input[type=number]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
}