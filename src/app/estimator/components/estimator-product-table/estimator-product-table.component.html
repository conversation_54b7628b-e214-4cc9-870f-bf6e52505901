<mat-accordion multi displayMode="flat">
  <mat-expansion-panel class="dark-bg">
    <mat-expansion-panel-header>
      <mat-panel-title>
        <div class="flex flex-row items-center">
          <mat-icon class="rkt-Icon icon--task" svgIcon="task-outlined"></mat-icon>
          <div id="product-name" class="rkt-Label-14 rkt-FontWeight--100 rkt-Spacing--ml8">{{ product().ProductName }}</div>
        </div>
      </mat-panel-title>
    </mat-expansion-panel-header>
    <div class="card rounded-xl rkt-Elevation-6 overflow-hidden">
      <table mat-table [dataSource]="product().PricingOptions">
        <ng-container matColumnDef="ProductCode">
          <th mat-header-cell *matHeaderCellDef>Loan</th>
          <td mat-cell *matCellDef="let option">
            <mat-checkbox
              (click)="$event.stopPropagation()"
              [checked]="checkedPricingOptionIds().includes(option.Id)"
              (change)="$event ? update($event.checked, option): null"
            ></mat-checkbox>
            {{ product().ProductCode }}
          </td>
        </ng-container>
        <ng-container matColumnDef="payment">
          <th mat-header-cell *matHeaderCellDef>Payment</th>
          <td mat-cell *matCellDef="let option">
            {{ option.PaymentInformation?.TotalPayment | currency}}
          </td>
        </ng-container>
        <ng-container matColumnDef="rate">
          <th mat-header-cell *matHeaderCellDef>Rate</th>
          <td mat-cell *matCellDef="let option">
            {{ option.Rate.Adjusted }}
          </td>
        </ng-container>
        <ng-container matColumnDef="points">
          <th mat-header-cell *matHeaderCellDef>Points</th>
          <td mat-cell *matCellDef="let option">
            {{ option.Points.Final }}
          </td>
        </ng-container>
        <ng-container matColumnDef="apr">
          <th mat-header-cell *matHeaderCellDef>APR</th>
          <td mat-cell *matCellDef="let option">
            {{ option.APR }}%
          </td>
        </ng-container>
        <tr class="dark-bg" mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr 
          description="Selected Pricing Option" 
          appTrackClick (click)="selectPricingOption(row)" 
          [ngClass]="{selected: row.Id === selectedPricingOptionId()}" 
          mat-row
          *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>
    </div>
  </mat-expansion-panel>
</mat-accordion>
