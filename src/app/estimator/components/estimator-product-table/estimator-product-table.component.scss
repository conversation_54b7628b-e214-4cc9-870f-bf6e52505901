.mat-icon.icon--task ::ng-deep svg {
  color: #ce2742;
}

#product-name {
  margin-right: 0.5rem;
}

.card {
  border: 1px solid var(--mdc-outlined-card-container-color);
}

.mat-mdc-row:nth-child(even) {
  background-color: #F2F2F2;
}

.mat-mdc-row:hover{
  background-color: #2196F34D;
}

.mat-mdc-row.selected {
  background-color: #2196F34D;
}

mat-expansion-panel:not([class*=mat-elevation-z]) {
  box-shadow: none;
}

.mat-expansion-panel-header.mat-expanded:focus, .mat-expansion-panel-header.mat-expanded:hover {
  // override background: inherit; on hover
  background: none;
}

:host-context(.rkt-DarkMode) {
  .dark-bg {
    background-color: #191919;
  }
  .mat-mdc-row:nth-child(odd) {
    background-color: #191919;
  }
  .mat-mdc-row:nth-child(even) {
    background-color: #0a0a0a;
  }
  .mat-mdc-row:hover{
    background-color: #2196F34D;
  }
  .mat-mdc-row.selected {
    background-color: #2196F34D;
  }
}