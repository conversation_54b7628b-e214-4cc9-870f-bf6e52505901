import { <PERSON><PERSON><PERSON>cyPip<PERSON>, NgClass } from "@angular/common";
import { Component, inject, input } from "@angular/core";
import { toSignal } from "@angular/core/rxjs-interop";
import { MatCheckbox } from "@angular/material/checkbox";
import { MatExpansionModule } from "@angular/material/expansion";
import { MatIconModule } from "@angular/material/icon";
import { MatTableModule } from "@angular/material/table";
import { map } from "rxjs";
import { TrackClickDirective } from "../../../analytics/track-click.directive";
import { ParentEligibilityGroup, PricingOption, Product } from "../../models/products";
import { EstimatorService } from "../../services/estimator.service";

@Component({
  selector: 'app-estimator-product-table',
  standalone: true,
  templateUrl: './estimator-product-table.component.html',
  styleUrl: './estimator-product-table.component.scss',
  imports: [
    MatTableModule,
    MatIconModule,
    NgClass,
    CurrencyPipe,
    MatExpansionModule,
    MatCheckbox,
    TrackClickDirective
  ],
  providers: [],
})
export class EstimatorProductTableComponent {
  product = input.required<Product, Product>({transform: (product) => {
    product.PricingOptions.sort((a, b) => a.Rate.Base - b.Rate.Base);
    return product;
  }});
  private estimatorService = inject(EstimatorService);
  displayedColumns = ['ProductCode', 'payment', 'rate', 'points', 'apr'];
  ParentEligibilityGroup = ParentEligibilityGroup

  selectedPricingOptionId = toSignal<string | null>(this.estimatorService.selectedPricingOption$.pipe(
    map(option => option?.pricingOption.Id ?? null))
  );

  checkedPricingOptionIds = toSignal(this.estimatorService.checkedPricingOptions$.pipe(
    map(options => options.map(o => o.pricingOption.Id),
  )), { initialValue: [] });

  selectPricingOption(pricingOption: PricingOption) {
    this.estimatorService.selectPricingOption(pricingOption.Id, this.product().ProductCode);
  }

  update(checked: boolean, pricingOption: PricingOption) {
    this.estimatorService.updateCheckedPricingOption(checked, pricingOption, this.product());
  }
}
