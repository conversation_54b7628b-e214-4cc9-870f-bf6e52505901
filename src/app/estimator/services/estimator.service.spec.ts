import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from "@angular/core/testing";
import { LoanPurpose } from "@rocket-logic/rl-xp-bff-models";
import { TeamMemberRoles } from "../../services/team-member-data/team-member-data.serivce";
import { AprFee, FtdDetails, ParentEligibilityGroup } from "../models/products";
import { EstimatorService } from "./estimator.service";

describe('EstimatorService', () => {
  let service: EstimatorService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [HttpClientTestingModule],
      providers: [EstimatorService]
    });

    service = TestBed.inject(EstimatorService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('getProductFees should return the correct fees for non government schwab', () => {
    service.loanPurposeControl.setValue(LoanPurpose.Purchase);
    
    service.calcForm.patchValue({
      purchasePrice: 200000,
      downPaymentAmount: 40000,
      downPaymentPercentage: 20
    });
    const teamMemberRoles: TeamMemberRoles = {
      isSchwabBanker: false,
      isFranchiseBanker: false
    };
    const parentEligibilityGroups = [ParentEligibilityGroup.Schwab];
    const aprFees: AprFee[] = [
      {
        Code: '810',
        Amount: 2500,
        Name: 'Processing Fee'
      },
      {
        Code: '801',
        Amount: 2500,
        Name: 'Loan Origination'
      },
      {
        Code: '802',
        Amount: 2500,
        Name: 'Loan Discount Fee'
      },
    ];
    const ftdDetails = createTestFTDDetails({
      NonFinancedAmounts: {
        Details: {
          NetPerDiem: {
            Total: 100
          },
          NetFees: {
            Details: {
              APRFees: aprFees
            }
          }
        }
      }
    });
    const resultFees = service.getProductFees(parentEligibilityGroups, ftdDetails, teamMemberRoles);

    expect(resultFees.originationFee?.Amount).toEqual(1195);
    expect(resultFees.loanDiscountFee?.Amount).toEqual(2500);
    expect(resultFees.processingFee).toBe(null);
  });

  it('getProductFees should return the correct fees for FHA or VA schwab', () => {
    service.loanPurposeControl.setValue(LoanPurpose.Purchase);
    
    service.calcForm.patchValue({
      purchasePrice: 200000,
      downPaymentAmount: 40000,
      downPaymentPercentage: 20
    });
    const teamMemberRoles: TeamMemberRoles = {
      isSchwabBanker: false,
      isFranchiseBanker: false
    };
    const parentEligibilityGroups = [ParentEligibilityGroup.Schwab, ParentEligibilityGroup.FHA];
    const aprFees: AprFee[] = [
      {
        Code: '810',
        Amount: 2500,
        Name: 'Processing Fee'
      },
      {
        Code: '801',
        Amount: 2500,
        Name: 'Loan Origination'
      },
      {
        Code: '802',
        Amount: 2500,
        Name: 'Loan Discount Fee'
      },
    ];
    const ftdDetails = createTestFTDDetails({
      NonFinancedAmounts: {
        Details: {
          NetPerDiem: {
            Total: 100
          },
          NetFees: {
            Details: {
              APRFees: aprFees
            }
          }
        }
      }
    });
    const resultFees = service.getProductFees(parentEligibilityGroups, ftdDetails, teamMemberRoles);

    expect(resultFees.originationFee).toBe(null);
    expect(resultFees.loanDiscountFee?.Amount).toEqual(2500);
    expect(resultFees.processingFee).toBe(null);
  });

  it('getProductFees should return the correct fees for FHA for Franchise bankers', () => {
    service.loanPurposeControl.setValue(LoanPurpose.Purchase);
    
    service.calcForm.patchValue({
      purchasePrice: 200000,
      downPaymentAmount: 40000,
      downPaymentPercentage: 20
    });
    const teamMemberRoles: TeamMemberRoles = {
      isSchwabBanker: false,
      isFranchiseBanker: true
    };
    const parentEligibilityGroups = [ParentEligibilityGroup.FHA];
    const aprFees: AprFee[] = [
      {
        Code: '801',
        Amount: 2500,
        Name: 'Loan Origination'
      },
      {
        Code: '802',
        Amount: 2500,
        Name: 'Loan Discount Fee'
      },
    ];
    const ftdDetails = createTestFTDDetails({
      NonFinancedAmounts: {
        Details: {
          NetPerDiem: {
            Total: 100
          },
          NetFees: {
            Details: {
              APRFees: aprFees
            }
          }
        }
      }
    });
    const resultFees = service.getProductFees(parentEligibilityGroups, ftdDetails, teamMemberRoles);

    expect(resultFees.originationFee?.Amount).toEqual(1600);
    expect(resultFees.loanDiscountFee?.Amount).toEqual(2500);
    expect(resultFees.processingFee).toBe(null);
  });

  it('getProductFees should return the correct fees for non government for Franchise bankers', () => {
    service.loanPurposeControl.setValue(LoanPurpose.Purchase);
    
    service.calcForm.patchValue({
      purchasePrice: 200000,
      downPaymentAmount: 40000,
      downPaymentPercentage: 20
    });
    const teamMemberRoles: TeamMemberRoles = {
      isSchwabBanker: false,
      isFranchiseBanker: true
    };
    const parentEligibilityGroups = [ParentEligibilityGroup.FannieMae];
    const aprFees: AprFee[] = [
      {
        Code: '810',
        Amount: 2500,
        Name: 'Processing Fee'
      },
      {
        Code: '801',
        Amount: 2500,
        Name: 'Loan Origination'
      },
      {
        Code: '802',
        Amount: 2500,
        Name: 'Loan Discount Fee'
      },
    ];
    const ftdDetails = createTestFTDDetails({
      NonFinancedAmounts: {
        Details: {
          NetPerDiem: {
            Total: 100
          },
          NetFees: {
            Details: {
              APRFees: aprFees
            }
          }
        }
      }
    });
    const resultFees = service.getProductFees(parentEligibilityGroups, ftdDetails, teamMemberRoles);

    expect(resultFees.originationFee?.Amount).toEqual(2500);
    expect(resultFees.loanDiscountFee?.Amount).toEqual(2500);
    expect(resultFees.processingFee?.Amount).toEqual(900);
  });

  it('getProductFees should return the default fees for VA for Franchise bankers', () => {
    service.loanPurposeControl.setValue(LoanPurpose.Purchase);
    
    service.calcForm.patchValue({
      purchasePrice: 200000,
      downPaymentAmount: 40000,
      downPaymentPercentage: 20
    });
    const teamMemberRoles: TeamMemberRoles = {
      isSchwabBanker: false,
      isFranchiseBanker: true
    };
    const parentEligibilityGroups = [ParentEligibilityGroup.VA];
    const aprFees: AprFee[] = [
      {
        Code: '801',
        Amount: 1275,
        Name: 'Loan Origination'
      },
      {
        Code: '802',
        Amount: 2500,
        Name: 'Loan Discount Fee'
      },
    ];
    const ftdDetails = createTestFTDDetails({
      NonFinancedAmounts: {
        Details: {
          NetPerDiem: {
            Total: 100
          },
          NetFees: {
            Details: {
              APRFees: aprFees
            }
          }
        }
      }
    });
    const resultFees = service.getProductFees(parentEligibilityGroups, ftdDetails, teamMemberRoles);

    expect(resultFees.originationFee?.Amount).toEqual(1275);
    expect(resultFees.loanDiscountFee?.Amount).toEqual(2500);
    expect(resultFees.processingFee).toEqual(null);
  });
})

function createTestFTDDetails(overrides: Partial<FtdDetails>): FtdDetails {
  return {
    FinalLoanAmount: {
      Total: 100,
      Details: {
        BaseLoanAmount: {
          Total: 100,
        }
      }
    },
    NonFinancedAmounts: {
      Details: {
        NetPerDiem: {
          Total: 100
        },
        NetFees: {
          Details: {
            APRFees: [],
            ...overrides.NonFinancedAmounts?.Details.NetFees.Details
          }
        }
      }
    },
    ...overrides,
  }
}