import { FeesCalcResponse } from '../models/fees-calc-response';

export const mockFeesResponse = {
  FeeDetail: [
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: true,
      IsAppFee: false,
      FeeNumber: '1346',
      Unassigned: 0,
      Jacket<PERSON>umber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Buyer Paid Real Estate Agent Commission Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1346',
      Hud1RollTo: '1346',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1306',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Well/Septic/Safe Water',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1305',
      Hud1RollTo: '1301',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '816',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Life of Loan Tax Service',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'CoreLogic Tax Services',
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '3',
      Hud1LineNumber: '806',
      Hud1RollTo: '806',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '805',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: "Final Insp/Add'l Appraisal Fee",
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Amrock, LLC',
      IsNetFunded: true,
      AllowEdits: true,
      GfeBlockNumber: '3',
      Hud1LineNumber: '809',
      Hud1RollTo: '809',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: true,
      IsAppFee: true,
      FeeNumber: '1109',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 678,
      IsFlatFee: true,
      FeeDescription: "Lender's Title Insurance",
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1104',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'MORTGAGEPREMIUM',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 17,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1320',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Miscellaneous Inspections',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1318',
      Hud1RollTo: '1318',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1321',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Miscellaneous Inspections',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1319',
      Hud1RollTo: '1319',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1112',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'ALTA 8.1 Environmental Protection Lien',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1130',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'EPA',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 27,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '804',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 100,
      IsFlatFee: true,
      FeeDescription: 'Credit Report',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Credit Bureau',
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '3',
      Hud1LineNumber: '805',
      Hud1RollTo: '805',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: true,
      IsAppFee: true,
      FeeNumber: '801',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Loan Origination Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Sys-file',
      IsNetFunded: true,
      AllowEdits: true,
      GfeBlockNumber: '1',
      Hud1LineNumber: '801',
      Hud1RollTo: '803',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: true,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '803',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 315,
      IsFlatFee: true,
      FeeDescription: 'Appraisal Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Amrock, LLC',
      IsNetFunded: true,
      AllowEdits: true,
      GfeBlockNumber: '3',
      Hud1LineNumber: '804',
      Hud1RollTo: '804',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'APPRAISALSTAND',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 64,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1322',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Property Inspection',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: ORM Property Inspection (1320)',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1320',
      Hud1RollTo: '1320',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '802',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Loan Discount Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Sys-file',
      IsNetFunded: true,
      AllowEdits: true,
      GfeBlockNumber: '2',
      Hud1LineNumber: '802',
      Hud1RollTo: '803',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: true,
      IsAppFee: true,
      FeeNumber: '807',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Flood Life of Loan Coverage',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 5,
      PaidTo: 'Address Name: Flood Company',
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '3',
      Hud1LineNumber: '807',
      Hud1RollTo: '807',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1110',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 989.93,
      IsFlatFee: true,
      FeeDescription: "Owner's Title Insurance",
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '5',
      Hud1LineNumber: '1103',
      Hud1RollTo: '1103',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'OWNERSPREMIUM',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 18,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1323',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Property Inspection',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: ORM Property Inspection (1321)',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1321',
      Hud1RollTo: '1321',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '888',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 175,
      IsFlatFee: true,
      FeeDescription: 'Appraisal Services',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Amrock, LLC',
      IsNetFunded: true,
      AllowEdits: true,
      GfeBlockNumber: '3',
      Hud1LineNumber: '804',
      Hud1RollTo: '804',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'APPRAISALMARGIN',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 65,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1113',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'ALTA 9 Restrictions, Encroachments, Minerals',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1131',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 34,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: true,
      IsAppFee: true,
      FeeNumber: '808',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Flood Determination Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 8,
      PaidTo: 'Address Name: Flood Company',
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '3',
      Hud1LineNumber: '808',
      Hud1RollTo: '808',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '809',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Underwriting Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Sys-file',
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '1',
      Hud1LineNumber: '801',
      Hud1RollTo: '803',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1311',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'VOD/VOM/VOR',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: true,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1306',
      Hud1RollTo: '1306',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '812',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Appraisal Review Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'ClearCapital.com, Inc.',
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '3',
      Hud1LineNumber: '814',
      Hud1RollTo: '0',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: 'B',
      DisplayId: '50',
      AtlasFeeNumber: 0,
      ClosingGroupId: 'B',
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1312',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Payoff Request Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1307',
      Hud1RollTo: '1307',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '817',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Credit Monitoring Service',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Equifax Information Services, LLC',
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '3',
      Hud1LineNumber: '819',
      Hud1RollTo: '805',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1114',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: "Add'l Endorsement Fee",
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1139',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '810',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 1195,
      IsFlatFee: true,
      FeeDescription: 'Processing Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Sys-file',
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '1',
      Hud1LineNumber: '801',
      Hud1RollTo: '803',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1107',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: "Attorney's Fees",
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Closing Agent',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1115',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'ATTORNEY',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 16,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 2,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1312a',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Payoff Request Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1308',
      Hud1RollTo: '1308',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '806',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Wire Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Title Company',
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1140',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1115',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: "Add'l Endorsement Fee",
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1138',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '903',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Hazard Insurance Premium',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: "Address Name: Homeowner's Insurance",
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '11',
      Hud1LineNumber: '903',
      Hud1RollTo: '903',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1125',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Miscellaneous Closing Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'BLANK',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1142',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 6,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1312b',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Payoff Request Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1309',
      Hud1RollTo: '1309',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '904',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Flood Insurance Premium',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Flood Insurance',
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '11',
      Hud1LineNumber: '904',
      Hud1RollTo: '904',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '815',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 24.95,
      IsFlatFee: true,
      FeeDescription: 'MERS Registration Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'MERS',
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '1',
      Hud1LineNumber: '801',
      Hud1RollTo: '30',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: 'B',
      DisplayId: '330',
      AtlasFeeNumber: 0,
      ClosingGroupId: 'B',
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1126',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Miscellaneous Closing Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'BLANK',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1143',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 6,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1390',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Broker Compliance Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '704',
      Hud1RollTo: null,
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1118',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Title Endorsements',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1127',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: true,
      IsAppFee: true,
      FeeNumber: '813',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'MI Paid in Cash',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '3',
      Hud1LineNumber: '813',
      Hud1RollTo: '813',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1324',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Miscellaneous',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Blank',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1322',
      Hud1RollTo: '1322',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1127',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Miscellaneous Closing Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'BLANK',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1144',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 6,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1116',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Tax Certification Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Amrock, LLC',
      IsNetFunded: true,
      AllowEdits: true,
      GfeBlockNumber: '3',
      Hud1LineNumber: '812',
      Hud1RollTo: '812',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1119',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: null,
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: null,
      Hud1LineNumber: null,
      Hud1RollTo: null,
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '902',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Upfront Mortgage Insurance Premium',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: MI Company',
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '3',
      Hud1LineNumber: '902',
      Hud1RollTo: '902',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1326',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Miscellaneous',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1324',
      Hud1RollTo: '1324',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1309',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Unpaid Taxes',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: '',
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '',
      Hud1LineNumber: '104',
      Hud1RollTo: '',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: true,
      IsAppFee: false,
      FeeNumber: '1345',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Buyer Paid Real Estate Agent Commission Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1345',
      Hud1RollTo: '1345',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: true,
      IsAppFee: true,
      FeeNumber: '1313',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Condo Insurance Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'HOA',
      IsNetFunded: true,
      AllowEdits: true,
      GfeBlockNumber: '3',
      Hud1LineNumber: '816',
      Hud1RollTo: '816',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1309N',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Unpaid Taxes',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: '',
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '',
      Hud1LineNumber: '104',
      Hud1RollTo: '',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1124',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Miscellaneous Closing Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'BLANK',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1141',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: true,
      IsAppFee: true,
      FeeNumber: '1313a',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Condo Questionnaire',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'HOA',
      IsNetFunded: true,
      AllowEdits: true,
      GfeBlockNumber: '3',
      Hud1LineNumber: '817',
      Hud1RollTo: '817',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1310',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Escrow Holdback',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '',
      Hud1LineNumber: '',
      Hud1RollTo: '',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1128',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Tax Service Fee (Closing Agent)',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Closing Agent',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1145',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: true,
      IsAppFee: true,
      FeeNumber: '1313b',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Condo Budget/ByLaws/Master',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'HOA',
      IsNetFunded: true,
      AllowEdits: true,
      GfeBlockNumber: '3',
      Hud1LineNumber: '818',
      Hud1RollTo: '818',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1325',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Miscellaneous',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1323',
      Hud1RollTo: '1323',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1129',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Wire Fee-Closing Agent',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Closing Agent',
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1146',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1301',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Survey',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Surveyor',
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1128',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1101',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 550,
      IsFlatFee: true,
      FeeDescription: 'Settlement or Closing Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Closing Agent',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1102',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'FULLESCROW1ST',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 2,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1122',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Recording Service',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1136',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'RECORDING',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 62,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 1,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1131',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Miscellaneous Closing Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Blank',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1147',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1303',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Subordination Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '3',
      Hud1LineNumber: '815',
      Hud1RollTo: '815',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1105',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Document Preparation Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1112',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'DEEDPREP',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 14,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1123',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: "Add'l Closing Fee",
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Blank',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1123',
      Hud1RollTo: '1123',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1013',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Special Assessments',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1012',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1132',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Miscellaneous Closing Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1148',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1014',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Other Insurance',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1013',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1106',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Notary Fees',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1114',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'NOTARY',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 15,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1117',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: "Add'l Endorsement Fee",
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Title Company',
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1139',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1340',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'HOA Capital Contribution Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1340',
      Hud1RollTo: '1340',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'Closing Agent',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1307',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 35,
      IsFlatFee: true,
      FeeDescription: 'Express Mail/Courier Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Closing Agent',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1137',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 63,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1015',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Other Taxes',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1014',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1001',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Hazard Insurance',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1002',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1339',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'HOA Start Up Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1339',
      Hud1RollTo: '1339',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'Closing Agent',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1016',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'School Taxes',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1015',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1105B',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Deed Preparation Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1113',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1002',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Mortgage Insurance',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1003',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1338',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'HOA Conveyance Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1338',
      Hud1RollTo: '1338',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'Closing Agent',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1017',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Supplemental Taxes',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1016',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1003',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'City Taxes',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1005',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1337',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'HOA Transfer Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1337',
      Hud1RollTo: '1337',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'Closing Agent',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1018',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Other Taxes: MISCELLANEOUS',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1017',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1102',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Abstract or Title Search',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Title Company',
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1109',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'SEARCHEXAM',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 6,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1004',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'County Taxes',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1004',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1019',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Other Taxes: MISCELLANEOUS',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1018',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1341',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'HOA Initial Contribution Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1341',
      Hud1RollTo: '1341',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'Closing Agent',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1006',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Flood Insurance',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1006',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1103',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Title Examination Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1110',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'ABSTRACT',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 7,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1020',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Other Taxes: MISCELLANEOUS',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1019',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1342',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'HOA Working Capital Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1342',
      Hud1RollTo: '1342',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'Closing Agent',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1007',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Village Taxes',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1007',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1104',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Title Insurance Binder',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Title Company',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1111',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'TITLECERT',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 13,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1302',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Pest Inspection',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1303',
      Hud1RollTo: '1301',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1008',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Aggregate Acct. Adjustment',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: true,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1010',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1336',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Home Warranty (Optional) Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1336',
      Hud1RollTo: '1336',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1010',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Hurricane Insurance',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1008',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1335',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Private Transfer Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1335',
      Hud1RollTo: '1335',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1011',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Earthquake Insurance',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1009',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1343',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Builder Fees',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1343',
      Hud1RollTo: '1343',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'Closing Agent',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1120',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Admin Support',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Address Name: Title Company',
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1134',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: 'ADMINCLOSING',
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 57,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: false,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1012',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: false,
      FeeDescription: 'Wind Insurance',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '9',
      Hud1LineNumber: '1011',
      Hud1RollTo: '1001',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1344',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Auction Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '6',
      Hud1LineNumber: '1344',
      Hud1RollTo: '1344',
      RespaToleranceAmount: 100,
      FeeRate: 0,
      DisburseBy: 'Closing Agent',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: true,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: true,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1121',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Copies of Supporting Documents',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '4',
      Hud1LineNumber: '1135',
      Hud1RollTo: '1101',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 61,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: true,
      IsAppFee: true,
      FeeNumber: '1207a',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Transfer Taxes-County-Deed',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Saginaw',
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '8',
      Hud1LineNumber: '1207a',
      Hud1RollTo: '1203',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 293.7,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: true,
      IsAppFee: true,
      FeeNumber: '1203a',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Transfer Taxes-State-Deed',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Michigan',
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '8',
      Hud1LineNumber: '1205deed',
      Hud1RollTo: '1203',
      RespaToleranceAmount: 0,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 2002.5,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1201A',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 35,
      IsFlatFee: true,
      FeeDescription: 'Recording Fee Deed',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Saginaw County Register of Deeds',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '7',
      Hud1LineNumber: '1202deed',
      Hud1RollTo: '1201',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: false,
      FeeNumber: '1204',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Assignment Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'ErnstPayee',
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '8',
      Hud1LineNumber: '1206',
      Hud1RollTo: '1203',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1201B',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 30,
      IsFlatFee: true,
      FeeDescription: 'Recording Fee Mortgage',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Saginaw County Register of Deeds',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '7',
      Hud1LineNumber: '1202mortgage',
      Hud1RollTo: '1201',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1205',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Misc Rec/Transfer Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: null,
      IsNetFunded: false,
      AllowEdits: false,
      GfeBlockNumber: '8',
      Hud1LineNumber: '1207',
      Hud1RollTo: '1203',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: 'None',
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
    {
      IncludeinFederalPtsandFees: false,
      IncludeinStatePtsandFees: false,
      IncludeinStateHighCost: false,
      IsTPO: false,
      ImportFeeAmount: true,
      IsPrepaidFinanceCharge: false,
      IsGovAllowed: false,
      IsAppFee: true,
      FeeNumber: '1201C',
      Unassigned: 0,
      JacketNumber: '',
      IsLenderPaid: false,
      FeeAmount: 0,
      IsFlatFee: true,
      FeeDescription: 'Recording Releases Fee',
      SellerAmount: 0,
      OtherAmount: 0,
      ClientAmountPOC: 0,
      SellerAmountPOC: 0,
      OtherAmountPOC: 0,
      PaidTo: 'Saginaw County Register of Deeds',
      IsNetFunded: false,
      AllowEdits: true,
      GfeBlockNumber: '7',
      Hud1LineNumber: '1202release',
      Hud1RollTo: '1201',
      RespaToleranceAmount: 10,
      FeeRate: 0,
      DisburseBy: null,
      RuleSetName: '',
      IncludeAcquisitionCost: false,
      AtlasFeeCode: null,
      GroupId: null,
      DisplayId: null,
      AtlasFeeNumber: 0,
      ClosingGroupId: null,
      SPLFeeTypeNumber: 0,
      SellerCustomaryAmount: 0,
      ItemizedAmounts: [],
    },
  ],
  ApprDetail: [],
  LoanInfo: {
    AppraisalManagementCompanyID: null,
    AppraisalManagementCompanyName: null,
    TitleProductType: 'TITLECOMMITMENT',
    TitleCompanyPremiumSplit: 1417.74,
    TitleEvidenceServicePremiumSplit: 0,
    TitleUnderwriterPremiumSplit: 250.19,
    TotalTitlePremium: 1667.93,
    TitleCoDiscOwnerPremium: 989.93,
    TitleCoDiscLenderPremium: 678,
    TitleCoPremiumDifference: 0,
    TitleCoUndiscOwnerPremium: 0,
    TitleCoUndiscLenderPremium: 0,
    NumberofPagesMortgage: 20,
    UnderwriterCode: 'AMROCK',
  },
  MortgageInsuranceInfo: {
    ConventionalTotalFactor1st: 0,
    ConventionalTotalFactorRenewal: 0,
    FHAMonthlyFactor: 0,
    FHATotalUpfrontFactor: 0,
    LTVCutoff: 0,
    MICoveragePercentage: 0,
    MIPlanType: null,
    MIProvider: null,
    NumberOfPaymentsRate1: 0,
    NumberOfPaymentsRate2: 0,
    TypeisLenderPaidPremium: false,
    TypeisSplitPremium: false,
    VAUpfrontFactor: 0,
    HarpMICertificateType: null,
    HarpMIDecisionType: null,
    HarpMICoveragePercent: 0,
    HarpMIInitPremRateDurMonths: 0,
    HarpMIDurationType: null,
    HarpMIPremiumPaymentType: null,
    HarpMIInitPremAtClosType: null,
    HarpMIRenewalCalcType: null,
    HarpMIInitPremRatePercent: 0,
    HarpMIPremRatePlanType: null,
    HarpMIRenewalRateFirst: 0,
    HarpMIRenewalRateSecond: 0,
    HarpMIRenewRateDuration1st: 0,
    HarpMIRenewRateDuration2nd: 0,
    HarpMIMunicipalTaxRate: 0,
    HarpMIStateTaxRate: 0,
    HarpMICountyTaxRate: 0,
    NumberMonthsEscrow: 0,
    HarpMICertificateNumber: null,
    HarpMICompanyName: null,
    IsMIRequired: false,
    IsMIFinanced: true,
    MIMunicipalTaxRate: 0,
    MIStateTaxRate: 0,
    MICountyTaxRate: 0,
    MICalcType: null,
    ARMMonthsToFirstRateAdj: 0,
    LenderMessageNoMIExists: false,
    Error: null,
  },
  TransferTaxes: [
    {
      FeeNumber: '1207a',
      Jurisdiction: 'County',
      TaxType: 'DeedTax',
      TaxTotal: 293.7,
      BuyerCustomaryAmount: 0,
      SellerCustomaryAmount: 293.7,
    },
    {
      FeeNumber: '1203a',
      Jurisdiction: 'State',
      TaxType: 'DeedTax',
      TaxTotal: 2002.5,
      BuyerCustomaryAmount: 0,
      SellerCustomaryAmount: 2002.5,
    },
  ],
  BrokenRules: [],
  Exceptions: { Exception: null },
  HasErrors: false,
} as unknown as FeesCalcResponse;
