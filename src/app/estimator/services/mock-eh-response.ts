import { ProductsResponse } from '../models/products';

export const mockEhResponse = {
	"ScheduleOfDates": {
		"ClosingDate": "2024-12-06T00:00:00",
		"DisbursementDate": "2024-12-06T00:00:00",
		"DocumentationDueDate": "2024-11-02T00:00:00",
		"FirstPaymentDate": "2025-02-01T00:00:00"
	},
	"RecommendedPurchasePrice": 267000.0,
	"RecommendedDownPayment": 67000.0,
	"PurchaseScenarioType": "PreStructured",
	"AppliedAssetsRequired": null,
	"ReserveAssetsRequired": null,
	"AssetsNeeded": null,
	"ReservesNeeded": null,
	"ClientCollection": {
		"AreFirstTimeHomeBuyers": false,
		"AreVeterans": true,
		"Clients": [
			{
				"IncomeCollection": {
					"Incomes": [
						{
							"Id": "7a00c83e-ea74-4a4b-9bb5-c2dfbc6b6bb4",
							"EligibleAmounts": {
								"SchwabHELOC": 480000.0,
								"QLHeloc": 480000.0,
								"Advantage": 480000.0,
								"FannieMaeHARPHighBalance": 480000.0,
								"FannieMaeHighBalanceReliefRefi": 480000.0,
								"FreddieMacHARPHighBalance": 480000.0,
								"FreddieMacHighBalanceReliefRefi": 480000.0,
								"USDAConforming": 480000.0,
								"FHAJumboNonCreditQualifyingStreamline": 480000.0,
								"FHAConformingNonCreditQualifyingStreamline": 480000.0,
								"VAJumboNonCreditQualifyingStreamline": 480000.0,
								"VAConformingNonCreditQualifyingStreamline": 480000.0,
								"FHAJumboCreditQualifyingStreamline": 480000.0,
								"FHAConformingCreditQualifyingStreamline": 480000.0,
								"VAJumboCreditQualifyingStreamline": 480000.0,
								"VAConformingCreditQualifyingStreamline": 480000.0,
								"FannieMaeReliefRefi": 480000.0,
								"FreddieMacReliefRefi": 480000.0,
								"AgencyPlusInterestOnly": 480000.0,
								"AgencyPlusNonInterestOnly": 480000.0,
								"SchwabJumboATRInterestOnly": 480000.0,
								"SchwabConformingInterestOnly": 480000.0,
								"SchwabJumboATRNonInterestOnly": 480000.0,
								"SchwabConformingNonInterestOnly": 480000.0,
								"SchwabJumboQM": 480000.0,
								"FHAJumboNonStreamline": 480000.0,
								"FHAConformingNonStreamline": 480000.0,
								"VAJumboNonStreamline": 480000.0,
								"VAConformingNonStreamline": 480000.0,
								"QLJumboStandard": 480000.0,
								"FannieMaeHighBalance": 480000.0,
								"FreddieMacHighBalance": 480000.0,
								"FannieMaeConformingNonHighBalance": 480000.0,
								"FreddieMacNonHighBalance": 480000.0,
								"FreddieMacHomePossibleHighBalance": 480000.0,
								"FreddieMacHomePossibleNonHighBalance": 480000.0,
								"FannieMaeHomeReadyHighBalance": 480000.0,
								"FannieMaeHomeReadyNonHighBalance": 480000.0,
								"Assumption": 480000.0,
								"AllAccessJumbo": 480000.0,
								"FannieMaeRefiNowConformingHighBalance": 480000.0,
								"FannieMaeRefiNowConformingNonHighBalance": 480000.0,
								"FreddieMacRefiPossibleConformingHighBalance": 480000.0,
								"FreddieMacRefiPossibleConformingNonHighBalance": 480000.0,
								"FreddieMacBorrowSmartConformingHighBalance": 480000.0,
								"FreddieMacBorrowSmartConformingNonHighBalance": 480000.0,
								"ClosedEndSeconds": 480000.0,
								"PaymentSmart": 480000.0,
								"BankStatementLoan": 480000.0,
								"FannieMaeSpecialPurposeCreditProgramHighBalance": 480000.0,
								"FannieMaeSpecialPurposeCreditProgramNonHighBalance": 480000.0,
								"FreddieMacBorrowSmartAccessConformingHighBalance": 480000.0,
								"FreddieMacBorrowSmartAccessConformingNonHighBalance": 480000.0,
								"FannieMaeRocketOneNonHighBalance": 480000.0,
								"FreddieMacRocketOneNonHighBalance": 480000.0,
								"FannieMaeSchwabMortgageAdvantageProgramNonHighBalance": 480000.0,
								"FannieMaeHighBalanceSchwab": 480000.0,
								"FannieMaeNonHighBalanceSchwab": 480000.0,
								"AmeripriseJumbo": 480000.0
							},
							"FederalWithholdings": 114048.0,
							"SocialSecurityWithholdings": 24576.0,
							"StateWithholdings": 20400.0,
							"Messages": null
						}
					],
					"DaysWorked": 1827
				},
				"DebtCollection": {
					"Debts": [
						{
							"Id": null,
							"CreditorName": null,
							"EligibleAmounts": {
								"SchwabHELOC": 0.0,
								"QLHeloc": 0.0,
								"Advantage": 0.0,
								"FannieMaeHARPHighBalance": 0.0,
								"FannieMaeHighBalanceReliefRefi": 0.0,
								"FreddieMacHARPHighBalance": 0.0,
								"FreddieMacHighBalanceReliefRefi": 0.0,
								"USDAConforming": 0.0,
								"FHAJumboNonCreditQualifyingStreamline": 0.0,
								"FHAConformingNonCreditQualifyingStreamline": 0.0,
								"VAJumboNonCreditQualifyingStreamline": 0.0,
								"VAConformingNonCreditQualifyingStreamline": 0.0,
								"FHAJumboCreditQualifyingStreamline": 0.0,
								"FHAConformingCreditQualifyingStreamline": 0.0,
								"VAJumboCreditQualifyingStreamline": 0.0,
								"VAConformingCreditQualifyingStreamline": 0.0,
								"FannieMaeReliefRefi": 0.0,
								"FreddieMacReliefRefi": 0.0,
								"AgencyPlusInterestOnly": 0.0,
								"AgencyPlusNonInterestOnly": 0.0,
								"SchwabJumboATRInterestOnly": 0.0,
								"SchwabConformingInterestOnly": 0.0,
								"SchwabJumboATRNonInterestOnly": 0.0,
								"SchwabConformingNonInterestOnly": 0.0,
								"SchwabJumboQM": 0.0,
								"FHAJumboNonStreamline": 0.0,
								"FHAConformingNonStreamline": 0.0,
								"VAJumboNonStreamline": 0.0,
								"VAConformingNonStreamline": 0.0,
								"QLJumboStandard": 0.0,
								"FannieMaeHighBalance": 0.0,
								"FreddieMacHighBalance": 0.0,
								"FannieMaeConformingNonHighBalance": 0.0,
								"FreddieMacNonHighBalance": 0.0,
								"FreddieMacHomePossibleHighBalance": 0.0,
								"FreddieMacHomePossibleNonHighBalance": 0.0,
								"FannieMaeHomeReadyHighBalance": 0.0,
								"FannieMaeHomeReadyNonHighBalance": 0.0,
								"AllAccessJumbo": 0.0,
								"FannieMaeRefiNowConformingHighBalance": 0.0,
								"FannieMaeRefiNowConformingNonHighBalance": 0.0,
								"FreddieMacRefiPossibleConformingHighBalance": 0.0,
								"FreddieMacRefiPossibleConformingNonHighBalance": 0.0,
								"FreddieMacBorrowSmartConformingHighBalance": 0.0,
								"FreddieMacBorrowSmartConformingNonHighBalance": 0.0,
								"ClosedEndSeconds": 0.0,
								"PaymentSmart": 0.0,
								"BankStatementLoan": 0.0,
								"FannieMaeSpecialPurposeCreditProgramHighBalance": 0.0,
								"FannieMaeSpecialPurposeCreditProgramNonHighBalance": 0.0,
								"FreddieMacBorrowSmartAccessConformingHighBalance": 0.0,
								"FreddieMacBorrowSmartAccessConformingNonHighBalance": 0.0,
								"FannieMaeRocketOneNonHighBalance": 0.0,
								"FreddieMacRocketOneNonHighBalance": 0.0,
								"FannieMaeSchwabMortgageAdvantageProgramNonHighBalance": 0.0,
								"FannieMaeHighBalanceSchwab": 0.0,
								"FannieMaeNonHighBalanceSchwab": 0.0,
								"AmeripriseJumbo": 0.0
							},
							"WasConsolidated": false,
							"ExclusionDetails": {},
							"Type": "CurrentHomeMonthlyPayment"
						}
					]
				},
				"AssetCollection": {
					"Assets": [
						{
							"Id": "1d6bde04-ce29-47b1-9734-00df04d1d797",
							"EligibleAmounts": {
								"SchwabHELOC": 1000000.0,
								"QLHeloc": 1000000.0,
								"Advantage": 1000000.0,
								"FannieMaeHARPHighBalance": 1000000.0,
								"FannieMaeHighBalanceReliefRefi": 1000000.0,
								"FreddieMacHARPHighBalance": 1000000.0,
								"FreddieMacHighBalanceReliefRefi": 1000000.0,
								"USDAConforming": 1000000.0,
								"FHAJumboNonCreditQualifyingStreamline": 1000000.0,
								"FHAConformingNonCreditQualifyingStreamline": 1000000.0,
								"VAJumboNonCreditQualifyingStreamline": 1000000.0,
								"VAConformingNonCreditQualifyingStreamline": 1000000.0,
								"FHAJumboCreditQualifyingStreamline": 1000000.0,
								"FHAConformingCreditQualifyingStreamline": 1000000.0,
								"VAJumboCreditQualifyingStreamline": 1000000.0,
								"VAConformingCreditQualifyingStreamline": 1000000.0,
								"FannieMaeReliefRefi": 1000000.0,
								"FreddieMacReliefRefi": 1000000.0,
								"AgencyPlusInterestOnly": 1000000.0,
								"AgencyPlusNonInterestOnly": 1000000.0,
								"SchwabJumboATRInterestOnly": 1000000.0,
								"SchwabConformingInterestOnly": 1000000.0,
								"SchwabJumboATRNonInterestOnly": 1000000.0,
								"SchwabConformingNonInterestOnly": 1000000.0,
								"SchwabJumboQM": 1000000.0,
								"FHAJumboNonStreamline": 1000000.0,
								"FHAConformingNonStreamline": 1000000.0,
								"VAJumboNonStreamline": 1000000.0,
								"VAConformingNonStreamline": 1000000.0,
								"QLJumboStandard": 1000000.0,
								"FannieMaeHighBalance": 1000000.0,
								"FreddieMacHighBalance": 1000000.0,
								"FannieMaeConformingNonHighBalance": 1000000.0,
								"FreddieMacNonHighBalance": 1000000.0,
								"FreddieMacHomePossibleHighBalance": 1000000.0,
								"FreddieMacHomePossibleNonHighBalance": 1000000.0,
								"FannieMaeHomeReadyHighBalance": 1000000.0,
								"FannieMaeHomeReadyNonHighBalance": 1000000.0,
								"Assumption": 1000000.0,
								"AllAccessJumbo": 1000000.0,
								"FannieMaeRefiNowConformingHighBalance": 1000000.0,
								"FannieMaeRefiNowConformingNonHighBalance": 1000000.0,
								"FreddieMacRefiPossibleConformingHighBalance": 1000000.0,
								"FreddieMacRefiPossibleConformingNonHighBalance": 1000000.0,
								"FreddieMacBorrowSmartConformingHighBalance": 1000000.0,
								"FreddieMacBorrowSmartConformingNonHighBalance": 1000000.0,
								"ClosedEndSeconds": 1000000.0,
								"PaymentSmart": 1000000.0,
								"BankStatementLoan": 1000000.0,
								"FannieMaeSpecialPurposeCreditProgramHighBalance": 1000000.0,
								"FannieMaeSpecialPurposeCreditProgramNonHighBalance": 1000000.0,
								"FreddieMacBorrowSmartAccessConformingHighBalance": 1000000.0,
								"FreddieMacBorrowSmartAccessConformingNonHighBalance": 1000000.0,
								"FannieMaeRocketOneNonHighBalance": 1000000.0,
								"FreddieMacRocketOneNonHighBalance": 1000000.0,
								"FannieMaeSchwabMortgageAdvantageProgramNonHighBalance": 1000000.0,
								"FannieMaeHighBalanceSchwab": 1000000.0,
								"FannieMaeNonHighBalanceSchwab": 1000000.0,
								"AmeripriseJumbo": 1000000.0
							}
						}
					]
				},
				"Position": 0,
				"Id": "0c53778f-31b0-44bf-b0f4-2bb0c83057da",
				"CreditId": "0c53778f-31b0-44bf-b0f4-2bb0c83057da"
			}
		],
		"QualifyingCreditScore": 740
	},
	"ProductGroups": [
		{
			"AmortizationType": "Heloc",
			"AgencyType": "QL",
			"Products": [],
			"QualifyingFailureReasons": [],
			"RecommendedCashoutAmount": null,
			"LoanPurpose": "Purchase",
			"IsAllCashout": false,
			"AppliedAssetsRequired": null,
			"ReserveAssetsRequired": null,
			"DownPaymentPercentage": null,
			"EligibilityTargets": null
		},
		{
			"AmortizationType": "Fixed",
			"AgencyType": "NonAgency",
			"Products": [],
			"QualifyingFailureReasons": [],
			"RecommendedCashoutAmount": null,
			"LoanPurpose": "Purchase",
			"IsAllCashout": false,
			"AppliedAssetsRequired": null,
			"ReserveAssetsRequired": null,
			"DownPaymentPercentage": null,
			"EligibilityTargets": null
		},
		{
			"AmortizationType": "ARM",
			"AgencyType": "NonAgency",
			"Products": [],
			"QualifyingFailureReasons": [],
			"RecommendedCashoutAmount": null,
			"LoanPurpose": "Purchase",
			"IsAllCashout": false,
			"AppliedAssetsRequired": null,
			"ReserveAssetsRequired": null,
			"DownPaymentPercentage": null,
			"EligibilityTargets": null
		},
		{
			"AmortizationType": "Fixed",
			"AgencyType": "FHA",
			"Products": [
				{
					"IsEligible": true,
					"IsRecommended": true,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": true
					},
					"RecommendationPriorities": {
						"Purchase": 17
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "915",
					"ProductName": "FHA 15 Year Fixed",
					"ProductDescription": "FHA 15-Year Fixed",
					"TermMonths": 180,
					"TermDifference": -180,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "FHAConformingNonStreamline",
					"ParentEligibilityGroups": [
						"Government",
						"Conforming",
						"FHA",
						"FHAConforming",
						"NonStreamline"
					],
					"ProductGroupType": "FHA",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"DUEligible",
						"FHA",
						"Government",
						"Jumbo_None",
						"MI FHA Non-Streamline",
						"QMUWType_VSH",
						"Retail",
						"Retail FHA Full Doc",
						"RumpelLoan",
						"UWSource_DU"
					],
					"UndiscountedBasePoints": 0.875,
					"UndiscountedBaseRate": 7.25,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "915|FHAConformingNonStreamline|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 7.28,
							"MaxAPR": 7.36,
							"Points": {
								"Base": 2.0,
								"Final": 1.25,
								"UndiscountedAllIn": 0.125,
								"Collected": 1.25,
								"Adjustments": [
									{
										"Value": 0.0,
										"Message": "FHA 200000-224999"
									},
									{
										"Value": -0.75,
										"Message": "Retail Purchase FHA Credit"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 7.25,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0519,
							"FrontEndDTI": 0.051936249999999996,
							"MortgageInsurance": {
								"Duration": 11,
								"LTV": "0.01",
								"Percent": 0.15
							},
							"LtvInformation": {
								"Rounded": 0.7491,
								"LTV": 0.7490636704119851,
								"CLTV": 0.7491,
								"HCLTV": 0.7491,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 76977.51,
								"Details": {
									"TotalNetNonFinancedItems": 76977.51,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 280477.51,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 76977.51,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 7388.51,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Origination Fee",
															"Code": "801",
															"Amount": 2000.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 2543.75,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1954.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 533.1,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 533.1,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 203500.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 3500.0,
											"UFMIPDetails": {
												"BaseUFMIP": 3500.0,
												"UFMIPRefundAmount": 0.0
											}
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "Temporary buydown not allowed with LE and Initial Contact dates prior to 9/15/22"
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 1.25
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 1.75,
							"UnroundedUFMIP": 3500.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -106996.8,
								"TenYearSavingsAmount": -213993.6,
								"FifteenYearSavingsAmount": -320990.4,
								"TwentyYearSavingsAmount": null,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1783.28
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 78916.06,
								"TotalReserveAssetAmount": 921083.94,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 6.0,
							"IsCashout": false,
							"AmountFinanced": 194033.2,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 2076.98,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 24.53,
									"PrincipleAndInterestPayment": 1758.75,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1783.28,
											"Base": 1758.75,
											"MortgageInsurance": 24.53,
											"Rate": 6.375,
											"Duration": 12,
											"Period": 1
										},
										{
											"Total": 1782.22,
											"Base": 1758.75,
											"MortgageInsurance": 23.47,
											"Rate": 6.375,
											"Duration": 12,
											"Period": 13
										},
										{
											"Total": 1781.1,
											"Base": 1758.75,
											"MortgageInsurance": 22.35,
											"Rate": 6.375,
											"Duration": 12,
											"Period": 25
										},
										{
											"Total": 1779.89,
											"Base": 1758.75,
											"MortgageInsurance": 21.14,
											"Rate": 6.375,
											"Duration": 12,
											"Period": 37
										},
										{
											"Total": 1778.61,
											"Base": 1758.75,
											"MortgageInsurance": 19.86,
											"Rate": 6.375,
											"Duration": 12,
											"Period": 49
										},
										{
											"Total": 1777.25,
											"Base": 1758.75,
											"MortgageInsurance": 18.5,
											"Rate": 6.375,
											"Duration": 12,
											"Period": 61
										},
										{
											"Total": 1775.79,
											"Base": 1758.75,
											"MortgageInsurance": 17.04,
											"Rate": 6.375,
											"Duration": 12,
											"Period": 73
										},
										{
											"Total": 1774.24,
											"Base": 1758.75,
											"MortgageInsurance": 15.49,
											"Rate": 6.375,
											"Duration": 12,
											"Period": 85
										},
										{
											"Total": 1772.58,
											"Base": 1758.75,
											"MortgageInsurance": 13.83,
											"Rate": 6.375,
											"Duration": 12,
											"Period": 97
										},
										{
											"Total": 1770.82,
											"Base": 1758.75,
											"MortgageInsurance": 12.07,
											"Rate": 6.375,
											"Duration": 12,
											"Period": 109
										},
										{
											"Total": 1768.95,
											"Base": 1758.75,
											"MortgageInsurance": 10.2,
											"Rate": 6.375,
											"Duration": 12,
											"Period": 121
										},
										{
											"Total": 1758.75,
											"Base": 1758.75,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 48,
											"Period": 133
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 14,
							"RecommendationPriorities": {
								"Purchase": 14
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7885.625,
								"Adjusted": 7885.62,
								"TotalTargetProfit": 7885.62,
								"TargetProfitPercent": 3.875,
								"AdjustedPercent": 3.875,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.875
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 2000.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 3500.0,
									"Message": "ClosingCosts:Ufmip"
								},
								{
									"Value": 2543.75,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 533.1,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1976.61,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2889.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 4866.5599999999995,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 209999.31,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 2077.45,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 7433.76,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"FHAFixed"
					],
					"UnderwritingSource": null,
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 17,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				}
			],
			"QualifyingFailureReasons": [],
			"RecommendedCashoutAmount": null,
			"LoanPurpose": "Purchase",
			"IsAllCashout": false,
			"AppliedAssetsRequired": null,
			"ReserveAssetsRequired": null,
			"DownPaymentPercentage": null,
			"EligibilityTargets": {
				"APR": {
					"Min": null,
					"Max": 7.98,
					"DependentTargets": null
				},
				"Debt": null,
				"Income": null,
				"BackendDTI": null,
				"Assets": null,
				"ReserveAssets": null,
				"LTV": null,
				"SubordinatedLienBalance": null,
				"CreditScore": null,
				"Tradelines": null,
				"ClosingDate": null,
				"ApplicationDate": null,
				"CLTV": null,
				"HCLTV": null,
				"HelocInitialDraw": null,
				"CommitmentPeriod": null,
				"FinancedProperties": null,
				"PartnerAssets": null,
				"LoanAmount": null,
				"MonthsAtCurrentJob": null,
				"ResidualIncome": null,
				"FrontendDTI": null,
				"Foreclosure": null,
				"ShortSale": null,
				"MortgageLates": null,
				"Bankruptcy": null,
				"MortgageHistory": null,
				"CollectionBalance": null
			}
		},
		{
			"AmortizationType": "ARM",
			"AgencyType": "FHA",
			"Products": [],
			"QualifyingFailureReasons": [],
			"RecommendedCashoutAmount": null,
			"LoanPurpose": "Purchase",
			"IsAllCashout": false,
			"AppliedAssetsRequired": null,
			"ReserveAssetsRequired": null,
			"DownPaymentPercentage": null,
			"EligibilityTargets": null
		},
		{
			"AmortizationType": "Fixed",
			"AgencyType": "VA",
			"Products": [
				{
					"IsEligible": true,
					"IsRecommended": false,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": false
					},
					"RecommendationPriorities": {
						"Purchase": 8
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "815V",
					"ProductName": "VA 15 Year Fixed",
					"ProductDescription": "VA 15-Year Fixed",
					"TermMonths": 180,
					"TermDifference": -180,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "VAConformingNonStreamline",
					"ParentEligibilityGroups": [
						"Government",
						"Conforming",
						"VA",
						"VAConforming",
						"NonStreamline"
					],
					"ProductGroupType": "VA",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"DUEligible",
						"Government",
						"Jumbo_None",
						"MI VA Non-IRRRL",
						"QMUWType_VSH",
						"Retail",
						"Retail VA Full Doc",
						"RumpelLoan",
						"UWSource_DU",
						"VA"
					],
					"UndiscountedBasePoints": 0.75,
					"UndiscountedBaseRate": 7.25,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "815V|VAConformingNonStreamline|Rate:6.25|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 6.897,
							"MaxAPR": 7.56,
							"Points": {
								"Base": 2.0,
								"Final": 1.5,
								"UndiscountedAllIn": 0.25,
								"Collected": 1.5,
								"Adjustments": [
									{
										"Value": 0.0,
										"Message": "Purchase >= $200K"
									},
									{
										"Value": -0.5,
										"Message": "Retail Purchase VA Credit"
									},
									{
										"Value": 0.0,
										"Message": "VA 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.25,
								"Adjusted": 6.25,
								"UndiscountedAllInRate": 7.25,
								"Adjustments": [
									{
										"Value": 6.25,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.25
							},
							"IsEligible": true,
							"DTI": 0.0507,
							"FrontEndDTI": 0.05074975,
							"MortgageInsurance": {
								"Duration": 0,
								"LTV": "0.01",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.7491,
								"LTV": 0.7490636704119851,
								"CLTV": 0.7491,
								"HCLTV": 0.7491,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 76843.21,
								"Details": {
									"TotalNetNonFinancedItems": 76843.21,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 279343.21,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 76843.21,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 7267.26,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Origination Fee",
															"Code": "801",
															"Amount": 2000.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 29.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 3037.5,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 2199.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 520.05,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 520.05,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 202500.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 2500.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "Temporary buydown not allowed with LE and Initial Contact dates prior to 9/15/22"
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 1.5
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 1.25,
							"UnroundedUFMIP": 2500.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -104177.4,
								"TenYearSavingsAmount": -208354.8,
								"FifteenYearSavingsAmount": -312532.2,
								"TwentyYearSavingsAmount": null,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1736.29
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 79275.51,
								"TotalReserveAssetAmount": 920724.49,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 4.0,
							"IsCashout": false,
							"AmountFinanced": 194412.5,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 2029.99,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1736.29,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1736.29,
											"Base": 1736.29,
											"MortgageInsurance": 0.0,
											"Rate": 6.25,
											"Duration": 180,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 13,
							"RecommendationPriorities": {
								"Purchase": 13
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7593.75,
								"Adjusted": 7593.75,
								"TotalTargetProfit": 7593.75,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 2000.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 2500.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 3037.5,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 520.0500000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 2221.61,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2029.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 4251.56,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 209865.00999999998,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 2029.99,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6805.71,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"VAFixed",
						"VAGeneral"
					],
					"UnderwritingSource": null,
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 8,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				},
				{
					"IsEligible": true,
					"IsRecommended": false,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": false
					},
					"RecommendationPriorities": {
						"Purchase": 5
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "820V",
					"ProductName": "VA 20 Year Fixed",
					"ProductDescription": "VA 20-Year Fixed",
					"TermMonths": 240,
					"TermDifference": -240,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "VAConformingNonStreamline",
					"ParentEligibilityGroups": [
						"Government",
						"Conforming",
						"VA",
						"VAConforming",
						"NonStreamline"
					],
					"ProductGroupType": "VA",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"DUEligible",
						"Government",
						"Jumbo_None",
						"MI VA Non-IRRRL",
						"QMUWType_VSH",
						"Retail",
						"Retail VA Full Doc",
						"UWSource_DU",
						"VA"
					],
					"UndiscountedBasePoints": 0.0,
					"UndiscountedBaseRate": 7.625,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "820V|VAConformingNonStreamline|Rate:7|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 7.536,
							"MaxAPR": 7.83,
							"Points": {
								"Base": 2.0,
								"Final": 1.5,
								"UndiscountedAllIn": -0.5,
								"Collected": 1.5,
								"Adjustments": [
									{
										"Value": 0.0,
										"Message": "Purchase >= $200K"
									},
									{
										"Value": -0.5,
										"Message": "Retail Purchase VA Credit"
									},
									{
										"Value": 0.0,
										"Message": "VA 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 7.0,
								"Adjusted": 7.0,
								"UndiscountedAllInRate": 7.625,
								"Adjustments": [
									{
										"Value": 7.0,
										"Message": "Base Price"
									}
								],
								"Qualifying": 7.0
							},
							"IsEligible": true,
							"DTI": 0.0466,
							"FrontEndDTI": 0.04659225,
							"MortgageInsurance": {
								"Duration": 0,
								"LTV": "0.01",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.7491,
								"LTV": 0.7490636704119851,
								"CLTV": 0.7491,
								"HCLTV": 0.7491,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 76905.76,
								"Details": {
									"TotalNetNonFinancedItems": 76905.76,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 279405.76,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 76905.76,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 7267.26,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Origination Fee",
															"Code": "801",
															"Amount": 2000.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 29.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 3037.5,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 2199.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 582.6,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 582.6,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 202500.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 2500.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "Temporary buydown not allowed with LE and Initial Contact dates prior to 9/15/22"
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 1.5
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": false,
							"AreGoalsRecommended": {
								"Purchase": false
							},
							"UFMIPRate": 1.25,
							"UnroundedUFMIP": 2500.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -94199.4,
								"TenYearSavingsAmount": -188398.8,
								"FifteenYearSavingsAmount": -282598.2,
								"TwentyYearSavingsAmount": -376797.6,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1569.99
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 79338.06,
								"TotalReserveAssetAmount": 920661.94,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 4.0,
							"IsCashout": false,
							"AmountFinanced": 194349.95,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 1863.69,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1569.99,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1569.99,
											"Base": 1569.99,
											"MortgageInsurance": 0.0,
											"Rate": 7.0,
											"Duration": 240,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 18,
							"RecommendationPriorities": {
								"Purchase": 18
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7593.75,
								"Adjusted": 7593.75,
								"TotalTargetProfit": 7593.75,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 2000.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 2500.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 3037.5,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 582.6,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 2221.61,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2029.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 4251.56,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 209927.56,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 1863.69,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6868.26,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						},
						{
							"Id": "820V|VAConformingNonStreamline|Rate:6.99|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 7.526,
							"MaxAPR": 7.83,
							"Points": {
								"Base": 2.0,
								"Final": 1.5,
								"UndiscountedAllIn": -0.5,
								"Collected": 1.5,
								"Adjustments": [
									{
										"Value": 0.0,
										"Message": "Purchase >= $200K"
									},
									{
										"Value": -0.5,
										"Message": "Retail Purchase VA Credit"
									},
									{
										"Value": 0.0,
										"Message": "VA 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.99,
								"Adjusted": 6.99,
								"UndiscountedAllInRate": 7.625,
								"Adjustments": [
									{
										"Value": 6.99,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.99
							},
							"IsEligible": true,
							"DTI": 0.0466,
							"FrontEndDTI": 0.04656175,
							"MortgageInsurance": {
								"Duration": 0,
								"LTV": "0.01",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.7491,
								"LTV": 0.7490636704119851,
								"CLTV": 0.7491,
								"HCLTV": 0.7491,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 76904.86,
								"Details": {
									"TotalNetNonFinancedItems": 76904.86,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 279404.86,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 76904.86,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 7267.26,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Origination Fee",
															"Code": "801",
															"Amount": 2000.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 29.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 3037.5,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 2199.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 581.7,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 581.7,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 202500.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 2500.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "Temporary buydown not allowed with LE and Initial Contact dates prior to 9/15/22"
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 1.5
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 1.25,
							"UnroundedUFMIP": 2500.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -94126.2,
								"TenYearSavingsAmount": -188252.4,
								"FifteenYearSavingsAmount": -282378.6,
								"TwentyYearSavingsAmount": -376504.8,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1568.77
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 79337.16,
								"TotalReserveAssetAmount": 920662.84,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 4.0,
							"IsCashout": false,
							"AmountFinanced": 194350.85,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 1862.47,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1568.77,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1568.77,
											"Base": 1568.77,
											"MortgageInsurance": 0.0,
											"Rate": 6.99,
											"Duration": 240,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 17,
							"RecommendationPriorities": {
								"Purchase": 17
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7593.75,
								"Adjusted": 7593.75,
								"TotalTargetProfit": 7593.75,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 2000.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 2500.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 3037.5,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 581.7,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 2221.61,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2029.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 4251.56,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 209926.66,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 1862.47,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6867.36,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"VAFixed",
						"VAGeneral"
					],
					"UnderwritingSource": null,
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 5,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				},
				{
					"IsEligible": true,
					"IsRecommended": false,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": false
					},
					"RecommendationPriorities": {
						"Purchase": 2
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "825V",
					"ProductName": "VA 25 Year Fixed",
					"ProductDescription": "VA 25-Year Fixed",
					"TermMonths": 300,
					"TermDifference": -300,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "VAConformingNonStreamline",
					"ParentEligibilityGroups": [
						"Government",
						"Conforming",
						"VA",
						"VAConforming",
						"NonStreamline"
					],
					"ProductGroupType": "VA",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"DUEligible",
						"Government",
						"Jumbo_None",
						"MI VA Non-IRRRL",
						"QMUWType_VSH",
						"Retail",
						"Retail VA Full Doc",
						"UWSource_DU",
						"VA"
					],
					"UndiscountedBasePoints": 0.0,
					"UndiscountedBaseRate": 7.625,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "825V|VAConformingNonStreamline|Rate:6.75|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 7.203,
							"MaxAPR": 7.83,
							"Points": {
								"Base": 2.0,
								"Final": 1.5,
								"UndiscountedAllIn": -0.5,
								"Collected": 1.5,
								"Adjustments": [
									{
										"Value": 0.0,
										"Message": "Purchase >= $200K"
									},
									{
										"Value": -0.5,
										"Message": "Retail Purchase VA Credit"
									},
									{
										"Value": 0.0,
										"Message": "VA 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.75,
								"Adjusted": 6.75,
								"UndiscountedAllInRate": 7.625,
								"Adjustments": [
									{
										"Value": 6.75,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.75
							},
							"IsEligible": true,
							"DTI": 0.0423,
							"FrontEndDTI": 0.042319999999999997,
							"MortgageInsurance": {
								"Duration": 0,
								"LTV": "0.01",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.7491,
								"LTV": 0.7490636704119851,
								"CLTV": 0.7491,
								"HCLTV": 0.7491,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 76884.91,
								"Details": {
									"TotalNetNonFinancedItems": 76884.91,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 279384.91,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 76884.91,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 7267.26,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Origination Fee",
															"Code": "801",
															"Amount": 2000.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 29.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 3037.5,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 2199.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 561.75,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 561.75,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 202500.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 2500.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "Temporary buydown not allowed with LE and Initial Contact dates prior to 9/15/22"
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 1.5
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 1.25,
							"UnroundedUFMIP": 2500.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -83946.0,
								"TenYearSavingsAmount": -167892.0,
								"FifteenYearSavingsAmount": -251838.0,
								"TwentyYearSavingsAmount": -335784.0,
								"TwentyFiveYearSavingsAmount": -419730.0,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1399.1
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 79317.21,
								"TotalReserveAssetAmount": 920682.79,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 4.0,
							"IsCashout": false,
							"AmountFinanced": 194370.8,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 1692.8,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1399.1,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1399.1,
											"Base": 1399.1,
											"MortgageInsurance": 0.0,
											"Rate": 6.75,
											"Duration": 300,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 15,
							"RecommendationPriorities": {
								"Purchase": 15
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7593.75,
								"Adjusted": 7593.75,
								"TotalTargetProfit": 7593.75,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 2000.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 2500.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 3037.5,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 561.75,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 2221.61,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2029.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 4251.56,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 209906.71,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 1692.8,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6847.41,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"VAFixed",
						"VAGeneral"
					],
					"UnderwritingSource": null,
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 2,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				},
				{
					"IsEligible": true,
					"IsRecommended": true,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": true
					},
					"RecommendationPriorities": {
						"Purchase": 1
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "830V",
					"ProductName": "VA 30 Year Fixed",
					"ProductDescription": "VA 30-Year Fixed",
					"TermMonths": 360,
					"TermDifference": -360,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "VAConformingNonStreamline",
					"ParentEligibilityGroups": [
						"Government",
						"Conforming",
						"VA",
						"VAConforming",
						"NonStreamline"
					],
					"ProductGroupType": "VA",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"DUEligible",
						"Government",
						"Jumbo_None",
						"MI VA Non-IRRRL",
						"OneRightPrice",
						"QMUWType_VSH",
						"Retail",
						"Retail VA Full Doc",
						"RumpelLoan",
						"UWSource_DU",
						"VA"
					],
					"UndiscountedBasePoints": 0.0,
					"UndiscountedBaseRate": 7.875,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "830V|VAConformingNonStreamline|Rate:6.75|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 7.155,
							"MaxAPR": 8.09,
							"Points": {
								"Base": 2.0,
								"Final": 1.5,
								"UndiscountedAllIn": -0.5,
								"Collected": 1.5,
								"Adjustments": [
									{
										"Value": 0.0,
										"Message": "Purchase >= $200K"
									},
									{
										"Value": -0.5,
										"Message": "Retail Purchase VA Credit"
									},
									{
										"Value": 0.0,
										"Message": "VA 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.75,
								"Adjusted": 6.75,
								"UndiscountedAllInRate": 7.875,
								"Adjustments": [
									{
										"Value": 6.75,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.75
							},
							"IsEligible": true,
							"DTI": 0.0402,
							"FrontEndDTI": 0.040178000000000005,
							"MortgageInsurance": {
								"Duration": 0,
								"LTV": "0.01",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.7491,
								"LTV": 0.7490636704119851,
								"CLTV": 0.7491,
								"HCLTV": 0.7491,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 76884.91,
								"Details": {
									"TotalNetNonFinancedItems": 76884.91,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 279384.91,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 76884.91,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 7267.26,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Origination Fee",
															"Code": "801",
															"Amount": 2000.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 29.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 3037.5,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 2199.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 561.75,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 561.75,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 202500.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 2500.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "Temporary buydown not allowed with LE and Initial Contact dates prior to 9/15/22"
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 1.5
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 1.25,
							"UnroundedUFMIP": 2500.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -78805.2,
								"TenYearSavingsAmount": -157610.4,
								"FifteenYearSavingsAmount": -236415.6,
								"TwentyYearSavingsAmount": -315220.8,
								"TwentyFiveYearSavingsAmount": -394026.0,
								"ThirtyYearSavingsAmount": -472831.2,
								"LoanPaymentDifferenceAmount": -1313.42
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 79317.21,
								"TotalReserveAssetAmount": 920682.79,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 4.0,
							"IsCashout": false,
							"AmountFinanced": 194370.8,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 1607.12,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1313.42,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1313.42,
											"Base": 1313.42,
											"MortgageInsurance": 0.0,
											"Rate": 6.75,
											"Duration": 360,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 16,
							"RecommendationPriorities": {
								"Purchase": 16
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7593.75,
								"Adjusted": 7593.75,
								"TotalTargetProfit": 7593.75,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 2000.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 2500.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 3037.5,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 561.75,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 2221.61,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2029.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 4251.56,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 209906.71,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 1607.1200000000001,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6847.41,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"VAFixed",
						"VAGeneral"
					],
					"UnderwritingSource": null,
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 1,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				}
			],
			"QualifyingFailureReasons": [],
			"RecommendedCashoutAmount": null,
			"LoanPurpose": "Purchase",
			"IsAllCashout": false,
			"AppliedAssetsRequired": null,
			"ReserveAssetsRequired": null,
			"DownPaymentPercentage": null,
			"EligibilityTargets": {
				"APR": null,
				"Debt": null,
				"Income": null,
				"BackendDTI": null,
				"Assets": null,
				"ReserveAssets": null,
				"LTV": null,
				"SubordinatedLienBalance": null,
				"CreditScore": null,
				"Tradelines": null,
				"ClosingDate": null,
				"ApplicationDate": null,
				"CLTV": null,
				"HCLTV": null,
				"HelocInitialDraw": null,
				"CommitmentPeriod": null,
				"FinancedProperties": null,
				"PartnerAssets": null,
				"LoanAmount": null,
				"MonthsAtCurrentJob": null,
				"ResidualIncome": null,
				"FrontendDTI": null,
				"Foreclosure": null,
				"ShortSale": null,
				"MortgageLates": null,
				"Bankruptcy": null,
				"MortgageHistory": null,
				"CollectionBalance": null
			}
		},
		{
			"AmortizationType": "ARM",
			"AgencyType": "VA",
			"Products": [],
			"QualifyingFailureReasons": [],
			"RecommendedCashoutAmount": null,
			"LoanPurpose": "Purchase",
			"IsAllCashout": false,
			"AppliedAssetsRequired": null,
			"ReserveAssetsRequired": null,
			"DownPaymentPercentage": null,
			"EligibilityTargets": null
		},
		{
			"AmortizationType": "Fixed",
			"AgencyType": "FannieMae",
			"Products": [
				{
					"IsEligible": true,
					"IsRecommended": true,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": true
					},
					"RecommendationPriorities": {
						"Purchase": 6
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "330",
					"ProductName": "Conforming 15 Year Fixed",
					"ProductDescription": "15-Year Fixed",
					"TermMonths": 180,
					"TermDifference": -180,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "FannieMaeConformingNonHighBalance",
					"ParentEligibilityGroups": [
						"Conventional",
						"Agency",
						"Conforming",
						"FannieMae",
						"FannieMaeConforming",
						"NonHighBalance",
						"Standard"
					],
					"ProductGroupType": "FannieMae",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"ConventionalAgency",
						"DUEligible",
						"Jumbo_None",
						"LPEligible",
						"MI Conventional",
						"OneRightPrice",
						"QMUWType_VSH",
						"Retail",
						"Retail Agency Fixed",
						"Retail Agency Fixed Non-HB",
						"RumpelLoan",
						"UWSource_DU"
					],
					"UndiscountedBasePoints": 0.125,
					"UndiscountedBaseRate": 7.5,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "330|FannieMaeConformingNonHighBalance|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 6.925,
							"MaxAPR": 7.56,
							"Points": {
								"Base": 2.0,
								"Final": 2.125,
								"UndiscountedAllIn": 0.25,
								"Collected": 2.125,
								"Adjustments": [
									{
										"Value": 0.125,
										"Message": "Agency 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "Retail Purchase Agency Credit"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 7.5,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0506,
							"FrontEndDTI": 0.05055525,
							"MortgageInsurance": {
								"Duration": 10,
								"LTV": "0.80",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.75,
								"LTV": 0.7490636704119851,
								"CLTV": 0.75,
								"HCLTV": 0.75,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 78146.96,
								"Details": {
									"TotalNetNonFinancedItems": 78146.96,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 278146.96,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 78146.96,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 8559.76,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Processing Fee",
															"Code": "810",
															"Amount": 1125.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Underwriting Fee",
															"Code": "809",
															"Amount": 375.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 4250.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1919.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 531.3,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 531.3,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 200000.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "Temporary buydown not allowed with LE and Initial Contact dates prior to 9/15/22"
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 2.125
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 0.0,
							"UnroundedUFMIP": 0.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -103710.6,
								"TenYearSavingsAmount": -207421.2,
								"FifteenYearSavingsAmount": -311131.8,
								"TwentyYearSavingsAmount": null,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1728.51
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 81791.76,
								"TotalReserveAssetAmount": 918208.24,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 9.0,
							"IsCashout": false,
							"AmountFinanced": 193203.75,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 2022.21,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1728.51,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1728.51,
											"Base": 1728.51,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 180,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 1,
							"RecommendationPriorities": {
								"Purchase": 1
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7500.0,
								"Adjusted": 7500.0,
								"TotalTargetProfit": 7500.0,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 0.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 4250.0,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 531.3000000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1941.61,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2014.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 3956.56,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 210793.75999999998,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 2022.21,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6521.96,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"ConventionalFixed"
					],
					"UnderwritingSource": "DU",
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 6,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				},
				{
					"IsEligible": true,
					"IsRecommended": false,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": false
					},
					"RecommendationPriorities": {
						"Purchase": 15
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "Y11",
					"ProductName": "Conforming 11 Year Fixed",
					"ProductDescription": "11-Year Fixed",
					"TermMonths": 132,
					"TermDifference": -132,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "FannieMaeConformingNonHighBalance",
					"ParentEligibilityGroups": [
						"Conventional",
						"Agency",
						"Conforming",
						"FannieMae",
						"FannieMaeConforming",
						"NonHighBalance",
						"Standard"
					],
					"ProductGroupType": "FannieMae",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"ConventionalAgency",
						"DUEligible",
						"Jumbo_None",
						"LPEligible",
						"MI Conventional",
						"QMUWType_VSH",
						"Retail",
						"Retail Agency Fixed",
						"Retail Yourgages Non-HB",
						"RumpelLoan",
						"UWSource_DU",
						"YOURgage"
					],
					"UndiscountedBasePoints": 0.5,
					"UndiscountedBaseRate": 7.25,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "Y11|FannieMaeConformingNonHighBalance|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 7.089,
							"MaxAPR": 7.29,
							"Points": {
								"Base": 2.0,
								"Final": 2.125,
								"UndiscountedAllIn": 0.625,
								"Collected": 2.125,
								"Adjustments": [
									{
										"Value": 0.125,
										"Message": "Agency 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "Retail Purchase Agency Credit"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 7.25,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0601,
							"FrontEndDTI": 0.06013874999999999,
							"MortgageInsurance": {
								"Duration": 10,
								"LTV": "0.80",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.75,
								"LTV": 0.7490636704119851,
								"CLTV": 0.75,
								"HCLTV": 0.75,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 78146.96,
								"Details": {
									"TotalNetNonFinancedItems": 78146.96,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 278146.96,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 78146.96,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 8559.76,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Processing Fee",
															"Code": "810",
															"Amount": 1125.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Underwriting Fee",
															"Code": "809",
															"Amount": 375.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 4250.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1919.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 531.3,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 531.3,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 200000.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "\"Temporary buydown not allowed on this product\""
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 2.125
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 0.0,
							"UnroundedUFMIP": 0.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -126711.0,
								"TenYearSavingsAmount": -253422.0,
								"FifteenYearSavingsAmount": null,
								"TwentyYearSavingsAmount": null,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -2111.85
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 81791.76,
								"TotalReserveAssetAmount": 918208.24,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 9.0,
							"IsCashout": false,
							"AmountFinanced": 193203.75,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 2405.55,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 2111.85,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 2111.85,
											"Base": 2111.85,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 132,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 2,
							"RecommendationPriorities": {
								"Purchase": 2
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7500.0,
								"Adjusted": 7500.0,
								"TotalTargetProfit": 7500.0,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 0.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 4250.0,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 531.3000000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1941.61,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2014.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 3956.56,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 210793.75999999998,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 2405.5499999999997,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6521.96,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"ConventionalFixed"
					],
					"UnderwritingSource": "DU",
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 15,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				},
				{
					"IsEligible": true,
					"IsRecommended": false,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": false
					},
					"RecommendationPriorities": {
						"Purchase": 13
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "Y12",
					"ProductName": "Conforming 12 Year Fixed",
					"ProductDescription": "12-Year Fixed",
					"TermMonths": 144,
					"TermDifference": -144,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "FannieMaeConformingNonHighBalance",
					"ParentEligibilityGroups": [
						"Conventional",
						"Agency",
						"Conforming",
						"FannieMae",
						"FannieMaeConforming",
						"NonHighBalance",
						"Standard"
					],
					"ProductGroupType": "FannieMae",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"ConventionalAgency",
						"DUEligible",
						"Jumbo_None",
						"LPEligible",
						"MI Conventional",
						"QMUWType_VSH",
						"Retail",
						"Retail Agency Fixed",
						"Retail Yourgages Non-HB",
						"RumpelLoan",
						"UWSource_DU",
						"YOURgage"
					],
					"UndiscountedBasePoints": 0.5,
					"UndiscountedBaseRate": 7.25,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "Y12|FannieMaeConformingNonHighBalance|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 7.038,
							"MaxAPR": 7.29,
							"Points": {
								"Base": 2.0,
								"Final": 2.125,
								"UndiscountedAllIn": 0.625,
								"Collected": 2.125,
								"Adjustments": [
									{
										"Value": 0.125,
										"Message": "Agency 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "Retail Purchase Agency Credit"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 7.25,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0571,
							"FrontEndDTI": 0.057111,
							"MortgageInsurance": {
								"Duration": 10,
								"LTV": "0.80",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.75,
								"LTV": 0.7490636704119851,
								"CLTV": 0.75,
								"HCLTV": 0.75,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 78146.96,
								"Details": {
									"TotalNetNonFinancedItems": 78146.96,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 278146.96,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 78146.96,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 8559.76,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Processing Fee",
															"Code": "810",
															"Amount": 1125.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Underwriting Fee",
															"Code": "809",
															"Amount": 375.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 4250.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1919.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 531.3,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 531.3,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 200000.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "\"Temporary buydown not allowed on this product\""
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 2.125
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 0.0,
							"UnroundedUFMIP": 0.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -119444.4,
								"TenYearSavingsAmount": -238888.8,
								"FifteenYearSavingsAmount": null,
								"TwentyYearSavingsAmount": null,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1990.74
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 81791.76,
								"TotalReserveAssetAmount": 918208.24,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 9.0,
							"IsCashout": false,
							"AmountFinanced": 193203.75,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 2284.44,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1990.74,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1990.74,
											"Base": 1990.74,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 144,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 3,
							"RecommendationPriorities": {
								"Purchase": 3
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7500.0,
								"Adjusted": 7500.0,
								"TotalTargetProfit": 7500.0,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 0.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 4250.0,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 531.3000000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1941.61,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2014.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 3956.56,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 210793.75999999998,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 2284.44,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6521.96,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"ConventionalFixed"
					],
					"UnderwritingSource": "DU",
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 13,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				},
				{
					"IsEligible": true,
					"IsRecommended": false,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": false
					},
					"RecommendationPriorities": {
						"Purchase": 11
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "Y13",
					"ProductName": "Conforming 13 Year Fixed",
					"ProductDescription": "13-Year Fixed",
					"TermMonths": 156,
					"TermDifference": -156,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "FannieMaeConformingNonHighBalance",
					"ParentEligibilityGroups": [
						"Conventional",
						"Agency",
						"Conforming",
						"FannieMae",
						"FannieMaeConforming",
						"NonHighBalance",
						"Standard"
					],
					"ProductGroupType": "FannieMae",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"ConventionalAgency",
						"DUEligible",
						"Jumbo_None",
						"LPEligible",
						"MI Conventional",
						"QMUWType_VSH",
						"Retail",
						"Retail Agency Fixed",
						"Retail Yourgages Non-HB",
						"RumpelLoan",
						"UWSource_DU",
						"YOURgage"
					],
					"UndiscountedBasePoints": 0.125,
					"UndiscountedBaseRate": 7.5,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "Y13|FannieMaeConformingNonHighBalance|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 6.994,
							"MaxAPR": 7.56,
							"Points": {
								"Base": 2.0,
								"Final": 2.125,
								"UndiscountedAllIn": 0.25,
								"Collected": 2.125,
								"Adjustments": [
									{
										"Value": 0.125,
										"Message": "Agency 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "Retail Purchase Agency Credit"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 7.5,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0546,
							"FrontEndDTI": 0.05456925,
							"MortgageInsurance": {
								"Duration": 10,
								"LTV": "0.80",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.75,
								"LTV": 0.7490636704119851,
								"CLTV": 0.75,
								"HCLTV": 0.75,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 78146.96,
								"Details": {
									"TotalNetNonFinancedItems": 78146.96,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 278146.96,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 78146.96,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 8559.76,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Processing Fee",
															"Code": "810",
															"Amount": 1125.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Underwriting Fee",
															"Code": "809",
															"Amount": 375.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 4250.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1919.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 531.3,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 531.3,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 200000.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "\"Temporary buydown not allowed on this product\""
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 2.125
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 0.0,
							"UnroundedUFMIP": 0.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -113344.2,
								"TenYearSavingsAmount": -226688.4,
								"FifteenYearSavingsAmount": null,
								"TwentyYearSavingsAmount": null,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1889.07
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 81791.76,
								"TotalReserveAssetAmount": 918208.24,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 9.0,
							"IsCashout": false,
							"AmountFinanced": 193203.75,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 2182.77,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1889.07,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1889.07,
											"Base": 1889.07,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 156,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 4,
							"RecommendationPriorities": {
								"Purchase": 4
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7500.0,
								"Adjusted": 7500.0,
								"TotalTargetProfit": 7500.0,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 0.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 4250.0,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 531.3000000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1941.61,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2014.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 3956.56,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 210793.75999999998,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 2182.77,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6521.96,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"ConventionalFixed"
					],
					"UnderwritingSource": "DU",
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 11,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				},
				{
					"IsEligible": true,
					"IsRecommended": false,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": false
					},
					"RecommendationPriorities": {
						"Purchase": 9
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "Y14",
					"ProductName": "Conforming 14 Year Fixed",
					"ProductDescription": "14-Year Fixed",
					"TermMonths": 168,
					"TermDifference": -168,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "FannieMaeConformingNonHighBalance",
					"ParentEligibilityGroups": [
						"Conventional",
						"Agency",
						"Conforming",
						"FannieMae",
						"FannieMaeConforming",
						"NonHighBalance",
						"Standard"
					],
					"ProductGroupType": "FannieMae",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"ConventionalAgency",
						"DUEligible",
						"Jumbo_None",
						"LPEligible",
						"MI Conventional",
						"QMUWType_VSH",
						"Retail",
						"Retail Agency Fixed",
						"Retail Yourgages Non-HB",
						"RumpelLoan",
						"UWSource_DU",
						"YOURgage"
					],
					"UndiscountedBasePoints": 0.125,
					"UndiscountedBaseRate": 7.5,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "Y14|FannieMaeConformingNonHighBalance|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 6.957,
							"MaxAPR": 7.56,
							"Points": {
								"Base": 2.0,
								"Final": 2.125,
								"UndiscountedAllIn": 0.25,
								"Collected": 2.125,
								"Adjustments": [
									{
										"Value": 0.125,
										"Message": "Agency 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "Retail Purchase Agency Credit"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 7.5,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0524,
							"FrontEndDTI": 0.052409500000000005,
							"MortgageInsurance": {
								"Duration": 10,
								"LTV": "0.80",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.75,
								"LTV": 0.7490636704119851,
								"CLTV": 0.75,
								"HCLTV": 0.75,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 78146.96,
								"Details": {
									"TotalNetNonFinancedItems": 78146.96,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 278146.96,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 78146.96,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 8559.76,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Processing Fee",
															"Code": "810",
															"Amount": 1125.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Underwriting Fee",
															"Code": "809",
															"Amount": 375.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 4250.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1919.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 531.3,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 531.3,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 200000.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "\"Temporary buydown not allowed on this product\""
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 2.125
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 0.0,
							"UnroundedUFMIP": 0.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -108160.8,
								"TenYearSavingsAmount": -216321.6,
								"FifteenYearSavingsAmount": null,
								"TwentyYearSavingsAmount": null,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1802.68
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 81791.76,
								"TotalReserveAssetAmount": 918208.24,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 9.0,
							"IsCashout": false,
							"AmountFinanced": 193203.75,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 2096.38,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1802.68,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1802.68,
											"Base": 1802.68,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 168,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 5,
							"RecommendationPriorities": {
								"Purchase": 5
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7500.0,
								"Adjusted": 7500.0,
								"TotalTargetProfit": 7500.0,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 0.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 4250.0,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 531.3000000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1941.61,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2014.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 3956.56,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 210793.75999999998,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 2096.38,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6521.96,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"ConventionalFixed"
					],
					"UnderwritingSource": "DU",
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 9,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				}
			],
			"QualifyingFailureReasons": [],
			"RecommendedCashoutAmount": null,
			"LoanPurpose": "Purchase",
			"IsAllCashout": false,
			"AppliedAssetsRequired": null,
			"ReserveAssetsRequired": null,
			"DownPaymentPercentage": null,
			"EligibilityTargets": {
				"APR": null,
				"Debt": null,
				"Income": null,
				"BackendDTI": null,
				"Assets": null,
				"ReserveAssets": null,
				"LTV": null,
				"SubordinatedLienBalance": null,
				"CreditScore": null,
				"Tradelines": null,
				"ClosingDate": null,
				"ApplicationDate": null,
				"CLTV": null,
				"HCLTV": null,
				"HelocInitialDraw": null,
				"CommitmentPeriod": null,
				"FinancedProperties": null,
				"PartnerAssets": null,
				"LoanAmount": null,
				"MonthsAtCurrentJob": null,
				"ResidualIncome": null,
				"FrontendDTI": null,
				"Foreclosure": null,
				"ShortSale": null,
				"MortgageLates": null,
				"Bankruptcy": null,
				"MortgageHistory": null,
				"CollectionBalance": null
			}
		},
		{
			"AmortizationType": "ARM",
			"AgencyType": "FannieMae",
			"Products": [
				{
					"IsEligible": true,
					"IsRecommended": true,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": true
					},
					"RecommendationPriorities": {
						"Purchase": 3
					},
					"ArmInfo": {
						"InitialAdjustmentPeriod": 84,
						"LifetimeAdjustmentCap": 5.0,
						"SubsequentAdjustmentCap": 1.0,
						"InitialAdjustmentCap": 5.0,
						"ARMIndex": 4.854,
						"IndexName": "30 Day Average of SOFR Index",
						"SubsequentAdjustmentPeriod": 6.0,
						"LifetimeCeiling": 0.0
					},
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "276",
					"ProductName": "7/6 ARM Conforming",
					"ProductDescription": "7/6 Arm Conforming",
					"TermMonths": 360,
					"TermDifference": -360,
					"AmortizationType": "ARM",
					"EligibilityGroupType": "FannieMaeConformingNonHighBalance",
					"ParentEligibilityGroups": [
						"Conventional",
						"Agency",
						"Conforming",
						"FannieMae",
						"FannieMaeConforming",
						"NonHighBalance",
						"Standard"
					],
					"ProductGroupType": "FannieMae",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"ConventionalAgency",
						"DUEligible",
						"Jumbo_None",
						"MI Conventional",
						"QMUWType_VSH",
						"Retail",
						"Retail Agency ARMs",
						"UWSource_DU",
						"allproducts"
					],
					"UndiscountedBasePoints": 1.125,
					"UndiscountedBaseRate": 6.99,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "276|FannieMaeConformingNonHighBalance|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 7.471,
							"MaxAPR": 8.44,
							"Points": {
								"Base": 2.0,
								"Final": 2.625,
								"UndiscountedAllIn": 1.75,
								"Collected": 2.625,
								"Adjustments": [
									{
										"Value": 0.375,
										"Message": "FN Pur L 70.01-75 FICO 740-759"
									},
									{
										"Value": 0.0,
										"Message": "FN Pur ARM LTV 70.01-75"
									},
									{
										"Value": 0.0,
										"Message": "Retail Purchase Agency Credit"
									},
									{
										"Value": 0.25,
										"Message": "Agency 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 6.99,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0385,
							"FrontEndDTI": 0.038536,
							"MortgageInsurance": {
								"Duration": 10,
								"LTV": "0.80",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.75,
								"LTV": 0.7490636704119851,
								"CLTV": 0.75,
								"HCLTV": 0.75,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 79146.96,
								"Details": {
									"TotalNetNonFinancedItems": 79146.96,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 279146.96,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 79146.96,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 9559.76,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Processing Fee",
															"Code": "810",
															"Amount": 1125.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Underwriting Fee",
															"Code": "809",
															"Amount": 375.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 5250.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1919.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 531.3,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 531.3,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 200000.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "Temporary buydown not allowed with LE and Initial Contact dates prior to 9/15/22"
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 2.625
							},
							"ArmInfo": {
								"PotentialInterestRateAfterLock": 7.875,
								"PotentialPIMIPaymentAfterLock": 1417.24,
								"PotentialMonthlyPaymentAfterLock": 1710.94,
								"FullyIndexedRate": 7.875,
								"MaximumInterestRate": 0.0,
								"Margin": {
									"Base": 3.0,
									"Adjusted": 3.0,
									"Adjustments": [
										{
											"Value": 3.0,
											"Message": "Base Margin"
										}
									]
								}
							},
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 0.0,
							"UnroundedUFMIP": 0.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -74864.4,
								"TenYearSavingsAmount": -149728.8,
								"FifteenYearSavingsAmount": -224593.2,
								"TwentyYearSavingsAmount": -299457.6,
								"TwentyFiveYearSavingsAmount": -374322.0,
								"ThirtyYearSavingsAmount": -449186.4,
								"LoanPaymentDifferenceAmount": -1247.74
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 83791.76,
								"TotalReserveAssetAmount": 916208.24,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 9.0,
							"IsCashout": false,
							"AmountFinanced": 192203.75,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 1541.44,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1247.74,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1247.74,
											"Base": 1247.74,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 84,
											"Period": 1
										},
										{
											"Total": 1417.24,
											"Base": 1417.24,
											"MortgageInsurance": 0.0,
											"Rate": 7.875,
											"Duration": 276,
											"Period": 85
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 6,
							"RecommendationPriorities": {
								"Purchase": 6
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 5750.0,
								"Adjusted": 5750.0,
								"TotalTargetProfit": 5750.0,
								"TargetProfitPercent": 2.875,
								"AdjustedPercent": 2.875,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 2.875
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 0.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 5250.0,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 531.3000000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1941.61,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2014.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 3956.56,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 211793.75999999998,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 1541.44,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6521.96,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"ConventionalARM"
					],
					"UnderwritingSource": "DU",
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 3,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				}
			],
			"QualifyingFailureReasons": [],
			"RecommendedCashoutAmount": null,
			"LoanPurpose": "Purchase",
			"IsAllCashout": false,
			"AppliedAssetsRequired": null,
			"ReserveAssetsRequired": null,
			"DownPaymentPercentage": null,
			"EligibilityTargets": {
				"APR": null,
				"Debt": null,
				"Income": null,
				"BackendDTI": null,
				"Assets": null,
				"ReserveAssets": null,
				"LTV": null,
				"SubordinatedLienBalance": null,
				"CreditScore": null,
				"Tradelines": null,
				"ClosingDate": null,
				"ApplicationDate": null,
				"CLTV": null,
				"HCLTV": null,
				"HelocInitialDraw": null,
				"CommitmentPeriod": null,
				"FinancedProperties": null,
				"PartnerAssets": null,
				"LoanAmount": null,
				"MonthsAtCurrentJob": null,
				"ResidualIncome": null,
				"FrontendDTI": null,
				"Foreclosure": null,
				"ShortSale": null,
				"MortgageLates": null,
				"Bankruptcy": null,
				"MortgageHistory": null,
				"CollectionBalance": null
			}
		},
		{
			"AmortizationType": "Fixed",
			"AgencyType": "FreddieMac",
			"Products": [
				{
					"IsEligible": true,
					"IsRecommended": true,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": true
					},
					"RecommendationPriorities": {
						"Purchase": 7
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "330",
					"ProductName": "Conforming 15 Year Fixed",
					"ProductDescription": "15-Year Fixed",
					"TermMonths": 180,
					"TermDifference": -180,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "FreddieMacNonHighBalance",
					"ParentEligibilityGroups": [
						"Conventional",
						"Agency",
						"Conforming",
						"FreddieMac",
						"FreddieMacConforming",
						"NonHighBalance",
						"Standard"
					],
					"ProductGroupType": "FreddieMac",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"ConventionalAgency",
						"DUEligible",
						"Jumbo_None",
						"LPEligible",
						"MI Conventional",
						"OneRightPrice",
						"QMUWType_VSH",
						"Retail",
						"Retail Agency Fixed",
						"Retail Agency Fixed Non-HB",
						"RumpelLoan",
						"UWSource_DU"
					],
					"UndiscountedBasePoints": 0.125,
					"UndiscountedBaseRate": 7.5,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "330|FreddieMacNonHighBalance|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 6.925,
							"MaxAPR": 7.56,
							"Points": {
								"Base": 2.0,
								"Final": 2.125,
								"UndiscountedAllIn": 0.25,
								"Collected": 2.125,
								"Adjustments": [
									{
										"Value": 0.125,
										"Message": "Agency 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "Retail Purchase Agency Credit"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 7.5,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0506,
							"FrontEndDTI": 0.05055525,
							"MortgageInsurance": {
								"Duration": 10,
								"LTV": "0.80",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.749,
								"LTV": 0.7490636704119851,
								"CLTV": 0.749,
								"HCLTV": 0.749,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 78146.96,
								"Details": {
									"TotalNetNonFinancedItems": 78146.96,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 278146.96,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 78146.96,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 8559.76,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Processing Fee",
															"Code": "810",
															"Amount": 1125.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Underwriting Fee",
															"Code": "809",
															"Amount": 375.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 4250.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1919.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 531.3,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 531.3,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 200000.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "Temporary buydown not allowed with LE and Initial Contact dates prior to 9/15/22"
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 2.125
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 0.0,
							"UnroundedUFMIP": 0.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -103710.6,
								"TenYearSavingsAmount": -207421.2,
								"FifteenYearSavingsAmount": -311131.8,
								"TwentyYearSavingsAmount": null,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1728.51
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 81791.76,
								"TotalReserveAssetAmount": 918208.24,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 9.0,
							"IsCashout": false,
							"AmountFinanced": 193203.75,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 2022.21,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1728.51,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1728.51,
											"Base": 1728.51,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 180,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 7,
							"RecommendationPriorities": {
								"Purchase": 7
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7500.0,
								"Adjusted": 7500.0,
								"TotalTargetProfit": 7500.0,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 0.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 4250.0,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 531.3000000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1919.81,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2014.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 3934.76,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 210771.96,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 2022.21,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6521.96,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"ConventionalFixed"
					],
					"UnderwritingSource": "LP",
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 7,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				},
				{
					"IsEligible": true,
					"IsRecommended": false,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": false
					},
					"RecommendationPriorities": {
						"Purchase": 16
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "Y11",
					"ProductName": "Conforming 11 Year Fixed",
					"ProductDescription": "11-Year Fixed",
					"TermMonths": 132,
					"TermDifference": -132,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "FreddieMacNonHighBalance",
					"ParentEligibilityGroups": [
						"Conventional",
						"Agency",
						"Conforming",
						"FreddieMac",
						"FreddieMacConforming",
						"NonHighBalance",
						"Standard"
					],
					"ProductGroupType": "FreddieMac",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"ConventionalAgency",
						"DUEligible",
						"Jumbo_None",
						"LPEligible",
						"MI Conventional",
						"QMUWType_VSH",
						"Retail",
						"Retail Agency Fixed",
						"Retail Yourgages Non-HB",
						"RumpelLoan",
						"UWSource_DU",
						"YOURgage"
					],
					"UndiscountedBasePoints": 0.5,
					"UndiscountedBaseRate": 7.25,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "Y11|FreddieMacNonHighBalance|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 7.089,
							"MaxAPR": 7.29,
							"Points": {
								"Base": 2.0,
								"Final": 2.125,
								"UndiscountedAllIn": 0.625,
								"Collected": 2.125,
								"Adjustments": [
									{
										"Value": 0.125,
										"Message": "Agency 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "Retail Purchase Agency Credit"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 7.25,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0601,
							"FrontEndDTI": 0.06013874999999999,
							"MortgageInsurance": {
								"Duration": 10,
								"LTV": "0.80",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.749,
								"LTV": 0.7490636704119851,
								"CLTV": 0.749,
								"HCLTV": 0.749,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 78146.96,
								"Details": {
									"TotalNetNonFinancedItems": 78146.96,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 278146.96,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 78146.96,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 8559.76,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Processing Fee",
															"Code": "810",
															"Amount": 1125.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Underwriting Fee",
															"Code": "809",
															"Amount": 375.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 4250.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1919.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 531.3,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 531.3,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 200000.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "\"Temporary buydown not allowed on this product\""
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 2.125
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 0.0,
							"UnroundedUFMIP": 0.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -126711.0,
								"TenYearSavingsAmount": -253422.0,
								"FifteenYearSavingsAmount": null,
								"TwentyYearSavingsAmount": null,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -2111.85
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 81791.76,
								"TotalReserveAssetAmount": 918208.24,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 9.0,
							"IsCashout": false,
							"AmountFinanced": 193203.75,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 2405.55,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 2111.85,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 2111.85,
											"Base": 2111.85,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 132,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 8,
							"RecommendationPriorities": {
								"Purchase": 8
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7500.0,
								"Adjusted": 7500.0,
								"TotalTargetProfit": 7500.0,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 0.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 4250.0,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 531.3000000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1919.81,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2014.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 3934.76,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 210771.96,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 2405.5499999999997,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6521.96,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"ConventionalFixed"
					],
					"UnderwritingSource": "LP",
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 16,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				},
				{
					"IsEligible": true,
					"IsRecommended": false,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": false
					},
					"RecommendationPriorities": {
						"Purchase": 14
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "Y12",
					"ProductName": "Conforming 12 Year Fixed",
					"ProductDescription": "12-Year Fixed",
					"TermMonths": 144,
					"TermDifference": -144,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "FreddieMacNonHighBalance",
					"ParentEligibilityGroups": [
						"Conventional",
						"Agency",
						"Conforming",
						"FreddieMac",
						"FreddieMacConforming",
						"NonHighBalance",
						"Standard"
					],
					"ProductGroupType": "FreddieMac",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"ConventionalAgency",
						"DUEligible",
						"Jumbo_None",
						"LPEligible",
						"MI Conventional",
						"QMUWType_VSH",
						"Retail",
						"Retail Agency Fixed",
						"Retail Yourgages Non-HB",
						"RumpelLoan",
						"UWSource_DU",
						"YOURgage"
					],
					"UndiscountedBasePoints": 0.5,
					"UndiscountedBaseRate": 7.25,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "Y12|FreddieMacNonHighBalance|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 7.038,
							"MaxAPR": 7.29,
							"Points": {
								"Base": 2.0,
								"Final": 2.125,
								"UndiscountedAllIn": 0.625,
								"Collected": 2.125,
								"Adjustments": [
									{
										"Value": 0.125,
										"Message": "Agency 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "Retail Purchase Agency Credit"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 7.25,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0571,
							"FrontEndDTI": 0.057111,
							"MortgageInsurance": {
								"Duration": 10,
								"LTV": "0.80",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.749,
								"LTV": 0.7490636704119851,
								"CLTV": 0.749,
								"HCLTV": 0.749,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 78146.96,
								"Details": {
									"TotalNetNonFinancedItems": 78146.96,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 278146.96,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 78146.96,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 8559.76,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Processing Fee",
															"Code": "810",
															"Amount": 1125.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Underwriting Fee",
															"Code": "809",
															"Amount": 375.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 4250.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1919.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 531.3,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 531.3,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 200000.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "\"Temporary buydown not allowed on this product\""
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 2.125
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 0.0,
							"UnroundedUFMIP": 0.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -119444.4,
								"TenYearSavingsAmount": -238888.8,
								"FifteenYearSavingsAmount": null,
								"TwentyYearSavingsAmount": null,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1990.74
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 81791.76,
								"TotalReserveAssetAmount": 918208.24,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 9.0,
							"IsCashout": false,
							"AmountFinanced": 193203.75,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 2284.44,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1990.74,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1990.74,
											"Base": 1990.74,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 144,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 9,
							"RecommendationPriorities": {
								"Purchase": 9
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7500.0,
								"Adjusted": 7500.0,
								"TotalTargetProfit": 7500.0,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 0.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 4250.0,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 531.3000000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1919.81,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2014.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 3934.76,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 210771.96,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 2284.44,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6521.96,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"ConventionalFixed"
					],
					"UnderwritingSource": "LP",
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 14,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				},
				{
					"IsEligible": true,
					"IsRecommended": false,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": false
					},
					"RecommendationPriorities": {
						"Purchase": 12
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "Y13",
					"ProductName": "Conforming 13 Year Fixed",
					"ProductDescription": "13-Year Fixed",
					"TermMonths": 156,
					"TermDifference": -156,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "FreddieMacNonHighBalance",
					"ParentEligibilityGroups": [
						"Conventional",
						"Agency",
						"Conforming",
						"FreddieMac",
						"FreddieMacConforming",
						"NonHighBalance",
						"Standard"
					],
					"ProductGroupType": "FreddieMac",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"ConventionalAgency",
						"DUEligible",
						"Jumbo_None",
						"LPEligible",
						"MI Conventional",
						"QMUWType_VSH",
						"Retail",
						"Retail Agency Fixed",
						"Retail Yourgages Non-HB",
						"RumpelLoan",
						"UWSource_DU",
						"YOURgage"
					],
					"UndiscountedBasePoints": 0.125,
					"UndiscountedBaseRate": 7.5,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "Y13|FreddieMacNonHighBalance|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 6.994,
							"MaxAPR": 7.56,
							"Points": {
								"Base": 2.0,
								"Final": 2.125,
								"UndiscountedAllIn": 0.25,
								"Collected": 2.125,
								"Adjustments": [
									{
										"Value": 0.125,
										"Message": "Agency 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "Retail Purchase Agency Credit"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 7.5,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0546,
							"FrontEndDTI": 0.05456925,
							"MortgageInsurance": {
								"Duration": 10,
								"LTV": "0.80",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.749,
								"LTV": 0.7490636704119851,
								"CLTV": 0.749,
								"HCLTV": 0.749,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 78146.96,
								"Details": {
									"TotalNetNonFinancedItems": 78146.96,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 278146.96,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 78146.96,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 8559.76,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Processing Fee",
															"Code": "810",
															"Amount": 1125.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Underwriting Fee",
															"Code": "809",
															"Amount": 375.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 4250.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1919.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 531.3,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 531.3,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 200000.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "\"Temporary buydown not allowed on this product\""
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 2.125
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 0.0,
							"UnroundedUFMIP": 0.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -113344.2,
								"TenYearSavingsAmount": -226688.4,
								"FifteenYearSavingsAmount": null,
								"TwentyYearSavingsAmount": null,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1889.07
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 81791.76,
								"TotalReserveAssetAmount": 918208.24,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 9.0,
							"IsCashout": false,
							"AmountFinanced": 193203.75,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 2182.77,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1889.07,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1889.07,
											"Base": 1889.07,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 156,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 10,
							"RecommendationPriorities": {
								"Purchase": 10
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7500.0,
								"Adjusted": 7500.0,
								"TotalTargetProfit": 7500.0,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 0.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 4250.0,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 531.3000000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1919.81,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2014.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 3934.76,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 210771.96,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 2182.77,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6521.96,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"ConventionalFixed"
					],
					"UnderwritingSource": "LP",
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 12,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				},
				{
					"IsEligible": true,
					"IsRecommended": false,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": false
					},
					"RecommendationPriorities": {
						"Purchase": 10
					},
					"ArmInfo": null,
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "Y14",
					"ProductName": "Conforming 14 Year Fixed",
					"ProductDescription": "14-Year Fixed",
					"TermMonths": 168,
					"TermDifference": -168,
					"AmortizationType": "Fixed",
					"EligibilityGroupType": "FreddieMacNonHighBalance",
					"ParentEligibilityGroups": [
						"Conventional",
						"Agency",
						"Conforming",
						"FreddieMac",
						"FreddieMacConforming",
						"NonHighBalance",
						"Standard"
					],
					"ProductGroupType": "FreddieMac",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"AllProducts",
						"ConventionalAgency",
						"DUEligible",
						"Jumbo_None",
						"LPEligible",
						"MI Conventional",
						"QMUWType_VSH",
						"Retail",
						"Retail Agency Fixed",
						"Retail Yourgages Non-HB",
						"RumpelLoan",
						"UWSource_DU",
						"YOURgage"
					],
					"UndiscountedBasePoints": 0.125,
					"UndiscountedBaseRate": 7.5,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "Y14|FreddieMacNonHighBalance|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 6.957,
							"MaxAPR": 7.56,
							"Points": {
								"Base": 2.0,
								"Final": 2.125,
								"UndiscountedAllIn": 0.25,
								"Collected": 2.125,
								"Adjustments": [
									{
										"Value": 0.125,
										"Message": "Agency 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "Retail Purchase Agency Credit"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 7.5,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0524,
							"FrontEndDTI": 0.052409500000000005,
							"MortgageInsurance": {
								"Duration": 10,
								"LTV": "0.80",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.749,
								"LTV": 0.7490636704119851,
								"CLTV": 0.749,
								"HCLTV": 0.749,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 78146.96,
								"Details": {
									"TotalNetNonFinancedItems": 78146.96,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 278146.96,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 78146.96,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 8559.76,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Processing Fee",
															"Code": "810",
															"Amount": 1125.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Underwriting Fee",
															"Code": "809",
															"Amount": 375.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 4250.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1919.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 531.3,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 531.3,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 200000.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "\"Temporary buydown not allowed on this product\""
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 2.125
							},
							"ArmInfo": null,
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 0.0,
							"UnroundedUFMIP": 0.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -108160.8,
								"TenYearSavingsAmount": -216321.6,
								"FifteenYearSavingsAmount": null,
								"TwentyYearSavingsAmount": null,
								"TwentyFiveYearSavingsAmount": null,
								"ThirtyYearSavingsAmount": null,
								"LoanPaymentDifferenceAmount": -1802.68
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 81791.76,
								"TotalReserveAssetAmount": 918208.24,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 9.0,
							"IsCashout": false,
							"AmountFinanced": 193203.75,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 2096.38,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1802.68,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1802.68,
											"Base": 1802.68,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 168,
											"Period": 1
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 11,
							"RecommendationPriorities": {
								"Purchase": 11
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 7500.0,
								"Adjusted": 7500.0,
								"TotalTargetProfit": 7500.0,
								"TargetProfitPercent": 3.75,
								"AdjustedPercent": 3.75,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 3.75
							},
							"ShortageInformation": null,
							"QMIneligibilities": [],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 0.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 4250.0,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 531.3000000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1919.81,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2014.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 3934.76,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 210771.96,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 2096.38,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6521.96,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"ConventionalFixed"
					],
					"UnderwritingSource": "LP",
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 10,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				}
			],
			"QualifyingFailureReasons": [],
			"RecommendedCashoutAmount": null,
			"LoanPurpose": "Purchase",
			"IsAllCashout": false,
			"AppliedAssetsRequired": null,
			"ReserveAssetsRequired": null,
			"DownPaymentPercentage": null,
			"EligibilityTargets": {
				"APR": null,
				"Debt": null,
				"Income": null,
				"BackendDTI": null,
				"Assets": null,
				"ReserveAssets": null,
				"LTV": null,
				"SubordinatedLienBalance": null,
				"CreditScore": null,
				"Tradelines": null,
				"ClosingDate": null,
				"ApplicationDate": null,
				"CLTV": null,
				"HCLTV": null,
				"HelocInitialDraw": null,
				"CommitmentPeriod": null,
				"FinancedProperties": null,
				"PartnerAssets": null,
				"LoanAmount": null,
				"MonthsAtCurrentJob": null,
				"ResidualIncome": null,
				"FrontendDTI": null,
				"Foreclosure": null,
				"ShortSale": null,
				"MortgageLates": null,
				"Bankruptcy": null,
				"MortgageHistory": null,
				"CollectionBalance": null
			}
		},
		{
			"AmortizationType": "ARM",
			"AgencyType": "FreddieMac",
			"Products": [
				{
					"IsEligible": true,
					"IsRecommended": true,
					"IsCEMALoan": false,
					"AreGoalsRecommended": {
						"Purchase": true
					},
					"RecommendationPriorities": {
						"Purchase": 4
					},
					"ArmInfo": {
						"InitialAdjustmentPeriod": 84,
						"LifetimeAdjustmentCap": 5.0,
						"SubsequentAdjustmentCap": 1.0,
						"InitialAdjustmentCap": 5.0,
						"ARMIndex": 4.854,
						"IndexName": "30 Day Average of SOFR Index",
						"SubsequentAdjustmentPeriod": 6.0,
						"LifetimeCeiling": 0.0
					},
					"TaxEscrowAccountIncluded": true,
					"InsuranceEscrowAccountIncluded": true,
					"ProductCode": "276",
					"ProductName": "7/6 ARM Conforming",
					"ProductDescription": "7/6 Arm Conforming",
					"TermMonths": 360,
					"TermDifference": -360,
					"AmortizationType": "ARM",
					"EligibilityGroupType": "FreddieMacNonHighBalance",
					"ParentEligibilityGroups": [
						"Conventional",
						"Agency",
						"Conforming",
						"FreddieMac",
						"FreddieMacConforming",
						"NonHighBalance",
						"Standard"
					],
					"ProductGroupType": "FreddieMac",
					"DocumentationType": "full",
					"FinalMIRate": 0.0,
					"MIType": "BorrowerPaid",
					"Tags": [
						"ConventionalAgency",
						"DUEligible",
						"Jumbo_None",
						"MI Conventional",
						"QMUWType_VSH",
						"Retail",
						"Retail Agency ARMs",
						"UWSource_DU",
						"allproducts"
					],
					"UndiscountedBasePoints": 1.125,
					"UndiscountedBaseRate": 6.99,
					"QualifyingFailureReasons": [],
					"Ineligibilites": null,
					"PricingOptions": [
						{
							"Id": "276|FreddieMacNonHighBalance|Rate:6.375|Points:2|BorrowerPaid|DownPayment:67000",
							"InfluencerBrokerCompensation": 0.0,
							"APR": 7.497,
							"MaxAPR": 8.44,
							"Points": {
								"Base": 2.0,
								"Final": 2.875,
								"UndiscountedAllIn": 2.0,
								"Collected": 2.875,
								"Adjustments": [
									{
										"Value": 0.375,
										"Message": "FH Pur L 70.01-75 FICO 740-759"
									},
									{
										"Value": 0.0,
										"Message": "FH Pur ARM LTV 70.01-75"
									},
									{
										"Value": 0.0,
										"Message": "Retail Purchase Agency Credit"
									},
									{
										"Value": 0.25,
										"Message": "FH Underwriting Source is LP"
									},
									{
										"Value": 0.25,
										"Message": "Agency 200000-224999"
									},
									{
										"Value": 0.0,
										"Message": "45-Day Commitment"
									},
									{
										"Value": 2.0,
										"Message": "Base Price"
									}
								],
								"CollectedPointsAdjustmentPermissions": null
							},
							"Rate": {
								"Base": 6.375,
								"Adjusted": 6.375,
								"UndiscountedAllInRate": 6.99,
								"Adjustments": [
									{
										"Value": 6.375,
										"Message": "Base Price"
									}
								],
								"Qualifying": 6.375
							},
							"IsEligible": true,
							"DTI": 0.0385,
							"FrontEndDTI": 0.038536,
							"MortgageInsurance": {
								"Duration": 10,
								"LTV": "0.80",
								"Percent": 0.0
							},
							"LtvInformation": {
								"Rounded": 0.749,
								"LTV": 0.7490636704119851,
								"CLTV": 0.749,
								"HCLTV": 0.749,
								"CalculatedFromFinalLoanAmount": false
							},
							"Ineligibilities": [],
							"ReserveAmount": 0.0,
							"ReserveMonths": 0.0,
							"CashToClose": {
								"Total": 79646.96,
								"Details": {
									"TotalNetNonFinancedItems": 79646.96,
									"EarnestMoneyDeposit": -0.0,
									"SellerConcessions": -0.0,
									"TotalDebtPaidOffWithAssets": 0.0,
									"AdditionalItemsToBePaid": 0.0,
									"UfmipRefund": -0.0,
									"GiftOfEquity": -0.0,
									"LendersTitleInsuranceAdjustment": 0.0,
									"CreditCardRewardPoints": -0.0,
									"ApplicationDepositAmount": -0.0,
									"PurchaseExcessLoanAmountAdjustment": 0.0
								}
							},
							"FundsToClose": {
								"Total": 279646.96,
								"Details": {
									"NonFinancedAmounts": {
										"Total": 79646.96,
										"Details": {
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null,
											"DownPayment": 67000.0,
											"GrantAmount": -0.0,
											"CashoutAdjustment": 0.0,
											"NetFees": {
												"Total": 10059.76,
												"Details": {
													"APRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Processing Fee",
															"Code": "810",
															"Amount": 1125.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Underwriting Fee",
															"Code": "809",
															"Amount": 375.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached APR Fees",
															"Code": "",
															"Amount": 889.95,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														},
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Points",
															"Code": "802",
															"Amount": 5750.0,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													],
													"NonAPRFees": [
														{
															"LenderPaidCreditApplied": 0.0,
															"Name": "Cached Non APR Fees",
															"Code": "",
															"Amount": 1919.81,
															"FeeRollUpId": null,
															"GroupType": null,
															"ClosingGroupType": null,
															"ClientPaidAmount": null,
															"ClientPaidOutsideOfClosingAmount": 0.0,
															"SellerPaidAtClosingAmount": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													]
												}
											},
											"NetPerDiem": {
												"Total": 531.3,
												"Details": {
													"NumberOfOddDays": 15,
													"PerDiemInterest": 531.3,
													"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
													"SellerPaidOutsideOfClosingAmount": 0.0
												}
											},
											"NetPrepaidEscrows": {
												"Total": 2055.9,
												"Details": {
													"AmountDueAtClosing": 1174.8,
													"PrepaidTaxes": 1246.0,
													"PrepaidInsurance": 240.3,
													"LenderPaidCreditAppliedToEscrows": 0.0,
													"AggregateAdjustment": -605.2,
													"AmountDueAtClosingDetails": {
														"TaxAmountDueAtClosing": 213.6,
														"InsuranceAmountDueAtClosing": 961.2
													},
													"EscrowBreakdown": [],
													"ClientAmountPaidOutsideOfClose": 0.0,
													"SellerPaidOutsideOfClosing": null,
													"OtherPaidAtClosing": null
												}
											},
											"NetTaxProrations": {
												"Total": 0.0,
												"Details": {
													"BorrowerPaidTaxProrations": 0.0,
													"LenderPaidCreditAppliedToTaxProrations": 0.0,
													"SellerPaidTaxProrations": 0.0,
													"SellerResponsibleTaxProrations": 0.0,
													"ProrationsChargedToBuyer": 0.0,
													"ClientPaidTaxProrationAmount": 0.0,
													"TaxProrationBreakdown": []
												}
											}
										}
									},
									"FinalLoanAmount": {
										"Total": 200000.0,
										"Details": {
											"BaseLoanAmount": {
												"Total": 200000.0,
												"RefinanceDetails": null,
												"PurchaseDetails": {
													"PurchasePrice": 267000.0,
													"DownPayment": 67000.0,
													"SecondaryLiens": 0.0,
													"AdditionalItemsToBePaid": 0.0,
													"DownPaymentPercentage": 0.250936329588015,
													"FinancedNetFees": {
														"Total": 0.0,
														"Details": {
															"APRFees": [],
															"NonAPRFees": []
														}
													},
													"FinancedNetPerDiemInterest": {
														"Total": 0.0,
														"Details": {
															"NumberOfOddDays": 0,
															"PerDiemInterest": 0.0,
															"LenderPaidCreditAppliedToPerDiemInterest": 0.0,
															"SellerPaidOutsideOfClosingAmount": 0.0
														}
													},
													"FinancedNetPrepaidEscrows": {
														"Total": 0.0,
														"Details": {
															"AmountDueAtClosing": 0.0,
															"PrepaidTaxes": 0.0,
															"PrepaidInsurance": 0.0,
															"LenderPaidCreditAppliedToEscrows": 0.0,
															"AggregateAdjustment": 0.0,
															"AmountDueAtClosingDetails": null,
															"EscrowBreakdown": null,
															"ClientAmountPaidOutsideOfClose": 0.0,
															"SellerPaidOutsideOfClosing": null,
															"OtherPaidAtClosing": null
														}
													},
													"FinancedNetTaxProrations": {
														"Total": 0.0,
														"Details": {
															"BorrowerPaidTaxProrations": 0.0,
															"LenderPaidCreditAppliedToTaxProrations": 0.0,
															"SellerPaidTaxProrations": 0.0,
															"SellerResponsibleTaxProrations": 0.0,
															"ProrationsChargedToBuyer": 0.0,
															"ClientPaidTaxProrationAmount": 0.0,
															"TaxProrationBreakdown": null
														}
													},
													"RoundingAdjustment": -0.0
												}
											},
											"VAFundingFee": 0.0,
											"UFMIP": 0.0,
											"UFMIPDetails": null
										}
									},
									"TotalLenderPaidCredit": 0.0,
									"LenderPaidCreditDetails": {
										"OptimalTargetProfitLenderPaidCredit": -0.0,
										"OptimalTargetProfitLenderPaidCreditPercent": 0.0,
										"AutomaticLenderPaidCreditsFromFees": 0.0,
										"LenderPaidCreditsFromPoints": 0.0,
										"WaivedFeesCreditAmount": -0.0,
										"IncentiveLenderPaidCreditAmount": -0.0,
										"ManualLenderPaidCreditAmount": 0.0,
										"ManualLenderPaidCreditAmountMaximum": null,
										"ManualLenderPaidCreditAmountMinimum": 0.0
									}
								}
							},
							"HasSufficientAssetsForReserves": true,
							"HasSufficientAssetsForCashToClose": true,
							"LendersTitleInsuranceAdjustment": null,
							"GrantInformation": [],
							"BuydownFundsSourceType": "",
							"TotalBuydownCost": null,
							"BuydownDurationInYears": 0,
							"FailedToApplyBuydown": false,
							"BuydownAllowed": false,
							"LenderPaidBuydownAllowed": true,
							"BuydownNotAllowedReasons": [
								{
									"Value": 1.0,
									"Message": "Temporary buydown not allowed with LE and Initial Contact dates prior to 9/15/22"
								}
							],
							"LenderPaidBuydownNotAllowedReasons": [],
							"UncappedCalculatedExtensionCost": null,
							"UncappedCalculatedRelockCost": null,
							"HasNewRateSelectedDueToRateNotAvailableForRelock": null,
							"TodayBasePoints": null,
							"TotalInterestRate": null,
							"Heloc": null,
							"BreakEvenMonths": null,
							"Compensation": {
								"PartnerCompensationAmount": 0.0,
								"AdjustedDiscountPoints": 2.875
							},
							"ArmInfo": {
								"PotentialInterestRateAfterLock": 7.875,
								"PotentialPIMIPaymentAfterLock": 1417.24,
								"PotentialMonthlyPaymentAfterLock": 1710.94,
								"FullyIndexedRate": 7.875,
								"MaximumInterestRate": 0.0,
								"Margin": {
									"Base": 3.0,
									"Adjusted": 3.0,
									"Adjustments": [
										{
											"Value": 3.0,
											"Message": "Base Margin"
										}
									]
								}
							},
							"IsViable": false,
							"IsRecommended": true,
							"AreGoalsRecommended": {
								"Purchase": true
							},
							"UFMIPRate": 0.0,
							"UnroundedUFMIP": 0.0,
							"IsTexas50a6": false,
							"Benefits": {
								"FiveYearSavingsAmount": -74864.4,
								"TenYearSavingsAmount": -149728.8,
								"FifteenYearSavingsAmount": -224593.2,
								"TwentyYearSavingsAmount": -299457.6,
								"TwentyFiveYearSavingsAmount": -374322.0,
								"ThirtyYearSavingsAmount": -449186.4,
								"LoanPaymentDifferenceAmount": -1247.74
							},
							"EligibleAmounts": {
								"TotalAppliedAssetAmount": 84791.76,
								"TotalReserveAssetAmount": 915208.24,
								"TotalIncomeAmount": 40000.0,
								"TotalDebtAmount": 0.0,
								"TotalEligibleGiftFundAmount": 0.0,
								"TotalEligibleAssetAmount": 1000000.0,
								"TotalIncomeWithholdings": 13252.0
							},
							"LockExpirationDate": "2024-12-14T00:00:00",
							"MaximumSellerConcessions": 9.0,
							"IsCashout": false,
							"AmountFinanced": 191703.75,
							"CommitmentPeriod": 45,
							"TotalMonthlyRentalIncomeUsed": 0.0,
							"SubjectPropertyNetRentalIncome": 0.0,
							"SellerConcessionsUsed": 0.0,
							"RealtorCreditsUsed": 0.0,
							"BrokerCreditsUsed": 0.0,
							"AdjustedSalesAmount": 0.0,
							"PaymentInformation": {
								"TotalPayment": 1541.44,
								"PaymentInformationDetails": {
									"MortgageInsurancePayment": 0.0,
									"PrincipleAndInterestPayment": 1247.74,
									"MonthlyTaxPayment": 213.6,
									"MonthlyInsurancePayment": 80.1,
									"Payments": [
										{
											"Total": 1247.74,
											"Base": 1247.74,
											"MortgageInsurance": 0.0,
											"Rate": 6.375,
											"Duration": 84,
											"Period": 1
										},
										{
											"Total": 1417.24,
											"Base": 1417.24,
											"MortgageInsurance": 0.0,
											"Rate": 7.875,
											"Duration": 276,
											"Period": 85
										}
									]
								}
							},
							"SmartCash": null,
							"RecommendationPriority": 12,
							"RecommendationPriorities": {
								"Purchase": 12
							},
							"Incentives": [],
							"MaxLoanAmount": null,
							"MonthlyDebtToCureDTI": 0.0,
							"MonthlyIncomeToCureDTI": 0.0,
							"TargetDTI": 0.0,
							"AppliedAssetsRequired": 0.0,
							"ReserveAssetsRequired": 0.0,
							"EligibilityTargets": null,
							"IneligibilityReasons": null,
							"RevenueInformation": {
								"Total": 5750.0,
								"Adjusted": 5750.0,
								"TotalTargetProfit": 5750.0,
								"TargetProfitPercent": 2.875,
								"AdjustedPercent": 2.875,
								"PercentOfTargetProfit": 100.0,
								"ServicingRevenueAdjustmentPercent": 0.0,
								"TotalServicingRevenueAdjustment": 0.0,
								"TotalRevenuePercentage": 2.875
							},
							"ShortageInformation": null,
							"QMIneligibilities": [
								{
									"Value": 238.88749999999982,
									"Message": "Max Points and Fees threshold exceeded."
								}
							],
							"StoppingIneligibilities": [],
							"RegulatoryFindingsId": null,
							"CalculationDetails": [
								{
									"Value": 0.0,
									"Message": "Origination Calc 1: origination fee"
								},
								{
									"Value": 200000.0,
									"Message": "ClosingCosts:Payoffs"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Funding Fee"
								},
								{
									"Value": 5750.0,
									"Message": "ClosingCosts:Points Amount"
								},
								{
									"Value": 531.3000000000001,
									"Message": "ClosingCosts:Prepaid Interest"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:MI Paid in cash"
								},
								{
									"Value": 2055.9,
									"Message": "ClosingCosts:Escrows Amount"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Escrows Credit"
								},
								{
									"Value": 1919.81,
									"Message": "ClosingCosts:NonApr Fees"
								},
								{
									"Value": 2014.95,
									"Message": "ClosingCosts:Additional Apr Fees"
								},
								{
									"Value": 3934.76,
									"Message": "ClosingCosts:Additional Fees"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Percent Of Loan Amount LPC"
								},
								{
									"Value": 0.0,
									"Message": "ClosingCosts:Flat LPCs"
								},
								{
									"Value": 212271.96,
									"Message": "ClosingCosts: Total"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount start"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount rounded"
								},
								{
									"Value": 200000.0,
									"Message": "pLoanAmount Round Loan Amount end"
								},
								{
									"Value": 1541.44,
									"Message": "DTI:Debt"
								},
								{
									"Value": 40000.0,
									"Message": "DTI:Income"
								},
								{
									"Value": 6521.96,
									"Message": "Apply Credits: Initial Closing Costs"
								}
							],
							"LenderPaidBuydownOptOutReason": null
						}
					],
					"Disclaimers": [],
					"DisclaimerTemplates": [
						"ConventionalARM"
					],
					"UnderwritingSource": "LP",
					"MaintenanceAndUtilityExpenses": 0.0,
					"RecommendationPriority": 4,
					"AlimonyReducedIncome": false,
					"QMTestVarianceAmount": 0.0,
					"QMTestVariancePercent": 0.0,
					"UseOriginalPurchasePriceAsPropertyValue": false,
					"EligibilityTargets": {
						"APR": null,
						"Debt": null,
						"Income": null,
						"BackendDTI": null,
						"Assets": null,
						"ReserveAssets": null,
						"LTV": null,
						"SubordinatedLienBalance": null,
						"CreditScore": null,
						"Tradelines": null,
						"ClosingDate": null,
						"ApplicationDate": null,
						"CLTV": null,
						"HCLTV": null,
						"HelocInitialDraw": null,
						"CommitmentPeriod": null,
						"FinancedProperties": null,
						"PartnerAssets": null,
						"LoanAmount": null,
						"MonthsAtCurrentJob": null,
						"ResidualIncome": null,
						"FrontendDTI": null,
						"Foreclosure": null,
						"ShortSale": null,
						"MortgageLates": null,
						"Bankruptcy": null,
						"MortgageHistory": null,
						"CollectionBalance": null
					}
				}
			],
			"QualifyingFailureReasons": [],
			"RecommendedCashoutAmount": null,
			"LoanPurpose": "Purchase",
			"IsAllCashout": false,
			"AppliedAssetsRequired": null,
			"ReserveAssetsRequired": null,
			"DownPaymentPercentage": null,
			"EligibilityTargets": {
				"APR": null,
				"Debt": null,
				"Income": null,
				"BackendDTI": null,
				"Assets": null,
				"ReserveAssets": null,
				"LTV": null,
				"SubordinatedLienBalance": null,
				"CreditScore": null,
				"Tradelines": null,
				"ClosingDate": null,
				"ApplicationDate": null,
				"CLTV": null,
				"HCLTV": null,
				"HelocInitialDraw": null,
				"CommitmentPeriod": null,
				"FinancedProperties": null,
				"PartnerAssets": null,
				"LoanAmount": null,
				"MonthsAtCurrentJob": null,
				"ResidualIncome": null,
				"FrontendDTI": null,
				"Foreclosure": null,
				"ShortSale": null,
				"MortgageLates": null,
				"Bankruptcy": null,
				"MortgageHistory": null,
				"CollectionBalance": null
			}
		}
	],
	"QualifyingFailureReasons": [],
	"Disclaimers": [
		{
			"Value": "Mortgage Rate",
			"Message": "Mortgage rates could change daily. Remember - we do not have all your information, so the rate and payment results you see above may not completely reflect your actual situation. Rocket Mortgage offers a wide variety of loan options, and you might still qualify for a loan even if your situation doesn't match our assumptions.  Certain restrictions may apply."
		},
		{
			"Value": "FHAFixed",
			"Message": "<span class=\"legal__copy--title\">{LOAN_PERIOD}-year Fixed-Rate Loan:</span> An interest rate of {RATE}% ({APR}% APR) is for the cost of {POINTS} Point(s) (${DOLLAR_AMOUNT_OF_POINTS}) paid at closing. On a ${LOAN_AMOUNT} mortgage, you would make monthly payments of ${PAYMENT}. Monthly payment does not include taxes and insurance premiums. The actual payment amount will be greater. Payment assumes a loan-to-value (LTV) of {LTV}%. Payment includes a one-time upfront mortgage insurance premium at 1.75% of the base loan amount and a monthly mortgage insurance premium (MIP) calculated at {FHA_MIP_PERCENT}% of the base loan amount. For mortgages with a loan-to-value (LTV) ratio of {FHA_MIP_LTV}%, the {FHA_MIP_PERCENT}% monthly MIP will be paid for the first {FHA_MIP_DURATION} years of the mortgage term. Thereafter, the monthly loan payment will consist of equal monthly"
		},
		{
			"Value": "VAFixed",
			"Message": "<span class=\"legal__copy--title\">{LOAN_PERIOD}-year Fixed-Rate VA Loan:</span> An interest rate of {RATE}% ({APR}% APR) is for a cost of {POINTS} Point(s) (${DOLLAR_AMOUNT_OF_POINTS}) paid at closing. On a ${LOAN_AMOUNT} mortgage, you would make monthly payments of ${PAYMENT}. Monthly payment does not include taxes and insurance premiums. The actual payment amount will be greater. Payment assumes a loan-to-value (LTV) of {LTV}%. VA loans do not require PMI. The VA loan is a benefit of military service and only offered to veterans, surviving spouses and active duty military."
		},
		{
			"Value": "VAGeneral",
			"Message": "Rocket Mortgage is a VA-approved Lender; not endorsed or sponsored by the Dept. of Veterans Affairs or any government agency."
		},
		{
			"Value": "ConventionalFixed",
			"Message": "<span class=\"legal__copy--title\">{LOAN_PERIOD}-year Fixed-Rate Loan:</span> An interest rate of {RATE}% ({APR}% APR) is for the cost of {POINTS} point(s) (${DOLLAR_AMOUNT_OF_POINTS}) paid at closing. On a ${LOAN_AMOUNT} mortgage, you would make monthly payments of ${PAYMENT}. Monthly payment does not include taxes and insurance premiums. The actual payment amount will be greater. Payment assumes a loan-to-value (LTV) of {LTV}%."
		},
		{
			"Value": "ConventionalARM",
			"Message": "<span class=\"legal__copy--title\">Adjustable-Rate Mortgage:</span> The initial interest rate on {ADJUSTMENT_PERIOD}-year Adjustable-Rate Loan is {RATE}% ({APR}% APR) for the cost of {POINTS} Point(s) (${DOLLAR_AMOUNT_OF_POINTS}) paid in cash at closing. For a ${LOAN_AMOUNT} loan, the monthly payment for the initial period is ${PAYMENT}. Payment does not include taxes and insurance premiums. The actual payment amount will be greater. This has a {LOAN_PERIOD}-year amortization with a fixed rate of interest for the first {ADJUSTMENT_PERIOD} years, after which the interest rate may adjust every 6 months thereafter for the remainder of the mortgage term using a fully indexed rate (index plus margin rounded to the nearest 0.125%). Initial interest rate adjustment cannot change more than 5%, and thereafter, each subsequent periodic interest rate adjustment cannot change more than 1%. Rate increases are capped at 5% for the life of the loan. Interest rate will never be less than the margin. An interest rate adjustment may increase your monthly payment."
		}
	],
	"Status": "RecommendationMade",
	"Property": {
		"AnnualInsuranceAmount": 961.2,
		"AnnualTaxAmount": 2563.*********,
		"Address": {
			"State": "MI",
			"County": {
				"FIPS": "26125",
				"Name": "OAKLAND",
				"Limits": {
					"FannieCountyMedianIncome": 8108.33,
					"FreddieCountyMedianIncome": 8108.33,
					"ConformingLimit": 766550.0,
					"IsSPCPEligible": false,
					"IsBSAEligible": true,
					"FHALimit": 498257.0,
					"VaNationalLimit": 766550.0,
					"ConventionalNationalLimit": 802650.0,
					"VaLimit": 766550.0,
					"FhaNationalLimit": 766550.0,
					"FreddieConventionalNationalLimit": 802650.0,
					"FreddieConformingLimit": 766550.0,
					"ConventionalJumboLimit": 766550.0,
					"ConventionalJumboNationalLimit": 766550.0,
					"SchwabNationalLimit": 766550.0,
					"SchwabConformingLimit": 766550.0
				}
			}
		},
		"LienCollectionResponse": {
			"TotalForcedSubordinatedLienPayments": 0.0,
			"TotalSubordinatedLienPayments": 0.0,
			"AreLiensForcedSubordinated": false,
			"SecondaryLiensFinancedAmount": 0.0,
			"Lien": null
		}
	},
	"LastPricingUpdateDate": "2024-10-29T20:00:41Z",
	"PreviousPricingUpdateDate": null,
	"CurrentPricingUpdateDate": null,
	"DebtExclusionLimits": {},
	"IsRocketPricingEligible": false,
	"IsRocketProPricingEligible": false,
	"PartnershipPricingTier": null,
	"EligibilityTargets": null
} as unknown as ProductsResponse;
