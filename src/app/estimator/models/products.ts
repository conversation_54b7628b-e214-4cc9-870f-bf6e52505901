import { ProductFees } from "../components/estimator-sidebar/estimator-sidebar.component";

export enum AmortizationType {
  Fixed = 'Fixed',
  ARM = 'ARM',
}

export enum ParentEligibilityGroup {
  Conventional = 'Conventional',
  NonAgency = 'NonAgency',
  InterestOnly = 'InterestOnly',
  FHA = 'FHA',
  VA = 'VA',
  FreddieMac = 'FreddieMac',
  FannieMae = 'FannieMae',
  Government = 'Government',
  Schwab = 'Schwab',
}

export interface GroupedProducts {
  fixedProducts: Product[];
  adjustableProducts: Product[];
  interestOnlyProducts: Product[];
  yourgageProducts: Product[];
  fixedIneligibleProducts: Product[];
  adjustableIneligibleProducts: Product[];
  interestOnlyIneligibleProducts: Product[];
  yourgageIneligibleProducts: Product[];
}

export interface ProductsResponse {
  ProductGroups: ProductGroup[];
  Property: EstimateProperty;
}

export interface ProductGroup {
  AmortizationType: AmortizationType;
  Products: Product[];
}

export interface CheckedPricingOption {
  pricingOption: PricingOption;
  productName: string;
  product: Product;
  loanAmount: number;
}

export interface EstimateToShare {
  pricingOption: PricingOption;
  productName: string;
  loanAmount: number;
  feeDetails?: Fee[];
  productFees: AprFee[];
  totalFees: number;
}

export interface FeeAdjustments {
  optionId: string;
  productFees: ProductFees;
}

export interface Product {
  ProductCode: string;
  ProductName: string;
  AmortizationType: AmortizationType;
  PricingOptions: PricingOption[];
  ProductGroupType: string;
  TermMonths: number;
  ParentEligibilityGroups: ParentEligibilityGroup[];
  Tags: Tag[];
  IsEligible: boolean;
  Ineligibilites: Ineligibility[];
}

export enum Tag {
  Yourgage = 'YOURgage',
  Cadillac = 'Cadillac',
  Schwab = 'Schwab',
}

export interface PricingOption {
  Id: string;
  PaymentInformation: PaymentInformation;
  Rate: Rate;
  Points: Points;
  APR: number;
  FundsToClose: FundsToClose;
  LtvInformation: LtvInformation;
  CashToClose: CashToClose;
}

export interface Points {
  Base: number;
  Final: number;
  UndiscountedAllIn: number;
  Collected: number;
  Adjustments: Adjustment[];
}

export interface Adjustment {
  Value: number;
  Message: string;
}

export interface Rate {
  Base: number;
  Adjusted: number;
  Adjustments: Adjustment[];
}

export interface CashToClose {
  Total: number;
}

export interface PaymentInformation {
  TotalPayment: number;
  PaymentInformationDetails: PaymentInformationDetails;
}

export interface PaymentInformationDetails {
  MortgageInsurancePayment: number;
  PrincipleAndInterestPayment: number;
  MonthlyTaxPayment: number;
  MonthlyInsurancePayment: number;
}

export interface FundsToClose {
  Details: FtdDetails;
}

export interface FtdDetails {
  FinalLoanAmount: FinalLoanAmount;
  NonFinancedAmounts: {
    Details: {
      NetPerDiem: {
        Total: number;
      };
      NetFees: {
        Details: {
          APRFees: AprFee[];
        }
      }
    };
  };
}

export interface AprFee {
  Name: string;
  Code: string;
  Amount: number;
}

export interface FinalLoanAmount {
  Total: number;
  Details: {
    BaseLoanAmount: {
      Total: number;
      PurchaseDetails?: PurchaseDetails;
      RefinanceDetails?: RefinanceDetails;
    };
  };
}

export interface PurchaseDetails {
  PurchasePrice: number;
}

export interface RefinanceDetails {
  Total: number;
  MortgageBalance: number;
  FinancedNetFees: {
    Total: number;
    Details: {
      APRFees: AprFee[];
    }
  };
}

export interface LtvInformation {
  Rounded: number;
  CLTV: number;
}

export interface EstimateProperty {
  Address: EstimateAddress;
}

export interface EstimateAddress {
  State: string;
  County: EstimateCounty;
}

export interface EstimateCounty {
  FIPS: string;
  Name: string;
  Limits: CountyLimits;
}

export interface CountyLimits {
  FHALimit: number;
  ConventionalJumboLimit: number;
}

export interface Fee {
  name: string,
  value: number,
}

export interface Ineligibility {
  Message: string;
}
