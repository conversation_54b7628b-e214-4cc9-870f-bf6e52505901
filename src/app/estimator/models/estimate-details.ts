import { OccupancyType, State } from "@rocket-logic/rl-xp-bff-models";
import { PropertyType } from "@rocket-logic/rl-xp-bff-models/dist/enums/subject-property-type";

export interface EstimateDetails {
  calcInputs?: {
    occupancyType?: OccupancyType;
    propertyType?: PropertyType;
    zipCode?: string;
    countyName?: string;
    state?: State;
    purchasePrice?: number;
    creditScore?: number;
    downPaymentAmount?: number;
    downPaymentPercentage?: number;
    loanAmount?: number;
    schwabBalance?: number;
    isCashout: boolean;
    isMilitary: boolean;
    isEscrowWaived: boolean;
    currentLoanAmount?: number;
    estimatedPropertyValue?: number;
    isRIAClient: boolean;
    desiredCashoutAmount?: number;
    annualIncome: number;
    isFirstTimeHomeBuyer: boolean;
  };
  selectedProductCodes?: string[];
  loanPurpose: string;
  createdAt: string;
}