import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'formPathLabel',
  standalone: true,
})
export class FormPathLabelPipe implements PipeTransform {
  private partLabels = new Map<string, string>([
    ['addressLine1', 'Street'],
    ['state', 'State'],
    ['city', 'City'],
    ['zipCode', 'Zip Code'],
    ['formerResidences', 'Previous Address'],
    ['currentResidence', 'Current Address'],
    ['branch', 'Branch of Service'],
    ['firstName', 'First Name'],
    ['lastName', 'Last Name'],
    ['ssn', 'Social Security Number'],
    ['confirmSSN', 'Confirm Social Security Number'],
    ['assetValue', 'Asset Value'],
    ['rocketLogicClientIds', 'Asset Owner(s)'],
    ['monthlyRent', 'Monthly Rent'],
    ['purchasePrice', 'Purchase Price'],
    ['emailAddress', 'Email Address'],
    ['phoneNumbers', 'Phone Number Type'],
    ['employmentStartDate', 'Employment Start Date'],
    ['employmentEndDate', 'Employment End Date'],
    ['jobTitle', 'Job Title'],
    ['employerName', 'Employer Name'],
    ['clientReportedAmount', 'Amount Earned'],
    ['frequency', 'Frequency'],
    ['sellerConcessions', 'Seller Concessions'],
    ['contractClosingDate', 'Contract Closing Date'],
    ['downPaymentAmount', 'Down Payment Amount'],
    ['ownershipTransferPrice', 'Original Purchase Price'],
    ['clientIds', 'Client Ids'],
    ['dateOfBirth', 'Date of Birth'],
    ['ownershipTransferredOn', 'Date Acquired'],
    ['loanAmount', 'Loan Amount'],
    ['drawDownAmount', 'Draw Down Amount'],
    ['lienPosition', 'Lien Position'],
    ['helocDetails', 'Heloc Details'],
  ]);

  transform(path: string): string {
    const parts = path.split('.');
    const label = parts
      .map((part) => this.partLabels.get(part))
      .join(' ')
      .trimStart();
    return label || parts[parts.length - 1];
  }
}
