import { Clipboard } from '@angular/cdk/clipboard';
import { Component, computed, DestroyRef, inject, input } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatRippleModule } from '@angular/material/core';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Router } from '@angular/router';
import { RktSkeletonModule } from '@rocketcentral/rocket-design-system-angular';
import { AnalyticsBrowser } from '@segment/analytics-next';
import { catchError, from, map, of, throwError } from 'rxjs';
import { environment } from '../../environments/environment';
import { RecentLoansService } from '../services/recent-loans/recent-loans.service';
import { TeamMemberDataService } from '../services/team-member-data/team-member-data.serivce';
import { getErrorMessage } from '../util/get-error-message';

@Component({
  selector: 'app-loan-search',
  standalone: true,
  imports: [
    MatInputModule,
    MatFormFieldModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    ReactiveFormsModule,
    RktSkeletonModule,
    MatRippleModule,
  ],
  templateUrl: './loan-search.component.html',
  styleUrl: './loan-search.component.scss',
})
export class LoanSearchComponent {
  private teamMemberDataService = inject(TeamMemberDataService);
  isEstimatorEnabled = environment.estimator?.enabled;
  analytics = inject(AnalyticsBrowser, { optional: true });
  recentLoansService = inject(RecentLoansService);
  showSubtext = input(true);
  destroyRef = inject(DestroyRef);
  clipboard = inject(Clipboard);
  snackburrr = inject(MatSnackBar);
  recentLoansWithClients = toSignal(this.recentLoansService.recentLoansWithClients$);
  sortedLoansWithClients = computed(() => {
    const recentLoans = this.recentLoansService.recentLoans();
    const clonedLoans = structuredClone(this.recentLoansWithClients()?.data || []);

    const sortedLoans = clonedLoans.sort(
      (a, b) => recentLoans.indexOf(a.loan) - recentLoans.indexOf(b.loan),
    );

    recentLoans.forEach((loan) => {
      if (!sortedLoans.some((item) => item.loan === loan)) {
        sortedLoans.unshift({ loan, client: '' });
      }
    });

    return sortedLoans;
  });
  loanSearchControl = new FormControl('', [
    environment.appEnvironment === 'prod' ? Validators.pattern(/^3/) : Validators.nullValidator,
    Validators.minLength(10),
    Validators.required,
  ]);
  errorMessage = computed(() => {
    this.statusChange();
    return this.getErrorMessage();
  });
  private router = inject(Router);
  private statusChange = toSignal(this.loanSearchControl.statusChanges);

  hasEstimatorAccess = toSignal(this.teamMemberDataService.teamMemberRoles$.pipe(
    map((teamMemberRoles) => (teamMemberRoles.isFranchiseBanker || teamMemberRoles.isSchwabBanker) && this.isEstimatorEnabled),
    catchError(() => of(false))
  ));

  processInput(input: HTMLInputElement) {
    input.value = input.value.replaceAll(/[^0-9]/g, '');
  }

  onKeydown(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.onSearch();
    }
  }

  clickRecentLoan(loanNumber: string) {
    this.loanSearchControl.patchValue(loanNumber);
    this.onSearch();
  }

  onSearch() {
    if (this.loanSearchControl.valid) {
      const loanNumber = this.loanSearchControl.value;
      this.analytics?.track('Loan Searched', {
        loanNumber,
      });

      from(this.router.navigate(['/', 'loan', loanNumber]))
        .pipe(
          catchError((error) => {
            this.snackburrr.open(`Something went wrong. Failed to navigate to loan '${loanNumber}'`, 'Dismiss', {
              panelClass: 'rkt-Snackbar',
              duration: 5000,
            });
            return throwError(() => new Error(error));
          })
        )
        .subscribe(() => {
          this.recentLoansService.handleStoreRecentLoan(loanNumber!);
          this.loanSearchControl.reset('');
        });
    }
  }

  getErrorMessage() {
    return getErrorMessage(this.loanSearchControl, [
      ['pattern', () => 'Loan Number must start with a 3'],
      ['minLength', () => 'Loan Number must be at least 10 characters'],
    ]);
  }

  copyLoanNumber(event: Event, loanNumber: string) {
    event.stopPropagation();
    this.clipboard.copy(loanNumber);
    this.snackburrr.open(`Copied ${loanNumber} to clipboard`, 'Dismiss', {
      panelClass: 'rkt-Snackbar',
      duration: 1000,
    });
  }

  routeToTool(tool: string) {
    this.router.navigate([tool]);
  }
}
