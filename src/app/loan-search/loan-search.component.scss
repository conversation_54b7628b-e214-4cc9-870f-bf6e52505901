:host {
  display: flex;
  height: 100%;
  width: 100%;
  justify-content: center;
  align-items: center;
}

.recent-card {
  cursor: pointer;

  padding: 8px 12px;

  p {
    margin: 0;
  }

  background-color: var(--rlxp-recent-loan-list-item-background-color);
  border-radius: var(--rlxp-default-border-radius);

  &:hover {
    background-color: var(--rlxp-recent-loan-list-item-hover-background-color);
  }
}

mat-card.mat-mdc-card.mat-mdc-card {
  padding: 0;
  width:300px;

}

span.rkt-Skeleton, .recent-card {
  height: 56px;
  width:100%;
}
