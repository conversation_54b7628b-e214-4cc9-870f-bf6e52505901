<div class="flex flex-col gap-6">
  <mat-card class="rkt-Card p-0">
    @if (showSubtext()) {
      <mat-card-header>
        <mat-card-title>
          <div class="rkt-Heading-18 rkt-mb3">Loan Search</div>
        </mat-card-title>
  
        <mat-card-subtitle>
          <p class="rkt-Body-16 rkt-mb4 rkt-Color--gray-600">Enter a loan number to get started</p>
        </mat-card-subtitle>
      </mat-card-header>
    }
    <mat-card-content>
      <mat-form-field class="rkt-FormField w-full">
        <mat-label>Loan Number</mat-label>
        <input
        data-synthetic-monitor-id="loan-search-input"
          class="rkt-Input"
          #search
          matInput
          (input)="processInput(search)"
          (keydown)="onKeydown($event)"
          [formControl]="loanSearchControl"
          maxlength="10"
        />
        <div matSuffix>
          <button mat-icon-button (click)="onSearch()">
            <mat-icon svgIcon="search-outlined"></mat-icon>
          </button>
        </div>
        <mat-error>{{ errorMessage() }}</mat-error>
      </mat-form-field>
  
      @if(recentLoansService.recentLoans().length > 0 ) {
        <p class="rkt-Caption-12 rkt-mb4">RECENT LOANS</p>
      }
      
      @if (recentLoansService.recentLoans().length > 0 && !recentLoansWithClients()?.loading) {
        <div class="flex flex-col gap-2">
          @for (loanClient of sortedLoansWithClients(); track loanClient) {
            <div
              class="recent-card rkt-Elevation-3 flex justify-between gap-2 max-w-xs w-full min-w-max items-center h-14"
              matRipple
              (click)="clickRecentLoan(loanClient?.loan!)"
              (keypress)="clickRecentLoan(loanClient?.loan!)"
              tabindex="0"
            >
              <div class="rkt-Label-14">
                <p>
                  {{ loanClient?.loan }}
                </p>
                <p>
                  {{ loanClient?.client }}
                </p>
              </div>
              <div
                (click)="copyLoanNumber($event, loanClient.loan)"
                (keypress)="copyLoanNumber($event, loanClient.loan)"
                tabindex="0"
                matRipple
              >
                <mat-icon class="rkt-Icon" svgIcon="content_copy-outlined"></mat-icon>
              </div>
            </div>
          }
        </div>
      } @else if (recentLoansWithClients()?.loading) {
        <div class="flex flex-col gap-3">
          <!-- We know the number of recent loans from local storage but we load the client info -->
          @for(i of recentLoansService.recentLoans(); track i) {
            <span rktSkeleton></span>
          }
        </div>
      }
    </mat-card-content>
  </mat-card>

  @if (hasEstimatorAccess()) {
    <mat-card class="rkt-Card p-0">
      <mat-card-header>
        <mat-card-title>
          <div class="rkt-Heading-18 rkt-mb3">Other Tools</div>
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="flex flex-col gap-2">
          <a
          class="recent-card rkt-Elevation-3 flex justify-between gap-2 max-w-xs w-full min-w-max items-center h-14"
          matRipple
          tabindex="0"
          (click)="routeToTool('estimator')"
          (keypress)="routeToTool('estimator')"
        >
          <div class="rkt-Label-14"
          >
            <p>
              Quote Estimator
            </p>
          </div>
          <mat-icon class="rkt-Icon" svgIcon="calculate-outlined"></mat-icon>
        </a>
        </div>
      </mat-card-content>
    </mat-card>
  }
</div>
