import { Component, inject } from '@angular/core';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import {
  ActiveSidenavScreenService,
  SidenavScreen,
} from '../../services/active-sidenav-screen/active-sidenav-screen.service';
import { MessageCenterService } from '../../services/message-center/message-center.service';

@Component({
  selector: 'app-message-center-status',
  standalone: true,
  imports: [RktTagEnterpriseModule, MatTooltipModule],
  templateUrl: './message-center-status.component.html',
  styleUrl: './message-center-status.component.scss',
})
export class MessageCenterStatusComponent {
  private readonly messageCenterService = inject(MessageCenterService);
  private readonly activeSidenavScreenService = inject(ActiveSidenavScreenService);

  numberOfErrors = this.messageCenterService.errorCount;
  numberOfWarnings = this.messageCenterService.warningCount;
  numberOfInfo = this.messageCenterService.infoCount;

  openMessageCenter() {
    this.activeSidenavScreenService.activate(SidenavScreen.MessageCenter);
  }
}
