import { signal } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { AddressDataService } from '../services/address/address-data.service';
import { CreditService } from '../services/credit/credit.service';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import { LoanStateService } from '../services/entity-state/loan-state/loan-state.service';
import { ValidationErrorProviderService } from '../services/error/validation-error-provider.service';
import { FormNavSectionService } from '../services/form-nav/form-nav-section.service';
import { MessageCenterService } from '../services/message-center/message-center.service';

import { ApplicationIslandComponent } from './application-island.component';

describe('ApplicationIslandComponent', () => {
  let component: ApplicationIslandComponent;
  let fixture: ComponentFixture<ApplicationIslandComponent>;

  beforeEach(async () => {
    const loanStateSpy = jasmine.createSpyObj('LoanStateService', ['registerAppIsland'], {
      isLoanEditingDisabled$: of(false),
      isInitialized$: of(true),
      isLoanDeactivated$: of(false),
      state: signal({ data: null })
    });
    const loanEditingStateSpy = jasmine.createSpyObj('LoanEditingState', ['init'], {
      isLoanEditingDisabled: signal(false)
    });
    const messageCenterSpy = jasmine.createSpyObj('MessageCenterService', ['init'], {
      errorCount: signal(0),
      warningCount: signal(0),
      infoCount: signal(0),
      errors: signal([])
    });
    const formNavSpy = jasmine.createSpyObj('FormNavSectionService', ['registerAppIsland', 'deregisterAppIsland']);
    const creditSpy = jasmine.createSpyObj('CreditService', ['init']);
    const addressSpy = jasmine.createSpyObj('AddressDataService', ['init']);
    const validationSpy = jasmine.createSpyObj('ValidationErrorProviderService', ['init'], {
      validationErrors$: of([]),
      errors: signal([])
    });

    await TestBed.configureTestingModule({
      imports: [ApplicationIslandComponent],
      providers: [
        { provide: LoanStateService, useValue: loanStateSpy },
        { provide: LoanEditingState, useValue: loanEditingStateSpy },
        { provide: MessageCenterService, useValue: messageCenterSpy },
        { provide: FormNavSectionService, useValue: formNavSpy },
        { provide: CreditService, useValue: creditSpy },
        { provide: AddressDataService, useValue: addressSpy },
        { provide: ValidationErrorProviderService, useValue: validationSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(ApplicationIslandComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
