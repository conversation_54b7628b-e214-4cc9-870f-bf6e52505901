import { Component, computed, inject, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatTooltip } from '@angular/material/tooltip';
import { finalize } from 'rxjs';
import { environment } from '../../../environments/environment';
import { AvoService } from '../../analytics/avo/avo.service';
import { TrackClickDirective } from '../../analytics/track-click.directive';
import { DenyButtonComponent } from '../../deny-withdraw/deny-button/deny-button.component';
import { WithdrawButtonComponent } from '../../deny-withdraw/withdraw-button/withdraw-button.component';
import { ViewHistoryButtonComponent } from '../../loan-history/view-history-button/view-history-button.component';
import { ActiveActionService } from '../../services/active-action.service';
import {
    ActiveSidenavScreenService,
    SidenavScreen,
} from '../../services/active-sidenav-screen/active-sidenav-screen.service';
import { CompletedLoanFormBuilderService } from '../../services/completed-loan-form-builder/completed-loan-form.service';
import { CreditAuthorizationService } from '../../services/credit/credit-authorization.service';
import { STATE_SERVICES } from '../../services/entity-state/abstract-entity-state.service';
import { UPDATE_HANDLERS } from '../../services/entity-state/abstract-update-handler.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import { FormNavInputService } from '../../services/form-nav/form-nav-input.service';
import { LoanIdService } from '../../services/loan-id/loan-id.service';
import { MessageCenterService } from '../../services/message-center/message-center.service';
import { LoanAccessVerifierReasons } from '../../services/user-authorization/default-access';
import { UserAuthorizationService } from '../../services/user-authorization/user-authorization.service';
import { navigateWithFullReferrerUrl } from '../../util/navigation-utilities';

export enum AppIslandAction {
  GetPricing = 'GetPricing',
  CreditRequest = 'CreditRequest',
}

@Component({
  selector: 'app-app-island-action-button',
  standalone: true,
  imports: [
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    MatMenuModule,
    DenyButtonComponent,
    WithdrawButtonComponent,
    ViewHistoryButtonComponent,
    TrackClickDirective,
    MatTooltip,
  ],
  templateUrl: './app-island-action-button.component.html',
  styleUrl: './app-island-action-button.component.scss',
})
export class AppIslandActionButtonComponent {
  private readonly activeActionService = inject(ActiveActionService);
  private readonly creditAuthService = inject(CreditAuthorizationService);
  private readonly formInputNavService = inject(FormNavInputService);
  private readonly userAuthorizationService = inject(UserAuthorizationService);
  private readonly updateHandlers = inject(UPDATE_HANDLERS);
  private readonly stateServices = inject(STATE_SERVICES);
  private readonly messageCenterService = inject(MessageCenterService);
  loanStateService = inject(LoanStateService);
  activeSidenavScreenService = inject(ActiveSidenavScreenService);
  completedLoanFormBuilder = inject(CompletedLoanFormBuilderService);
  loanIdService = inject(LoanIdService);
  avoService = inject(AvoService);
  clientStateService = inject(ClientStateService);

  hasConflictingLoans = this.userAuthorizationService.hasConflictingLoans;
  productItemsToComplete = computed(
    () => this.formInputNavService.availableProductSections().length,
  );
  unsavedChanges = computed(() => {
    return this.updateHandlers.reduce((acc, handler) => acc + handler.unsavedChanges(), 0);
  });
  isUpdating = computed(() => this.stateServices.some((service) => service.isUpdating()));

  activeAction = this.activeActionService.activeAction;
  actionDisabled = computed(() => {
    switch (this.activeAction()) {
      case AppIslandAction.CreditRequest:
        return !this.creditAuthService.canViewCreditRequestManager();
      case AppIslandAction.GetPricing:
        return !this.isLoanComplete()
          ? this.productItemsToComplete() > 0 ||
              this.unsavedChanges() > 0 ||
              this.isUpdating() ||
              this.hasConflictingLoans()
          : false;
    }
  });
  disabled = computed(() => this.messageCenterService.shouldDisableActions());
  isLoading = signal(false);
  isLoanComplete = toSignal(this.loanStateService.isLoanXpCompleted$, { initialValue: false });
  isDeactivated = toSignal(inject(LoanStateService).isLoanDeactivated$);
  isDenyWithdrawAllowed = toSignal(inject(UserAuthorizationService).isDenyWithdrawAllowed$);
  isLoanEditingDisabled = toSignal(inject(LoanEditingState).isLoanEditingDisabled$);

  shouldDisableAction = computed(
    () => this.disabled() || this.actionDisabled() || this.isDeactivated() || this.isLoanEditingDisabled(),
  );

  readonly AppIslandAction = AppIslandAction;
  readonly FolderReceived = LoanAccessVerifierReasons.FolderReceived;

  onCreditRequestClick() {
    this.activeSidenavScreenService.activate(SidenavScreen.CreditReportManager);
  }

  onGetPricingClick() {
    if (this.isLoanComplete()) {
      this.avoService.pricingRequested$.next({
        gcids: <string[]>(
          Array.from(this.clientStateService.stateValues() ?? [], (client) => client.gcid)
        ),
      });
      navigateWithFullReferrerUrl(`${environment.productsUrl}/loan/${this.loanIdService.loanId()}`);
      return;
    }

    const completedLoanForm = this.completedLoanFormBuilder.buildCompletedLoanForm();
    this.isLoading.set(true);
    return this.loanStateService
      .completeInitialApplication$(completedLoanForm)
      .pipe(finalize(() => this.isLoading.set(false)))
      .subscribe((loanId) => {
        navigateWithFullReferrerUrl(`${environment.productsUrl}/loan/${loanId}`);
      });
  }
}
