<div class="container">
  @switch (activeAction()) {
    @case (AppIslandAction.CreditRequest) {
      <button
        data-synthetic-monitor-id="credit-request-button"
        data-testid="credit-request-button"
        class="rkt-Button rkt-Button--large rkt-Button--has-icon left-button whitespace-nowrap"
        [disabled]="shouldDisableAction()"
        [class.rkt-Button--is-disabled]="shouldDisableAction()"
        mat-flat-button
        (click)="onCreditRequestClick()"
        color="primary"
        appTrackClick
        description="Credit Request"
      >
        <mat-icon svgIcon="rl-crm" class="rkt-Icon" />
        Credit Request
      </button>
    }
    @case (AppIslandAction.GetPricing) {
      <button
        data-synthetic-monitor-id="get-pricing-button"
        data-testid="get-pricing-button"
        class="rkt-Button rkt-Button--large rkt-Button--has-icon left-button"
        [class.rkt-Button--is-spinning]="isLoading()"
        [disabled]="shouldDisableAction() || isLoading()"
        [class.rkt-Button--is-disabled]="shouldDisableAction()"
        mat-flat-button
        (click)="onGetPricingClick()"
        color="primary"
        appTrackClick
        description="Get Pricing"
      >
        @if (isLoading()) {
          <mat-icon iconPositionStart class="rkt-Icon">
            <mat-spinner
              role="presentation"
              class="rkt-Spinner rkt-Spinner--enterprise"
              diameter="20"
            />
          </mat-icon>
        } @else {
          <mat-icon svgIcon="arrow_forward-outlined" iconPositionStart class="rkt-Icon" />
        }
        Get Pricing
      </button>
    }
  }
  <button
    [matMenuTriggerFor]="menu"
    [disabled]="disabled()"
    mat-icon-button
    color="primary"
    class="rkt-Button rkt-Button--large right-button"
    data-testid="action-menu-trigger"
    [class.rkt-Button--is-disabled]="disabled()"
    [matTooltip]="
      isDenyWithdrawAllowed() === FolderReceived ? 'Loan already submitted to underwriting' : ''
    "
  >
    <mat-icon
      aria-hidden="false"
      aria-label="Get Pricing Additional Menu"
      fontIcon="arrow_drop_down"
    />
  </button>

  <mat-menu #menu xPosition="before" data-testid="action-menu">
    <app-deny-button />
    <app-withdraw-button />
    @if (isLoanComplete()) {
      <app-view-history-button />
    }
  </mat-menu>
</div>
