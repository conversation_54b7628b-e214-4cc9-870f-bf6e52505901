:host {
  display: flex;
}

.container {
  display: flex;
  align-items: center;

  button {
    display: flex;
    gap: 8px;

    mat-icon {
      margin: 0;
    }
  }
}

.left-button {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  padding: 8px 16px;
}

.right-button[mat-icon-button] {
  width: 40px;
  height: 40px;
  border-left: 1px solid var(--rlxp-button-group-divider-color, white);
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  border-top-left-radius: 0;
  border-bottom-left-radius: 0;

  &.rkt-Button--is-disabled {
    border-color: var(--rlxp-button-group-disabled-divider-color);
  }
}
