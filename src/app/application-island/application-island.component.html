<div class="container rkt-Elevation-6" [class.deactivated]="isLoanDeactivated()">
  <div class="app-island-container">
    <div
      class="left-app-island-container"
      [class.deactivated]="isLoanDeactivated()"
      [class.border-transparent]="!messagesExist()"
    >
      @if (isLoanDeactivated()) {
        <div class="flex items-center justify-start w-full">
          <div class="flex gap-4">
            <mat-icon svgIcon="cancel-two_tone" color="warn" class="rkt-Icon app-Icon--error" />
            <div class="flex items-center">
              <span class="rkt-Caption-12 rkt-FontWeight--700 flex items-center">
                Loan {{ loanDeactivationReason()?.deactivationType }}
              </span>
            </div>
          </div>
        </div>
      } @else {
        <ng-content select="[appIslandStatus]" />

        @if (messagesExist()) {
          <mat-divider rktStackItem class="mat-divider rkt-VerticalDivider" [vertical]="true" />
          <app-message-center-status />
        }
      }
    </div>
    <div class="action-group-flex-container">
      @if (isInitialized() && !shouldLock()) {
        <ng-content select="[appIslandProgress]" />
      }
      <ng-content select="[appIslandAction]" />
    </div>
  </div>

  <app-app-island-errors />
</div>
