import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatChipsModule } from '@angular/material/chips';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RktStackModule } from '@rocketcentral/rocket-design-system-angular';
import {
  RktAlertEnterpriseModule,
  RktLinkEnterpriseModule,
  RktTagEnterpriseModule,
} from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AddressDataService } from '../services/address/address-data.service';
import { CreditService } from '../services/credit/credit.service';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import { LoanStateService } from '../services/entity-state/loan-state/loan-state.service';
import { FormNavSectionService } from '../services/form-nav/form-nav-section.service';
import { MessageCenterService } from '../services/message-center/message-center.service';
import { AppIslandErrorsComponent } from './app-island-errors/app-island-errors.component';
import { MessageCenterStatusComponent } from './message-center-status/message-center-status.component';

@Component({
  selector: 'app-application-island',
  standalone: true,
  templateUrl: './application-island.component.html',
  styleUrl: './application-island.component.scss',
  imports: [
    RktLinkEnterpriseModule,
    MatChipsModule,
    MatIconModule,
    MatButtonModule,
    MatProgressSpinnerModule,
    RktAlertEnterpriseModule,
    RktStackModule,
    AppIslandErrorsComponent,
    RktTagEnterpriseModule,
    MatDividerModule,
    MessageCenterStatusComponent,
  ],
})
export class ApplicationIslandComponent implements OnInit, OnDestroy {
  creditService = inject(CreditService);
  private loanStateService = inject(LoanStateService);
  private loanEditingState = inject(LoanEditingState);
  private formNavService = inject(FormNavSectionService);
  private elementRef = inject(ElementRef);
  private messageCenterService = inject(MessageCenterService);
  addressDataService = inject(AddressDataService);

  messagesExist = computed(() => {
    return (
      this.messageCenterService.errorCount() > 0 ||
      this.messageCenterService.warningCount() > 0 ||
      this.messageCenterService.infoCount() > 0
    );
  });
  isLoanEditingDisabled = this.loanEditingState.isLoanEditingDisabled;

  isInitialized = toSignal(this.loanStateService.isInitialized$, { requireSync: true });
  lockingError = computed(() =>
    this.messageCenterService.errors().some((error) => error?.shouldLock),
  );
  shouldLock = computed(() => this.isLoanEditingDisabled() || this.lockingError());

  loanDeactivationReason = computed(
    () => this.loanStateService.state()?.data?.loanDeactivationDetails,
  );
  isLoanDeactivated = toSignal(this.loanStateService.isLoanDeactivated$);

  ngOnInit(): void {
    this.formNavService.registerAppIsland(this.elementRef);
  }

  ngOnDestroy(): void {
    this.formNavService.deregisterAppIsland();
  }
}
