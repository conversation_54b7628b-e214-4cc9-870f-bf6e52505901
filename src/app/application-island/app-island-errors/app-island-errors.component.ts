import { Component, inject } from '@angular/core';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { FormPathLabelPipe } from '../../form-path-label.pipe';
import { ValidationErrorProviderService } from '../../services/error/validation-error-provider.service';
import { EntityNamePipe } from '../../util/entity-name.pipe';

@Component({
  selector: 'app-app-island-errors',
  standalone: true,
  imports: [RktAlertEnterpriseModule, FormPathLabelPipe, EntityNamePipe],
  templateUrl: './app-island-errors.component.html',
  styleUrl: './app-island-errors.component.scss',
})
export class AppIslandErrorsComponent {
  readonly validationErrorService = inject(ValidationErrorProviderService);
  readonly errors = this.validationErrorService.errors;

  dismissAllErrors() {
    this.errors().forEach((error) => error?.onDismiss());
  }
}
