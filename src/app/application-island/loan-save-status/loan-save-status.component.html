<div class="save-status-group">
  @if (isUpdating()) {
    <mat-spinner
      class="rkt-ProgressSpinner rkt-ProgressSpinner--enterprise"
      diameter="24"
    />
    <span class="rkt-Caption-12 rkt-FontWeight--500">
      {{ saveType() === SaveTriggerType.Auto ? 'Auto ' : '' }}Saving Loan
    </span>
  } @else if (isLoanArchived()) {
    <mat-icon svgIcon="lock-two_tone" class="rkt-Icon" />
    <span class="rkt-Caption-12 rkt-FontWeight--500">Loan Archived</span>
  } @else if (shouldLock()) {
    <mat-icon svgIcon="lock-two_tone" class="rkt-Icon" />
    <span class="rkt-Caption-12 rkt-FontWeight--500"><PERSON><PERSON> Locked</span>
  } @else if (updateFailed()) {
    <mat-icon svgIcon="error-two_tone" variant="error" class="rkt-Icon app-Icon--error" />
    <span class="rkt-Caption-12 rkt-FontWeight--500"
      >{{ saveType() === SaveTriggerType.Auto ? 'Auto' : 'Loan' }} Save Failed</span
    >
  } @else if (unsavedChanges() > 0) {
    <div class="unsaved-changes">
      <app-inline-badge>{{ unsavedChanges() }}</app-inline-badge>
      <span class="rkt-Caption-12 rkt-FontWeight--500">Unsaved Changes</span>
      <button
        rktLinkEnterprise
        class="rkt-Caption-12 rkt-FontWeight--500"
        (click)="onSave()"
      >
        Save Now
      </button>
    </div>
  } @else {
    <mat-icon
      svgIcon="check_circle-two_tone"
      variant="success"
      class="rkt-Icon app-Icon--success transform-none"
    />
    <span class="rkt-Caption-12 rkt-FontWeight--500">Loan Saved</span>
  }
</div>