import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RktLinkEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { merge } from 'rxjs';
import { InlineBadgeComponent } from '../../inline-badge/inline-badge.component';
import { EntityStateErrorType, STATE_SERVICES } from '../../services/entity-state/abstract-entity-state.service';
import { UPDATE_HANDLERS } from '../../services/entity-state/abstract-update-handler.service';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import { ValidationErrorProviderService } from '../../services/error/validation-error-provider.service';
import { MessageCenterService } from '../../services/message-center/message-center.service';
import { ManualSaveService } from '../../services/save-trigger/manual-save.service';
import { SaveTriggerType } from '../../services/save-trigger/save-trigger';
import { UserAuthorizationService } from '../../services/user-authorization/user-authorization.service';

@Component({
  selector: 'app-loan-save-status',
  standalone: true,
  imports: [MatProgressSpinnerModule, MatIconModule, InlineBadgeComponent, RktLinkEnterpriseModule],
  templateUrl: './loan-save-status.component.html',
  styleUrl: './loan-save-status.component.scss'
})
export class LoanSaveStatusComponent {
  private readonly manualSaveService = inject(ManualSaveService);
  private readonly updateHandlers = inject(UPDATE_HANDLERS);
  private readonly stateServices = inject(STATE_SERVICES);
  private readonly messageCenterService = inject(MessageCenterService);
  private readonly loanStateService = inject(LoanStateService);
  private readonly loanEditingState = inject(LoanEditingState);
  private readonly validationErrorService = inject(ValidationErrorProviderService);
  private readonly userAuthorizationService = inject(UserAuthorizationService);

  readonly SaveTriggerType = SaveTriggerType;

  isLoanEditingDisabled = this.loanEditingState.isLoanEditingDisabled;
  isLoanArchived = toSignal(this.userAuthorizationService.isLoanArchived$, { initialValue: false });
  unsavedChanges = computed(() => {
    return this.updateHandlers.reduce((acc, handler) => acc + handler.unsavedChanges(), 0);
  });
  saveType = toSignal(merge(...this.updateHandlers.map((handler) => handler.saveType$)));
  isUpdating = computed(() => this.stateServices.some((service) => service.isUpdating()));
  isFetching = computed(() => this.stateServices.some((service) => service.isFetching()));
    updateFailed = computed(
      () =>
        this.validationErrorService.errors().length ||
        this.messageCenterService
          .errors()
          .some((error) => error.type === EntityStateErrorType.UpdateError),
    );

  lockingError = computed(() =>
    this.messageCenterService.errors().some((error) => error?.shouldLock),
  );
  shouldLock = computed(() => this.isLoanEditingDisabled() || this.lockingError());


  onSave() {
    this.manualSaveService.saveChanges();
  }
}
