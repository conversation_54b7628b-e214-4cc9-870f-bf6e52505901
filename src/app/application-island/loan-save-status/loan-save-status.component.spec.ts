import { signal } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of } from 'rxjs';
import { STATE_SERVICES } from '../../services/entity-state/abstract-entity-state.service';
import { UPDATE_HANDLERS } from '../../services/entity-state/abstract-update-handler.service';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import { ValidationErrorProviderService } from '../../services/error/validation-error-provider.service';
import { MessageCenterService } from '../../services/message-center/message-center.service';
import { ManualSaveService } from '../../services/save-trigger/manual-save.service';
import { UserAuthorizationService } from '../../services/user-authorization/user-authorization.service';
import { LoanSaveStatusComponent } from './loan-save-status.component';

describe('LoanSaveStatusComponent', () => {
  let component: LoanSaveStatusComponent;
  let fixture: ComponentFixture<LoanSaveStatusComponent>;

  beforeEach(async () => {
    const userAuthSpy = jasmine.createSpyObj('UserAuthorizationService', ['init'], {
      isLoanArchived$: of(false)
    });
    const loanStateSpy = jasmine.createSpyObj('LoanStateService', ['init'], {
      isLoanEditingDisabled$: of(false),
      isInitialized$: of(true),
      state: signal({ data: null })
    });
    const loanEditingStateSpy = jasmine.createSpyObj('LoanEditingState', ['init'], {
      isLoanEditingDisabled: signal(false)
    });
    const messageCenterSpy = jasmine.createSpyObj('MessageCenterService', ['init'], {
      errorCount: signal(0),
      warningCount: signal(0),
      infoCount: signal(0),
      errors: signal([])
    });
    const validationSpy = jasmine.createSpyObj('ValidationErrorProviderService', ['init'], {
      validationErrors$: of([]),
      errors: signal([])
    });
    const manualSaveSpy = jasmine.createSpyObj('ManualSaveService', ['init']);

    await TestBed.configureTestingModule({
      imports: [LoanSaveStatusComponent],
      providers: [
        { provide: UserAuthorizationService, useValue: userAuthSpy },
        { provide: LoanStateService, useValue: loanStateSpy },
        { provide: LoanEditingState, useValue: loanEditingStateSpy },
        { provide: MessageCenterService, useValue: messageCenterSpy },
        { provide: ValidationErrorProviderService, useValue: validationSpy },
        { provide: ManualSaveService, useValue: manualSaveSpy },
        { provide: UPDATE_HANDLERS, useValue: [] },
        { provide: STATE_SERVICES, useValue: [] }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LoanSaveStatusComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should show archived loan status when loan is archived', async () => {
    // Arrange - Reset TestBed and configure with archived loan
    TestBed.resetTestingModule();

    const archivedUserAuthSpy = jasmine.createSpyObj('UserAuthorizationService', ['init'], {
      isLoanArchived$: of(true)
    });
    const loanStateSpy = jasmine.createSpyObj('LoanStateService', ['init'], {
      isLoanEditingDisabled$: of(false),
      isInitialized$: of(true),
      state: signal({ data: null })
    });
    const loanEditingStateSpy = jasmine.createSpyObj('LoanEditingState', ['init'], {
      isLoanEditingDisabled: signal(false)
    });
    const messageCenterSpy = jasmine.createSpyObj('MessageCenterService', ['init'], {
      errorCount: signal(0),
      warningCount: signal(0),
      infoCount: signal(0),
      errors: signal([])
    });
    const validationSpy = jasmine.createSpyObj('ValidationErrorProviderService', ['init'], {
      validationErrors$: of([]),
      errors: signal([])
    });
    const manualSaveSpy = jasmine.createSpyObj('ManualSaveService', ['init']);

    await TestBed.configureTestingModule({
      imports: [LoanSaveStatusComponent],
      providers: [
        { provide: UserAuthorizationService, useValue: archivedUserAuthSpy },
        { provide: LoanStateService, useValue: loanStateSpy },
        { provide: LoanEditingState, useValue: loanEditingStateSpy },
        { provide: MessageCenterService, useValue: messageCenterSpy },
        { provide: ValidationErrorProviderService, useValue: validationSpy },
        { provide: ManualSaveService, useValue: manualSaveSpy },
        { provide: UPDATE_HANDLERS, useValue: [] },
        { provide: STATE_SERVICES, useValue: [] }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LoanSaveStatusComponent);
    component = fixture.componentInstance;

    // Act
    fixture.detectChanges();

    // Assert
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.textContent).toContain('Loan Archived');
    const lockIcon = compiled.querySelector('mat-icon[svgIcon="lock-two_tone"]');
    expect(lockIcon).toBeTruthy();
  });

  it('should not show archived loan status when loan is not archived', () => {
    // Arrange - The default setup already has isLoanArchived$ as false

    // Act
    fixture.detectChanges();

    // Assert
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.textContent).not.toContain('Loan Archived');
  });

  it('should prioritize archived loan over locked loan', async () => {
    // Arrange - Reset TestBed and configure with archived and locked loan
    TestBed.resetTestingModule();

    const archivedUserAuthSpy = jasmine.createSpyObj('UserAuthorizationService', ['init'], {
      isLoanArchived$: of(true)
    });
    const lockedLoanStateSpy = jasmine.createSpyObj('LoanStateService', ['init'], {
      isLoanEditingDisabled$: of(true),
      isInitialized$: of(true),
      state: signal({ data: null })
    });
    const loanEditingStateSpy = jasmine.createSpyObj('LoanEditingState', ['init'], {
      isLoanEditingDisabled: signal(true)
    });
    const messageCenterSpy = jasmine.createSpyObj('MessageCenterService', ['init'], {
      errorCount: signal(0),
      warningCount: signal(0),
      infoCount: signal(0),
      errors: signal([])
    });
    const validationSpy = jasmine.createSpyObj('ValidationErrorProviderService', ['init'], {
      validationErrors$: of([]),
      errors: signal([])
    });
    const manualSaveSpy = jasmine.createSpyObj('ManualSaveService', ['init']);

    await TestBed.configureTestingModule({
      imports: [LoanSaveStatusComponent],
      providers: [
        { provide: UserAuthorizationService, useValue: archivedUserAuthSpy },
        { provide: LoanStateService, useValue: lockedLoanStateSpy },
        { provide: LoanEditingState, useValue: loanEditingStateSpy },
        { provide: MessageCenterService, useValue: messageCenterSpy },
        { provide: ValidationErrorProviderService, useValue: validationSpy },
        { provide: ManualSaveService, useValue: manualSaveSpy },
        { provide: UPDATE_HANDLERS, useValue: [] },
        { provide: STATE_SERVICES, useValue: [] }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(LoanSaveStatusComponent);
    component = fixture.componentInstance;

    // Act
    fixture.detectChanges();

    // Assert
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.textContent).toContain('Loan Archived');
    expect(compiled.textContent).not.toContain('Loan Locked');
  });
});
