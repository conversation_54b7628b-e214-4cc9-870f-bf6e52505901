:host {
  border-radius: 12px;
}

.container {
  padding: 12px;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--mdc-outlined-card-container-color);
}

:host {
  ::ng-deep {
    .rkt-Alert {
      padding: 12px;
    }

    p.rkt-Alert__text {
      margin: 0px;
    }

    .rkt-Caption-12 {
      white-space: nowrap;
    }
  }
}

.app-island-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 4px;
}

.action-group-flex-container {
  display: flex;
  justify-content: end;
  gap: 16px;
}

.left-app-island-container {
  border-radius: 8px;
  border: 0.5px solid var(--rlxp-gray-200, #d3d3d3);
  position: relative;
  align-items: center;
  justify-content: space-around;
  display: flex;
  gap: 8px;
  padding: 8px;

  &.deactivated {
    border: none;
  }

  &.border-transparent {
    border-color: transparent;
  }
}

.mat-divider {
  background-color: var(--mdc-outlined-card-container-color);
  align-self: normal;
  margin: -8px 0;
}
