import { TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { LargestContentPaintTrackerService } from '@rocket-logic/ngx-web-vitals';
import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { AppComponent } from './app.component';

describe('AppComponent', () => {
  beforeEach(() =>
    MockBuilder(AppComponent)
      .mock(LargestContentPaintTrackerService, { finalLCP$: NEVER })
      .mock(Router, { events: NEVER }),
  );
  beforeEach(() => {
    TestBed.compileComponents();
  });

  it('should create the app', () => {
    const fixture = TestBed.createComponent(AppComponent);
    const app = fixture.componentInstance;
    expect(app).toBeTruthy();
  });
});
