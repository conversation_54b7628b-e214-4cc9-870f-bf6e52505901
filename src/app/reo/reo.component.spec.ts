import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { <PERSON><PERSON><PERSON>uilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import {
    LiabilityFormRef,
    LiabilityFormService,
} from '../services/entity-state/liability-state/liability-form.service';
import { LiabilityStateService } from '../services/entity-state/liability-state/liability-state.service';
import { LiabilitySubscriptionManager } from '../services/entity-state/liability-state/liability-subscription-manager.service';
import { LiabilityUpdateHandlerService } from '../services/entity-state/liability-state/liability-update-handler.service';
import { LienAssociationHandlerService } from '../services/entity-state/liability-state/lien-association-handler.service';
import { LienPositionHandlerService } from '../services/entity-state/liability-state/lien-position-handler.service';
import { LIABILITY_AUTO_SAVE_TRIGGER } from '../services/entity-state/liability-state/provide-liability-state';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import { OwnedPropertyFormService } from '../services/entity-state/owned-property-state/owned-property-form.service';
import { OwnedPropertyStateService } from '../services/entity-state/owned-property-state/owned-property-state.service';
import { OwnedPropertySubscriptionManager } from '../services/entity-state/owned-property-state/owned-property-subscription-manager.service';
import { OwnedPropertyUpdateHandlerService } from '../services/entity-state/owned-property-state/owned-property-update-handler.service';
import { OWNED_PROPERTY_AUTO_SAVE_TRIGGER } from '../services/entity-state/owned-property-state/provide-owned-property-state';
import { FormNavSectionService } from '../services/form-nav/form-nav-section.service';
import { ReoComponent } from './reo.component';

describe('ReoComponent', () => {
  let component: ReoComponent;
  let fixture: ComponentFixture<ReoComponent>;

  beforeEach(() =>
    MockBuilder(ReoComponent)
      .keep(LiabilityFormService)
      .keep(LiabilityFormRef)
      .keep(OwnedPropertyFormService)
      .mock(OwnedPropertyUpdateHandlerService)
      .mock(LiabilityUpdateHandlerService)
      .mock(LienPositionHandlerService)
      .mock(LienAssociationHandlerService)
      .mock(LiabilitySubscriptionManager)
      .mock(LiabilityStateService, { state$: NEVER, state: signal(undefined) })
      .mock(OwnedPropertyStateService, { state: signal(undefined), state$: NEVER })
      .mock(FormNavSectionService, { activeSection: signal(null) })
      .mock(LoanEditingState, {
        isLoanEditingDisabled$: NEVER,
      })
      .mock(OwnedPropertySubscriptionManager)
      .provide({ provide: LIABILITY_AUTO_SAVE_TRIGGER, useValue: { registerControls: () => {} } })
      .provide({
        provide: OWNED_PROPERTY_AUTO_SAVE_TRIGGER,
        useValue: { registerControls: () => {} },
      }),
  );
  beforeEach(() => {
    fixture = TestBed.createComponent(ReoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
