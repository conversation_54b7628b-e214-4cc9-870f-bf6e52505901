import { <PERSON><PERSON><PERSON>cyPipe } from '@angular/common';
import { Component, computed, input } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { LiabilityType, OwnedProperty } from '@rocket-logic/rl-xp-bff-models';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { AllLiabilityGroup } from '../../services/entity-state/liability-state/form-types';
import { pascalCaseSplit } from '../../util/formatting-helpers';
import { PascalCaseSplitPipe } from '../../util/pascal-case-split.pipe';

@Component({
  selector: 'app-liability',
  standalone: true,
  imports: [
    MatCardModule,
    RktTagEnterpriseModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    C<PERSON><PERSON>cyPipe,
    PascalCaseSplitPipe,
  ],
  templateUrl: './liability.component.html',
  styleUrl: './liability.component.scss',
})
export class LiabilityComponent {
  formGroup = input.required<AllLiabilityGroup>();
  ownedProperties = input.required<OwnedProperty[]>();
  liabilityType = computed(() => {
    switch (this.formGroup().value.liabilityType) {
      case LiabilityType.MortgageLoan:
        return 'Mortgage';
      default:
        return pascalCaseSplit(this.formGroup().value.liabilityType ?? '');
    }
  });
}
