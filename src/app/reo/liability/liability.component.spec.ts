import { ComponentFixture, TestBed } from '@angular/core/testing';

import { FormControl, FormGroup } from '@angular/forms';
import { MockBuilder } from 'ng-mocks';
import { LiabilityComponent } from './liability.component';

describe('LiabilityComponent', () => {
  let component: LiabilityComponent;
  let fixture: ComponentFixture<LiabilityComponent>;

  beforeEach(() => MockBuilder(LiabilityComponent));
  beforeEach(() => {
    fixture = TestBed.createComponent(LiabilityComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput(
      'formGroup',
      new FormGroup({ ownedPropertyId: new FormControl() }),
    );
    fixture.componentRef.setInput('ownedProperties', []);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
