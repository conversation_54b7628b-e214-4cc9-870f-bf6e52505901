<mat-card class="rkt-Card lien-card">
  <div class="title-group">
    <span class="rkt-Label-14 rkt-FontWeight--700">{{formGroup().value.creditorName}}</span>
    <span class="rkt-Label-14 rkt-FontWeight--400">{{formGroup().value.unpaidBalance | currency}} Balance, {{formGroup().value.monthlyPaymentAmount | currency}}/mo</span>
    
  </div>
  <div class="control-group" [formGroup]="formGroup()">
    @if (formGroup().value.ownedPropertyId === null) {
      <rkt-tag-enterprise  iconName="link_off" iconPosition="right">{{liabilityType()}}</rkt-tag-enterprise>
    } @else {
      <rkt-tag-enterprise variant="success" iconName="link-outlined" iconPosition="right">{{liabilityType()}}</rkt-tag-enterprise>
    }
    <mat-form-field class="associated-property rkt-FormField" subscriptSizing="dynamic"  color="accent">
      <mat-label>Associate to</mat-label>
      <mat-select class="rkt-Input" formControlName="ownedPropertyId">
        <mat-option [value]="null">None</mat-option>
        @for (ownedProperty of ownedProperties(); track ownedProperty.id) {
          <mat-option [value]="ownedProperty.id">{{ownedProperty.address.addressLine1}}</mat-option>
        }
      </mat-select>
    </mat-form-field>
  </div>
</mat-card>