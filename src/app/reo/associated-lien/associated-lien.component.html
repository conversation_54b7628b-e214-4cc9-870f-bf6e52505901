<mat-expansion-panel #panel class="rkt-AccordionPanel" [hideToggle]="true">
  <mat-expansion-panel-header>
    <mat-panel-title class="gap-2">
      <span class="rkt-Label-14 rkt-FontWeight--700">{{ creditorName() }}</span>
      <span class="rkt-Label-14 rkt-FontWeight--400"
        >{{ unpaidBalance() | currency }} Balance, {{ monthlyPayment() | currency }}/mo</span
      >
      <rkt-tag-enterprise variant="success" iconPosition="right" iconName="link-outlined">
        {{ liabilityType() }}
      </rkt-tag-enterprise>
    </mat-panel-title>
    <mat-icon
      svgIcon="expand_more-outlined"
      color="accent"
      [class.rotate-180]="panel.expanded"
      class="transition-transform"
    />
  </mat-expansion-panel-header>

  <div [formGroup]="lienForm()" class="form-grid">
    <mat-form-field class="rkt-FormField col-span-5">
      <mat-label>Creditor</mat-label>
      <input matInput class="rkt-Input" formControlName="creditorName" />
    </mat-form-field>
    <app-formatted-number-input
      class="col-span-5"
      label="Unpaid Balance"
      prefix="$"
      [control]="lienForm().controls.unpaidBalance"
      [allowNegative]="false"
    />
    <app-client-select
      label="Debt Owner(s)"
      [control]="lienForm().controls.clientIds"
      class="col-span-10"
    />
    <app-formatted-number-input
      class="col-span-4"
      label="Monthly Lien Payment"
      prefix="$"
      [control]="lienForm().controls.monthlyPaymentAmount"
      [allowNegative]="false"
    />
    <app-formatted-number-input
      class="col-span-3"
      label="Interest Rate"
      [allowNegative]="false"
      [control]="lienForm().controls.interestRate"
      suffix="%"
      [maxLength]="3"
      [decimalLimit]="3"
    >
    </app-formatted-number-input>
    <mat-slide-toggle
      class="rkt-SlideToggle col-span-3"
      color="accent"
      formControlName="monthlyPaymentIncludesTaxesAndInsurance"
    >
      <span class="rkt-SlideToggle__label rkt-Spacing--ml8">Includes Escrow</span>
    </mat-slide-toggle>
    <mat-form-field class="rkt-FormField col-span-5">
      <mat-label>Amortization</mat-label>
      <mat-select
        class="rkt-Input"
        appNavInput
        [inputSection]="ProductMilestones.AmortizationType"
        formControlName="amortizationType"
        [shouldRegister]="shouldRegisterLienMilestone()"
      >
        <mat-option [value]="null">None</mat-option>
        @for (option of amortizationTypeOptions(); track option) {
          <mat-option [value]="option.value">{{ option.display }}</mat-option>
        }
      </mat-select>
    </mat-form-field>
    <mat-form-field class="rkt-FormField col-span-5">
      <mat-label>Mortgage Type</mat-label>
      <mat-select formControlName="mortgageType" class="rkt-Input">
        <mat-option [value]="null">None</mat-option>
        @for (option of mortgageTypeOptions; track option) {
          <mat-option [value]="option.value">{{ option.display }}</mat-option>
        }
      </mat-select>
    </mat-form-field>
    <mat-slide-toggle
      class="rkt-SlideToggle col-span-5"
      color="accent"
      #mortgageInsuranceToggle
      [checked]="mortgageInsuranceAmount()"
      (change)="onToggleChange($event)"
      [disabled]="lienForm().disabled"
    >
      <span class="rkt-SlideToggle__label rkt-Spacing--ml8">Includes Mortgage Insurance</span>
    </mat-slide-toggle>
    @if (mortgageInsuranceToggle.checked) {
      <app-formatted-number-input
        class="col-span-5"
        label="Monthly Mortgage Insurance Amount"
        prefix="$"
        [control]="lienForm().controls.mortgageInsuranceMonthlyPaymentAmount"
        [allowNegative]="false"
      />
    }
  </div>
  <div class="flex justify-end gap-2">
    <button
      mat-button
      cdkOverlayOrigin
      #trigger="cdkOverlayOrigin"
      (click)="menu.toggleMenu()"
      [class.rkt-Button--is-disabled]="isDisabled()"
      [disabled]="isDisabled()"
      class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
    >
      <mat-icon color="primary">move_up</mat-icon>
      Reassociate Lien
    </button>

    @if (!lienForm().getRawValue().isOnCreditReport) {
      <button
        mat-button
        class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
        (click)="onDeleteClick()"
        [class.rkt-Button--is-disabled]="isDisabled()"
        [disabled]="isDisabled()"
      >
        <mat-icon color="primary" svgIcon="delete-outlined"></mat-icon>
        Delete Lien
      </button>
    }
  </div>
</mat-expansion-panel>

<app-lien-menu
  #menu
  [overlayOrigin]="trigger"
  [ownedProperties]="ownedProperties()"
  (associate)="onAssociate($event)"
/>
