:host {
  ::ng-deep {
    rkt-tag-enterprise .rkt-Tag.rkt-Tag--enterprise {
      display: flex;
    }

    .rkt-AccordionPanel .mat-expansion-panel-body {
      padding: 8px 12px 16px;
    }
  }
}

.form-grid {
  display: grid;
  grid-template: auto / repeat(10, 1fr);
  column-gap: 16px;
  row-gap: 12px;
  align-items: center;

  mat-slide-toggle {
    margin-bottom: 20px;
  }
}

mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel:hover,
mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel.mat-expanded {
    background-color: var(--mat-expansion-panel-background-color);

    .mat-expansion-panel-header.mat-expansion-panel-header:hover {
        background-color: var(--mat-expansion-panel-hover-background-color);
    }
}

mat-expansion-panel .mat-expansion-panel-header {
  padding: 8px 12px;
}