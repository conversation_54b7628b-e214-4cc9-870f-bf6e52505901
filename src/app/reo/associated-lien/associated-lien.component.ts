import { OverlayModule } from '@angular/cdk/overlay';
import { C<PERSON>rencyPipe } from '@angular/common';
import { Component, computed, inject, input, OnInit, output, viewChild } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule, MatExpansionPanel } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  AmortizationType,
  LoanPurpose,
  MortgageType,
  OwnedProperty,
} from '@rocket-logic/rl-xp-bff-models';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { ClientSelectComponent } from '../../assets/client-select/client-select.component';
import { InputSectionDirective } from '../../form-nav/nav-section/input-section.directive';
import { FormattedNumberInputComponent } from '../../question-input/formatted-number-input/formatted-number-input.component';
import { AllLiabilityGroup } from '../../services/entity-state/liability-state/form-types';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import { ProductMilestoneFormInput } from '../../services/form-nav/form-nav-input.service';
import { formatAmortizationType } from '../../util/formaters/format-amortization-type';
import { formatMortgageType } from '../../util/formaters/format-mortgage-type';
import { toValueChangesSignal } from '../../util/value-changes-signal';
import { LienMenuComponent } from '../lien-menu/lien-menu.component';

@Component({
  selector: 'app-associated-lien',
  standalone: true,
  imports: [
    MatExpansionModule,
    CurrencyPipe,
    RktTagEnterpriseModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    FormattedNumberInputComponent,
    MatSlideToggleModule,
    MatSelectModule,
    MatIconModule,
    MatButtonModule,
    LienMenuComponent,
    OverlayModule,
    ClientSelectComponent,
    InputSectionDirective,
  ],
  templateUrl: './associated-lien.component.html',
  styleUrl: './associated-lien.component.scss',
})
export class AssociatedLienComponent implements OnInit {
  loanStateService = inject(LoanStateService);
  isDisabled = inject(LoanEditingState).isLoanEditingDisabled;

  lienForm = input.required<AllLiabilityGroup>();
  ownedProperties = input.required<OwnedProperty[]>();
  isSubjectProperty = input.required<boolean>();
  delete = output();
  expansionPanel = viewChild(MatExpansionPanel);

  creditorName = toValueChangesSignal<string>(this.lienForm, 'creditorName');
  unpaidBalance = toValueChangesSignal<number>(this.lienForm, 'unpaidBalance');
  monthlyPayment = toValueChangesSignal<number>(this.lienForm, 'monthlyPaymentAmount');
  liabilityType = toValueChangesSignal<string>(this.lienForm, 'liabilityType');
  mortgageInsuranceAmount = toValueChangesSignal<number>(
    this.lienForm,
    'mortgageInsuranceMonthlyPaymentAmount',
  );
  mortgageType = toValueChangesSignal<MortgageType>(this.lienForm, 'mortgageType');

  loanData = computed(() => this.loanStateService.state()?.data);
  shouldRegisterLienMilestone = computed(
    () => this.loanData()?.loanPurpose === LoanPurpose.Refinance && this.isSubjectProperty(),
  );
  filteredAmortizationTypes = computed(() => {
    const defaultTypes = [
      AmortizationType.Fixed,
      AmortizationType.AdjustableRate,
      AmortizationType.NegativeAmortization,
    ];

    switch (this.mortgageType()) {
      case MortgageType.Conventional:
        return [
          ...defaultTypes,
          AmortizationType.Step,
          AmortizationType.Balloon,
          AmortizationType.AdjustableRateBalloon,
          AmortizationType.InterestOnly,
        ];
      case MortgageType.FHA:
        return [...defaultTypes, AmortizationType.GraduatedPaymentAdjustableRateMortgage];
      default:
        return defaultTypes;
    }
  });
  amortizationTypeOptions = computed(() =>
    this.filteredAmortizationTypes().map((value) => ({
      value,
      display: formatAmortizationType(value),
    })),
  );

  readonly ProductMilestones = ProductMilestoneFormInput;
  readonly mortgageTypeOptions = Object.values(MortgageType).map((value) => ({
    value,
    display: formatMortgageType(value),
  }));

  ngOnInit() {
    if (this.lienForm().value.id === null) {
      // Open the expansion panel if this is a new lien
      this.expansionPanel()?.open();
    }
  }

  onAssociate(ownedPropertyId?: string | null) {
    const ownedPropertyIdControl = this.lienForm().controls.ownedPropertyId;
    ownedPropertyIdControl.markAsDirty();
    ownedPropertyIdControl.setValue(ownedPropertyId ?? null);
  }

  onToggleChange(toggleChange: MatSlideToggleChange) {
    if (!toggleChange.checked) {
      const mortgageInsuranceAmountControl =
        this.lienForm().controls.mortgageInsuranceMonthlyPaymentAmount;
      mortgageInsuranceAmountControl.markAsDirty();
      mortgageInsuranceAmountControl.setValue(null);
    }
  }

  onDeleteClick() {
    this.delete.emit();
  }
}
