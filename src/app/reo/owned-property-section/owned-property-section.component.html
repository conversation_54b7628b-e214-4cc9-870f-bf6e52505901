<div class="flex flex-col gap-4">
  <div class="flex items-center gap-2">
    <mat-icon svgIcon="house-two_tone" color="accent"></mat-icon>
    <span class="rkt-Label-16 rkt-FontWeight--700">{{ address() | address }}</span>

    @if (isSubjectProperty()) {
      <rkt-tag-enterprise variant="info">Subject Property</rkt-tag-enterprise>
    }
    @if (isFreeAndClear()) {
      <rkt-tag-enterprise variant="info">Free & Clear</rkt-tag-enterprise>
    }
    @if (isCurrentResidence()) {
      <rkt-tag-enterprise variant="info">Current Residence</rkt-tag-enterprise>
    }
  </div>
  <app-owned-property-detail
    [formGroup]="formGroup()"
    (delete)="onDelete()"
  ></app-owned-property-detail>
  <div class="flex justify-between items-center">
    <div class="rkt-Label-14">Associated Liens: {{ associatedLienForms().length }}</div>
    <button
      mat-button
      class="rkt-Button rkt-Button--large rkt-Button--tertiary rkt-Button--has-icon"
      color="accent"
      (click)="onAddLien()"
      [class.rkt-Button--is-disabled]="isUnsavedProperty() || isDisabled()"
      [disabled]="isUnsavedProperty() || isDisabled()"
    >
      <mat-icon class="rkt-Icon" color="primary" svgIcon="add_circle-outlined"></mat-icon>
      Add Lien
    </button>
  </div>
  @for (lienFormMap of associatedLienForms(); track lienFormMap[0]) {
    <app-associated-lien
      [lienForm]="lienFormMap[1]"
      [ownedProperties]="otherOwnedProperties()"
      (delete)="onDeleteLien(lienFormMap[0])"
      [isSubjectProperty]="isSubjectProperty()"
    />
  }
</div>
