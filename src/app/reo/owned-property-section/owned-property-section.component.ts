import { Component, computed, inject, input, output } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { Address, LiabilityType, OwnedProperty } from '@rocket-logic/rl-xp-bff-models';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { combineLatestWith, map } from 'rxjs';
import { AllLiabilityGroup } from '../../services/entity-state/liability-state/form-types';
import {
  LiabilityFormRef,
  LiabilityFormService,
} from '../../services/entity-state/liability-state/liability-form.service';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { OwnedPropertyGroup } from '../../services/entity-state/owned-property-state/form-types';
import { AddressPipe } from '../../util/address.pipe';
import { toValueChangesSignal } from '../../util/value-changes-signal';
import { AssociatedLienComponent } from '../associated-lien/associated-lien.component';
import { OwnedPropertyDetailComponent } from '../owned-property-detail/owned-property-detail.component';

@Component({
  selector: 'app-owned-property-section',
  standalone: true,
  imports: [
    MatIconModule,
    AddressPipe,
    RktTagEnterpriseModule,
    AssociatedLienComponent,
    OwnedPropertyDetailComponent,
    MatButtonModule,
  ],
  templateUrl: './owned-property-section.component.html',
  styleUrl: './owned-property-section.component.scss',
})
export class OwnedPropertySectionComponent {
  liabilityFormService = inject(LiabilityFormService);
  liabilityFormRef = inject(LiabilityFormRef);
  formGroup = input.required<OwnedPropertyGroup>();
  ownedProperties = input.required<OwnedProperty[]>();
  delete = output();

  ownedPropertyId = toValueChangesSignal<string>(this.formGroup, 'id');
  ownedPropertyId$ = toObservable(this.ownedPropertyId);
  isUnsavedProperty = computed(() => !this.ownedPropertyId());
  address = toValueChangesSignal<Address>(this.formGroup, 'address');
  isSubjectProperty = toValueChangesSignal<boolean>(this.formGroup, 'isSubjectProperty');
  isFreeAndClear = toValueChangesSignal(this.formGroup, 'isOwnedFreeAndClear');
  isCurrentResidence = toValueChangesSignal(this.formGroup, 'isCurrentResidence');
  liabilityIds = toValueChangesSignal<string[]>(this.formGroup, 'liabilityIds');
  isDisabled = inject(LoanEditingState).isLoanEditingDisabled;

  otherOwnedProperties = computed(() => {
    const id = this.formGroup().value.id;
    return this.ownedProperties().filter((property) => property.id !== id);
  });

  onAddLien() {
    this.liabilityFormService.addLiability(LiabilityType.MortgageLoan, this.ownedPropertyId());
  }

  onDeleteLien(liabilityKey: string) {
    this.liabilityFormService.deleteLiability(liabilityKey);
  }

  associatedLienForms = toSignal(
    this.liabilityFormRef.formMapChanges$.pipe(
      combineLatestWith(this.ownedPropertyId$),
      map(([liabilityFormGroups, ownedPropertyId]) => {
        return liabilityFormGroups
          .filter(
            ([_, liabilityFormGroup]) =>
              liabilityFormGroup.value.ownedPropertyId === ownedPropertyId,
          )
          .sort(([, a], [, b]) => this.sortByLienPosition(a, b));
      }),
    ),
    {
      initialValue: [],
    },
  );

  onDelete() {
    this.delete.emit();
  }

  private sortByLienPosition(a: AllLiabilityGroup, b: AllLiabilityGroup): number {
    const aPosition = a.getRawValue().lienInformation.lienPosition;
    const bPosition = b.getRawValue().lienInformation.lienPosition;

    if (aPosition != null && bPosition != null) {
      return aPosition - bPosition;
    }

    return 0;
  }
}
