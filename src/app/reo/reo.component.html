<app-form-section [title]="'REO'" [isCollapsible]="false" [formSection]="FormSection.REO">
  @if (!isFetching()) {
    @if (unassociatedLiabilityForms()?.length ?? 0 > 0) {
      <div class="liability-group">
        @for (liabilityFormMap of unassociatedLiabilityForms(); track liabilityFormMap[0]) {
          <app-liability [formGroup]="liabilityFormMap[1]" [ownedProperties]="ownedProperties()" />
        }
      </div>
    }
    @for (propertyForm of ownedPropertyMapEntries(); track propertyForm[0]) {
      <app-owned-property-section
        [formGroup]="propertyForm[1]"
        [ownedProperties]="ownedProperties()"
        (delete)="onDeleteProperty(propertyForm[0])"
        class="pb-3"
      />
    }
    <button
      mat-button
      class="rkt-Button rkt-Button--large rkt-Button--tertiary rkt-Button--has-icon self-start mt-3"
      color="accent"
      (click)="onAddProperty()"
      [disabled]="isDisabled()"
      [class.rkt-Button--is-disabled]="isDisabled()"
      data-testid="add-property-button"
    >
      <mat-icon class="rkt-Icon" color="primary" svgIcon="add_circle-outlined"></mat-icon>
      Add Property
    </button>
  } @else {
    <app-reo-skeleton />
  }
</app-form-section>
