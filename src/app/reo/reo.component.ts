import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { map } from 'rxjs';
import { FormSectionComponent } from '../form-section/form-section.component';
import {
  LiabilityFormRef,
  LiabilityFormService,
} from '../services/entity-state/liability-state/liability-form.service';
import { LiabilityStateService } from '../services/entity-state/liability-state/liability-state.service';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import { OwnedPropertyFormService } from '../services/entity-state/owned-property-state/owned-property-form.service';
import { OwnedPropertyStateService } from '../services/entity-state/owned-property-state/owned-property-state.service';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { AssociatedLienComponent } from './associated-lien/associated-lien.component';
import { LiabilityComponent } from './liability/liability.component';
import { OwnedPropertySectionComponent } from './owned-property-section/owned-property-section.component';
import { ReoSkeletonComponent } from './reo-skeleton/reo-skeleton.component';

@Component({
  selector: 'app-reo',
  standalone: true,
  imports: [
    FormSectionComponent,
    LiabilityComponent,
    ReoSkeletonComponent,
    AssociatedLienComponent,
    OwnedPropertySectionComponent,
    MatButtonModule,
    MatIconModule,
  ],
  templateUrl: './reo.component.html',
  styleUrl: './reo.component.scss',
})
export class ReoComponent {
  liabilityFormRef = inject(LiabilityFormRef);
  liabilityFormService = inject(LiabilityFormService);
  ownedPropertyFormService = inject(OwnedPropertyFormService);
  liabilityStateService = inject(LiabilityStateService);
  ownedPropertyStateService = inject(OwnedPropertyStateService);
  formNavSectionService = inject(FormNavSectionService);
  isDisabled = toSignal(inject(LoanEditingState).isLoanEditingDisabled$);
  readonly FormSection = FormSection;

  unassociatedLiabilityForms = toSignal(
    this.liabilityFormService.unassociatedLiabilities$.pipe(
      map((liabilities) => {
        return liabilities.filter(
          ([, liabilityFormGroup]) => liabilityFormGroup?.getRawValue()?.ownedPropertyId == null,
        );
      }),
    ),
  );

  ownedPropertyMapEntries = computed(() =>
    Array.from(this.ownedPropertyFormService.entityFormMap().entries()).sort((a, b) => {
      if (a[1].value.isSubjectProperty) {
        return -1;
      }
      if (b[1].value.isSubjectProperty) {
        return 1;
      }
      return 0;
    }),
  );

  ownedProperties = computed(() => this.ownedPropertyStateService.stateValues());

  isFetching = computed(() => {
    const { data, fetching } = this.liabilityStateService.state() ?? {};
    return fetching && !data;
  });

  onAddProperty() {
    this.ownedPropertyFormService.addOwnedProperty();
  }

  onDeleteProperty(ownedPropKey: string) {
    this.ownedPropertyFormService.deleteOwnedProperty(ownedPropKey);
  }
}
