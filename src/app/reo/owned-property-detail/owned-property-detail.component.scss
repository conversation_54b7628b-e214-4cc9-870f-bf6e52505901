:host {
  ::ng-deep {
    .rkt-AccordionPanel .mat-expansion-panel-body {
      padding: 8px 12px 16px;
    }
  }
}

mat-expansion-panel .mat-expansion-panel-header {
  padding: 8px 12px;
}

mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel.rkt-AccordionPanel--enterprise:hover,
mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel.rkt-AccordionPanel--enterprise.mat-expanded {
    background-color: var(--mat-expansion-panel-background-color);

    .mat-expansion-panel-header.mat-expansion-panel-header:hover {
        background-color: var(--mat-expansion-panel-hover-background-color);
    }
}

.detail-grid {
  column-gap: 1rem;
  row-gap: 12px;
}