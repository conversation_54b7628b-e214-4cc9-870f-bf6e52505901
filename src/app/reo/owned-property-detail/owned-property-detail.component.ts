import { Component, computed, inject, input, OnInit, output, viewChild } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule, MatExpansionPanel } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { AddressComponent } from '../../address/address.component';
import { ClientSelectComponent } from '../../assets/client-select/client-select.component';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { OwnedPropertyGroup } from '../../services/entity-state/owned-property-state/form-types';
import { OCCUPANCY } from '../../util/occupancy';
import { PROPERTY } from '../../util/property';

@Component({
  selector: 'app-owned-property-detail',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatExpansionModule,
    MatIconModule,
    AddressComponent,
    MatSlideToggleModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatButtonModule,
    ClientSelectComponent,
  ],
  templateUrl: './owned-property-detail.component.html',
  styleUrl: './owned-property-detail.component.scss',
})
export class OwnedPropertyDetailComponent implements OnInit {
  formGroup = input.required<OwnedPropertyGroup>();
  isDisabled = inject(LoanEditingState).isLoanEditingDisabled;

  delete = output();

  expansionPanel = viewChild(MatExpansionPanel);
  addressForm = computed(() => this.formGroup().controls.address);

  readonly propertyTypeOptions = PROPERTY;
  readonly occupancyTypeOptions = OCCUPANCY;

  ngOnInit(): void {
    if (!this.formGroup().getRawValue().id) {
      this.expansionPanel()?.open();
    }
  }

  onDeleteClick() {
    this.delete.emit();
  }
}
