<mat-expansion-panel class="rkt-AccordionPanel rkt-AccordionPanel--enterprise" [hideToggle]="true" #panel>
  <mat-expansion-panel-header>
    <mat-panel-title>
      <span class="rkt-Label-14">Property Details</span>
    </mat-panel-title>
    <mat-icon
      svgIcon="expand_more-outlined"
      color="accent"
      [class.rotate-180]="panel.expanded"
      class="transition-transform"
    />
  </mat-expansion-panel-header>
  <div class="grid grid-cols-10 detail-grid">
    <app-address [addressForm]="addressForm()" [showCounty]="true" class="col-span-10" />
    <ng-container [formGroup]="formGroup()">
      <mat-form-field class="rkt-FormField col-span-5">
        <mat-label>Property Type</mat-label>
        <mat-select class="rkt-Input" formControlName="propertyType" data-synthetic-monitor-id="propertyType">
          <mat-option [value]="null">None</mat-option>
          @for (propertyType of propertyTypeOptions; track propertyType) {
            <mat-option
            attr.data-synthetic-monitor-id="{{ 'property-Type-option_' + propertyType.value }}"
            [value]="propertyType.value">{{ propertyType.display }}
           </mat-option>
          }
        </mat-select>
      </mat-form-field>
      <mat-form-field class="rkt-FormField col-span-5">
        <mat-label>Occupancy Type</mat-label>
        <mat-select class="rkt-Input" formControlName="occupancyType" data-synthetic-monitor-id="Occupancy Type">
          <mat-option [value]="null">None</mat-option>
          @for (occupancyType of occupancyTypeOptions; track occupancyType) {
            <mat-option
            attr.data-synthetic-monitor-id="{{ 'occupancyTypeOptions' + occupancyType.value }}"
            [value]="occupancyType.value">{{ occupancyType.display }}</mat-option>
          }
        </mat-select>
      </mat-form-field>
      <app-client-select [control]="formGroup().controls.ownerClientIds" label="Title Holder(s)" class="col-span-10" />
      <mat-slide-toggle class="rkt-SlideToggle col-span-5" color="accent" formControlName="isOwnedFreeAndClear">
        <span class="rkt-SlideToggle__label rkt-Spacing--ml8">Free and Clear</span>
      </mat-slide-toggle>
      <div class="col-span-5 flex justify-end gap-2">
        @if (!(formGroup().getRawValue().isSubjectProperty || formGroup().getRawValue().isCurrentResidence)) {
          <button
          mat-button class="rkt-Button rkt-Button--tertiary rkt-Button--has-icon"
          (click)="onDeleteClick()"
          [disabled]="isDisabled()"
          [class.rkt-Button--is-disabled]="isDisabled()"
          >
            <mat-icon color="primary" svgIcon="delete-outlined"></mat-icon>
            Delete Property
          </button>
        }
      </div>
    </ng-container>
  </div>
</mat-expansion-panel>
