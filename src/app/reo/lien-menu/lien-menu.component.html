
<ng-template
    cdkConnectedOverlay
    [cdkConnectedOverlayOrigin]="overlayOrigin()"
    [cdkConnectedOverlayOpen]="isMenuOpen()"
    (overlayOutsideClick)="toggleMenu()"
>
    <mat-card class="rkt-Card lien-menu">
        <mat-radio-group (change)="onSelect($event)">
            @for (property of ownedProperties(); track property.id) {
              <mat-radio-button class="rkt-Radio" [value]="property.id">Assign to {{property.address.addressLine1}}</mat-radio-button>
            }
            <mat-radio-button class="rkt-Radio" [value]="unassociateValue">Unassociate</mat-radio-button>
        </mat-radio-group>
    </mat-card>
</ng-template>