import { CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';
import { Component, input, output, signal } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatRadioChange, MatRadioModule } from '@angular/material/radio';
import { OwnedProperty } from '@rocket-logic/rl-xp-bff-models';

@Component({
  selector: 'app-lien-menu',
  standalone: true,
  imports: [OverlayModule, MatCardModule, MatRadioModule],
  templateUrl: './lien-menu.component.html',
  styleUrl: './lien-menu.component.scss',
})
export class LienMenuComponent {
  overlayOrigin = input.required<CdkOverlayOrigin>();
  ownedProperties = input.required<OwnedProperty[]>();
  associate = output<string | null>();

  readonly unassociateValue = 'unassociate';
  isMenuOpen = signal(false);

  toggleMenu() {
    this.isMenuOpen.update((isOpen) => !isOpen);
  }

  onSelect(event: MatRadioChange) {
    if (event.value === this.unassociateValue) {
      this.associate.emit(null);
    } else {
      this.associate.emit(event.value);
    }
    this.isMenuOpen.set(false);
  }
}
