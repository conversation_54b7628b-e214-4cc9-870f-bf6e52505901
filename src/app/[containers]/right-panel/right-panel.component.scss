:host {
  header {
    color: var(--rlxp-section-header-color);
    padding: 0 8px 0 16px;
  }

  .collapse-button {
    position: absolute;
    top: 1rem;
    left: -2rem;
    display: none;
  }

  .button-container:hover + .collapse-button,
  .collapse-button:hover {
    display: flex;
  }

  .button-container {
    padding: 12px 12px 0;

    &--is-collapsed {
      padding: 6px;
    }

    button.header-action.mat-mdc-icon-button {
      border-radius: 12px;
      padding: 12px;
      --mdc-icon-button-state-layer-size: 48px;

      ::ng-deep {
        .mat-mdc-button-persistent-ripple {
          border-radius: 12px;
        }

        mat-icon:not(.mat-icon-no-color) {
          color: var(--mat-icon-color);
        }
      }
    }

    button.header-action.mat-mdc-icon-button.active {
      ::ng-deep .mat-mdc-button-persistent-ripple::before {
        opacity: var(--mat-icon-button-pressed-state-layer-opacity);
      }
    }
  }

}

mat-divider {
  padding-top: 12px;
}

mat-card.rkt-Card {
  padding: 0;
  display: flex;
  gap: 12px;
  width: 400px;

  &.collapsed {
    width: 60px;
  }
}
