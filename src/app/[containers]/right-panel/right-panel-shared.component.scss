@use '@rocketcentral/rocket-design-system-styles/base/typography' as rkt-typography;
@use '@rocketcentral/rocket-design-system-styles/web/scss/settings' as rkt-settings;

@mixin right-panel-shared-tab {
  .mat-mdc-tab:not(.mdc-tab--stacked) {
    padding: 8px 0;
    height: unset;
  }

  .rkt-Tabs .mat-tab-label-active,
  .rkt-Tabs .rkt-Tabs__tab.Mui-selected .rkt-Tabs__label,
  .rkt-Tabs .mat-mdc-tab.mdc-tab--active .mdc-tab__text-label {
    @extend %rkt-Label-14;
    font-weight: 500;
    color: rkt-settings.$rkt-tabs-label-color-active;
  }

  .rkt-Tabs .mat-mdc-tab .mdc-tab__text-label {
    @extend %rkt-Label-14;
    font-weight: 400;
  }

  .rkt-Tabs__tab,
  .rkt-Tabs__tab.mdc-tab {
    min-width: unset;
  }

  .mat-mdc-tab.mdc-tab {
    flex-grow: 1;
  }

  .rkt-Tag__text {
    @extend %rkt-Label-14;
    font-weight: 500;
  }
}
