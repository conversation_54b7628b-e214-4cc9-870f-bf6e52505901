.rkt-Card {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  padding: 0;
  padding-left: 10px;
  max-height: calc(100vh - 1rem - 50px - 8px);
  overflow-y: auto;
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-10%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutToLeft {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-10%);
    opacity: 0;
  }
}

:host {
  animation: slideInFromLeft 0.1s ease-out forwards;
}

:host.hidden {
  animation: slideOutToLeft 0.1s ease-out forwards;
}
