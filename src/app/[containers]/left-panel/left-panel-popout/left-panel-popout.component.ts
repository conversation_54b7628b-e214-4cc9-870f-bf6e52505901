import { CdkPortalOutlet, ComponentPortal, ComponentType } from '@angular/cdk/portal';
import { Component } from '@angular/core';
import { MatCardModule } from '@angular/material/card';

@Component({
  selector: 'app-left-panel-popout',
  standalone: true,
  imports: [
    MatCardModule,
    CdkPortalOutlet
  ],
  templateUrl: './left-panel-popout.component.html',
  styleUrl: './left-panel-popout.component.scss'
})
export class LeftPanelPopoutComponent<T> {
  portal!: ComponentPortal<T>;

  attachComponent(component: ComponentType<T>): ComponentPortal<T> {
    this.portal = new ComponentPortal<T>(component);
    return this.portal;
  }
}
