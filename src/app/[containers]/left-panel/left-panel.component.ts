import { Overlay, OverlayContainer, OverlayModule, OverlayRef } from '@angular/cdk/overlay';
import { ComponentPortal, ComponentType } from '@angular/cdk/portal';
import {
  Component,
  ComponentRef,
  effect,
  inject, OnDestroy,
  Renderer2,
  signal
} from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { LeftPanelPopoutComponent } from './left-panel-popout/left-panel-popout.component';

@Component({
  selector: 'app-left-panel',
  templateUrl: './left-panel.component.html',
  styleUrls: ['./left-panel.component.scss'],
  standalone: true,
  imports: [OverlayModule, MatCardModule],
})
export class LeftPanelComponent<T> implements OnDestroy {
  overlay = inject(Overlay);
  private readonly overlayContainer = inject(OverlayContainer);
  private readonly renderer = inject(Renderer2);

  static readonly OVERLAY_CONTAINER_CLASS = 'left-panel-overlay-container';

  popoutContent = signal<{
    component: ComponentType<T> | null;
    triggeringElement: EventTarget | null;
  }>({ component: null, triggeringElement: null });
  activeOverlays: OverlayRef[] = [];
  currentOverlayRef: OverlayRef | null = null;
  containerRef: ComponentRef<LeftPanelPopoutComponent<T>> | null = null;
  componentRef: ComponentPortal<T> | null = null;
  overlayEffect = effect(() => {
    const { component, triggeringElement } = this.popoutContent();

    if (component && triggeringElement) {
      // Close all active overlays
      this.closeAllOverlays();

      // Create and configure a new OverlayRef
      this.currentOverlayRef = this.overlay.create({
        hasBackdrop: true,
        backdropClass: 'cdk-overlay-transparent-backdrop',
        positionStrategy: this.overlay
          .position()
          .flexibleConnectedTo(<Element>triggeringElement)
          .withPositions([
            {
              originX: 'end',
              originY: 'top',
              overlayX: 'start',
              overlayY: 'top',
              offsetY: -8,
            },
            {
              originX: 'end',
              originY: 'bottom',
              overlayX: 'start',
              overlayY: 'bottom',
              offsetY: 8,
            },
          ])
          .withDefaultOffsetX(-4)
          .withDefaultOffsetY(0)
          .withGrowAfterOpen(false)
          .withLockedPosition(false),
      });

      // Listen for backdrop clicks to close the overlay
      this.currentOverlayRef.backdropClick().subscribe(() => this.hidePopout());

      // Track the overlay in the active overlays array
      this.activeOverlays.push(this.currentOverlayRef);

      // Attach the container and dynamic component
      this.containerRef = this.currentOverlayRef.attach(
        new ComponentPortal<LeftPanelPopoutComponent<T>>(LeftPanelPopoutComponent),
      );
      this.componentRef = this.containerRef?.instance.attachComponent(component) ?? null;

      // Add a class to the overlay container to lower the z-index
      this.setOverlayContainerClass();
    } else {
      // Detach the overlay if no component is set
      this.detachOverlay();
    }
  });

  private closeAllOverlays(): void {
    this.activeOverlays.forEach((overlayRef) => overlayRef.detach());
    this.activeOverlays = [];
  }

  ngOnDestroy() {
    this.detachOverlay();
  }

  showPopout(component: ComponentType<T>, triggeringElement: EventTarget | null): void {
    this.popoutContent.set({ component, triggeringElement });
  }

  hidePopout(): void {
    this.popoutContent.set({ component: null, triggeringElement: null });
  }

  public togglePopout(component: ComponentType<T>, triggeringElement: EventTarget | null): void {
    if (this.popoutContent().component === component) {
      this.hidePopout();
    } else {
      this.showPopout(component, triggeringElement);
    }
  }

  private setOverlayContainerClass() {
    const element = this.overlayContainer.getContainerElement();
    this.renderer.addClass(element, LeftPanelComponent.OVERLAY_CONTAINER_CLASS);
  }

  private removeOverlayContainerClass() {
    const element = this.overlayContainer.getContainerElement();
    this.renderer.removeClass(element, LeftPanelComponent.OVERLAY_CONTAINER_CLASS);
  }

  private detachOverlay() {
    this.currentOverlayRef?.detach();
    this.currentOverlayRef = null;
    this.removeOverlayContainerClass();
  }
}
