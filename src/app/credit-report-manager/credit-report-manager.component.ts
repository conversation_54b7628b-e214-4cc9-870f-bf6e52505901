import { Component } from '@angular/core';
import { MatTabsModule } from '@angular/material/tabs';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { CreditHistoryComponent } from './credit-history/credit-history.component';
import { CreditRequestsComponent } from './credit-requests/credit-requests.component';

@Component({
  selector: 'app-credit-report-manager',
  standalone: true,
  imports: [MatTabsModule, CreditRequestsComponent, CreditHistoryComponent, RktTagEnterpriseModule],
  templateUrl: './credit-report-manager.component.html',
  styleUrl: './credit-report-manager.component.scss',
})
export class CreditReportManagerComponent {}
