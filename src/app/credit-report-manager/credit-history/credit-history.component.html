<mat-accordion multi displayMode="flat" color="accent">
  <mat-expansion-panel class="rkt-AccordionPanel rkt-AccordionPanel--enterprise" [expanded]="true">
    <mat-expansion-panel-header>
      <mat-panel-title class="rkt-AccordionPanel__header-title">
        Inactive Credit Reports
      </mat-panel-title>
    </mat-expansion-panel-header>
    @if (isFetchingReports()) {
      <div class="no-reports">
        <mat-spinner
          class="rkt-ProgressSpinner rkt-ProgressSpinner--enterprise"
          diameter="90"
        ></mat-spinner>
      </div>
    } @else if (inactiveReports().length === 0) {
      <div class="no-reports">
        <p class="rkt-Label-14 rkt-Color--gray-600">No Inactive Credit Reports Available</p>
      </div>
    } @else {
      <table mat-table class="rkt-Table rkt-Table--striped-odd" [dataSource]="inactiveReports()">
        <ng-container matColumnDef="date">
          <th mat-header-cell *matHeaderCellDef class="rkt-Caption-12">Date</th>
          <td mat-cell class="rkt-Caption-12" *matCellDef="let creditReport">
            {{ creditReport.firstIssuedOn | date: 'MM/dd/yyyy h:mm a' }}
          </td>
        </ng-container>
        <ng-container matColumnDef="report">
          <th mat-header-cell *matHeaderCellDef class="rkt-Caption-12">Inactive Reports</th>
          <td mat-cell *matCellDef="let creditReport">
            <a
              rktLinkEnterprise
              class="rkt-Caption-12 amp-mask"
              target="_blank"
              [href]="creditReport.url"
            >
              {{ creditReport | creditReport }}
            </a>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="reportColumns"></tr>
        <tr mat-row *matRowDef="let row; let even = even; columns: reportColumns"></tr>
      </table>
    }
  </mat-expansion-panel>
</mat-accordion>

<mat-accordion multi displayMode="flat" color="accent">
  <mat-expansion-panel class="rkt-AccordionPanel rkt-AccordionPanel--enterprise" [expanded]="true">
    <mat-expansion-panel-header>
      <mat-panel-title class="rkt-AccordionPanel__header-title">
        Credit Request History
      </mat-panel-title>
    </mat-expansion-panel-header>
    @if (isFetchingOrders()) {
      <div class="no-reports">
        <mat-spinner
          class="rkt-ProgressSpinner rkt-ProgressSpinner--enterprise"
          diameter="90"
        ></mat-spinner>
      </div>
    } @else if (creditOrders() === undefined || creditOrders().length === 0) {
      <div class="no-reports">
        <p class="rkt-Label-14 rkt-Color--gray-600">No Credit Request History Available</p>
      </div>
    } @else {
      <table mat-table class="rkt-Table rkt-Table--striped-odd" [dataSource]="creditOrders()">
        <ng-container matColumnDef="date">
          <th mat-header-cell *matHeaderCellDef class="rkt-Caption-12">Date</th>
          <td mat-cell class="rkt-Table__cell" id="date-cell" *matCellDef="let creditOrder">
            {{ creditOrder.firstIssuedOn | date: 'MM/dd/yyyy h:mm a' }}
          </td>
        </ng-container>
        <ng-container matColumnDef="clients">
          <th mat-header-cell *matHeaderCellDef class="rkt-Caption-12">Clients</th>
          <td mat-cell class="rkt-Table__cell amp-mask" *matCellDef="let creditOrder">
            @for (client of creditOrder.clientInfo; track client) {
              {{ client | clientName: true }}
            }
          </td>
        </ng-container>
        <ng-container matColumnDef="type">
          <th mat-header-cell *matHeaderCellDef class="rkt-Caption-12">Type</th>
          <td mat-cell class="rkt-Table__cell" *matCellDef="let creditOrder">
            {{ creditOrder.type }}
          </td>
        </ng-container>
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef class="rkt-Caption-12">Status</th>
          <td
            mat-cell
            class="rkt-Table__cell"
            [ngClass]="getOrderStatusClass(creditOrder.status)"
            *matCellDef="let creditOrder"
          >
            {{ creditOrder.status }}
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="orderColumns"></tr>
        <tr mat-row *matRowDef="let row; let even = even; columns: orderColumns"></tr>
      </table>
    }
  </mat-expansion-panel>
</mat-accordion>
