import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { CreditService } from '../../services/credit/credit.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { CreditHistoryComponent } from './credit-history.component';

describe('CreditHistoryComponent', () => {
  let component: CreditHistoryComponent;
  let fixture: ComponentFixture<CreditHistoryComponent>;

  beforeEach(() =>
    MockBuilder(CreditHistoryComponent)
      .mock(CreditService, {
        creditReports$: NEVER,
        creditOrders$: NEVER,
      })
      .mock(ClientStateService, { state$: NEVER }),
  );
  beforeEach(() => {
    fixture = TestBed.createComponent(CreditHistoryComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
