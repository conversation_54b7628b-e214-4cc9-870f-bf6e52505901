:host {
  display: flex;
  padding: 20px 16px;
  flex-direction: column;
  gap: 16px;
}

mat-expansion-panel {
  ::ng-deep .mat-expansion-panel-body {
    padding: 0 0 16px 0;
  }
}

.no-reports {
  display: flex;
  justify-content: center;
  align-items: center;
}

td.mat-mdc-cell.rkt-Caption-12,
td.mat-mdc-cell.rkt-Table__cell {
  padding: 14px 16px;
  line-height: 1rem;
}

td.mat-mdc-cell.rkt-Table__cell {
  font-size: 12px;
  vertical-align: top;
}

#date-cell {
  padding: 14px 0px 14px 14px;
}

.mat-column-date {
  width: 83px;
}

.completed {
  color: var(--rlxp-green-600) !important;
}

.failed {
  color: var(--rlxp-red-600) !important;
}

mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel,
mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel:hover,
mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel.mat-expanded {
  background-color: var(--mat-expansion-panel-background-color);

  .mat-expansion-panel-header.mat-expansion-panel-header:hover {
    background-color: var(--mat-expansion-panel-hover-background-color);
  }
}

.mat-expansion-panel-header.mat-expanded {
  background-color: var(--mat-expansion-panel-hover-background-color);
  &:hover,
  &:focus {
    background-color: var(--mat-expansion-panel-hover-background-color);
  }
}

:host-context(.rkt-DarkMode) {
  mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel,
  mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel:hover,
  mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel.mat-expanded {
    background-color: var(--rlxp-accordion-outlined-background-color);

    .mat-expansion-panel-header.mat-expansion-panel-header:hover {
      background-color: var(--rlxp-accordion-outlined-background-color);
    }
  }

  .mat-expansion-panel-header.mat-expanded {
    background-color: var(--rlxp-accordion-outlined-background-color);
    &:hover,
    &:focus {
      background-color: var(--rlxp-accordion-outlined-background-color);
    }
  }
}
