import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MatTabsModule } from '@angular/material/tabs';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import { MockBuilder } from 'ng-mocks';
import { CreditReportManagerComponent } from './credit-report-manager.component';

describe('CreditReportManagerComponent', () => {
  let component: CreditReportManagerComponent;
  let fixture: ComponentFixture<CreditReportManagerComponent>;

  beforeEach(() =>
    MockBuilder(CreditReportManagerComponent).keep(MatTabsModule).keep(NoopAnimationsModule),
  );
  beforeEach(() => {
    fixture = TestBed.createComponent(CreditReportManagerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
