import { DatePipe } from '@angular/common';
import { Component, computed, inject, input, signal } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDividerModule } from '@angular/material/divider';
import { CreditReport } from '@rocket-logic/rl-xp-bff-models';
import {
  RktLinkEnterpriseModule,
  RktTagEnterpriseModule,
} from '@rocketcentral/rocket-design-system-enterprise-angular';
import { ClientActivationHandlerService } from '../../services/entity-state/client-state/client-activation-handler.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { ClientNamePipe } from '../../util/client-name.pipe';
import { getCreditReportUrl } from '../../util/get-credit-report-url';

@Component({
  selector: 'app-credit-report',
  standalone: true,
  imports: [
    MatCardModule,
    RktTagEnterpriseModule,
    DatePipe,
    ClientNamePipe,
    MatButtonModule,
    MatDividerModule,
    RktLinkEnterpriseModule,
  ],
  templateUrl: './credit-report.component.html',
  styleUrl: './credit-report.component.scss',
})
export class CreditReportComponent {
  clientStateService = inject(ClientStateService);
  clientActivationHandlerService = inject(ClientActivationHandlerService);

  creditReportData = input.required<{ creditReport: CreditReport; isInvalid: boolean }>();

  showAdditionalCrmDetails = signal(false);

  clients = computed(() => {
    const clients = this.clientStateService.stateValues();
    const deactivatedClients = this.clientActivationHandlerService.deactivatedClients() ?? [];
    const allClients = [...clients, ...deactivatedClients];
    return allClients.filter((client) => {
      return this.creditReportData().creditReport.clients.some(
        (creditClient) => creditClient.clientId === client.id,
      );
    });
  });

  ficoScore = computed(() => {
    if (this.creditReportData().creditReport.qualifyingScore) {
      return `FICO ${this.creditReportData().creditReport.qualifyingScore}`;
    } else {
      return 'NO FICO SCORE';
    }
  });

  bureauType = computed(() => {
    switch (this.creditReportData().creditReport.repositoriesIncluded?.length) {
      case 3:
        return 'Tri-Merge';
      case 2:
        return 'Bi-Merge';
      case 1:
        return 'Single';
      default:
        return 'Unknown';
    }
  });

  openCreditReport() {
    window.open(getCreditReportUrl(this.creditReportData().creditReport), '_blank');
  }

  toggleAdditionalCrmDetails() {
    this.showAdditionalCrmDetails.set(!this.showAdditionalCrmDetails());
  }
}
