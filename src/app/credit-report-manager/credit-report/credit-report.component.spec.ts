import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { MockBuilder } from 'ng-mocks';
import { ClientActivationHandlerService } from '../../services/entity-state/client-state/client-activation-handler.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { CreditReportComponent } from './credit-report.component';

describe('CreditReportComponent', () => {
  let component: CreditReportComponent;
  let fixture: ComponentFixture<CreditReportComponent>;

  beforeEach(() =>
    MockBuilder(CreditReportComponent)
      .mock(ClientStateService, {
        state: signal(undefined),
        stateValues: signal([]),
      })
      .mock(ClientActivationHandlerService, { deactivatedClients: signal(undefined) }),
  );
  beforeEach(() => {
    fixture = TestBed.createComponent(CreditReportComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('creditReportData', {
      creditReport: { qualyfingScore: 2, clients: [] },
      isInvalid: true,
    });
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
