.info-container {
  display: grid;
  grid-template: auto / 1fr 1fr;
  gap: 8px;
}

.client-container {
  line-height: 1rem;
  display: flex;
  flex-direction: column;
}

.mat-mdc-card-header {
  padding: 0 0 12px 0;
  align-items: center;
  justify-content: space-between;
}

.mat-mdc-card-content {
  padding: 0;
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 12px;
}

a.show-more-link {
  align-self: center;
}

.rkt-Card:not(
    .rkt-AccordionPanel--on-dark .rkt-Card,
    .rkt-Card--on-dark,
    .rkt-Tabs--on-dark .rkt-Card,
    .rkt-Toggle--on-dark .rkt-Card
  ) {
  background-color: var(--rlxp-card-credit-report-background-color);
}

mat-card {
  border: 1px solid var(--rlxp-gray-200);
}

#show-more {
  display: flex;
  justify-content: center;
}

:host {
  ::ng-deep {
    .rkt-Tag--enterprise.rkt-Tag--success {
      border: 1px solid var(--rlxp-green-700);
    }

    .rkt-Tag--enterprise.rkt-Tag--warn {
      border: 1px solid var(--rlxp-rose-500);
    }
  }
}
