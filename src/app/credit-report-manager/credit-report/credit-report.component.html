<mat-card class="rkt-Card rkt-Card--enterprise credit-report-card">
  <mat-card-header>
    <mat-card-title class="rkt-Card__heading amp-mask">{{ ficoScore() }}</mat-card-title>
    @if (this.creditReportData().isInvalid) {
      <rkt-tag-enterprise variant="warn">Invalid</rkt-tag-enterprise>
    } @else {
      <rkt-tag-enterprise variant="success" iconPosition="right" iconName="check-outlined"
        >Active</rkt-tag-enterprise
      >
    }
  </mat-card-header>
  <mat-card-content>
    <div class="info-container">
      <span class="rkt-Caption-12">Client(s)</span>
      <div class="client-container">
        @for (client of clients(); track client.id) {
          <span class="rkt-Caption-12 rkt-FontWeight--500 amp-mask">{{
            client | clientName: true
          }}</span>
        }
      </div>

      <span class="rkt-Caption-12">Report Date</span>
      <span class="rkt-Caption-12 rkt-FontWeight--500">{{
        this.creditReportData().creditReport.firstIssuedOn | date: 'MM/dd/yyyy'
      }}</span>

      <span class="rkt-Caption-12">Request Type</span>
      <span class="rkt-Caption-12 rkt-FontWeight--500">{{
        this.creditReportData().creditReport.creditPullType
      }}</span>

      <span class="rkt-Caption-12">Report Type</span>
      <span class="rkt-Caption-12 rkt-FontWeight--500">{{
        this.creditReportData().creditReport.clients.length > 1
          ? 'Joint Report'
          : 'Individual Report'
      }}</span>

      <span class="rkt-Caption-12">Bureau Type</span>
      <span class="rkt-Caption-12 rkt-FontWeight--500">{{ this.bureauType() }}</span>
      @if (showAdditionalCrmDetails()) {
        <span class="rkt-Caption-12">Credit Ref. #</span>
        <span class="rkt-Caption-12 rkt-FontWeight--500">{{
          this.creditReportData().creditReport.referenceNumber
        }}</span>

        <span class="rkt-Caption-12">Requested on</span>
        <span class="rkt-Caption-12 rkt-FontWeight--500">{{
          this.creditReportData().creditReport.firstIssuedOn
        }}</span>

        <span class="rkt-Caption-12">Report ID</span>
        <span class="rkt-Caption-12 rkt-FontWeight--500">{{
          this.creditReportData().creditReport.reportId
        }}</span>

        <span class="rkt-Caption-12">Vendor</span>
        <span class="rkt-Caption-12 rkt-FontWeight--500">{{
          this.creditReportData().creditReport.vendor
        }}</span>
      }
    </div>
    <mat-divider class="rkt-HorizontalDivider" />
    <button
      rktLinkEnterprise
      class="rkt-Caption-12"
      id="show-more"
      (click)="toggleAdditionalCrmDetails()"
    >
      Show {{ showAdditionalCrmDetails() ? 'Less' : 'More' }}
    </button>
    <button
      class="rkt-Button rkt-Button--secondary"
      mat-stroked-button
      color="accent"
      (click)="openCreditReport()"
    >
      View Active Credit Report
    </button>
  </mat-card-content>
</mat-card>
