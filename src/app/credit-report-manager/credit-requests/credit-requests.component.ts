import { CommonModule } from '@angular/common';
import { Component, DestroyRef, computed, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { CreditReportOrder, CreditReportOrderStatus } from '@rocket-logic/rl-xp-bff-models';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { map } from 'rxjs';
import { HugeRadioGroupComponent } from '../../huge-radio-group/huge-radio-group.component';
import { CreditAuthorizationService } from '../../services/credit/credit-authorization.service';
import { CreditService } from '../../services/credit/credit.service';
import { SelectedCreditClientService } from '../../services/credit/selected-credit-client.service';
import { DarkModeService } from '../../services/dark-mode/dark-mode.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { ClientUpdateHandlerService } from '../../services/entity-state/client-state/client-update-handler.service';
import { UserAuthorizationService } from '../../services/user-authorization/user-authorization.service';
import { clientHasOutstandingTask } from '../../util/client-has-outstanding-task';
import { CreditReportComponent } from '../credit-report/credit-report.component';

@Component({
  selector: 'app-credit-requests',
  standalone: true,
  imports: [
    CommonModule,
    MatExpansionModule,
    HugeRadioGroupComponent,
    MatButtonModule,
    MatIconModule,
    ReactiveFormsModule,
    CreditReportComponent,
    MatProgressSpinnerModule,
    RktAlertEnterpriseModule,
    MatCheckboxModule,
  ],
  templateUrl: './credit-requests.component.html',
  styleUrl: './credit-requests.component.scss',
})
export class CreditRequestsComponent {
  readonly creditService = inject(CreditService);
  readonly creditAuthService = inject(CreditAuthorizationService);
  readonly clientService = inject(ClientStateService);
  readonly destroyRef = inject(DestroyRef);
  readonly userAuthorizationService = inject(UserAuthorizationService);
  readonly clientUpdateHandler = inject(ClientUpdateHandlerService);
  readonly selectedCreditClientService = inject(SelectedCreditClientService);
  readonly darkModeService = inject(DarkModeService);

  readonly creditOrders = toSignal(this.creditService.creditOrders$);

  private sortByLastUpdated = <T>(
    items: CreditReportOrder[],
    order: 'asc' | 'desc' = 'desc',
  ): CreditReportOrder[] => {
    return items.sort((a, b) => {
      if (!a.lastUpdatedOn && !b.lastUpdatedOn) return 0;
      if (!a.lastUpdatedOn) return order === 'desc' ? 1 : -1;
      if (!b.lastUpdatedOn) return order === 'desc' ? -1 : 1;

      const dateA = new Date(a.lastUpdatedOn);
      const dateB = new Date(b.lastUpdatedOn);

      return order === 'desc'
        ? dateB.getTime() - dateA.getTime()
        : dateA.getTime() - dateB.getTime();
    });
  };

  completedOrFailedCreditOrders = toSignal(
    this.creditService.creditOrders$.pipe(
      map((state) => state.data),
      map((orders) =>
        this.sortByLastUpdated(
          orders?.filter((order) => order.orderStatus !== CreditReportOrderStatus.Processing) ?? [],
        ),
      ),
    ),
    { initialValue: [] },
  );

  isFirstCreditPullForSelectedClients = computed(() => {
    const orders = this.creditOrders()?.data;

    return !orders?.some((report) =>
      this.selectedCreditClientService
        .selectedClient()
        ?.every((selectedClient) =>
          report.clients.some((client) => client.clientId === selectedClient),
        ),
    );
  });
  clientConsentForCreditPull = signal(false);

  THIRTY_DAYS: number = 30 * 24 * 60 * 60 * 1000;

  constructor() {
    this.userAuthorizationService.userScopes$
      .pipe(
        map((user) => user['isCreditUpgrade']),
        takeUntilDestroyed(),
      )
      .subscribe((isCreditUpgrade) => {
        this.isCreditUpgrade.set(isCreditUpgrade);
      });
  }

  isBankerLicensed = toSignal(this.userAuthorizationService.isBankerLicensed$);
  isCreditUpgrade = signal(false);
  creditPullType = toSignal(this.creditService.creditPullType$);

  isFetchingReports = toSignal(
    this.creditService.creditReports$.pipe(map((state) => state.fetching === true)),
  );
  noSelection = toSignal(
    this.selectedCreditClientService.selectedClientsControl.valueChanges.pipe(
      map((value) => value === null),
    ),
    { initialValue: true },
  );
  creditReports = toSignal(
    this.creditService.creditReports$.pipe(map((state) => state.data ?? [])),
    { initialValue: [] },
  );

  clients = computed(() => this.clientService.stateValues());
  activeCreditReports = computed(() => this.creditService.activeCreditReports());
  pullType = computed(() => this.creditPullType()?.creditPullType);
  isFetchingPullType = computed(() => this.creditPullType()?.fetching === true);
  pullTypeFailed = computed(() => this.creditPullType()?.error != null);
  transferDisabled = computed(() => this.activeReport() !== undefined);
  hasUnsavedClientChanges = computed(() => this.clientUpdateHandler.unsavedChanges() > 0);
  creditPullLoading = computed(
    () => this.creditService.isPullingCredit() || this.isFetchingPullType(),
  );
  transferCreditLoading = computed(() => this.creditService.isTransferringCredit());

  /**
   * Credit pull is disabled if:
   * - Credit pull is loading
   * - Credit pull type failed
   * - No clients are selected
   * - First credit pull for selected clients and client consent is not given
   * - Banker is not licensed to write
   * - Banker has conflicting loans
   */
  creditPullDisabled = computed(
    () =>
      !this.creditPullLoading() &&
      ((this.isFirstCreditPullForSelectedClients() && !this.clientConsentForCreditPull()) ||
        this.noSelection() ||
        this.pullTypeFailed() ||
        !this.canPullCredit() ||
        this.hasUnsavedClientChanges() ||
        this.clientService.isUpdating()),
  );

  shouldDisableCreditPull = computed(() => {
    if (!this.isBankerLicensed()?.write || this.userAuthorizationService.hasConflictingLoans()) {
      return true;
    } else {
      return this.creditPullDisabled() || this.creditPullLoading() || this.transferCreditLoading();
    }
  });

  shouldDisableCreditTransfer = computed(() => {
    if (!this.isBankerLicensed()?.write || this.userAuthorizationService.hasConflictingLoans()) {
      return true;
    } else {
      return (
        this.creditPullDisabled() ||
        this.transferCreditLoading() ||
        this.creditPullLoading() ||
        this.transferDisabled()
      );
    }
  });

  activeReport = computed(() => {
    const selectedClients = this.selectedCreditClientService.selectedClient();
    return this.activeCreditReports().find(
      (report) =>
        !report.isInvalid &&
        selectedClients?.every((selectedClient) =>
          report.creditReport.clients.find((client) => client.clientId === selectedClient),
        ),
    );
  });

  canPullCredit = computed(() => {
    if (!this.creditAuthService.creditRequirementsSatisfied()) {
      return false;
    }

    const activeReport = this.activeReport();
    if (activeReport === undefined) {
      return true;
    }

    const isScoreQualifying = (activeReport.creditReport.qualifyingScore ?? 0) >= 580;
    const isScoreUpgradable = (activeReport.creditReport.qualifyingScore ?? 0) >= 560;
    const reportIssuedDate = new Date(activeReport.creditReport.firstIssuedOn);
    const reportCutoffDate = new Date(Date.now() - this.THIRTY_DAYS);
    const isReportOverThirtyDaysOld = reportIssuedDate > reportCutoffDate;

    if (isScoreUpgradable && this.isCreditUpgrade()) {
      return true;
    }

    if (!isScoreQualifying && isReportOverThirtyDaysOld) {
      return true;
    }

    if (activeReport.creditReport.clients.some((client) => client.creditFreezes?.length ?? 0)) {
      return true;
    }

    if (
      clientHasOutstandingTask(
        this.creditService.creditTasks(),
        this.selectedCreditClientService.selectedClient(),
      )
    ) {
      return true;
    }

    return false;
  });

  pullCredit() {
    const clientIds = this.selectedCreditClientService.selectedClientsControl.value;
    if (clientIds === null) return;

    this.creditService
      .pullCredit$(clientIds, this.pullType())
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }

  transferCredit() {
    const clientIds = this.selectedCreditClientService.selectedClientsControl.value;
    if (clientIds === null) return;

    this.creditService
      .transferCredit$(clientIds)
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe();
  }
}
