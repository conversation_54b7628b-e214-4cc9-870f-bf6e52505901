:host {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 16px 16px;
}

.report-container {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px 0;
  gap: 8px;
  overflow-y: auto;
}

mat-expansion-panel {
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  border: 1px solid var(--rlxp-gray-200);

  ::ng-deep .mat-expansion-panel-body {
    padding: 4px 16px;
  }
}

.no-reports {
  justify-content: center;
}

.request-credit-container {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 16px;
}

.action-container {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  gap: 10px;
}

.help-text-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
}

.transfer-credit-alert {
  padding-top: 20px;
}

app-credit-report {
  display: block;
  width: 100%;
}

.no-credit-report {
  display: flex;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
  flex: 1 0 0;
}

.rkt-Icon {
  width: 120px;
  height: 120px;
}

mat-expansion-panel,
mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel.rkt-AccordionPanel--enterprise:hover,
mat-expansion-panel.rkt-AccordionPanel.mat-expansion-panel.mat-expansion-panel.rkt-AccordionPanel--enterprise.mat-expanded {
  background-color: var(--rlxp-accordion-outlined-background-color);

  .mat-expansion-panel:hover {
    background-color: var(--rlxp-accordion-outlined-background-color);
  }
}
