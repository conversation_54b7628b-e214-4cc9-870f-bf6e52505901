import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { FormControl } from '@angular/forms';
import { CreditPullType } from '@rocket-logic/rl-xp-bff-models';
import { MockBuilder } from 'ng-mocks';
import { NEVER, of } from 'rxjs';
import { HugeRadioGroupComponent } from '../../huge-radio-group/huge-radio-group.component';
import { CreditAuthorizationService } from '../../services/credit/credit-authorization.service';
import { CreditService } from '../../services/credit/credit.service';
import { SelectedCreditClientService } from '../../services/credit/selected-credit-client.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { ClientUpdateHandlerService } from '../../services/entity-state/client-state/client-update-handler.service';
import { LoanIdService } from '../../services/loan-id/loan-id.service';
import { UserAuthorizationService } from '../../services/user-authorization/user-authorization.service';
import { CreditRequestsComponent } from './credit-requests.component';

describe('CreditRequestsComponent', () => {
  let component: CreditRequestsComponent;
  let fixture: ComponentFixture<CreditRequestsComponent>;

  beforeEach(() =>
    MockBuilder(CreditRequestsComponent)
      .mock(CreditService, {
        creditReports$: NEVER,
        isPullingCredit: signal(false),
        isTransferringCredit: signal(false),
        creditTransferSuccessful: signal(false),
        activeCreditReports: signal([]),
        creditOrders$: of({ data: [], loading: false }),
        creditPullType$: of({
          creditPullType: CreditPullType.Hard,
          error: undefined,
          fetching: false,
        }),
      })
      .mock(CreditAuthorizationService)
      .mock(SelectedCreditClientService, {
        selectedClientsControl: new FormControl<string[] | null>(null),
        clientCreditGroups: signal([]),
        clientOptions: signal([]),
        selectedClient: signal([]),
      })
      .mock(ClientUpdateHandlerService)
      .mock(UserAuthorizationService, { isBankerLicensed$: NEVER, userScopes$: NEVER })
      .keep(HugeRadioGroupComponent)
      .mock(ClientStateService, { state: signal(undefined), stateValues: signal([]) })
      .mock(LoanIdService, { loanId$: of('123') })
  );
  beforeEach(() => {
    fixture = TestBed.createComponent(CreditRequestsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
