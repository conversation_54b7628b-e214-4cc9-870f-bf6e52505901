import { AsyncPipe } from '@angular/common';
import { Component } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule } from '@angular/material/dialog';
import { MatIcon } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { interval, map, take } from 'rxjs';
import { LeadService } from '../services/lead/lead.service';

@Component({
  selector: 'app-progress-bar-dialog',
  standalone: true,
  imports: [
    MatProgressBarModule,
    MatCardModule,
    AsyncPipe,
    RktAlertEnterpriseModule,
    MatDialogModule,
    MatIcon,
  ],
  templateUrl: './progress-bar-dialog.component.html',
  styleUrl: './progress-bar-dialog.component.scss',
})
export class ProgressBarDialogComponent {
  private loadingTime = LeadService.maxLoadingTime;
  progress$ = interval(100).pipe(
    take(this.loadingTime / 100),
    map((x) => (x + 1) * (100 / (this.loadingTime / 100))),
  );
}
