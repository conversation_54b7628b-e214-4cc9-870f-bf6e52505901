import { Routes } from '@angular/router';
import { LongFormComponent } from '../long-form/long-form.component';
import { pilotRedirectGuard } from '../pilot-redirect.guard';
import { authorizationGuard } from '../services/authorization/authorization.guard';
import { RlxpRootComponent } from './rlxp-root.component';

export const rlxpRoutes: Routes = [
  {
    path: ':loanId',
    component: RlxpRootComponent,
    canActivate: [pilotRedirectGuard, authorizationGuard],
    children: [
      { path: '', component: LongFormComponent },
    ],
  },
];
