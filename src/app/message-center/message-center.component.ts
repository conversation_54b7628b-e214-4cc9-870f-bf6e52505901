import { Component, inject } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { DarkModeService } from '../services/dark-mode/dark-mode.service';
import { MessageCenterService } from '../services/message-center/message-center.service';
import { MessageCenterErrorsComponent } from './message-center-errors/message-center-errors.component';
import { MessageCenterInfoComponent } from './message-center-info/message-center-info.component';
import { MessageCenterWarningsComponent } from './message-center-warnings/message-center-warnings.component';

@Component({
  selector: 'app-message-center',
  standalone: true,
  imports: [
    MatIconModule,
    MessageCenterErrorsComponent,
    MessageCenterWarningsComponent,
    MessageCenterInfoComponent,
    RktTagEnterpriseModule,
  ],
  templateUrl: './message-center.component.html',
  styleUrl: './message-center.component.scss',
})
export class MessageCenterComponent {
  darkModeService = inject(DarkModeService);

  readonly messageCenterService = inject(MessageCenterService);
}
