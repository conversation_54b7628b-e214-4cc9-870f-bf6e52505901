import { Component, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import {
  ActiveSidenavScreenService,
  SidenavScreen,
} from '../../services/active-sidenav-screen/active-sidenav-screen.service';
import { AssetDataPullService } from '../../services/asset-data-pull/asset-data-pull.service';
import { MessageCenterInfoType } from '../../services/message-center/message-center';
import { MessageCenterService } from '../../services/message-center/message-center.service';
import { PrefillStateService } from '../../services/prefill-state/prefill-state.service';
import { MessageAccordionComponent } from '../message-accordion/message-accordion.component';

@Component({
  selector: 'app-message-center-info',
  standalone: true,
  imports: [MessageAccordionComponent, MatIconModule, MatButtonModule],
  templateUrl: './message-center-info.component.html',
  styleUrl: './message-center-info.component.scss',
})
export class MessageCenterInfoComponent {
  readonly assetDataPullState = inject(AssetDataPullService).assetDataPullState;
  readonly hasPrefillData = toSignal(inject(PrefillStateService).hasPrefillData$);
  readonly activeSideNavScreenService = inject(ActiveSidenavScreenService);
  readonly messageCenterService = inject(MessageCenterService);

  readonly MessageCenterInfoType = MessageCenterInfoType;

  onPrefillClick() {
    this.activeSideNavScreenService.activate(SidenavScreen.PreFill);
  }
}
