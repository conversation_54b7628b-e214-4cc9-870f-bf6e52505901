<app-message-accordion
  title="Information"
  [count]="messageCenterService.infoCount()"
  variant="info"
  [shouldShowLoader]="false"
>
  @for (info of messageCenterService.info(); track info) {
    <section message-content>
      @if (info?.type === MessageCenterInfoType.AssetDataPull) {
        Asset Data Pull Success
      } @else if (info?.type === MessageCenterInfoType.HasPrefillData) {
        <p class="rkt-Label-14 rkt-Alert__text">Serviced Loan Data is Available</p>
        <div class="client-data-alert-actions">
          <button
            class="rkt-Button rkt-Button--has-icon rkt-Button--secondary"
            mat-flat-button
            (click)="onPrefillClick()"
            appTrackClick
            description="Open Serviced Loan Data"
          >
            <mat-icon class="material-icons-outlined" fontIcon="note_add"></mat-icon>
            View Serviced Loan Data
          </button>
        </div>
      }
    </section>
  }
</app-message-accordion>
