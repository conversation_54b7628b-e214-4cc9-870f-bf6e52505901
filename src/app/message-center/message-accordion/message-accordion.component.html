<mat-expansion-panel [class]="panelClass()" [expanded]="isExpanded()">
  <mat-expansion-panel-header class="message-panel__header">
    <mat-panel-title class="message-panel__header-title">
      @if (!shouldShowLoader()) {
        <mat-icon aria-hidden="true" [svgIcon]="icon()"></mat-icon>
        <span>{{ title() }} ({{ count() }})</span>
      } @else {
        <span rktSkeleton variant="circular"></span>
        <span rktSkeleton variant="rectangular"></span>
      }
    </mat-panel-title>
  </mat-expansion-panel-header>

  <div class="message-panel__body">
    @if (!shouldShowLoader()) {
      <ng-content select="[message-content]" />
    } @else {
      @for (row of skeletonRows(); track row) {
        <div class="skeleton-row">
          <span rktSkeleton variant="rectangular"></span>
        </div>
      }
    }
  </div>

  <div class="message-panel__footer">
    <ng-content select="[message-footer]"></ng-content>
  </div>
</mat-expansion-panel>
