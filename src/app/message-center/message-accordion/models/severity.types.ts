export const SEVERITY_CONFIG: ReadonlyMap<VariantType, { readonly icon: IconType }> = new Map([
  ['error', { icon: 'rl-block' }],
  ['warning', { icon: 'warning_amber-two_tone' }],
  ['info', { icon: 'info-two_tone' }],
  ['success', { icon: 'check_circle-two_tone' }],
]);

export type VariantType = 'error' | 'warning' | 'info' | 'success';

export type IconType =
  | 'rl-block'
  | 'warning_amber-two_tone'
  | 'info-two_tone'
  | 'check_circle-two_tone';
