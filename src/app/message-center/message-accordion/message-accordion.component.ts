import { Component, computed, input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIconModule } from '@angular/material/icon';
import { RktSkeletonModule } from '@rocketcentral/rocket-design-system-angular';
import { SEVERITY_CONFIG, VariantType } from './models/severity.types';

@Component({
  selector: 'app-message-accordion',
  standalone: true,
  imports: [MatExpansionModule, MatIconModule, MatButtonModule, RktSkeletonModule],
  templateUrl: './message-accordion.component.html',
  styleUrls: ['./message-accordion.component.scss'],
})
export class MessageAccordionComponent {
  readonly isExpanded = input<boolean>(true);
  readonly variant = input<VariantType>('info');
  readonly title = input.required<string>();
  readonly count = input(0);
  readonly shouldShowLoader = input(true);
  readonly skeletonRowToDisplay = input(3);

  protected readonly icon = computed(() => SEVERITY_CONFIG.get(this.variant())?.icon ?? 'info');

  protected readonly panelClass = computed(() => ({
    'message-panel': true,
    [`message-panel--${this.variant()}`]: this.shouldShowLoader() === false,
  }));

  protected readonly skeletonRows = computed(() =>
    Array.from({ length: this.skeletonRowToDisplay() }).map((_, i) => `skeleton-row-${i + 1}`),
  );
}
