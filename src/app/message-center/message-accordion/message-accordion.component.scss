@use '@rocketcentral/rocket-design-system-styles/web/scss/color' as rkt-colors;
@use '@rocketcentral/rocket-design-system-styles/web/scss/spacing' as rkt-spacing;
@use '@rocketcentral/rocket-design-system-styles/base/typography' as rkt-typography;

:host {
  --panel-padding-x: #{rkt-spacing.$rkt-spacing-12};
  --panel-padding-y: #{rkt-spacing.$rkt-spacing-16};
  --panel-border-radius: #{rkt-spacing.$rkt-spacing-12};
  --panel-background: #{rkt-colors.$rkt-white};
  --panel-border: #{rkt-colors.$rkt-gray-200};
  --panel-header-bg: #{rkt-colors.$rkt-gray-50};

  --skeleton-circular-size: 24px;
  --skeleton-rectangular-height: 16px;
  --skeleton-rectangular-width: 135px;
  --skeleton-rectangular-border-radius: 4px;

  --panel-error-header-bg: #{rkt-colors.$rkt-red-50};
  --panel-error-border: #{rkt-colors.$rkt-rose-500};
  --panel-warning-header-bg: #{rkt-colors.$rkt-tangerine-50};
  --panel-warning-border: #{rkt-colors.$rkt-tangerine-500};
  --panel-info-header-bg: #{rkt-colors.$rkt-blue-50};
  --panel-info-border: #{rkt-colors.$rkt-blue-500};

  ::ng-deep {
    .mat-expansion-panel-body {
      padding: #{rkt-spacing.$rkt-spacing-12} var(--panel-padding-x) 0;
    }

    .mat-expansion-indicator {
      padding-bottom: #{rkt-spacing.$rkt-spacing-8};
    }

    .mat-expansion-panel:not([class*='mat-elevation-z']) {
      box-shadow: none;
    }
  }
}

:host-context(.rkt-DarkMode) {
  --panel-color: #{rkt-colors.$rkt-white};
  --panel-background: #{rkt-colors.$rkt-gray-900};
  --panel-border: #{rkt-colors.$rkt-gray-700};

  --panel-error-header-bg: var(--rlxp-rose-dark);
  --panel-info-header-bg: var(--rlxp-info-dark);
  --panel-warning-header-bg: var(--rlxp-tangerine-dark);
}

.message-panel {
  $root: &;
  $types: error, warning, info;

  border: 1px solid var(--panel-border);
  border-radius: var(--panel-border-radius);
  background: var(--panel-background);
  color: var(--panel-color);

  @mixin header-styles($bg-color) {
    #{$root}__header,
    #{$root}__header:hover,
    &.mat-expansion-panel:not(.mat-expanded)
      .mat-expansion-panel-header:not([aria-disabled='true']):hover {
      background: $bg-color;
    }
  }

  &:not([class*='--']) {
    @include header-styles(var(--panel-header-bg));
  }

  @each $type in $types {
    &--#{$type} {
      border-color: var(--panel-#{$type}-border);
      .mat-icon {
        color: var(--panel-#{$type}-border);
      }
      @include header-styles(var(--panel-#{$type}-header-bg));
    }
  }

  &__header {
    height: auto;
    padding: var(--panel-padding-y) var(--panel-padding-x);
    border-radius: 0;
    background: var(--panel-header-bg);
    @extend %rkt-Label-14;

    &.mat-expanded {
      height: auto;
    }

    &-title {
      display: flex;
      align-items: center;
      gap: #{rkt-spacing.$rkt-spacing-8};
    }

    [rktSkeleton] {
      &[variant='circular'] {
        width: var(--skeleton-circular-size);
        height: var(--skeleton-circular-size);
      }
    }

    [rktSkeleton] {
      &[variant='rectangular'] {
        width: var(--skeleton-rectangular-width);
        height: var(--skeleton-rectangular-height);
        border-radius: var(--skeleton-rectangular-border-radius);
      }
    }
  }

  &__body,
  &__footer {
    color: var(--panel-color);

    [rktSkeleton] {
      &[variant='rectangular'] {
        height: var(--skeleton-rectangular-height);
        width: 100%;
        margin-bottom: #{rkt-spacing.$rkt-spacing-12};
        border-radius: var(--skeleton-rectangular-border-radius);
      }
    }

    .skeleton-row {
      &:not(:last-child) {
        border-bottom: 1px solid #{rkt-colors.$rkt-gray-200};
        margin-bottom: #{rkt-spacing.$rkt-spacing-12};
      }
    }
  }

  &__body {
    @extend %rkt-Body-14;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}
