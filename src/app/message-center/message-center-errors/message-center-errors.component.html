<app-message-accordion
  title="Stoppers"
  [count]="messageCenterService.errorCount()"
  variant="error"
  [shouldShowLoader]="false"
>
  @for (error of messageCenterService.errors(); track error) {
    <section message-content>
      @if (error?.type === LeadInitError.NotFound) {
        A lead matching <strong>{{ loanId() }}</strong> could not be found. RL-XP only allows loans
        with matching leads. Please contact support.
      } @else if (error?.type === LeadInitError.Unsupported) {
        This lead type is not supported. Please continue in LOLA/AMP.
      } @else if (isEntityStateError(error) && error.type === EntityStateErrorType.FetchError) {
        We were unable to get {{ error.context.name | entityName }}.
      } @else if (isEntityStateError(error) && error.type === EntityStateErrorType.UpdateError) {
        We were unable to save {{ error.context.name | entityName }}.
      } @else if (error?.type === LeadInitError.RmaImportFailed) {
        Something went wrong trying to transfer info from Rocket Mortgage Application (RMA). Please
        contact support.
      } @else if (isBankerLicensedError(error)) {
        You are not licensed in
        {{ error.context.comment }}.
        @if (error.context.commonId !== null) {
          Please transfer loan to a licensed banker.
        }
      } @else if (isConflictingLoansError(error)) {
        The client <b>{{ error.context.client | clientName: true }} </b> is currently working with
        <b>{{ error.context.preferredFirstname }} {{ error.context.preferredLastName }}</b>
        on loan number <b>{{ error.context.loanNumber }}</b
        >. Please transfer the lead.
      } @else if (isCreditTaskError(error)) {
        <app-credit-task [taskInfo]="error.context.task" />
      } @else if (error?.type === MessageCenterErrorType.AssetDataPull) {
        Asset Data Pull Failed
      } @else if (error?.type === MessageCenterErrorType.BankerNotAssignedToLead) {
        You are not assigned to this lead.
      } @else {
        Something went wrong!
      }
    </section>
  }
</app-message-accordion>
