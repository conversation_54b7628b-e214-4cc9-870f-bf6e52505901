import { Component, computed, inject } from '@angular/core';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { CreditTaskComponent } from '../../credit-task/credit-task.component';
import { ActiveActionService } from '../../services/active-action.service';
import { EntityStateErrorType } from '../../services/entity-state/abstract-entity-state.service';
import { LeadInitError } from '../../services/lead/lead.service';
import { LoanIdService } from '../../services/loan-id/loan-id.service';
import {
  isBankerLicensedError,
  isConflictingLoansError,
  isCreditTaskError,
  isEntityStateError,
  MessageCenterErrorType,
} from '../../services/message-center/message-center';
import { MessageCenterService } from '../../services/message-center/message-center.service';
import { ClientNamePipe } from '../../util/client-name.pipe';
import { EntityNamePipe } from '../../util/entity-name.pipe';
import { MessageAccordionComponent } from '../message-accordion/message-accordion.component';

@Component({
  selector: 'app-message-center-errors',
  standalone: true,
  imports: [
    MatIconModule,
    MatDividerModule,
    MessageAccordionComponent,
    CreditTaskComponent,
    ClientNamePipe,
    EntityNamePipe,
  ],
  templateUrl: './message-center-errors.component.html',
  styleUrl: './message-center-errors.component.scss',
})
export class MessageCenterErrorsComponent {
  readonly messageCenterService = inject(MessageCenterService);
  readonly activeActionService = inject(ActiveActionService);
  readonly loanId = inject(LoanIdService).loanId;

  readonly outstandingCreditTasks = computed(() =>
    this.activeActionService.outstandingCreditTasks(),
  );

  readonly LeadInitError = LeadInitError;
  readonly MessageCenterErrorType = MessageCenterErrorType;
  readonly EntityStateErrorType = EntityStateErrorType;

  readonly isBankerLicensedError = isBankerLicensedError;
  readonly isConflictingLoansError = isConflictingLoansError;
  readonly isCreditTaskError = isCreditTaskError;
  readonly isEntityStateError = isEntityStateError;
}
