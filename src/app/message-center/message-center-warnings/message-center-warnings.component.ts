import { Component, computed, inject } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { AssetDataPullService } from '../../services/asset-data-pull/asset-data-pull.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import {
  isAssetDataPullWarning,
  MesageCenterWarningType,
} from '../../services/message-center/message-center';
import { MessageCenterService } from '../../services/message-center/message-center.service';
import { VaResequenceComponent } from '../../va-resequence/va-resequence.component';
import { MessageAccordionComponent } from '../message-accordion/message-accordion.component';

@Component({
  selector: 'app-message-center-warnings',
  standalone: true,
  imports: [MessageAccordionComponent, VaResequenceComponent, MatIconModule],
  templateUrl: './message-center-warnings.component.html',
  styleUrl: './message-center-warnings.component.scss',
})
export class MessageCenterWarningsComponent {
  readonly clientStateService = inject(ClientStateService);
  readonly assetDataPullState = inject(AssetDataPullService).assetDataPullState;
  readonly messageCenterService = inject(MessageCenterService);
  readonly WarningType = MesageCenterWarningType;

  readonly isAssetDataPullWarning = isAssetDataPullWarning;

  readonly showVaResequenceWarning = computed(() =>
    this.clientStateService.showVaResequencingMessage(),
  );
}
