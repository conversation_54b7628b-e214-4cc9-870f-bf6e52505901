<app-message-accordion
  title="Warnings"
  [count]="messageCenterService.warningCount()"
  variant="warning"
  [shouldShowLoader]="false"
>
  @for (warning of messageCenterService.warnings(); track warning) {
    <section message-content>
      @if (isAssetDataPullWarning(warning)) {
        <div class="rkt-Body-14 rkt-FontWeight--700">Asset Data Pull Warnings</div>
        <ul>
          @for (failureReason of warning.failureReasons; track failureReason) {
            <li>
              {{ failureReason }}
            </li>
          }
        </ul>
      } @else if (warning?.type === WarningType.VaResequencingWarning) {
        <app-va-resequence />
      } @else if (warning?.type === WarningType.AddressNotFound) {
        Your changes have been saved. However, we couldn't validate the address you entered. Please
        double-check the address for any errors.
      }
    </section>
  }
</app-message-accordion>
