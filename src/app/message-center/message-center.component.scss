:host {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 0 16px;
  box-sizing: border-box;
  gap: 0.75rem;

  ::ng-deep {
    section:not(:last-of-type) {
      border-bottom: 1px solid var(--rlxp-nav-divider-color);
    }

    .mat-expansion-indicator::after {
      color: var(--rlxp-icon-label);
    }

    section {
      padding-bottom: 12px;
    }
  }
}

.title {
  margin-left: 16px;
  margin-bottom: 8px;
}

.no-messages {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  mat-icon {
    width: 5.1875rem;
    height: 5.1875rem;
  }
}
