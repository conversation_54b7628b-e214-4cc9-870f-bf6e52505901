<div class="form-container">
  <app-application-island class="rkt-Elevation-5">
    <app-loan-save-status appIslandStatus />
    <app-loan-milestone-progress appIslandProgress />
    <app-app-island-action-button appIslandAction />
  </app-application-island>
  <mat-card
    appearance="outlined"
    class="rkt-Card rkt-Card--enterprise form-card"
    [class.form-card--disabled]="!!loanLoadError()"
  >
    <app-loan-info appFormSectionOrder [formSection]="FormSection.LoanInfo" />

    <app-clients-info appFormSectionOrder [formSection]="FormSection.ClientInfo" />

    <app-subject-property-info appFormSectionOrder [formSection]="FormSection.SubjectProperty" />

    <app-credit-info appFormSectionOrder [formSection]="FormSection.Credit" />

    <app-assets appFormSectionOrder [formSection]="FormSection.Assets" />

    <app-income-info appFormSectionOrder [formSection]="FormSection.Income" />

    <!-- Conditional Form Sections -->
    @if (showPurchaseInfo()) {
      <app-purchase-info appFormSectionOrder [formSection]="FormSection.PurchaseInfo" />
    }

    @if (showRefi()) {
      <app-refinance-info appFormSectionOrder [formSection]="FormSection.RefinanceInfo" />
    }

    @if (showReo()) {
      <app-reo appFormSectionOrder [formSection]="FormSection.REO" />
    }
    <!--  -->
  </mat-card>
  
</div>
