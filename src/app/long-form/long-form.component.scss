:host {
  display: grid;
  grid-template-columns: 1fr 808px 1fr;
  min-height: 100%;
}

.form-container {
  grid-column: 2;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

mat-card.mdc-card.mat-mdc-card.form-card {
  display: flex;
  flex-direction: column;
  gap: 32px;

  &--disabled {
    filter: blur(5px);
    cursor: not-allowed;
  }
}

app-application-island {
  position: sticky;
  top: 0;
  z-index: 2;
  background-color: var(--rlxp-application-island-background-color);
  transition: transform 0.1s ease-out;

  &.scaled {
    transform: scale(1.01);
  }
}
