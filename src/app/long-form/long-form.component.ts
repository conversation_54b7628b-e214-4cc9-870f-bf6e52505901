import { CommonModule } from '@angular/common';
import {
  Component,
  DestroyRef,
  Injector,
  ViewContainerRef,
  computed,
  effect,
  inject
} from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { LoanPurpose } from '@rocket-logic/rl-xp-bff-models';
import { filter } from 'rxjs';
import { AppIslandActionButtonComponent } from '../application-island/app-island-action-button/app-island-action-button.component';
import { ApplicationIslandComponent } from '../application-island/application-island.component';
import { LoanMilestoneProgressComponent } from '../application-island/loan-milestone-progress/loan-milestone-progress.component';
import { LoanSaveStatusComponent } from '../application-island/loan-save-status/loan-save-status.component';
import { AssetsComponent } from '../assets/assets.component';
import { ClientsInfoComponent } from '../clients-info/clients-info.component';
import { CreditComponent } from '../credit-info/credit.component';
import { DenyWithdrawErrorsComponent } from '../deny-withdraw/deny-withdraw-errors/deny-withdraw-errors.component';
import { IncomeInfoComponent } from '../income-info/income-info.component';
import { LoanInfoComponent } from '../loan-info/loan-info.component';
import { ProgressBarDialogComponent } from '../progress-bar-dialog/progress-bar-dialog.component';
import { PurchaseInfoComponent } from '../purchase-info/purchase-info.component';
import { RefinanceInfoComponent } from '../refinance-info/refinance-info.component';
import { ReoComponent } from '../reo/reo.component';
import { CreditService } from '../services/credit/credit.service';
import { LoanInfoFormListenerService } from '../services/entity-state/loan-state/loan-info-form-listener.service';
import { LoanStateService } from '../services/entity-state/loan-state/loan-state.service';
import { FormSection } from '../services/form-nav/form-nav-section.service';
import { LoanStatusLoadingService } from '../services/loan-status-loading/loan-status-loading.service';
import { LoanAccessVerifierReasons } from '../services/user-authorization/default-access';
import { UserAuthorizationService } from '../services/user-authorization/user-authorization.service';
import { FormSectionOrderDirective } from '../settings/form-section-order/form-section-order.directive';
import { SettingsService } from '../settings/settings.service';
import { SubjectPropertyComponent } from '../subject-property-info/subject-property.component';

@Component({
  selector: 'app-long-form',
  standalone: true,
  imports: [
    LoanInfoComponent,
    PurchaseInfoComponent,
    RefinanceInfoComponent,
    ClientsInfoComponent,
    SubjectPropertyComponent,
    AssetsComponent,
    IncomeInfoComponent,
    ReoComponent,
    CreditComponent,
    MatCardModule,
    CommonModule,
    ApplicationIslandComponent,
    FormSectionOrderDirective,
    LoanSaveStatusComponent,
    LoanMilestoneProgressComponent,
    AppIslandActionButtonComponent,
  ],
  templateUrl: './long-form.component.html',
  styleUrl: './long-form.component.scss',
})
export class LongFormComponent {
  FormSection = FormSection;
  private loanInfoFormStateService = inject(LoanInfoFormListenerService);
  private loanStateService = inject(LoanStateService);
  private loanStatusLoadingService = inject(LoanStatusLoadingService);
  private dialog = inject(MatDialog);
  private injector = inject(Injector);
  private creditStateService = inject(CreditService);
  private viewContainerRef = inject(ViewContainerRef);
  private destroyRef = inject(DestroyRef);
  settings = inject(SettingsService);
  isDenyWithdrawAllowed = toSignal(inject(UserAuthorizationService).isDenyWithdrawAllowed$);

  private loanLoadingStatus = toSignal(this.loanStatusLoadingService.loanLoadingStatus$, {
    requireSync: true,
  });

  loanStateIsPurchase = toSignal(
    this.loanStateService.state$.pipe(
      filter((state) => state?.data?.loanPurpose == LoanPurpose.Purchase),
    ),
  );
  loanStateIsRefi = toSignal(
    this.loanStateService.state$.pipe(
      filter((state) => state?.data?.loanPurpose == LoanPurpose.Refinance),
    ),
  );
  loanLoadError = toSignal(this.loanStatusLoadingService.loanLoadingError$);
  refiSelected = this.loanInfoFormStateService.refiSelected;
  purchaseSelected = this.loanInfoFormStateService.purchaseSelected;

  showReo = computed(() => (this.creditStateService.creditReports()?.data?.length ?? 0) > 0);
  showRefi = computed(
    () => (this.refiSelected() || this.loanStateIsRefi()) && !this.purchaseSelected(),
  );
  showPurchaseInfo = computed(
    () => (this.purchaseSelected() || this.loanStateIsPurchase()) && !this.refiSelected(),
  );

  openPopup() {
    if (this.isDenyWithdrawAllowed() === LoanAccessVerifierReasons.FolderReceived) {
      const dialogRef = this.dialog.open(DenyWithdrawErrorsComponent, {
        panelClass: 'rkt-Dialog',
        backdropClass: 'rkt-Backdrop',
        viewContainerRef: this.viewContainerRef,
      });
    }
  }

  constructor() {
    effect(() => {
      const status = this.loanLoadingStatus();
      if (status.initializing) {
        this.dialog.open(ProgressBarDialogComponent, {
          id: `${ProgressBarDialogComponent.constructor.name}`,
          panelClass: 'rkt-Dialog',
          backdropClass: 'rkt-Backdrop',
          closeOnNavigation: false,
          disableClose: true,
          injector: this.injector,
        });
      } else {
        this.dialog.getDialogById(`${ProgressBarDialogComponent.constructor.name}`)?.close();
      }
    });
    effect(() => {
      this.openPopup();
    });
  }
}
