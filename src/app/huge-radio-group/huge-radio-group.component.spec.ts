import { ComponentFixture, TestBed } from '@angular/core/testing';

import { HugeRadioGroupComponent } from './huge-radio-group.component';

describe('HugeRadioGroupComponent', () => {
  let component: HugeRadioGroupComponent;
  let fixture: ComponentFixture<HugeRadioGroupComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [HugeRadioGroupComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(HugeRadioGroupComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('label', '');
    fixture.componentRef.setInput('options', []);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
