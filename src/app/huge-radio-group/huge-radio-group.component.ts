import { Component, effect, forwardRef, input, signal, viewChildren } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { MatRadioButton, MatRadioChange, MatRadioModule } from '@angular/material/radio';
import { RktStackModule } from '@rocketcentral/rocket-design-system-angular';

@Component({
  selector: 'app-huge-radio-group',
  standalone: true,
  imports: [MatRadioModule, RktStackModule],
  templateUrl: './huge-radio-group.component.html',
  styleUrl: './huge-radio-group.component.scss',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => HugeRadioGroupComponent),
      multi: true,
    },
  ],
})
export class HugeRadioGroupComponent implements ControlValueAccessor {
  private currentValue: unknown = null;
  options =
    input.required<
      { display: string; value: unknown; description?: string; disabled?: boolean }[]
    >();
  label = input<string>();
  splitAt = input<'0' | '1' | '2' | '3' | '4' | '5' | '6' | '7' | '8' | '9' | '10' | undefined>(
    '4',
  );
  isDisabled = signal(false);
  radioButtons = viewChildren(MatRadioButton);

  constructor() {
    effect(() => {
      this.setRadioButtonsChecked(this.radioButtons());
    });
  }

  onChange: (value: unknown) => void = () => {};
  onTouched = () => {};

  writeValue(value: unknown): void {
    this.currentValue = value;
    this.setRadioButtonsChecked(this.radioButtons());
  }

  onRadioChange(change: MatRadioChange) {
    this.currentValue = change.value;
    this.onChange(change.value);
    this.onTouched();
  }

  registerOnChange(fn: (value: unknown) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled.set(isDisabled);
  }

  private setRadioButtonsChecked(buttons: readonly MatRadioButton[]) {
    buttons.forEach(
      (radioButton) => (radioButton.checked = this.currentValue === radioButton.value),
    );
  }
}
