<div class="rkt-Fieldset">
  @if (label()) {
    <p class="rkt-Legend rkt-Label-14">{{label()}}</p>
  }
  <mat-radio-group (change)="onRadioChange($event)" [disabled]="isDisabled()">
    <rkt-stack itemSpacing="3" [splitAt]="splitAt()" additionalClasses="rkt-HugeInputContainer app-HugeInputContainer">
      @for (option of options(); track option.value) {
        <div rktStackItem class="rkt-RadioHuge app-RadioHuge" [class.rkt-RadioHuge--is-disabled]="isDisabled() || option.disabled" [class.rkt-RadioHuge--is-selected]="optionRadio.checked">
          <mat-radio-button [disabled]="option.disabled" class="rkt-RadioHuge__input" #optionRadio [value]="option.value">
            <span>{{option.display}}</span>
            @if (option.description) {
              <span class="rkt-Caption-12" [class.rkt-Color--gray-600]="!option.disabled" [class.rkt-Color--gray-400]="option.disabled">{{option.description}}</span>
            }
          </mat-radio-button>
        </div>
      }
    </rkt-stack>
  </mat-radio-group>
</div>
