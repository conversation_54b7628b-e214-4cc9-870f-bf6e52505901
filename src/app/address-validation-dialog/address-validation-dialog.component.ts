import { Component, inject } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatRadioModule } from '@angular/material/radio';
import { Address, State } from '@rocket-logic/rl-xp-bff-models';
import { RktStackModule } from '@rocketcentral/rocket-design-system-angular';
import { HugeRadioGroupComponent } from '../huge-radio-group/huge-radio-group.component';
import { SanitizePipe } from '../util/sanitize.pipe';

@Component({
  selector: 'app-address-validation-dialog',
  standalone: true,
  imports: [
    MatIconModule,
    MatDialogModule,
    HugeRadioGroupComponent,
    ReactiveFormsModule,
    MatButtonModule,
    MatRadioModule,
    RktStackModule,
    MatIconModule,
    SanitizePipe,
  ],
  templateUrl: './address-validation-dialog.component.html',
  styleUrl: './address-validation-dialog.component.scss',
})
export class AddressValidationDialogComponent {
  addressFormControl = new FormControl<Address | null>(null, Validators.required);
  data: [
    { isSuggested: false; address: Address },
    { isSuggested: true; address: Address | undefined },
  ] = inject(MAT_DIALOG_DATA);
  dialogRef: MatDialogRef<AddressValidationDialogComponent> = inject(MatDialogRef);

  formatAddress(address: Address): string {
    const { addressLine1, addressLine2, city, state, zipCode } = address;
    return [
      addressLine1 && `${addressLine1} ${addressLine2 ? `${addressLine2}` : ''}<br/>`,
      city && `${city}, `,
      state && `${state} `,
      zipCode && zipCode,
    ]
      .filter((value) => value)
      .join('');
  }

  diffAddresses(lesAddress: Address): string {
    const originalAddress = this.data[0].address;

    const diffedAddress = {
      addressLine1: this.diffAddressParts(
        originalAddress.addressLine1 ?? '',
        lesAddress.addressLine1 ?? '',
      ),
      addressLine2: this.diffAddressParts(
        originalAddress.addressLine2 ?? '',
        lesAddress.addressLine2 ?? '',
      ),
      city: this.generateFragment(originalAddress.city ?? '', lesAddress.city ?? ''),
      state: this.generateFragment(
        originalAddress.state ?? '',
        lesAddress.state ?? '',
      ) as unknown as State,
      zipCode: this.generateFragment(originalAddress.zipCode ?? '', lesAddress.zipCode ?? ''),
    };

    return this.formatAddress(diffedAddress);
  }

  private generateFragment(original: string, updated: string) {
    return original !== updated ? `<span class="text-rose-500">${updated}</span>` : updated;
  }

  private diffAddressParts(original: string, updated: string): string {
    const originalArray = original.split(' ');
    const updatedArray = updated.split(' ');

    return updatedArray
      .map((part, i) => this.generateFragment(originalArray[i], part))
      .join(' ')
      .trim();
  }
}
