<h2 class="rkt-Heading-20 rkt-FontWeight--500" mat-dialog-title>Confirm Address</h2>

<p class="rkt-Spacing--mb24 rkt-Dialog__text">
  Suggested changes are marked in <span class="text-rose-500">red</span>. Please pick which address
  to use.
</p>
<mat-dialog-content>
  <mat-radio-group [formControl]="addressFormControl">
    <div class="rkt-HugeInputContainer app-HugeInputContainer flex gap-4 p-4 min-w-full">
      @for (address of data; track address) {
        <div
          class="rkt-RadioHuge app-RadioHuge rkt-RadioHuge--stylized"
          [class.rkt-RadioHuge--is-selected]="optionRadio.checked"
        >
          <mat-radio-button class="rkt-RadioHuge__input" #optionRadio [value]="address.address">
            <mat-icon
              aria-hidden="false"
              class="rkt-Spacing--mr16"
              aria-label="Address"
              svgIcon="location-outlined"
            ></mat-icon>
            <div class="flex flex-col">
              <span class="rkt-Label-14">{{
                address.isSuggested ? 'What we suggest' : 'What was entered'
              }}</span>
              @if (address.isSuggested) {
                <span
                  class="rkt-Body-14"
                  [innerHTML]="diffAddresses(address?.address ?? {}) | sanitize"
                ></span>
              } @else {
                <span class="rkt-Body-14" [innerHTML]="formatAddress(address.address) | sanitize">
                </span>
              }
            </div>
          </mat-radio-button>
        </div>
      }
    </div>
  </mat-radio-group>
</mat-dialog-content>
<mat-dialog-actions class="flex gap-4" align="end">
  <button
    mat-flat-button
    class="rkt-Button rkt-Button--secondary"
    
    [mat-dialog-close]="null"
  >
    Cancel
  </button>
  <button
    mat-flat-button
    class="rkt-Button"
    color="primary"
    [disabled]="!addressFormControl.valid"
    [class.rkt-Button--is-disabled]="!addressFormControl.valid"
    [mat-dialog-close]="addressFormControl.value"
  >
    Confirm Address
  </button>
</mat-dialog-actions>
