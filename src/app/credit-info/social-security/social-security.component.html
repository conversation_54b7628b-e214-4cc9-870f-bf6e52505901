<mat-form-field class="rkt-FormField w-full" color="accent">
  <mat-label> {{ label() }}</mat-label>
  <input
    autocomplete="off"
    class="rkt-Input"
    matInput
    [formControl]="ssnControl()"
    appNavInput
    [inputSection]="FormInput.SSN"
    mask="XXX-XX-0000"
    [hiddenInput]="maskActive()"
    [errorStateMatcher]="ssnErrorStateMatcher"
  />
  <button
    matSuffix
  mat-icon-button
    class="rkt-ButtonIcon app-ButtonIcon"
    (click)="toggleHidden()"
    [class.rkt-ButtonIcon--is-disabled]="isDisabled()"
    [disabled]="isDisabled()"
    [attr.aria-label]="maskActive() ? 'Show SSN' : 'Hide SSN'"
  >
    <mat-icon class="rkt-Icon" [svgIcon]="maskActive() ? 'visibility-outlined' : 'visibility_off-outlined'"></mat-icon>
  </button>
  <mat-error>{{ errorMessage() }}</mat-error>
</mat-form-field>
