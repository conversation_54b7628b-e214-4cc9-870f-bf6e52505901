import {
  Component,
  Injector,
  OnInit,
  inject,
  input,
  runInInjectionContext,
  viewChildren,
} from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { combineLatest, distinctUntilChanged, map, startWith, switchMap } from 'rxjs';
import { ClientControls } from '../../services/entity-state/client-state/form-types';
import { getSsnWarnings } from '../../util/ssn-warning';
import { SocialSecurityComponent } from '../social-security/social-security.component';

@Component({
  selector: 'app-social-security-group',
  standalone: true,
  imports: [SocialSecurityComponent],
  templateUrl: './social-security-group.component.html',
  styleUrl: './social-security-group.component.scss',
})
export class SocialSecurityGroupComponent implements OnInit {
  injector = inject(Injector);
  clientForm = input.required<FormGroup<ClientControls>>();
  ssnComponents = viewChildren(SocialSecurityComponent);

  ngOnInit() {
    runInInjectionContext(this.injector, () => {
      toObservable(this.clientForm)
        .pipe(
          switchMap((clientForm) => {
            const { ssn, confirmSSN } = clientForm.controls.personalInformation.controls;
            return combineLatest([
              ssn.valueChanges.pipe(
                map(() => ssn.valid && ssn.value),
                startWith(ssn.valid && ssn.value),
              ),
              confirmSSN.valueChanges.pipe(
                map(() => confirmSSN.valid && confirmSSN.value),
                startWith(confirmSSN.valid && confirmSSN.value),
              ),
            ]);
          }),
          map(([ssnValid, confirmSSNValid]) => ssnValid && confirmSSNValid),
          distinctUntilChanged(),
          takeUntilDestroyed(),
        )
        .subscribe((ssnsValid) => {
          if (ssnsValid) {
            this.ssnComponents().forEach((ssnComponent) => ssnComponent.setHidden(true));
          }
        });
    });
  }

  getSsnErrorMessage(clientFormGroup: FormGroup): string {
    const ssn = clientFormGroup.get('personalInformation.ssn');
    const warningMessage = getSsnWarnings(ssn);

    if (ssn?.hasError('ssnsUniqueFail')) {
      return 'Must have different Social Security Numbers for each client';
    }

    if (warningMessage) {
      return warningMessage;
    }

    return 'Please enter a valid Social Security Number';
  }

  getConfirmSsnErrorMessage(clientFormGroup: FormGroup): string {
    const confirmSSN = clientFormGroup.get('personalInformation.confirmSSN');
    const warningMessage = getSsnWarnings(confirmSSN);

    if (confirmSSN?.hasError('ssnComparisonFail')) {
      return 'Social Security Numbers must be the same';
    }

    if (warningMessage) {
      return warningMessage;
    }

    return 'Please enter a valid Social Security Number';
  }
}
