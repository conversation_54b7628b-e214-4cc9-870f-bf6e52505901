import { CommonModule } from '@angular/common';
import { Component, computed, effect, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatRadioModule } from '@angular/material/radio';
import { MatSelectModule } from '@angular/material/select';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { provideNgxMask } from 'ngx-mask';
import { distinctUntilChanged, switchMap, take } from 'rxjs';
import { InputSectionDirective } from '../form-nav/nav-section/input-section.directive';
import { FormSectionComponent } from '../form-section/form-section.component';
import { FormattedDateInputComponent } from '../question-input/formatted-date-input/formatted-date-input.component';
import { CreditService } from '../services/credit/credit.service';
import { ClientFormService } from '../services/entity-state/client-state/client-form.service';
import { ClientStateService } from '../services/entity-state/client-state/client-state.service';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import { LoanStateService } from '../services/entity-state/loan-state/loan-state.service';
import { FormInput, ProductMilestoneFormInput } from '../services/form-nav/form-nav-input.service';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { LeadService } from '../services/lead/lead.service';
import { ClientNamePipe } from '../util/client-name.pipe';
import { CreditSkeletonComponent } from './credit-skeleton/credit-skeleton.component';
import { SocialSecurityGroupComponent } from './social-security-group/social-security-group.component';

@Component({
  selector: 'app-credit-info',
  standalone: true,
  imports: [
    FormSectionComponent,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatIconModule,
    MatButtonModule,
    CommonModule,
    ClientNamePipe,
    MatProgressSpinnerModule,
    RktTagEnterpriseModule,
    FormattedDateInputComponent,
    CreditSkeletonComponent,
    SocialSecurityGroupComponent,
    MatCheckboxModule,
    MatRadioModule,
    InputSectionDirective,
  ],
  providers: [provideNgxMask()],
  templateUrl: './credit.component.html',
  styleUrl: './credit.component.scss',
})
export class CreditComponent {
  clientStateService = inject(ClientStateService);
  clientFormService = inject(ClientFormService);
  formNavSectionService = inject(FormNavSectionService);
  private readonly loanStateService = inject(LoanStateService);
  private readonly loanEditingState = inject(LoanEditingState);
  private readonly leadService = inject(LeadService);
  private creditService = inject(CreditService);
  public readonly isRelocationLead$ = this.leadService.isRelocationLead$;
  public readonly hasInternationalCredit$ = this.leadService.hasInternationalCredit$;
  readonly FormInput = FormInput;
  readonly FormSection = FormSection;
  public today = new Date();
  readonly ProductMilestones = ProductMilestoneFormInput;

  internationalCreditControl = new FormControl<boolean | null | undefined>(null);
  checkboxFormValue = toSignal(this.internationalCreditControl.valueChanges);

  isLoading = signal<boolean>(false);
  isDisabled = computed(
    () =>
      !this.checkboxFormValue() ||
      this.isLoading() ||
      this.loanEditingState.isLoanEditingDisabled(),
  );

  isRelocationLead = toSignal(this.isRelocationLead$);

  constructor() {
    effect(() => {
      if (this.loanEditingState.isLoanEditingDisabled()) {
        this.internationalCreditControl.disable({ emitEvent: false });
      }
    });

    effect(() => {
      if (!this.isRelocationLead()) {
        return;
      }

      const activeReports = this.creditService.activeCreditReports();

      if (activeReports.length) {
        const hasQualifyingScore = activeReports.some(
          (report) => !report.isInvalid && report.creditReport.qualifyingScore,
        );
        if (hasQualifyingScore) {
          this.internationalCreditControl.setValue(false);
        }
      }
    });

    this.hasInternationalCredit$
      .pipe(take(1))
      .subscribe((value) => this.internationalCreditControl.setValue(value, { emitEvent: false }));

    this.internationalCreditControl.valueChanges
      .pipe(
        distinctUntilChanged(),
        switchMap((checked) => this.loanStateService.setHasInternationalCredit$(checked)),
        takeUntilDestroyed(),
      )
      .subscribe();
  }

  getPersonalInfoForm(clientFormGroup: FormGroup): FormGroup {
    return clientFormGroup.get('personalInformation') as FormGroup;
  }
}
