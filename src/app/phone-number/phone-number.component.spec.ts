import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MockBuilder, MockInstance } from 'ng-mocks';
import { SelectFieldComponent } from '../_shared/components/select-field/select-field.component';
import { TextFieldComponent } from '../_shared/components/text-field/text-field.component';
import { PhoneNumberComponent } from './phone-number.component';

describe('PhoneNumberComponent', () => {
  let component: PhoneNumberComponent;
  let fixture: ComponentFixture<PhoneNumberComponent>;

  beforeEach(() => MockBuilder(PhoneNumberComponent));
  beforeEach(() => {
    MockInstance(TextFieldComponent, 'matFormField', signal(undefined));
    MockInstance(SelectFieldComponent, 'matFormField', signal(undefined));
    fixture = TestBed.createComponent(PhoneNumberComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput(
      'phoneNumberForm',
      new FormGroup({ number: new FormControl(), type: new FormControl() }),
    );
    fixture.componentRef.setInput('isLoanEditingDisabled', false);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
