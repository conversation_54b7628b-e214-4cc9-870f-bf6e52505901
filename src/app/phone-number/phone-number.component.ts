import { Component, effect, input } from '@angular/core';
import { PhoneNumberType } from '@rocket-logic/rl-xp-bff-models';
import { SelectFieldComponent } from '../_shared/components/select-field/select-field.component';
import { TextFieldComponent } from '../_shared/components/text-field/text-field.component';
import { RlaFormFieldSuffixComponent } from '../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component';
import { RlaHighlightDirective } from '../assistant/directives/rla-highlight.directive';
import { InputSectionDirective } from '../form-nav/nav-section/input-section.directive';
import { PhoneNumberGroup } from '../services/entity-state/client-state/form-types';
import { ProductMilestoneFormInput } from '../services/form-nav/form-nav-input.service';
import { SelectOption } from '../type-ahead-select/type-ahead-select.component';
import { UntouchedErrorStateMatcher } from '../util/error-state-matchers/untouched-error-state-matcher';
import { getErrorMessage } from '../util/get-error-message';

@Component({
  selector: 'app-phone-number',
  standalone: true,
  imports: [
    InputSectionDirective,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    TextFieldComponent,
    SelectFieldComponent,
  ],
  templateUrl: './phone-number.component.html',
  styleUrl: './phone-number.component.scss',
})
export class PhoneNumberComponent {
  phoneNumberForm = input.required<PhoneNumberGroup>();
  isLoanEditingDisabled = input.required<boolean | undefined>();
  shouldRegisterMilestone = input(false);

  readonly ProductMilestoneFormInput = ProductMilestoneFormInput;
  untouchedErrorStateMatcher = new UntouchedErrorStateMatcher(true);

  constructor() {
    effect(() => {
      if (this.isLoanEditingDisabled()) {
        this.phoneNumberForm().disable();
      } else {
        this.phoneNumberForm().enable();
      }
    });
  }

  getPhoneTypeError() {
    const phoneTypeControl = this.phoneNumberForm().controls.type;
    return getErrorMessage(phoneTypeControl, [
      ['duplicateTypes', () => 'Cannot have more than one of the same phone type'],
    ]);
  }

  phoneTypeOptions: SelectOption<PhoneNumberType>[] = Object.values(PhoneNumberType).map(type => ({
    value: type,
    display: type,
  }));
}
