import { Component, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltipModule } from '@angular/material/tooltip';
import { CashOutReason } from '@rocket-logic/rl-xp-bff-models';
import { LienPosition, RefinanceGoal } from '@rocket-logic/rl-xp-bff-models/dist/refinance/enums';
import { RktSkeletonModule } from '@rocketcentral/rocket-design-system-angular';
import { EMPTY, map, of, startWith } from 'rxjs';
import { environment } from '../../environments/environment';
import { InputSectionDirective } from '../form-nav/nav-section/input-section.directive';
import { FormSectionComponent } from '../form-section/form-section.component';
import { FormattedNumberInputComponent } from '../question-input/formatted-number-input/formatted-number-input.component';
import { LoanFormService } from '../services/entity-state/loan-state/loan-form.service';
import { LoanStateService } from '../services/entity-state/loan-state/loan-state.service';
import { FormInput, ProductMilestoneFormInput } from '../services/form-nav/form-nav-input.service';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { LeadService } from '../services/lead/lead.service';
import { SchwabService } from '../services/schwab/schwab.service';
import { getErrorMessage } from '../util/get-error-message';
import { CheckboxInputComponent } from './checkbox-input/checkbox-input.component';

@Component({
  selector: 'app-refinance-info',
  standalone: true,
  imports: [
    FormSectionComponent,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatCheckboxModule,
    InputSectionDirective,
    RktSkeletonModule,
    CheckboxInputComponent,
    FormattedNumberInputComponent,
    MatIconModule,
    MatTooltipModule,
    InputSectionDirective,
  ],
  templateUrl: './refinance-info.component.html',
  styleUrl: './refinance-info.component.scss',
})
export class RefinanceInfoComponent {
  formService = inject(LoanFormService);
  loanStateService = inject(LoanStateService);
  readonly leadService = inject(LeadService);
  formNavSectionService = inject(FormNavSectionService);
  private schwabService = inject(SchwabService);
  private enableAmeripriseHeloc = environment.featureFlags?.allowAmeripriseHeloc ?? false;

  readonly FormInput = FormInput;
  readonly FormSection = FormSection;
  readonly ProductMilestoneFormInput = ProductMilestoneFormInput;

  isSchwab = toSignal(this.schwabService.isSchwab$);
  leadServiceDetails = toSignal(this.leadService.lead$);
  helocGroup = this.formService.loanForm?.controls.helocDetails;
  helocGroupError = toSignal(
    this.helocGroup?.valueChanges?.pipe(
      startWith(this.helocGroup?.value),
      map(() =>
        getErrorMessage(
          this.helocGroup,
          [['drawDownAmountExceedsLoanAmount', () => 'Draw down amount cannot exceed loan amount']],
          '',
        ),
      ),
    ) ?? of(''),
  );
  isHelocSupported = computed(() => {
    const isAmeripriseSupported =
      this.enableAmeripriseHeloc && this.leadServiceDetails()?.isAmeripriseLead;
    return this.isSchwab() || isAmeripriseSupported;
  });
  isAssumptionLead = computed(() => {
    return this.leadServiceDetails()?.isAssumptionLead;
  });
  get hasCashoutGoal() {
    return this.formService.loanForm?.get('refinanceGoals')?.value?.includes(RefinanceGoal.CashOut);
  }

  hasHelocGoal = toSignal(
    this.formService.loanForm
      ?.get('refinanceGoals')
      ?.valueChanges?.pipe(map((goals) => goals?.includes(RefinanceGoal.Heloc))) ?? of(false),
    {
      initialValue:
        this.formService.loanForm?.get('refinanceGoals')?.value?.includes(RefinanceGoal.Heloc) ??
        false,
    },
  );

  readonly desiredDrawAmountControl =
    this.formService.loanForm?.controls.helocDetails.controls.drawDownAmount;

  readonly desiredLoanAmountControl =
    this.formService.loanForm?.controls.helocDetails.controls.loanAmount;

  readonly desiredLienPosition =
    this.formService.loanForm?.controls.helocDetails.controls.lienPosition;

  get refinanceGoalsArray() {
    return this.formService.loanForm.get('refinanceGoals')?.value ?? [];
  }
  get desiredCashOutControl() {
    return this.formService.loanForm.get('desiredCashOutAmount') as FormControl;
  }
  get cashoutReason() {
    return this.formService.loanForm.get('reasonsForCashOut')?.value?.[0];
  }

  refiGoals = toSignal(
    this.formService.loanForm?.controls.refinanceGoals.valueChanges.pipe(
      startWith(this.formService.loanForm.controls.refinanceGoals.value),
      map((goals) => goals || []),
    ) ?? EMPTY,
  );

  refinanceGoalsOptions = computed(() => {
    const baseOptions: Array<{
      value: RefinanceGoal;
      label: string;
      isOptionDisabled: boolean | undefined;
    }> = [
        {
          value: RefinanceGoal.CashOut,
          label: 'Cash Out',
          isOptionDisabled: this.refiGoals()?.some(
            (goal) =>
              goal === RefinanceGoal.ShorterTerm ||
              goal === RefinanceGoal.LowerRate ||
              goal === RefinanceGoal.Heloc,
          ) || this.isAssumptionLead(),
        },
        {
          value: RefinanceGoal.ShorterTerm,
          label: 'Shorter Term',
          isOptionDisabled: this.refiGoals()?.some(
            (goal) => goal === RefinanceGoal.CashOut || goal === RefinanceGoal.Heloc,
          ) || this.isAssumptionLead(),
        },
        {
          value: RefinanceGoal.LowerRate,
          label: 'Lower Rate',
          isOptionDisabled: this.refiGoals()?.some(
            (goal) => goal === RefinanceGoal.CashOut || goal === RefinanceGoal.Heloc,
          ) || this.isAssumptionLead(),
        },
      ];

    if (this.isHelocSupported()) {
      baseOptions.push({
        value: RefinanceGoal.Heloc,
        label: 'HELOC',
        isOptionDisabled: this.refiGoals()?.some(
          (goal) =>
            goal === RefinanceGoal.CashOut ||
            goal == RefinanceGoal.ShorterTerm ||
            goal == RefinanceGoal.LowerRate,
        ) || this.isAssumptionLead(),
      });
    }
    if (this.isAssumptionLead()) {
      baseOptions.push({
        value: RefinanceGoal.Assumption,
        label: 'Assumption',
        isOptionDisabled: false
      })
    }
    return baseOptions;
  });

  public readonly cashoutReasonOptions = [
    { value: CashOutReason.DebtConsolidation, label: 'Debt Consolidation' },
    { value: CashOutReason.HomeImprovement, label: 'Home Improvement' },
    { value: CashOutReason.PurchaseOfOtherProperty, label: 'Purchase Of Other Property' },
    { value: CashOutReason.Other, label: 'Other' },
  ];

  public readonly lienOptions = [
    { value: LienPosition.FirstStandalone, label: 'First Lien Standalone' },
    { value: LienPosition.SecondStandalone, label: 'Second Lien Standalone' },
  ];

  public onSelectChange(cashoutReason: CashOutReason): void {
    this.formService.loanForm.get('reasonsForCashOut')?.patchValue([cashoutReason]);
  }

  shouldDisplay = computed(() => {
    const goals = this.refiGoals();
    return goals ? goals.length > 0 : false;
  });

  toolTipMessage = computed(() => {
    const cashOutSelected = this.refiGoals()?.includes(RefinanceGoal.CashOut);
    const helocSelected = this.formService.loanForm
      .get('refinanceGoals')
      ?.value?.includes(RefinanceGoal.Heloc);
    const shorterTermOrLowerRateSelected = this.refiGoals()?.some(
      (goal) => goal === RefinanceGoal.ShorterTerm || goal === RefinanceGoal.LowerRate,
    );

    if (this.isHelocSupported()) {
      if (cashOutSelected) {
        return 'HELOC, Shorter Term and/or Lower Rate cannot be selected in combination with Cash Out';
      } else if (shorterTermOrLowerRateSelected) {
        return 'HELOC and/or Cash Out cannot be selected in combination with Lower Rate or Shorter Term';
      } else if (helocSelected) {
        return 'Cash Out, Shorter Term, and/or Lower Rate cannot be selected in combination with HELOC';
      }
    } else {
      if (cashOutSelected) {
        return 'Shorter Term and/or Lower Rate cannot be selected in combination with Cash Out';
      } else if (shorterTermOrLowerRateSelected) {
        return 'Cash Out cannot be selected in combination with Lower Rate or Shorter Term';
      }
    }

    return '';
  });
}
