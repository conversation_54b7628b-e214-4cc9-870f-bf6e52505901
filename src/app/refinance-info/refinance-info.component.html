<app-form-section
  [title]="'Refinance Info'"
  [formGroup]="formService.loanForm"
  [isCollapsible]="false"
  [formSection]="FormSection.RefinanceInfo"
>
  @if (!loanStateService.isFetching()) {
    <div>
      <div class="row" id="goals-options">
        <app-checkbox-input
          appNavInput
          [inputSection]="FormInput.RefinanceGoals"
          [options]="refinanceGoalsOptions()"
          label="Goals"
          formControlName="refinanceGoals"
        />
        @if (shouldDisplay()) {
          <mat-icon
            class="rkt-TooltipPopper rkt-Color--blue-500 tool-tip-icon"
            color="info"
            matTooltipPosition="right"
            matTooltipClass="rkt-Tooltip"
            matTooltip="{{ toolTipMessage() }}"
            svgIcon="help_outline-outlined"
          ></mat-icon>
        }
      </div>
    </div>
    @if (hasCashoutGoal) {
      <div class="row">
        <mat-form-field floatLabel="always" class="rkt-FormField">
          <mat-label>Cash Out Purpose</mat-label>
          <mat-select class="rkt-Input" formControlName="reasonsForCashOut" multiple>
            @for (option of cashoutReasonOptions; track option) {
              <mat-option [value]="option.value">{{ option.label }}</mat-option>
            }
          </mat-select>
        </mat-form-field>
        <div class="rkt-FormField">
          <app-formatted-number-input
            class="rkt-Input"
            [allowNegative]="false"
            prefix="$"
            [control]="desiredCashOutControl"
            label="Cash Out Amount"
            appNavInput
            [inputSection]="ProductMilestoneFormInput.CashOutAmount"
          />
        </div>
      </div>
    } @else if (hasHelocGoal()) {
      <div>
        <span class="rkt-Label-14 rkt-FontWeight--500">HELOC</span>
        <ng-container [formGroup]="formService.loanForm.controls.helocDetails">
          <div class="row" id="heloc-fields">
            <app-formatted-number-input
              class="rkt-Input"
              [allowNegative]="false"
              prefix="$"
              [control]="desiredLoanAmountControl"
              label="Loan Amount"
              appNavInput
              [inputSection]="FormInput.DrawAmount"
            />
            <app-formatted-number-input
              class="rkt-Input"
              [allowNegative]="false"
              prefix="$"
              [control]="desiredDrawAmountControl"
              label="Draw Amount"
              appNavInput
              [inputSection]="FormInput.LoanAmount"
            />
            <mat-form-field class="rkt-FormField">
              <mat-label>Lien</mat-label>
              <mat-select
                appNavInput
                [inputSection]="FormInput.LienPosition"
                class="rkt-Input"
                formControlName="lienPosition"
              >
                @for (option of lienOptions; track option) {
                  <mat-option [value]="option.value">{{ option.label }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
          </div>
          @if (helocGroup.invalid) {
            <mat-error>{{ helocGroupError() }}</mat-error>
          }
        </ng-container>
      </div>
    }
  } @else {
    <div class="row">
      <span rktSkeleton variant="text"></span>
      <span rktSkeleton variant="rectangular" class="form-field-skeleton"></span>
    </div>
    <div class="row">
      <span rktSkeleton variant="text"></span>
      <span rktSkeleton variant="text"></span>
      <span rktSkeleton variant="text"></span>
      <span rktSkeleton variant="text"></span>
    </div>
  }
</app-form-section>
