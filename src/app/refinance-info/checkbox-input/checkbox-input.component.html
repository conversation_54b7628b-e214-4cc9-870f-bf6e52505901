@if (label(); as label) {
  <span class="rkt-Label-14 rkt-FontWeight--500" [class.label--required]="isRequired()">
    {{ label }}{{ isRequired() ? '*' : '' }}
  </span>
}

<div class="rkt-Fieldset">
  <rkt-stack>
    <div class="row">
      @for (option of options(); track option.value) {
        <mat-checkbox
          [disabled]="isDisabled() || option.isOptionDisabled"
          class="rkt-Checkbox rkt-Checkbox--enterprise"
          (change)="onCheckedChange($event)"
          [value]="option.value"
          >{{ option.label }}</mat-checkbox
        >
      }
    </div>
  </rkt-stack>
</div>
