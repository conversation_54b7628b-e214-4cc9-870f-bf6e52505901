import {
  AfterViewInit,
  Component,
  DestroyRef,
  effect,
  inject,
  input,
  signal,
  viewChildren,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ControlValueAccessor, NgControl } from '@angular/forms';
import { MatCheckbox, MatCheckboxChange, MatCheckboxModule } from '@angular/material/checkbox';
import { RktStackModule } from '@rocketcentral/rocket-design-system-angular';
import { distinctUntilChanged, startWith } from 'rxjs';

@Component({
  selector: 'app-checkbox-input',
  standalone: true,
  imports: [MatCheckboxModule, RktStackModule],
  templateUrl: './checkbox-input.component.html',
  styleUrl: './checkbox-input.component.scss',
  host: {
    '[attr.tabindex]': '0',
  },
})
export class CheckboxInputComponent implements ControlValueAccessor, AfterViewInit {
  private currentValue: Set<string> = new Set();
  private readonly ngControl = inject(NgControl, { self: true });
  private readonly destroyRef = inject(DestroyRef);
  isRequired = signal(false);
  options =
    input.required<{ label: string; value: string; isOptionDisabled: boolean | undefined }[]>();
  isDisabled = signal(false);
  checkboxes = viewChildren(MatCheckbox);
  label = input<string>();

  constructor() {
    this.ngControl.valueAccessor = this;
    effect(() => {
      this.setCheckboxDefaults(this.checkboxes());
    });
  }

  ngAfterViewInit(): void {
    this.ngControl
      .statusChanges!.pipe(
        startWith('VALID'),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => this.isRequired.set(this.ngControl.hasError('required')));
  }

  onChange: (value: unknown) => void = () => {};
  onTouched = () => {};

  writeValue(value: string[]): void {
    value?.forEach((val) => this.currentValue.add(val));
    this.setCheckboxDefaults(this.checkboxes());
  }

  onCheckedChange(change: MatCheckboxChange) {
    const changedValue = change.source.value;
    if (change.checked) {
      this.currentValue.add(changedValue);
    } else {
      this.currentValue.delete(changedValue);
    }
    this.onChange(Array.from(this.currentValue));
    this.onTouched();
  }

  registerOnChange(fn: (value: unknown) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }
  private setCheckboxDefaults(buttons: readonly MatCheckbox[]) {
    buttons.forEach(
      (radioButton) => (radioButton.checked = this.currentValue.has(radioButton.value)),
    );
  }

  setDisabledState(isDisabled: boolean): void {
    this.isDisabled.set(isDisabled);
  }
}
