import { Component, effect, inject, signal } from '@angular/core';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSelectModule } from '@angular/material/select';
import { finalize, iif } from 'rxjs';
import { ClientStateService } from '../services/entity-state/client-state/client-state.service';
import { ClientNamePipe } from '../util/client-name.pipe';

@Component({
  selector: 'app-va-resequence',
  standalone: true,
  imports: [
    MatIconModule,
    MatButtonModule,
    ClientNamePipe,
    MatSelectModule,
    MatInputModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './va-resequence.component.html',
  styleUrl: './va-resequence.component.scss',
})
export class VaResequenceComponent {
  readonly clientStateService = inject(ClientStateService);
  readonly nonPrimaryClientsWithVa = this.clientStateService.nonPrimaryClientsWithVa;
  readonly clientSelectControl = new FormControl(null);
  readonly makePrimaryLoading = signal(false);

  constructor() {
    effect(() => {
      if (this.nonPrimaryClientsWithVa().length > 1) {
        if (!this.clientSelectControl.hasValidator(Validators.required)) {
          this.clientSelectControl.addValidators(Validators.required);
        }
      } else {
        this.clientSelectControl.clearValidators();
      }

      this.clientSelectControl.updateValueAndValidity();
    });
  }

  makePrimary(clientId?: string | null) {
    this.makePrimaryLoading.set(true);

    iif(
      () => !clientId && !!this.nonPrimaryClientsWithVa()[0].id,
      this.clientStateService.makePrimary$(this.nonPrimaryClientsWithVa()[0].id!),
      this.clientStateService.makePrimary$(clientId!),
    )
      .pipe(finalize(() => this.makePrimaryLoading.set(false)))
      .subscribe();
  }
}
