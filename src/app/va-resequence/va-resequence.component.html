<span class="rkt-Label-14 rkt-FontWeight--700">VA Eligibility Update</span>
<p class="rkt-Alert__text">
  @if (nonPrimaryClientsWithVa().length === 1) {
    <b>{{ nonPrimaryClientsWithVa()[0] | clientName }}</b> has been set to have VA Eligible
    benefits. Re-sequence by clicking "Move to Primary" to get pricing for VA products.
  } @else {
    <b>
      @for (client of nonPrimaryClientsWithVa(); track client.id) {
        {{ client | clientName }}
        @if ($index < nonPrimaryClientsWithVa().length - 1) {
          <span class="not-bold">and</span>
        }
      }
    </b>
    Have been set to have VA Eligible benefits. Re-sequence by clicking
    <b>"Move to Primary"</b> for the client whose benefits will be used to get pricing for VA
    products.
  }
</p>

@if (nonPrimaryClientsWithVa().length > 1) {
  <mat-form-field class="rkt-FormField" color="accent" subscriptSizing="dynamic">
    <mat-label>Choose Client</mat-label>
    <mat-select class="rkt-Input" [formControl]="clientSelectControl">
      <mat-option [value]="null">None</mat-option>
      @for (client of nonPrimaryClientsWithVa(); track client.id) {
        <mat-option [value]="client.id">{{ client | clientName }}</mat-option>
      }
    </mat-select>
  </mat-form-field>
}

<button
  class="rkt-Button rkt-Button--secondary"
  (click)="makePrimary(clientSelectControl.value)"
  mat-stroked-button
  color="accent"
  [class.rkt-Button--is-disabled]="!clientSelectControl.valid && !makePrimaryLoading()"
  [class.rkt-Button--has-icon]="makePrimaryLoading()"
  [class.rkt-Button--is-spinning]="makePrimaryLoading()"
  [disabled]="!clientSelectControl.valid"
>
  @if (makePrimaryLoading()) {
    <mat-icon class="rkt-Icon rkt-Spacing--mr8">
      <mat-spinner role="presentation" class="rkt-Spinner" diameter="20" />
    </mat-icon>
  }
  Move To Primary
</button>
