<h1 mat-dialog-title>
  @switch (data.reason) {
    @case (AuthorizationErrorCode.UserIsClockedOut) {
      Clock in to continue working in Rocket Logic.
    }
    @case (AuthorizationErrorCode.UserNotAllowedToAccessTeamMemberLoans) {
      Loan Access denied for Team Member Loan.
    }
    @case (AuthorizationErrorCode.NotASchwabBanker) {
      Transfer loan to Schwab banker
    }
    @case (AuthorizationErrorCode.SchwabClientOnRetailLoan) {
      Please transfer this client to a Schwab banker
    }
    @default {
      Unauthorized
    }
  }
</h1>
<mat-dialog-content>
  @switch (data.reason) {
    @case (AuthorizationErrorCode.UserIsClockedOut) {
      <p>
        Please clock in using
        <a rktLinkEnterprise target="_blank" href="https://shorty/workday">Workday</a> and then
        refresh your page to continue working.
      </p>
    }
    @case (AuthorizationErrorCode.UserNotAllowedToAccessTeamMemberLoans) {
      <p>You are not able to access Team Member Loans.</p>
    }
    @case (AuthorizationErrorCode.UserDoesNotHaveAccessToLoanInAmp) {
      <p>You do not have access to this loan in AMP at this time.</p>
    }
    @case (AuthorizationErrorCode.NotASchwabBanker) {
      <p>This is a Schwab loan please transfer to a Schwab banker.</p>
    }
    @case (AuthorizationErrorCode.OriginatedByAmp) {
      <p>
        This loan already exists in AMP without any record in Rocket Logic. Please check to make
        sure you have entered the correct loan number.
      </p>
    }
    @case (AuthorizationErrorCode.InAmpArchive) {
      <p>
        This loan already exists in AMP without any record in Rocket Logic. Please check to make
        sure you have entered the correct loan number.
      </p>
    }
    @case (AuthorizationErrorCode.SchwabClientOnRetailLoan) {
      <p>This is a Schwab client. Please transfer to a Schwab banker.</p>
    }
    @default {
      <p>You are not able to access Rocket Logic at this time</p>
    }
  }
</mat-dialog-content>
