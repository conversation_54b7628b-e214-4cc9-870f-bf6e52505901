import { Component, Inject } from "@angular/core";
import { MAT_DIALOG_DATA, MatDialogModule } from "@angular/material/dialog";
import { MatIconModule } from "@angular/material/icon";
import { AuthorizationErrorCode } from "@rocket-logic/rl-xp-bff-models/dist/authorization";
import { RktLinkEnterpriseModule } from "@rocketcentral/rocket-design-system-enterprise-angular";


@Component({
  selector: 'app-unauthorized-dialog',
  standalone: true,
  imports: [
    MatIconModule,
    MatDialogModule,
    RktLinkEnterpriseModule
  ],
  templateUrl: './unauthorized-dialog.component.html',
  styleUrl: './unauthorized-dialog.component.scss'
})
export class UnauthorizedDialogComponent {
  public AuthorizationErrorCode = AuthorizationErrorCode;

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      reason: AuthorizationErrorCode
    },
  ) {}
}
