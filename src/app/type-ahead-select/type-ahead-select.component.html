<mat-form-field class="rkt-Autocomplete rkt-FormField" color="accent" [floatLabel]="!!preview() ? 'always' : 'auto'">
  <mat-label>{{ label() }}</mat-label>
  <input
    #formInput
    [formControl]="control()"
    [matAutocomplete]="auto"
    matInput
    (input)="filter(formInput.value)"
    (focus)="filter(formInput.value)"
    autocomplete="disabled"
    class="rkt-Input"
    [class.!h-0]="preview()"
    [class.!w-0]="preview()"
    [class.!opacity-0]="preview()"
  />
  @if (preview(); as preview) {
    <input
      matInput
      class="rkt-Input"
      autocomplete="disabled"
      [value]="preview"
    />
  }

  <div class="flex items-center" matSuffix>
    @if (isLoading()) {
      <div class="spinner">
        <mat-spinner
          role="presentation"
          class="rkt-ProgressSpinner rkt-ProgressSpinner--enterprise"
          diameter="16"
        ></mat-spinner>
      </div>
    }
    <ng-content select="[form-field-suffix]"></ng-content>
  </div>

  <mat-error>{{ getError() }}</mat-error>

  <mat-autocomplete
    #auto="matAutocomplete"
    autoActiveFirstOption
    [requireSelection]="requireSelection()"
    [displayWith]="displayWith()"
  >
    @if (!isLoading()) {
      @if (filteredOptions().length < 60) {
        @for (option of filteredOptions(); track option) {
          <mat-option class="rkt-Autocomplete__option" [value]="option.value">
            <div>
              {{ option.display }}
            </div>
            @if (option.secondaryText) {
              <div class="secondary-text">
                {{ option.secondaryText }}
              </div>
            }
          </mat-option>
        }
      } @else {
        <cdk-virtual-scroll-viewport itemSize="48" [minBufferPx]="240" [maxBufferPx]="480">
          <mat-option
            *cdkVirtualFor="let option of filteredOptions()"
            class="rkt-Autocomplete__option"
            [value]="option.value"
          >
            <div>
              {{ option.display }}
            </div>
            @if (option.secondaryText) {
              <div class="secondary-text">
                {{ option.secondaryText }}
              </div>
            }
          </mat-option>
        </cdk-virtual-scroll-viewport>
      }
    }
  </mat-autocomplete>
</mat-form-field>
