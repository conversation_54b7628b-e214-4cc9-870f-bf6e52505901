import { signal } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormControl } from '@angular/forms';
import { MockBuilder, MockInstance } from 'ng-mocks';
import { FormNavInputService } from '../services/form-nav/form-nav-input.service';
import { TypeAheadSelectComponent } from './type-ahead-select.component';

describe('TypeAheadSelectComponent', () => {
  let component: TypeAheadSelectComponent;
  let fixture: ComponentFixture<TypeAheadSelectComponent>;

  beforeEach(() => MockBuilder(TypeAheadSelectComponent).mock(FormNavInputService));

  beforeEach(() => {
    MockInstance(TypeAheadSelectComponent, 'matFormField', signal(undefined));
    fixture = TestBed.createComponent(TypeAheadSelectComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('control', new FormControl<any>({ value: '' }));
    fixture.componentRef.setInput('options', []);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
