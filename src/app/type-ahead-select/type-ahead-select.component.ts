import { ScrollingModule } from '@angular/cdk/scrolling';
import { Component, OnInit, forwardRef, input, signal, viewChild } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatAutocompleteModule } from '@angular/material/autocomplete';
import { MatForm<PERSON>ield, MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { CONTROL_PROVIDER, ControlProvider } from '../_shared/tokens/control-provider';
import { FIELD_VALUE_PREVIEWER, FieldValuePreviewer } from '../_shared/tokens/field-value-previewer';
import { MAT_FORM_FIELD_PROVIDER, MatFormFieldProvider } from '../_shared/tokens/mat-form-field-provider';
import { getErrorMessage } from '../util/get-error-message';

export interface SelectOption<T> {
  value: T;
  display: string;
  secondaryText?: string;
}

@Component({
  selector: 'app-type-ahead-select',
  standalone: true,
  imports: [
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    MatAutocompleteModule,
    MatIconModule,
    ScrollingModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './type-ahead-select.component.html',
  styleUrl: './type-ahead-select.component.scss',
  providers: [
    {
      provide: CONTROL_PROVIDER,
      useExisting: forwardRef(() => TypeAheadSelectComponent),
    },
    {
      provide: MAT_FORM_FIELD_PROVIDER,
      useExisting: forwardRef(() => TypeAheadSelectComponent),
    },
    {
      provide: FIELD_VALUE_PREVIEWER,
      useExisting: forwardRef(() => TypeAheadSelectComponent),
    },
  ],
})
export class TypeAheadSelectComponent implements OnInit, ControlProvider, MatFormFieldProvider, FieldValuePreviewer {
  private readonly defaultDisplayWith = (value: string) => {
    return this.options().find((x) => x.value === value)?.display ?? value;
  };

  matFormField = viewChild(MatFormField);
  control = input.required<FormControl<any>>();
  preview = signal<string | null>(null);
  options = input.required<SelectOption<any>[]>();
  label = input<string>('');
  isLoading = input<boolean>(false);
  requireSelection = input<boolean>(false);
  displayWith = input<(value: any) => string>(this.defaultDisplayWith);

  filteredOptions = signal<SelectOption<any>[]>([]);

  getError() {
    return getErrorMessage(this.control());
  }

  ngOnInit(): void {
    this.filteredOptions.set(this.options());
  }

  filter(value: string) {
    const filterValue = value.toLowerCase();
    const filteredOptions = this.options().filter((option) =>
      option.display.toLowerCase().startsWith(filterValue ?? ''),
    );
    this.filteredOptions.set(filteredOptions);
  }
}
