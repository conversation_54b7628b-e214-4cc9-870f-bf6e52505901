import { provideHttpClient, withInterceptors } from '@angular/common/http';
import { ApplicationConfig, ENVIRONMENT_INITIALIZER, importProvidersFrom } from '@angular/core';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MAT_FORM_FIELD_DEFAULT_OPTIONS } from '@angular/material/form-field';
import { MAT_PAGINATOR_DEFAULT_OPTIONS } from '@angular/material/paginator';
import { provideAnimationsAsync } from '@angular/platform-browser/animations/async';
import { provideRouter, RouteReuseStrategy } from '@angular/router';
import { authHttpInterceptorFn, provideAuth0 } from '@auth0/auth0-angular';
import { SPLUNK_LOGGER_CONFIG, SplunkLoggerModule } from '@decision-services/angular-splunk-logger';
import { provideRootTrailServices } from '@rocket-logic/support';
import { RktIconModule } from '@rocketcentral/rocket-design-system-angular';
import { provideEnvironmentNgxMask } from 'ngx-mask';
import { environment } from '../environments/environment';
import { provideAnalytics } from './analytics/provide-analytics';
import { provideDynatraceRum } from './dynatrace/provide-dynatrace-rum';
import { authInitFn } from './env-initializers/auth.init';
import { iconInitFn } from './env-initializers/icon.init';
import { NoRouteReuseStrategy } from './no-route-reuse-strategy';
import { AUTO_SAVE_DELAY } from './services/save-trigger/auto-save.service';
import {
  chatBankerTabRestrictionConfig,
  defaultTabRestrictionConfig,
} from './services/tab-restriction/config';
import { TAB_RESTRICTION_CONFIG } from './services/tab-restriction/token';

import { routes } from './app.routes';

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes),
    provideAnimationsAsync(),
    provideHttpClient(withInterceptors([authHttpInterceptorFn])),
    provideAuth0({
      domain: environment.auth0.domain,
      clientId: environment.auth0.clientId,
      cacheLocation: 'localstorage',
      authorizationParams: {
        redirect_uri: window.location.origin,
        audience: environment.auth0.audience,
      },
      httpInterceptor: {
        allowedList: [`${environment.dataProviderUrl}/*`],
      },
    }),
    provideNativeDateAdapter(),
    provideEnvironmentNgxMask(),
    importProvidersFrom(RktIconModule),
    importProvidersFrom(SplunkLoggerModule),
    {
      provide: SPLUNK_LOGGER_CONFIG,
      useValue: {
        appName: environment.appName,
        baseUrl: environment.splunkForwarderUrl,
        appEnvironment: environment.appEnvironment,
      },
    },
    {
      provide: MAT_FORM_FIELD_DEFAULT_OPTIONS,
      useValue: { appearance: 'outline', color: 'accent' },
    },
    {
      provide: MAT_PAGINATOR_DEFAULT_OPTIONS,
      useValue: { formFieldAppearance: 'fill' },
    },
    { provide: ENVIRONMENT_INITIALIZER, multi: true, useValue: authInitFn },
    { provide: ENVIRONMENT_INITIALIZER, multi: true, useValue: iconInitFn },
    { provide: AUTO_SAVE_DELAY, useValue: 3000 },
    {
      provide: RouteReuseStrategy,
      useClass: NoRouteReuseStrategy,
    },
    provideRootTrailServices(environment.support),
    provideAnalytics(environment.segmentWriteKey),
    provideDynatraceRum(),
    { provide: TAB_RESTRICTION_CONFIG, useValue: defaultTabRestrictionConfig, multi: true },
    { provide: TAB_RESTRICTION_CONFIG, useValue: chatBankerTabRestrictionConfig, multi: true },
  ],
};
