import { inject } from '@angular/core';
import { CanActivateFn } from '@angular/router';
import { catchError, iif, map, of, switchMap, tap } from 'rxjs';
import { environment } from '../environments/environment';
import { AuthorizationService, Pilot } from './services/authorization/authorization.service';
import { DataProviderService } from './services/data-provider/data-provider.service';

export const pilotRedirectGuard: CanActivateFn = (route) => {
  const authz = inject(AuthorizationService);
  const dataProviderService = inject(DataProviderService);
  const loanId = route.paramMap.get('loanId')!;

  const doesNotHaveRlbMetaData$ = dataProviderService.getLoanMetadata$(loanId).pipe(
    map(() => false),
    catchError(() => of(true)),
  );

  const pilotCheck$ = authz.getLoanAccess(loanId, [Pilot.RlXp]).pipe(
    map((response) => {
      return response[Pilot.RlXp];
    }),
    catchError(() => of(true)),
    switchMap((isInPilot) => iif(() => isInPilot, of(isInPilot), doesNotHaveRlbMetaData$)),
    tap((isInPilot) => {
      if (!isInPilot) {
        window.location.href = `${environment.legacyBankingUrl}/loan/${loanId}`;
      }
    }),
  );

  return pilotCheck$;
};
