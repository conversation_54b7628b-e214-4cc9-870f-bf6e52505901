import { Component, forwardRef } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  ControlValueAccessor,
  FormControl,
  NG_VALUE_ACCESSOR,
  ReactiveFormsModule
} from '@angular/forms';
import { MatAutocompleteModule, MatAutocompleteSelectedEvent } from '@angular/material/autocomplete';
import { MatDividerModule } from '@angular/material/divider';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { IncomeType } from '@rocket-logic/rl-xp-bff-models';
import { Subject, map, startWith } from 'rxjs';
import { SUPPORTED_INCOME_OPTIONS } from '../../services/entity-state/income-state/supported-income';

type IncomeAutocompleteOption = typeof SUPPORTED_INCOME_OPTIONS[number];

@Component({
  selector: 'app-income-type-autocomplete',
  standalone: true,
  imports: [
    MatSelectModule,
    MatAutocompleteModule,
    MatInputModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    MatDividerModule,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => IncomeTypeAutocompleteComponent),
      multi: true,
    },
  ],
  templateUrl: './income-type-autocomplete.component.html',
  styleUrl: './income-type-autocomplete.component.scss',
})
export class IncomeTypeAutocompleteComponent implements ControlValueAccessor {
  readonly incomeTypeControl = new FormControl<IncomeAutocompleteOption | null>(null);
  readonly IncomeType = IncomeType;

  input$ = new Subject<string | null>();

  readonly incomeTypeOptions$ = this.input$.pipe(
    map((inputValue) => {
      if (inputValue === null) return SUPPORTED_INCOME_OPTIONS;
      const lowercaseCtrlValue = inputValue.toLowerCase();
      return SUPPORTED_INCOME_OPTIONS.filter(option =>
        option.display.toLowerCase().includes(lowercaseCtrlValue));
    }),
    startWith(SUPPORTED_INCOME_OPTIONS),
  );
  readonly incomeTypeOptions = toSignal(this.incomeTypeOptions$, { requireSync: true });

  onChange: (value: unknown) => void = () => {};
  onTouched = () => {};

  selected(event: MatAutocompleteSelectedEvent): void {
    this.onTouched();
    this.onChange((event.option.value as IncomeAutocompleteOption).value);
  }

  displayFn(option: IncomeAutocompleteOption | null): string {
    return option?.display ?? '';
  }

  // CVA Implementations
  writeValue(value: IncomeAutocompleteOption | null): void {
    this.incomeTypeControl.setValue(value);
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    isDisabled
      ? this.incomeTypeControl.disable()
      : this.incomeTypeControl.enable();
  }
}
