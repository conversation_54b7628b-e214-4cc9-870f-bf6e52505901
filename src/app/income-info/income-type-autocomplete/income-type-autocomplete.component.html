<mat-form-field class="rkt-FormField"   color="accent">
  <mat-label>Income Type</mat-label>
  <input
    class="rkt-Input"
    placeholder="Income Type"
    [formControl]="incomeTypeControl"
    [matAutocomplete]="auto"
    matInput
    [matAutocompleteDisabled]="incomeTypeControl.disabled"
    (blur)="onTouched()"
    (input)="input$.next($any($event.target).value)"
    
    />
  <mat-autocomplete
    requireSelection
    autoActiveFirstOption
    #auto="matAutocomplete"
    (optionSelected)="selected($event)"
    [displayWith]="displayFn"
  >
    @for (option of incomeTypeOptions(); track option) {
      <mat-option class="rkt-Autocomplete__option" [value]="option">{{ option.display }}</mat-option>
      @if (option.value === IncomeType.Alimony) {
        <mat-divider />
      }
    }
  </mat-autocomplete>
</mat-form-field>
