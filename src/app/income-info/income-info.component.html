<app-form-section
  [title]="'Income'"
  [isCollapsible]="hasIncome()"
  [formSection]="FormSection.Income"
>
  @if (!isFetching()) {
    @for (clientGroup of clientIncomeMap(); track clientGroup.client) {
      @if (clientGroup.incomeMap.size) {
        <div class="mb-3">
          <div class="header">
            <p class="rkt-Label-16 rkt-FontWeight--700">{{ clientGroup.client | clientName }}</p>
            @if (clientGroup.client.isPrimaryBorrower) {
              <rkt-tag-enterprise
                variant="info"
                iconPosition="left"
                iconName="person_outline-outlined"
                >Primary Client</rkt-tag-enterprise
              >
            }
          </div>
          <div class="forms">
            @for (incomeMap of clientGroup.incomeMap.entries(); track incomeMap[0]) {
              <app-base-income
                class="mt-4 first:mt-0"
                [formGroup]="incomeMap[1]"
                (delete)="
                  onDeleteIncome(clientGroup.client, incomeMap[0], incomeMap[1].value.incomeType!)
                "
              ></app-base-income>
            }
          </div>
        </div>
      }
    }
    <app-add-income-button [clients]="clients()" (addIncome)="addIncome($event)" />
  } @else {
    <app-income-skeleton />
  }

  @if (!isFetching()) {
    <div section-summary>
      <div class="forms">
        @for (clientIncome of incomeSectionSummary(); track clientIncome) {
          <div class="client-section">
            <h3 class="rkt-Label-16 rkt-FontWeight--700">{{ clientIncome.clientName }}</h3>
            <div class="row income-section flex-wrap">
              @for (income of clientIncome.incomeSummary; track income) {
                <app-tile
                  [label]="income.label"
                  [content]="income.content"
                  (tileClick)="openFormSection()"
                >
                </app-tile>
              }
            </div>
          </div>
        }
      </div>
    </div>
  } @else {
    <div section-summary class="row flex-wrap">
      <app-tile-skeleton></app-tile-skeleton>
    </div>
  }
</app-form-section>
