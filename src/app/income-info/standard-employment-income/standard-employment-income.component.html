<ng-container [formGroup]="employmentFormGroup()">
  <div class="grid-container">
    <app-text-field
      [label]="incomeTypeLabel()"
      appRlaHighlight
      [errorStateMatcher]="untouchedErrorStateMatcher()"
      [control]="employmentFormGroup().controls.employerName"
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-text-field>

    <app-text-field
      class="job-title"
      appRlaHighlight
      label="Job Title"
      [control]="employmentFormGroup().controls.jobTitle"
      [errorStateMatcher]="untouchedErrorStateMatcher()"
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-text-field>

    @if (employmentStatus() !== EmploymentStatus.Previous) {
      <app-income-payment
        class="income-payment"
        [formGroup]="formGroup()"
        [showUntouchedError]="isIncomeSection()"
      />
    }

    <app-formatted-date-input
      appRlaHighlight
      class="start-date"
      label="Start Date"
      [control]="employerStartDateControl()"
      [maximumDate]="startDate()"
      [errorStateMatcher]="untouchedErrorStateMatcher()"
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-formatted-date-input>

    @if (employmentStatus() === EmploymentStatus.Previous) {
      <!-- TODO: Style me according to the Income mock -->
      <app-formatted-date-input
        appRlaHighlight
        class="end-date"
        label="End Date"
        [control]="employerEndDateControl()"
        [maximumDate]="today"
        [minimumDate]="employerStartDateControl().value"
        [errorStateMatcher]="untouchedErrorStateMatcher()"
      >
        <ng-container form-field-suffix>
          <app-rla-form-field-suffix />
        </ng-container>
      </app-formatted-date-input>
    }
    @if (formGroup().value.incomeType === IncomeType.Standard) {
      <mat-slide-toggle
        class="rkt-SlideToggle rkt-Label-14"
        color="accent"
        (change)="onToggleChange($event)"
        [checked]="isSavedPreviousIncome()"
        [disabled]="isDisabled()"
      >
        <span class="rkt-SlideToggle__label rkt-Spacing--ml8">
          Previous Employer
        </span></mat-slide-toggle
      >
    }
  </div>
</ng-container>
