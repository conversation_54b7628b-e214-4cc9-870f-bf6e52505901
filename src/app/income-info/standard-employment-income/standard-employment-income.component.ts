import { Component, computed, effect, inject, input } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { EmploymentStatus, EmploymentType, IncomeType } from '@rocket-logic/rl-xp-bff-models';
import { TextFieldComponent } from '../../_shared/components/text-field/text-field.component';
import { RlaFormFieldSuffixComponent } from '../../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component';
import { RlaHighlightDirective } from '../../assistant/directives/rla-highlight.directive';
import { FormattedDateInputComponent } from '../../question-input/formatted-date-input/formatted-date-input.component';
import {
  AllEmploymentGroup,
  AllIncomeGroup,
} from '../../services/entity-state/income-state/form-types';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import {
  FormNavSectionService,
  FormSection,
} from '../../services/form-nav/form-nav-section.service';
import { UntouchedErrorStateMatcher } from '../../util/error-state-matchers/untouched-error-state-matcher';
import { toValueChangesSignal } from '../../util/value-changes-signal';
import { IncomePaymentComponent } from '../income-payment/income-payment.component';

@Component({
  selector: 'app-standard-employment-income',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatSelectModule,
    IncomePaymentComponent,
    MatIconModule,
    FormattedDateInputComponent,
    MatSlideToggleModule,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    TextFieldComponent,
  ],
  templateUrl: './standard-employment-income.component.html',
  styleUrl: './standard-employment-income.component.scss',
})
export class StandardEmploymentIncomeComponent {
  formNavService = inject(FormNavSectionService);
  formGroup = input.required<AllIncomeGroup>();
  readonly IncomeType = IncomeType;
  readonly EmploymentStatus = EmploymentStatus;
  employmentFormGroup = computed(() => this.formGroup().get('employment') as AllEmploymentGroup);
  today = new Date();
  startDate = computed(() =>
    this.employmentStatus() === EmploymentStatus.Previous ? this.today : undefined,
  );
  isSavedPreviousIncome = computed(
    () => this.employmentFormGroup()?.get('employmentStatus')?.value === EmploymentStatus.Previous,
  );
  employmentStatus = toValueChangesSignal(this.employmentFormGroup, 'employmentStatus');
  isPrevious = computed(() => this.employmentStatus() === EmploymentStatus.Previous);
  employerEndDateControl = computed(
    () => this.employmentFormGroup().get('employmentEndDate') as FormControl,
  );
  employerStartDateControl = computed(
    () => this.employmentFormGroup().get('employmentStartDate') as FormControl,
  );

  isDisabled = inject(LoanEditingState).isLoanEditingDisabled;
  isIncomeSection = computed(() => this.formNavService.activeSection() === FormSection.Income);

  untouchedErrorStateMatcher = computed(
    () => new UntouchedErrorStateMatcher(this.isIncomeSection()),
  );

  incomeTypeLabel = computed(() => {
    if (this.formGroup().value.incomeType === IncomeType.Union) {
      return 'Line of Work';
    } else if (this.formGroup().value.incomeType === IncomeType.StandardSelfEmployment) {
      return 'Business Name';
    } else {
      return 'Employer Name';
    }
  })

  constructor() {
    effect(
      () => {
        const incomeValue = this.formGroup().value;

        if (
          (incomeValue.incomeType === IncomeType.Standard ||
            incomeValue.incomeType === IncomeType.Union ||
            incomeValue.incomeType === IncomeType.ActiveDuty) &&
          incomeValue.employment?.employmentType !== EmploymentType.StandardEmployment
        ) {
          this.formGroup().patchValue({
            employment: { employmentType: EmploymentType.StandardEmployment },
          });
        } else if (
          incomeValue.incomeType === IncomeType.StandardSelfEmployment &&
          incomeValue.employment?.employmentType !== EmploymentType.SelfEmployment
        ) {
          this.formGroup().patchValue({
            employment: { employmentType: EmploymentType.SelfEmployment },
          });
        }
      },
      { allowSignalWrites: true },
    );

    effect(() => {
      if (this.employmentStatus() !== EmploymentStatus.Previous) {
        this.employerEndDateControl().patchValue(null);
      }
    });
  }

  onToggleChange(toggleChange: MatSlideToggleChange) {
    this.employerEndDateControl().markAsDirty();
    if (toggleChange.checked) {
      this.formGroup().patchValue({ employment: { employmentStatus: EmploymentStatus.Previous } });
    } else {
      this.formGroup().patchValue({ employment: { employmentStatus: null } });
    }
  }
}
