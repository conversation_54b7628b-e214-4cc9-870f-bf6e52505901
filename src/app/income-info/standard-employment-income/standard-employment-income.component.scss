.grid-container {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  column-gap: 16px;
  row-gap: 12px;

  .employer-name {
    grid-column: span 4;
  }

  .job-title {
    grid-column: span 6;
  }

  .income-payment {
    grid-column: span 7;
  }

  .start-date {
    grid-column: span 3;
  }

  .end-date {
    grid-column: span 3;
  }

  mat-slide-toggle {
    grid-column: span 10;
    display: flex;
    width: max-content;
    margin-bottom: 20px;
  }
}
