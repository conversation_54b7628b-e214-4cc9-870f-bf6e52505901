<ng-container [formGroup]="formGroup()">
  @if (formGroup().value.incomeType) {
    <div class="header">
      <span class="rkt-Label-14 rkt-FontWeight--500">{{
        formGroup().value.incomeType | incomeDisplay
      }}</span>
      <button
        mat-icon-button
        [matMenuTriggerFor]="incomeOptions"
        class="rkt-ButtonIcon"
        [disabled]="isDisabled()"
        [class.rkt-ButtonIcon--is-disabled]="isDisabled()"
      >
        <mat-icon class="rkt-Icon" color="accent">more_vert</mat-icon>
      </button>
      <mat-menu #incomeOptions class="rkt-Menu">
        <button
          [disabled]="isDisabled()"
          mat-menu-item
          class="rkt-Menu__item"
          (click)="onDeleteClick()"
        >
          <mat-icon class="rkt-Icon" svgIcon="delete-outlined"></mat-icon>
          <span class="rkt-Menu__item-text">Delete</span>
        </button>
      </mat-menu>
    </div>
    @switch (formGroup().value.incomeType) {
      @case (IncomeType.Union) {
        <app-standard-employment-income [formGroup]="formGroup()" />
      }
      @case (IncomeType.Standard) {
        <app-standard-employment-income [formGroup]="formGroup()" />
      }
      @case (IncomeType.ActiveDuty) {
        <app-standard-employment-income [formGroup]="formGroup()" />
      }
      @case (IncomeType.StandardSelfEmployment) {
        <app-standard-employment-income [formGroup]="formGroup()" />
      }
      @default {
        <app-income-payment [formGroup]="formGroup()" />
      }
    }
  } @else {
    <div class="gap-4 flex justify-start align-baseline">
      <app-income-type-autocomplete class="income-type" formControlName="incomeType" />
      <button
        [disabled]="isDisabled()"
        mat-icon-button
        [class.rkt-ButtonIcon--is-disabled]="isDisabled()"
        class="rkt-ButtonIcon"
        (click)="onDeleteClick()"
      >
        <mat-icon class="rkt-Icon leading-4" svgIcon="delete-outlined"></mat-icon>
      </button>
    </div>
  }
</ng-container>
