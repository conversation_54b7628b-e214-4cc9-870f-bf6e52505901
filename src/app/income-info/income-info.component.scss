.header {
  display: flex;
  gap: 1rem;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;

  p {
    margin-bottom: 0;
  }
}

.income-add {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.forms {
  display: flex;
  flex-direction: column;

  > * {
    border-bottom: 1px solid var(--rlxp-default-divider-color);

    &:first-child {
      padding-top: 0;
    }
    &:last-child {
      border-bottom: 0;
    }
  }

  .client-section {
    padding: 12px 0;

    &:first-child {
      padding-top: 0;
    }
    &:last-child {
      padding-bottom: 0;
    }

    .income-section {
      padding-top: 8px;
    }
  }
}
