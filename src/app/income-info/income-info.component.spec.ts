import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { MockBuilder } from 'ng-mocks';
import { ClientStateService } from '../services/entity-state/client-state/client-state.service';
import { IncomeFormService } from '../services/entity-state/income-state/income-form.service';
import { IncomeStateService } from '../services/entity-state/income-state/income-state.service';
import { FormNavSectionService } from '../services/form-nav/form-nav-section.service';
import { IncomeInfoComponent } from './income-info.component';

describe('IncomeInfoComponent', () => {
  let component: IncomeInfoComponent;
  let fixture: ComponentFixture<IncomeInfoComponent>;

  beforeEach(() =>
    MockBuilder(IncomeInfoComponent)
      .mock(IncomeFormService, { clientIncomeMap: signal({}) })
      .mock(FormNavSectionService, { activeSection: signal(null) })
      .mock(IncomeStateService, { state: signal(undefined) })
      .mock(ClientStateService, { stateValues: signal([]) }),
  );
  beforeEach(() => {
    fixture = TestBed.createComponent(IncomeInfoComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
