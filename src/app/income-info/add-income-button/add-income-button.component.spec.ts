import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';

import { MockBuilder } from 'ng-mocks';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { AddIncomeButtonComponent } from './add-income-button.component';

describe('AddIncomeButtonComponent', () => {
  let component: AddIncomeButtonComponent;
  let fixture: ComponentFixture<AddIncomeButtonComponent>;

  beforeEach(() =>
    MockBuilder(AddIncomeButtonComponent).mock(LoanEditingState, {
    isLoanEditingDisabled: signal(false),
  }),
  );
  beforeEach(() => {
    fixture = TestBed.createComponent(AddIncomeButtonComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('clients', []);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
