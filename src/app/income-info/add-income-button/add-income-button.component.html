<button
  [disabled]="isDisabled()"
  mat-button
  data-synthetic-monitor-id="addIncome"
  class="rkt-Button rkt-Button--large rkt-Button--tertiary rkt-Button--has-icon"
  [class.rkt-Button--is-disabled]="isDisabled()"
  color="accent"
  [matMenuTriggerFor]="menu"
>
  <mat-icon class="rkt-Icon" color="primary" svgIcon="add_circle-outlined"></mat-icon>
  Add Income
</button>

<mat-menu #menu="matMenu">
  @for (client of clients(); track client) {
    <button
      mat-menu-item (click)="onClientClick(client)"
      [attr.data-synthetic-monitor-id]="'rlxp-add-income-client-' + $index">
      {{ client | clientName }}
    </button>
  }
</mat-menu>
