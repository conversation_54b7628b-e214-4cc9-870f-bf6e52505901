import { formatCurrency } from '@angular/common';
import { Component, computed, inject, viewChild } from '@angular/core';
import {
  Client,
  Income,
  IncomeType,
  isEmploymentIncome,
  isSelfEmploymentIncome,
} from '@rocket-logic/rl-xp-bff-models';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { FormSectionComponent } from '../form-section/form-section.component';
import { ClientStateService } from '../services/entity-state/client-state/client-state.service';
import { AllIncomeGroup } from '../services/entity-state/income-state/form-types';
import { IncomeFormService } from '../services/entity-state/income-state/income-form.service';
import { IncomeStateService } from '../services/entity-state/income-state/income-state.service';
import { mapIncomeTypeToString } from '../services/entity-state/income-state/supported-income';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { TileSkeletonComponent } from '../tile/tile-skeleton/tile-skeleton.component';
import { TileComponent } from '../tile/tile.component';
import { ClientNamePipe } from '../util/client-name.pipe';
import { getClientName } from '../util/get-client-name';
import { openFormSection } from '../util/open-form-section';
import { AddIncomeButtonComponent } from './add-income-button/add-income-button.component';
import { BaseIncomeComponent } from './base-income/base-income.component';
import { IncomeSkeletonComponent } from './income-skeleton/income-skeleton.component';

@Component({
  selector: 'app-income-info',
  standalone: true,
  imports: [
    FormSectionComponent,
    BaseIncomeComponent,
    AddIncomeButtonComponent,
    ClientNamePipe,
    RktTagEnterpriseModule,
    IncomeSkeletonComponent,
    TileComponent,
    TileSkeletonComponent,
  ],
  templateUrl: './income-info.component.html',
  styleUrl: './income-info.component.scss',
})
export class IncomeInfoComponent {
  formService = inject(IncomeFormService);
  incomeStateService = inject(IncomeStateService);
  clientStateService = inject(ClientStateService);
  formNavSectionService = inject(FormNavSectionService);
  formSectionComponentRef = viewChild.required(FormSectionComponent);
  readonly FormSection = FormSection;

  isFetching = computed(() => {
    const { data, fetching } = this.incomeStateService.state() ?? {};
    return (fetching && !data) ?? false;
  });
  clients = computed(() => this.clientStateService.stateValues());
  primaryClientId = computed(() => this.clients().find((client) => client.isPrimaryBorrower)?.id);

  clientIncomeMap = computed(() =>
    Object.entries(this.formService.clientIncomeMap())
      .map(([clientId, incomeMap]) => {
        const client = this.clients().find((client) => client.id === clientId);
        return { client, incomeMap };
      })
      .filter(
        (
          value,
        ): value is {
          client: Client;
          incomeMap: Map<string, AllIncomeGroup>;
        } => value.client !== undefined,
      ),
  );

  incomeSectionSummary = computed(() => {
    const clientIncomeEntries = this.clientIncomeMap();
    const summaries = clientIncomeEntries.map(({ client, incomeMap }) => {
      const clientName = getClientName(client);
      let incomeSummary = Array.from(incomeMap.entries()).map(([_, incomeGroup]) =>
        this.getIncomeSummary(incomeGroup),
      );
      if (incomeSummary.length === 0) {
        incomeSummary = [{ label: '', content: 'No Income' }];
      }
      return {
        clientName,
        incomeSummary,
      };
    });

    return this.filterSummaries(summaries);
  });

  // if none of the clients have income we do not want to display anything
  private filterSummaries(
    summaries: {
      clientName: string;
      incomeSummary: {
        label: string;
        content: string;
      }[];
    }[],
  ) {
    const filteredSummaries = summaries.filter((summary) => {
      return !summary.incomeSummary.every((item) => item.content === 'No Income');
    });
    return filteredSummaries.length > 0 ? summaries : [];
  }

  hasIncome = computed(() => {
    return this.clientIncomeMap().some((incomeMaps) => {
      const incomeFormArray = Array.from(incomeMaps.incomeMap.values());
      return incomeFormArray.length > 0;
    });
  });

  isPrimaryClient(clientId: string) {
    return this.primaryClientId() === clientId;
  }

  addIncome(client: Client) {
    this.formService.addIncome(client.id!);
  }

  onDeleteIncome(client: Client, incomeId: string, incomeType: IncomeType) {
    this.formService.deleteIncome(client.id!, incomeId, incomeType);
  }

  openFormSection() {
    openFormSection(() => this.formSectionComponentRef());
  }

  private getIncomeSummary(incomeForm: AllIncomeGroup) {
    const income = incomeForm.getRawValue() as Income;
    const incomeTypeControl = incomeForm.controls['incomeType'];
    const label =
      incomeTypeControl.value != null
        ? mapIncomeTypeToString(incomeTypeControl.value)
        : 'Unknown Income Type';
    let employerName = '';
    let amountString = '';
    const amount = income.totalMonthlyIncomeAmount
      ? formatCurrency(income.totalMonthlyIncomeAmount, 'en-US', '$', 'USD')
      : null;
    if (amount !== null) {
      amountString = `${amount}/monthly`;
    } else {
      amountString = 'No Amount Given';
    }
    if (isEmploymentIncome(income) || isSelfEmploymentIncome(income)) {
      const employment = income.employment;
      employerName = employment?.employerName ?? '';
    }

    const content = employerName ? `${employerName}: ${amountString}` : amountString;
    return { label, content };
  }
}
