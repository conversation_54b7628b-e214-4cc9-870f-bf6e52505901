<ng-container>
  <div class="container">
    <app-formatted-number-input
      [control]="clientReportedAmountControl()"
      [allowNegative]="false"
      prefix="$"
      label="Amount Earned"
      appRlaHighlight
      [showUntouchedError]="showUntouchedError()"
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-formatted-number-input>
    <app-select-field
      label="Frequency"
      dataSyntheticMonitorId="frequency"
      [control]="paymentFormGroup().controls.frequency"
      appRlaHighlight
      [options]="frequencyOptions"
      [errorStateMatcher]="errorStateMatcher()"
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-select-field>
  </div>
</ng-container>
