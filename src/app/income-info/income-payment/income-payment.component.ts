import { Component, computed, input } from '@angular/core';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { IncomePaymentFrequency } from '@rocket-logic/rl-xp-bff-models';
import { SelectFieldComponent } from '../../_shared/components/select-field/select-field.component';
import { RlaFormFieldSuffixComponent } from '../../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component';
import { RlaHighlightDirective } from '../../assistant/directives/rla-highlight.directive';
import { FormattedNumberInputComponent } from '../../question-input/formatted-number-input/formatted-number-input.component';
import {
  AllIncomeGroup,
  AllPaymentGroup,
} from '../../services/entity-state/income-state/form-types';
import { UntouchedErrorStateMatcher } from '../../util/error-state-matchers/untouched-error-state-matcher';
import { pascalCaseSplit } from '../../util/formatting-helpers';

@Component({
  selector: 'app-income-payment',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatSelectModule,
    FormattedNumberInputComponent,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    SelectFieldComponent,
  ],
  templateUrl: './income-payment.component.html',
  styleUrl: './income-payment.component.scss',
})
export class IncomePaymentComponent {
  readonly frequencyOptions = Object.values(IncomePaymentFrequency).map(value => ({
    value,
    display: pascalCaseSplit(value),
  }));
  formGroup = input.required<AllIncomeGroup>();
  showUntouchedError = input<boolean>(false);
  paymentFormGroup = computed(() => this.formGroup().get('base') as AllPaymentGroup);
  clientReportedAmountControl = computed(
    () => this.paymentFormGroup().get('clientReportedAmount') as FormControl,
  );
  errorStateMatcher = computed(() => new UntouchedErrorStateMatcher(this.showUntouchedError()));
}
