import { Component, computed, effect, inject, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleChange, MatSlideToggleModule } from '@angular/material/slide-toggle';
import { Address, Client, EscrowWaiver, ResidencyType } from '@rocket-logic/rl-xp-bff-models';
import { PaymentFrequency } from '@rocket-logic/rl-xp-bff-models/dist/enums/payment-frequency';
import { PropertyType } from '@rocket-logic/rl-xp-bff-models/dist/enums/subject-property-type';
import { RktSkeletonModule } from '@rocketcentral/rocket-design-system-angular';
import { map, startWith } from 'rxjs/operators';
import { AddressComponent, AddressType } from '../address/address.component';
import { InputSectionDirective } from '../form-nav/nav-section/input-section.directive';
import { FormSectionComponent } from '../form-section/form-section.component';
import { MilestoneChipComponent } from '../form-section/milestone-chip/milestone-chip.component';
import { FormattedDateInputComponent } from '../question-input/formatted-date-input/formatted-date-input.component';
import { FormattedNumberInputComponent } from '../question-input/formatted-number-input/formatted-number-input.component';
import { ClientFormService } from '../services/entity-state/client-state/client-form.service';
import { UseAsSubjectHandlerService } from '../services/entity-state/client-state/use-as-subject-handler.service';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import {
  LoanFormRef,
  LoanFormService,
} from '../services/entity-state/loan-state/loan-form.service';
import { LoanInfoFormListenerService } from '../services/entity-state/loan-state/loan-info-form-listener.service';
import { LoanStateService } from '../services/entity-state/loan-state/loan-state.service';
import { SubjectPropertyFormListenerService } from '../services/entity-state/subject-property-state/subject-property-form-listener.service';
import { SubjectPropertyFormRef } from '../services/entity-state/subject-property-state/subject-property-form.service';
import { SubjectPropertyStateService } from '../services/entity-state/subject-property-state/subject-property-state.service';
import { FormInput, ProductMilestoneFormInput } from '../services/form-nav/form-nav-input.service';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { SubjectPropertySkeletonComponent } from '../subject-property/subject-property-skeleton/subject-property-skeleton.component';
import { TileSkeletonComponent } from '../tile/tile-skeleton/tile-skeleton.component';
import { TileComponent } from '../tile/tile.component';
import { ATTACHMENT } from '../util/attachment';
import { ClientNamePipe } from '../util/client-name.pipe';
import { formatAddress } from '../util/format-address';
import { MANUFACTURED_TYPES } from '../util/manufactured-types';
import { OCCUPANCY } from '../util/occupancy';
import { openFormSection } from '../util/open-form-section';
import { PascalCaseSplitPipe } from '../util/pascal-case-split.pipe';
import { PAYMENT_FREQUENCY } from '../util/payment-frequency';
import { PROPERTY } from '../util/property';
import { SELLER_RELATIONSHIP } from '../util/seller-relationship';
import { STATES } from '../util/states';

@Component({
  selector: 'app-subject-property-info',
  standalone: true,
  imports: [
    FormSectionComponent,
    ReactiveFormsModule,
    RktSkeletonModule,
    MatSlideToggleModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatDatepickerModule,
    MatIconModule,
    MatButtonModule,
    AddressComponent,
    FormattedNumberInputComponent,
    SubjectPropertySkeletonComponent,
    InputSectionDirective,
    PascalCaseSplitPipe,
    FormattedDateInputComponent,
    ClientNamePipe,
    TileComponent,
    TileSkeletonComponent,
    MilestoneChipComponent,
  ],
  templateUrl: './subject-property.component.html',
  styleUrl: './subject-property.component.scss',
})
export class SubjectPropertyComponent {
  formServiceRef = inject(SubjectPropertyFormRef);
  loanFormRef = inject(LoanFormRef);
  loanFormService = inject(LoanFormService);
  loanStateService = inject(LoanStateService);
  loanEditingState = inject(LoanEditingState);
  subjectPropertyStateService = inject(SubjectPropertyStateService);
  loanInfoFormListener = inject(LoanInfoFormListenerService);
  subjectPropertyFormListener = inject(SubjectPropertyFormListenerService);
  useAsSubjectPropertyService = inject(UseAsSubjectHandlerService);
  clientFormService = inject(ClientFormService);
  formNavSectionService = inject(FormNavSectionService);
  formSectionComponentRef = viewChild.required(FormSectionComponent);
  readonly FormSection = FormSection;
  readonly currrentDate = new Date();

  clients = computed(() =>
    this.clientFormService
      .clientsWithSubjectPropEnabled()
      ?.filter(
        (client) =>
          ((client?.residenceInformation?.currentResidence?.residencyType !== ResidencyType.Own &&
            this.loanInfoFormListener.purchaseSelected()) ||
            this.loanInfoFormListener.refiSelected()) &&
          client?.residenceInformation?.currentResidence?.address?.addressLine1,
      ),
  );
  isDisabled = this.loanEditingState.isLoanEditingDisabled;
  addressSection = {
    state: FormInput.SubjectPropertyStateAddress,
    city: ProductMilestoneFormInput.SubjectPropertyCityAddress,
    street: ProductMilestoneFormInput.SubjectPropertyStreetAddress,
    shouldRegisterStreet: computed(
      () => this.loanInfoFormListener.hasSignedPa() || this.loanInfoFormListener.refiSelected(),
    ),
    zip: FormInput.SubjectPropertyZipAddress,
    county: ProductMilestoneFormInput.SubjectPropertyCounty,
  };

  useAsSubjectPropertyControl = this.useAsSubjectPropertyService.useAsSubjectDropdownControl;
  public readonly Frequency = Object.values(PaymentFrequency);
  hoiFormGroup = this.formServiceRef.subjectPropertyForm.controls.hoi;
  floodInsuranceFormGroup = this.formServiceRef.subjectPropertyForm.controls.floodInsurance;
  refiSubjectPropertyInformationGroup =
    this.formServiceRef.subjectPropertyForm.controls.refinanceInformation;
  readonly AddressType = AddressType;
  readonly ProductMilestones = ProductMilestoneFormInput;

  appraisalOnlyPropertyTypes: PropertyType[] = [PropertyType.Townhouse];
  propertyTypeOptions = PROPERTY.filter(
    (option) => !this.appraisalOnlyPropertyTypes.includes(option.value),
  );
  propertyType = computed(() => this.subjectPropertyStateService.state()?.data?.propertyType);

  isAppraisalOnlyPropertyType = computed(() =>
    this.appraisalOnlyPropertyTypes.includes(this.propertyType()!),
  );
  selectedPropertyTypeOption = computed(() =>
    PROPERTY.find((opt) => opt.value === this.propertyType()),
  );

  shouldShowTexas50a6 = computed(() => {
    const isRefi = this.loanInfoFormListener.refiSelected();
    const isSubjectPropertyInTexas = this.subjectPropertyFormListener.isTexasProperty();

    return isRefi && isSubjectPropertyInTexas;
  });

  mappedAddress = toSignal(
    this.formServiceRef.subjectPropertyForm.controls['address'].valueChanges.pipe(
      startWith(this.formServiceRef.subjectPropertyForm.controls['address'].value),
      map((address) => formatAddress(address as Address)),
    ),
  );

  readonly isMobileManufactured = this.subjectPropertyFormListener.isMobileManufactured;
  readonly isTwoToFourFamily = this.subjectPropertyFormListener.isTwoToFourFamily;
  readonly isAttachmentType = this.subjectPropertyFormListener.isAttachmentType;
  readonly occupancyTypeOptions = OCCUPANCY;
  readonly attachmentType = ATTACHMENT;
  readonly paymentFrequency = PAYMENT_FREQUENCY;
  readonly sellerRelationship = SELLER_RELATIONSHIP;
  readonly manufacturedTypes = MANUFACTURED_TYPES;
  readonly stateOptions = STATES;

  private readonly hoaRequiredPropertyTypes = [
    PropertyType.ManufacturedCondominium,
    PropertyType.SiteCondo,
    PropertyType.Condominium,
    PropertyType.Coop,
  ];

  isNalActive = this.loanFormRef.isNonArmsLength;
  isNewConstructionControl =
    this.loanFormRef.loanForm.controls.newConstructionDetails.controls.isNewConstruction;
  hasFloodInsurance = signal(false);
  readonly EscrowWaiver = Object.values(EscrowWaiver);

  private hoaPropertyTypeSelected$ = this.formServiceRef.subjectPropertyForm
    .get('propertyType')!
    .valueChanges.pipe(
      map((propertyType) =>
        propertyType ? this.hoaRequiredPropertyTypes.includes(propertyType) : false,
      ),
    );
  hoaPropertyTypeSelected = toSignal(this.hoaPropertyTypeSelected$);

  constructor() {
    this.formServiceRef.subjectPropertyForm.controls.floodInsurance.valueChanges
      .pipe(takeUntilDestroyed())
      .subscribe((changes) => (changes.amount ? this.hasFloodInsurance.set(true) : null));

    effect(() => {
      if (this.clients()?.length === 0) {
        this.useAsSubjectPropertyControl.setValue(null);
      }
    });
  }

  hasHoa = computed(() => {
    const hoaPaymentExists =
      this.subjectPropertyStateService.state()?.data?.hoaPayment !== undefined;
    return hoaPaymentExists || this.hoaPropertyTypeSelected();
  });

  get sellerRelationshipToBuyerControl() {
    return this.loanFormRef.loanForm.controls.sellerRelationshipToBorrower;
  }

  get hoaAmountControl() {
    return this.formServiceRef.subjectPropertyForm.controls.hoaPayment.controls.amount;
  }

  get hoaFrequencyControl() {
    return this.formServiceRef.subjectPropertyForm.controls.hoaPayment.controls.frequency;
  }

  nalToggleChanges(checked: boolean) {
    this.isNalActive.set(checked);

    if (!checked) {
      this.loanFormRef.loanForm.controls.sellerRelationshipToBorrower.markAsDirty();
      this.loanFormRef.loanForm.controls.sellerRelationshipToBorrower.setValue(null);
    }
  }

  hoaToggleChanges(checked: boolean) {
    if (!checked) {
      this.formServiceRef.subjectPropertyForm.controls.hoaPayment.controls.amount.markAsDirty();
      this.formServiceRef.subjectPropertyForm.controls.hoaPayment.controls.frequency.markAsDirty();

      this.formServiceRef.subjectPropertyForm.controls.hoaPayment.controls.amount.setValue(null);
      this.formServiceRef.subjectPropertyForm.controls.hoaPayment.controls.frequency.setValue(null);
    }
  }

  getAddressForm(subjectPropertyForm: FormGroup): FormGroup {
    return subjectPropertyForm.get('address') as FormGroup;
  }

  getManufacturedInfoForm(manufacturedInfoForm: FormGroup): FormGroup {
    return manufacturedInfoForm.get('manufacturedInformation') as FormGroup;
  }

  getHoaPaymentForm(hoaPaymentForm: FormGroup): FormGroup {
    return hoaPaymentForm.get('hoaPayment') as FormGroup;
  }

  getNumberOfUnitsControl(numberOfUnitsForm: FormGroup): FormControl {
    return numberOfUnitsForm.get('numberOfUnits') as FormControl;
  }

  floodInsuranceToggle(toggleChange: MatSlideToggleChange) {
    this.hasFloodInsurance.set(toggleChange.checked);
    if (!toggleChange.checked && this.floodInsuranceFormGroup.controls.amount.value) {
      this.floodInsuranceFormGroup.controls.amount.markAsDirty();
      this.floodInsuranceFormGroup.controls.amount.setValue(null);
    }
  }

  openFormSection() {
    openFormSection(() => this.formSectionComponentRef());
  }

  compareClientById(client1?: Client, client2?: Client) {
    return client1?.id === client2?.id;
  }
}
