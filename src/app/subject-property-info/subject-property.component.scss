.property-occupancy-container {
  padding-top: 12px;
  display: grid;
  column-gap: 16px;
  row-gap: 12px;
  grid-template-columns: repeat(2, 1fr);
}

.hoa-nal-container {
  padding-top: 12px;
  padding-bottom: 12px;
  column-gap: 16px;
  row-gap: 12px;
  display: flex;
}

.property-conditions-container,
.hoa-container {
  display: grid;
  column-gap: 16px;
  row-gap: 12px;
  grid-template-columns: repeat(2, 1fr);
}

.nal-container {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
}

.non-arms-container {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
}

.use-as-subject-prop-container {
  display: flex;
  padding-bottom: 12px;

  mat-form-field {
    flex: 0.5;
  }
}
