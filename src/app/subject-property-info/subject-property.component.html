<app-form-section [title]="'Subject Property'" [formSection]="FormSection.SubjectProperty">
  @if (!subjectPropertyStateService.isFetching()) {
    <ng-container [formGroup]="formServiceRef.subjectPropertyForm">
      <div>
        <p class="rkt-Label-14 rkt-FontWeight--500">Address</p>
        @if (clients()?.length) {
          <div class="use-as-subject-prop-container">
            <mat-form-field class="rkt-FormField">
              <mat-label>Use As Subject Property</mat-label>
              <mat-select
                [formControl]="useAsSubjectPropertyControl"
                [compareWith]="compareClientById"
                class="rkt-Input"
                data-testid="use-as-subject-property-select"
              >
                <mat-option [value]="null">None</mat-option>
                @for (client of clients(); track client) {
                  <mat-option [value]="client">{{
                    client?.residenceInformation?.currentResidence?.address?.addressLine1
                  }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
          </div>
        }
        <app-address
          [addressForm]="getAddressForm(formServiceRef.subjectPropertyForm)"
          [addressSection]="addressSection"
          [showCounty]="true"
        />
        <div class="property-occupancy-container">
          <mat-form-field class="rkt-FormField">
            <mat-label>Property Type</mat-label>
            <mat-select
              class="rkt-Input"
              appNavInput
              [inputSection]="ProductMilestones.PropertyType"
              formControlName="propertyType"
              data-testid="property-type-select"
            >
              <mat-option [value]="null">None</mat-option>
              @for (status of propertyTypeOptions; track status) {
                <mat-option
                  [attr.data-synthetic-monitor-id]="'property-Type-option_' + status.value"
                  [value]="status.value"
                  >{{ status.display }}</mat-option
                >
              }
              @if (isAppraisalOnlyPropertyType()) {
                <mat-option [value]="selectedPropertyTypeOption()!.value">{{
                  selectedPropertyTypeOption()!.display
                }}</mat-option>
              }
            </mat-select>
          </mat-form-field>
          @if (isAttachmentType()) {
            <mat-form-field class="rkt-FormField">
              <mat-label>Attachment Type</mat-label>
              <mat-select class="rkt-Input" formControlName="attachmentType" data-testid="attachment-type-select">
                <mat-option [value]="null">None</mat-option>
                @for (status of attachmentType; track status) {
                  <mat-option [value]="status.value">{{ status.display }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
          }
          <mat-form-field class="rkt-FormField">
            <mat-label>Occupancy Type</mat-label>
            <mat-select
              class="rkt-Input"
              appNavInput
              [inputSection]="ProductMilestones.Occupancy"
              formControlName="occupancyType"
              data-testid="occupancy-type-select"
            >
              <mat-option [value]="null">None</mat-option>
              @for (status of occupancyTypeOptions; track status) {
                <mat-option
                  [attr.data-synthetic-monitor-id]="'occupancy-type-select-option_' + status.value"
                  [value]="status.value"
                  >{{ status.display }}</mat-option
                >
              }
            </mat-select>
          </mat-form-field>
        </div>

        <div class="hoa-nal-container">
          <mat-slide-toggle
            [disabled]="isDisabled()"
            class="rkt-SlideToggle"
            [checked]="hasHoa()"
            (change)="hoaToggleChanges($event.checked)"
            #hoaToggle
            data-testid="hoa-toggle"
          >
            <span class="rkt-SlideToggle__label rkt-Spacing--ml8">HOA</span>
          </mat-slide-toggle>
          @if (loanInfoFormListener.purchaseSelected()) {
            <mat-slide-toggle
              class="rkt-SlideToggle"
              [checked]="isNalActive()"
              (change)="nalToggleChanges($event.checked)"
              [disabled]="isDisabled()"
              data-testid="nal-toggle"
            >
              <span class="rkt-SlideToggle__label rkt-Spacing--ml8">
                Non-arms length transaction
              </span>
            </mat-slide-toggle>
            <mat-slide-toggle class="rkt-SlideToggle" [formControl]="isNewConstructionControl" data-testid="new-construction-toggle">
              <span class="rkt-SlideToggle__label rkt-Spacing--ml8"> New Construction </span>
            </mat-slide-toggle>
          } @else {
            @if (shouldShowTexas50a6()) {
              <mat-slide-toggle
                [formControl]="loanFormService.loanForm.controls.isPreviousLoanTexas50a6"
                class="rkt-SlideToggle"
                color="accent"
                data-testid="texas50a6-toggle"
              >
                <span class="rkt-SlideToggle__label rkt-Spacing--ml8"> Tx50A6 </span>
              </mat-slide-toggle>
            }

            <mat-slide-toggle
              class="rkt-SlideToggle mb-4"
              [checked]="hasFloodInsurance()"
              (change)="floodInsuranceToggle($event)"
              [disabled]="isDisabled()"
              color="accent"
              data-testid="flood-insurance-toggle"
            >
              <span class="rkt-SlideToggle__label rkt-Spacing--ml8"> Flood Insurance</span>
            </mat-slide-toggle>
          }
        </div>
      </div>
      @if (isMobileManufactured() || isTwoToFourFamily()) {
        <div>
          <p class="rkt-Label-14 rkt-FontWeight--500">Property Type Conditions</p>
          <div class="property-conditions-container">
            @if (isMobileManufactured()) {
              <mat-form-field
                [formGroup]="getManufacturedInfoForm(formServiceRef.subjectPropertyForm)"
                class="rkt-FormField"
              >
                <mat-label>Mobile Manufactured Type</mat-label>
                <mat-select class="rkt-Input" formControlName="numberOfSections" data-testid="manufactured-type-select">
                  <mat-option [value]="null">None</mat-option>
                  @for (status of manufacturedTypes; track status) {
                    <mat-option [value]="status.value">{{ status.display }}</mat-option>
                  }
                </mat-select>
              </mat-form-field>
            }
            @if (isTwoToFourFamily()) {
              <div>
                <app-formatted-number-input
                  [allowNegative]="false"
                  label="Number of Units"
                  [control]="getNumberOfUnitsControl(formServiceRef.subjectPropertyForm)"
                />
              </div>
            }
          </div>
        </div>
      }
    </ng-container>
    <ng-container [formGroup]="loanFormService.loanForm">
      @if (isNalActive() && loanInfoFormListener.purchaseSelected()) {
        <div>
          <p class="rkt-Label-14 rkt-FontWeight--500">Non-Arms Length Conditions</p>
          <div class="non-arms-container">
            <mat-form-field class="rkt-FormField">
              <mat-label>Relationship to Seller</mat-label>
              <mat-select class="rkt-Input" formControlName="sellerRelationshipToBorrower" data-testid="seller-relationship-select">
                <mat-option [value]="null">None</mat-option>
                @for (status of sellerRelationship; track status) {
                  <mat-option [value]="status.value">{{ status.display }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      } @else if (loanInfoFormListener.refiSelected()) {
        <div class="flex gap-4">
          <div class="flex-1">
            <p class="rkt-Label-14 rkt-FontWeight--500">Property Insurance</p>
            <div [formGroup]="hoiFormGroup">
              <app-formatted-number-input
                [allowNegative]="false"
                label="Annual HOI Amount"
                [control]="hoiFormGroup.controls.amount"
              />
            </div>
          </div>
          @if (hasFloodInsurance()) {
            <div class="flex-1">
              <p class="rkt-Label-14 rkt-FontWeight--500">Flood Insurance</p>
              <div [formGroup]="floodInsuranceFormGroup">
                <app-formatted-number-input
                  [allowNegative]="false"
                  label="Annual Flood Insurance Amount"
                  [control]="floodInsuranceFormGroup.controls.amount"
                />
              </div>
            </div>
          }
          <div class="flex-1">
            <p class="rkt-Label-14 rkt-FontWeight--500">Escrow Waiver</p>
            <mat-form-field class="rkt-FormField w-full" floatLabel="always">
              <mat-label>Escrow Waiver</mat-label>
              <mat-select class="rkt-Input" formControlName="escrowWaiver" placeholder="None" data-testid="escrow-waiver-select">
                <mat-option [value]="null">None</mat-option>
                @for (waiver of EscrowWaiver; track waiver) {
                  <mat-option [value]="waiver">{{ waiver | pascalCaseSplit }}</mat-option>
                }
              </mat-select>
            </mat-form-field>
          </div>
        </div>
        <div class="grid grid-cols-3 gap-4 grid-rows-none">
          <p class="rkt-Label-14 rkt-FontWeight--500 col-start-1">Original Purchase Details</p>
          <p class="col-start-2"></p>
          <p class="rkt-Label-14 rkt-FontWeight--500 col-start-3">Estimated Value</p>
          <ng-container [formGroup]="refiSubjectPropertyInformationGroup">
            <app-formatted-date-input
              label="Date Acquired"
              [control]="formServiceRef.subjectPropertyForm.controls.ownershipTransferredOn"
              [maximumDate]="currrentDate"
            />
            <app-formatted-number-input
              [allowNegative]="false"
              label="Original Purchase Price"
              prefix="$"
              [control]="refiSubjectPropertyInformationGroup.controls.ownershipTransferPrice"
            />
            <app-formatted-number-input
              [allowNegative]="false"
              label="Estimated Value"
              prefix="$"
              [control]="formServiceRef.subjectPropertyForm.controls.estimatedPropertyValue"
              appNavInput
              [inputSection]="ProductMilestones.EstimatedValue"
            />
          </ng-container>
        </div>
      }
      @if (hoaToggle.checked) {
        <div>
          <p class="rkt-Label-14 rkt-FontWeight--500">HOA Conditions</p>
          <div
            class="hoa-container"
            [formGroup]="getHoaPaymentForm(formServiceRef.subjectPropertyForm)"
          >
            <app-formatted-number-input
              [control]="hoaAmountControl"
              [allowNegative]="false"
              prefix="$"
              label="Payment Amount"
              appNavInput
              [inputSection]="ProductMilestones.HoaPaymentAmount"
            />
            <mat-form-field class="rkt-FormField">
              <mat-label>Frequency</mat-label>
              <mat-select class="rkt-Input" formControlName="frequency">
                <mat-option [value]="null">None</mat-option>
                <mat-option value="Monthly">Monthly</mat-option>
                <!-- Temporary change until AMP supports more frequency types
                 @for (status of paymentFrequency; track status) {
                  <mat-option [value]="status.value">{{ status.display }}</mat-option>
                } -->
              </mat-select>
            </mat-form-field>
          </div>
        </div>
      }
    </ng-container>
  } @else {
    <app-subject-property-skeleton />
  }

  @if (!subjectPropertyStateService.isFetching()) {
    <div section-summary class="row flex-wrap">
      <app-tile
        [label]="'Address'"
        [content]="mappedAddress()"
        (tileClick)="openFormSection()"
      ></app-tile>
      @if (propertyType()) {
        <app-tile
          [label]="'Property Type'"
          [content]="propertyType() | pascalCaseSplit"
          (tileClick)="openFormSection()"
        ></app-tile>
      }
    </div>
  } @else {
    <div section-summary class="row flex-wrap">
      <app-tile-skeleton></app-tile-skeleton>
    </div>
  }
  @if (!subjectPropertyStateService.isFetching()) {
    <div milestone-chip>
      <app-milestone-chip
        [formSection]="FormSection.SubjectProperty"
        (chipClick)="openFormSection()"
      ></app-milestone-chip>
    </div>
  }
</app-form-section>
