import { CommonModule } from '@angular/common';
import {
  Component,
  DestroyRef,
  Injector,
  OnInit,
  Signal,
  effect,
  forwardRef,
  inject,
  input,
  runInInjectionContext,
} from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ValidatorFn, Validators } from '@angular/forms';
import { filter, map, startWith, switchMap, tap } from 'rxjs';
import { CONTROL_PROVIDER, ControlProvider } from '../_shared/tokens/control-provider';
import { FormSection, InputSectionDirective } from '../form-nav/nav-section/input-section.directive';
import { FormattedNumberInputComponent } from '../question-input/formatted-number-input/formatted-number-input.component';
import { ProductMilestoneFormInput } from '../services/form-nav/form-nav-input.service';

export enum LastTouchedInput {
  Amount = 'Amount',
  Percentage = 'Percentage',
}

@Component({
  selector: 'app-calculated-input',
  templateUrl: './calculated-input.component.html',
  styleUrls: ['./calculated-input.component.scss'],
  standalone: true,
  imports: [FormattedNumberInputComponent, CommonModule, InputSectionDirective],
  providers: [
    {
      provide: CONTROL_PROVIDER,
      useExisting: forwardRef(() => CalculatedInputComponent),
    },
  ],
})
export class CalculatedInputComponent implements OnInit, ControlProvider {
  private destroyRef = inject(DestroyRef);
  injector = inject(Injector);
  total = input<number | undefined>(0);
  prefix = input<string>('');
  label = input<string>('');
  suffix = input<string>('');
  allowNegative = input.required<boolean>();
  maxLength = input<number>();
  controlType = input<FormSection>();
  readonly ProductInputMilestone = ProductMilestoneFormInput;

  private minValidator = Validators.min(0);
  private maxValidator?: ValidatorFn;

  percentageControl = new FormControl<number | null>(0, {
    validators: [Validators.min(0), Validators.max(100)],
  });

  control = input.required<FormControl<number | null>>();

  percentageChanged = toSignal(
    this.percentageControl.valueChanges.pipe(
      filter((change) => change !== this.percentage),
      tap((change) => {
        this.lastTouchedInput = LastTouchedInput.Percentage;
        this.percentage = change ?? 0;
      }),
      map((change) => change),
    ),
  ) as Signal<number | null>;

  amountChanged = toSignal(
    toObservable(this.control).pipe(
      switchMap((control) => control.valueChanges),
      filter((change) => change !== this.amount),
      tap((change) => {
        this.lastTouchedInput = LastTouchedInput.Amount;
        this.amount = change ?? 0;
      }),
      map((change) => change),
    ),
  );

  lastTouchedInput: LastTouchedInput = LastTouchedInput.Percentage;
  private amount = 0;
  private percentage = 0;
  private totalCopy = 0;

  disabledState$ = toObservable(this.control).pipe(
    switchMap((control) =>
      control.statusChanges.pipe(
        startWith(this.control()?.status ?? 'ENABLED'),
        map((controlStatus) => controlStatus === 'DISABLED'),
      ),
    ),
  );

  constructor() {
    effect(
      () => {
        const total = this.total();

        this.totalCopy = total ?? 0;
        if (!total || total <= 0) {
          return;
        }

        if (!this.control().hasValidator(this.minValidator)) {
          this.control().addValidators(this.minValidator);
        }
        if (this.maxValidator) {
          this.control().removeValidators(this.maxValidator);
        }
        const maxValidatorAmount = this.getMaxValidationAmount(total);
        this.maxValidator = Validators.max(maxValidatorAmount);
        this.control().addValidators(this.maxValidator);
        this.control().updateValueAndValidity();

        if (this.lastTouchedInput === LastTouchedInput.Percentage) {
          const percentage = this.percentageControl.value ?? 0;
          this.updateAmount(percentage, total);
        } else if (this.lastTouchedInput === LastTouchedInput.Amount) {
          const amount = this.control().value ?? 0;
          this.updatePercentage(amount, total);
        }
      },
      { allowSignalWrites: true },
    );

    effect(
      () => {
        const percentage = this.percentageChanged();
        if (percentage == null) {
          return;
        }
        this.updateAmount(percentage, this.totalCopy);

        if (percentage >= 100) {
          this.percentageControl.setValue(100, { emitEvent: false });
        }
      },
      { allowSignalWrites: true },
    );

    effect(
      () => {
        const amount = this.amountChanged();
        if (amount == null) {
          return;
        }

        this.updatePercentage(this.control().value ?? 0, this.totalCopy);
      },
      { allowSignalWrites: true },
    );
  }

  //The Input Control Signal is only guaranteed after the component has been initialized
  ngOnInit(): void {
    runInInjectionContext(this.injector, () => {
      //calculates the percentage on load since it's not saved in state
      if (this.control()?.value && this.total()) {
        this.updatePercentage(this.control().value ?? 0, this.total() ?? 0);
      }
    });

    this.disabledState$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((isDisabled) => {
      if (isDisabled) {
        this.percentageControl.disable();
      } else {
        this.percentageControl.enable();
      }
    });
  }

  private updatePercentage(amount: number, total: number): void {
    let percentage = 0;

    if (total <= amount) {
      percentage = 100;
    } else {
      percentage = (amount / total) * 100;
    }

    if (percentage > 100) {
      percentage = 100;
    }

    this.percentage = percentage;
    this.percentageControl.setValue(percentage);
  }

  private updateAmount(percentage: number, total: number): void {
    const amount = (percentage / 100) * total;
    this.amount = amount;
    this.control().markAsDirty();
    this.control().setValue(amount);
  }

  private getMaxValidationAmount(total: number): number {
    // down payment & seller concessions can not be greater than or equal to 100% of the purchase price
    if (
      this.controlType() === ProductMilestoneFormInput.DownPayment ||
      this.label() === 'Seller Concessions'
    ) {
      return total - 1;
    } else {
      return total;
    }
  }
}
