<div id="container">
  <app-formatted-number-input
    id="amount"
    [allowNegative]="allowNegative()"
    [control]="control()"
    [label]="label()"
    [prefix]="prefix()"
    appNavInput
    [shouldRegister]="controlType() !== null"
    [inputSection]="controlType()"
  />

  <app-formatted-number-input
    id="percentage"
    [allowNegative]="allowNegative()"
    [control]="percentageControl"
    suffix="%"
    [maxLength]="3"
    [ariaLabel]="label()"
  />
</div>
