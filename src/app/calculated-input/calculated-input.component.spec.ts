import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { MockBuilder, MockInstance } from 'ng-mocks';
import { FormattedNumberInputComponent } from '../question-input/formatted-number-input/formatted-number-input.component';
import { CalculatedInputComponent } from './calculated-input.component';

describe('CalculatedInputComponent', () => {
  let component: CalculatedInputComponent;
  let fixture: ComponentFixture<CalculatedInputComponent>;

  beforeEach(() => {
    return MockBuilder(CalculatedInputComponent);
  });

  beforeEach(() => {
    MockInstance(FormattedNumberInputComponent, 'matFormField', signal(undefined));
    fixture = TestBed.createComponent(CalculatedInputComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('total', undefined);
    fixture.componentRef.setInput('prefix', "");
    fixture.componentRef.setInput('label', "");
    fixture.componentRef.setInput('suffix', "");
    fixture.componentRef.setInput('allowNegative', null);
    fixture.componentRef.setInput('maxLength', null);
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
