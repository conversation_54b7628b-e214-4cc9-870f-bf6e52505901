import { Routes } from '@angular/router';
import { LoanSearchComponent } from './loan-search/loan-search.component';
import { PageNotFoundComponent } from './page-not-found/page-not-found.component';

export const routes: Routes = [
  {
    path: '',
    pathMatch: 'full',
    component: LoanSearchComponent,
  },
  {
    path: 'loan',
    loadChildren: () => import('./[rlxp-root]/rlxp-root.routes').then((c) => c.rlxpRoutes),
  },
  {
    path: 'estimator',
    loadComponent: () => import('./estimator/components/estimator-page/estimator-page.component').then((m) => m.EstimatorPageComponent),
  },
  {
    path: 'estimator/:opportunityId',
    loadComponent: () => import('./estimator/components/estimator-page/estimator-page.component').then((m) => m.EstimatorPageComponent),
  },
  {
    path: '404',
    component: PageNotFoundComponent,
  },
  { path: '**', redirectTo: '404' },
];
