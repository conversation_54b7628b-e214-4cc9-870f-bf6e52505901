import { Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { filter, startWith, switchMap } from 'rxjs';
import { environment } from '../../../environments/environment';
import { DataProviderService } from '../data-provider/data-provider.service';
import { LoanIdService } from '../loan-id/loan-id.service';

@Injectable({
  providedIn: 'root',
})
export class BankerAssignmentService {
  private dataProvider = inject(DataProviderService);
  private loanIdService = inject(LoanIdService);

  public userIsAssignedToLead$ = this.loanIdService.loanId$.pipe(
    filter(
      (loanId): loanId is string =>
        !!loanId && !loanId.startsWith('999') && environment.appEnvironment === 'prod',
    ),
    switchMap((loanId) => this.dataProvider.getIfUserIsAssignedToLead$(loanId)),
    startWith(true),
    takeUntilDestroyed(),
  );
}
