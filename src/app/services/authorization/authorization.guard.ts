import { inject } from '@angular/core';
import {
  ActivatedRouteSnapshot,
  Router,
  UrlTree,
  createUrlTreeFromSnapshot,
} from '@angular/router';
import { EMPTY, Observable, iif, of } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { LoanAuthorizationService } from './loan-authorization.service';

export const authorizationGuard = (
  route: ActivatedRouteSnapshot,
): Observable<boolean | UrlTree> => {
  const router = inject(Router);
  const authz = inject(LoanAuthorizationService);

  const loanId = route.paramMap.get('loanId')!;
  const authCheck$ = authz.checkAuthorization(loanId, ['rl-xp']).pipe(
    catchError((_) => {
      router.navigate(['']); // TODO - oops page
      return EMPTY;
    }),
    map(
      ({ authorization }) =>
        authorization.isUserAuthorized || createUrlTreeFromSnapshot(route, ['..']),
    ),
  );
  const canActivate$ = iif(() => environment.appEnvironment === 'local', of(true), authCheck$);
  return canActivate$;
};
