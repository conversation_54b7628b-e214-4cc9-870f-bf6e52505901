import { Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { notificationSubjects } from '@rocket-logic/rl-xp-bff-models';
import { AuthorizationErrorCode } from '@rocket-logic/rl-xp-bff-models/dist/authorization';
import {
  BehaviorSubject,
  EMPTY,
  Observable,
  filter,
  map,
  share,
  shareReplay,
  switchAll,
  withLatestFrom
} from 'rxjs';
import { UnauthorizedDialogComponent } from '../../unauthorized/unauthorized-dialog.component';
import { UserNotificationService } from '../notification/user-notification.service';
import {
  AuthorizationService,
  LoanAuthorization,
  VerifierExclusionCode,
} from './authorization.service';

const CLOCK_EVENT = 'rock-human-services.time-tracking.clock-changed-event';

@Injectable({
  providedIn: 'root',
})
export class LoanAuthorizationService {
  private authzService = inject(AuthorizationService);
  private notification = inject(UserNotificationService);
  private router = inject(Router);
  private dialog = inject(MatDialog);
  private dialogRef?: MatDialogRef<UnauthorizedDialogComponent>;
  private lastCall$ = new BehaviorSubject<Observable<LoanAuthorization>>(EMPTY);
  public currentAuthorization$ = this.lastCall$.pipe(switchAll());

  private clockNotification$ = this.notification.notifications$.pipe(
    filter((message) => message.type === CLOCK_EVENT),
    map((message) => message.payload as notificationSubjects.TeamMemberClockChanged),
    share(),
  );

  private clockOutWatcher = this.clockNotification$
    .pipe(
      filter((clockEvent) => clockEvent.eventtype === 'ClockOut'),
      takeUntilDestroyed(),
    )
    .subscribe((_) => this.openDialog(AuthorizationErrorCode.UserIsClockedOut));

  private clockInWatcher = this.clockNotification$
    .pipe(
      filter((clockEvent) => clockEvent.eventtype === 'ClockIn'),
      withLatestFrom(this.currentAuthorization$),
      takeUntilDestroyed(),
    )
    .subscribe(([, currentAuthorization]) =>
      this.checkAuthorization(currentAuthorization.loanId, ['rl-xp']),
    );

  private watcher = this.currentAuthorization$
    .pipe(takeUntilDestroyed())
    .subscribe(({ authorization }) => {
      if (authorization.isUserAuthorized === false) {
        const isRedirecting = this.shouldRedirect(authorization.authorizationErrorCode);
        if (isRedirecting) {
          this.router.navigate(['']);
        }
        this.openDialog(authorization.authorizationErrorCode, isRedirecting);
      }

      if (authorization.applicationAuth['rl-xp']?.read === false) {
        // TODO - what if they are authorized in general but not for this specific app
      }
    });

  private loanAccessWatcher = this.currentAuthorization$
    .pipe(
      filter((LoanAuthorization: LoanAuthorization) => {
        const errorCode = LoanAuthorization?.authorization?.authorizationErrorCode;
        return (
          errorCode !== undefined &&
          (errorCode === AuthorizationErrorCode.InAmpArchive ||
            errorCode === AuthorizationErrorCode.OriginatedByAmp)
          );
        }),
        takeUntilDestroyed(),
  ).subscribe((LoanAuthorization: LoanAuthorization) => {
    if (LoanAuthorization.authorization.authorizationErrorCode) {
      this.openDialog(LoanAuthorization.authorization.authorizationErrorCode);
    }
  });

  private schwabWatcher = this.currentAuthorization$
    .pipe(
      map((response) => response.authorization.applicationAuth['rl-xp']?.exclusion?.reason),
      takeUntilDestroyed(),
    )
    .subscribe((exclusionReason) => {
      if (exclusionReason === VerifierExclusionCode.NotASchwabBanker) {
        this.openDialog(AuthorizationErrorCode.NotASchwabBanker);
      } else if (exclusionReason === VerifierExclusionCode.SchwabClientOnRetailLoan) {
        this.openDialog(AuthorizationErrorCode.SchwabClientOnRetailLoan);
      }
    });

  /**
   * Checks and updates user-based access decisions.
   *
   * @param {string} loanNumber - The loan number to check authorization for.
   * @param {string[]} appIds - The application IDs to check authorization for.
   * @throws {Error} Throws an error if the loan number is missing.
   */
  public checkAuthorization(loanNumber: string, appIds: string[]) {
    if (!loanNumber) {
      throw new Error('loan authorization check missing loan number');
    }
    const authCheck$ = this.authzService.getAuthorization(loanNumber, appIds).pipe(shareReplay(1));
    this.lastCall$.next(authCheck$);
    return authCheck$;
  }

  private openDialog(reason?: AuthorizationErrorCode, allowClose = false) {
    this.dialogRef?.close();
    this.dialogRef = this.dialog.open(UnauthorizedDialogComponent, {
      data: { reason },
      panelClass: ['rkt-Dialog', 'rkt-Dialog--enterprise'],
      backdropClass: 'rkt-Backdrop',
      minWidth: '50%',
      disableClose: !allowClose,
    });
  }

  protected shouldRedirect(reason?: AuthorizationErrorCode) {
    return (
      reason === AuthorizationErrorCode.UserNotAllowedToAccessTeamMemberLoans ||
      reason === AuthorizationErrorCode.UserDoesNotHaveAccessToLoanInAmp
    );
  }
}
