import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import {
  CreateManualExclusionRequest,
  LoanAccessDecision,
  UserAuthorizationResponse,
} from '@rocket-logic/rl-xp-bff-models/dist/authorization';
import { BehaviorSubject, Observable, map, tap } from 'rxjs';
import { environment } from '../../../environments/environment';

// LAA verifier exclusion reasons
export enum VerifierExclusionCode {
  NotASchwabBanker = 'NotSchwabBanker',
  SchwabClientOnRetailLoan = 'SchwabClientOnRetailLoan',
  LoanIsArchived = 'LoanIsArchived',
}

export type LoanAuthorization = {
  loanId: string;
  authorization: UserAuthorizationResponse;
};

export interface LoanAuthorizationRequest {
  pilotIds: string[];
}

export enum Pilot {
  RlXp = 'rl-xp',
  RocketLogicAssets = 'RocketLogicAssets',
  Assistant = 'RlxpAssistant',
  ClientDataImport = 'RlxpClientDataImport',
}

@Injectable({
  providedIn: 'root',
})
export class AuthorizationService {
  private readonly BASE_URL = environment.dataProviderUrl;
  private httpClient = inject(HttpClient);
  private pilotDecisionsSubject = new BehaviorSubject<LoanAccessDecision>({});
  public pilotDecisions$ = this.pilotDecisionsSubject.asObservable();

  /**
   * Initiates a user access call to determine authorization for a given loan and set of application IDs.
   *
   * @param {string} loanId - The unique identifier for the loan.
   * @param {string[]} appIds - An array of application IDs to check authorization for.
   */
  getAuthorization(loanId: string, appIds: string[]): Observable<LoanAuthorization> {
    return this.httpClient
      .post<UserAuthorizationResponse>(`${this.BASE_URL}/authorization/loan/${loanId}`, {
        pilotIds: appIds,
      })
      .pipe(map((authorization): LoanAuthorization => ({ loanId, authorization })),
    );
  }

  createExclusion(
    loanId: string,
    exclusionRequest: CreateManualExclusionRequest,
  ): Observable<void> {
    return this.httpClient.post<void>(
      `${this.BASE_URL}/authorization/loan/${loanId}/exclusion`,
      exclusionRequest,
    );
  }

  /**
   * Initiates a loan access call to determine the access decision for a given loan and set of pilots.
   *
   * @param {string} loanId - The unique identifier for the loan.
   * @param {Pilot[]} pilots - An array of pilots to check access for.
   */
  getLoanAccess(loanId: string, pilots: Pilot[]): Observable<LoanAccessDecision> {
    return this.httpClient
      .post<LoanAccessDecision>(`${this.BASE_URL}/authorization/loan/${loanId}/access`, {
        pilotIds: pilots,
      })
      .pipe(
        map((response) => {
          const currentDecisions = this.pilotDecisionsSubject.getValue();
          return { ...currentDecisions, ...response };
        }),
        tap((response) => this.pilotDecisionsSubject.next(response)),
      );
  }
}
