import { inject, Injectable } from '@angular/core';
import { distinctUntilChanged, filter, map, share, switchMap } from 'rxjs';
import { LoanIdService } from '../loan-id/loan-id.service';
import { NotificationService } from './notification.service';

export interface PublishedMessage<T = any> {
  type: string;
  source: string;
  channel: string;
  payload: T;
}


@Injectable()
export class LoanNotificationService {
  private loanId = inject(LoanIdService);
  private notification = inject(NotificationService);

  public notifications$ = this.loanId.nonNullableLoanId$.pipe(
    distinctUntilChanged(),
    switchMap((loanId) => this.notification.connectTo(loanId)),
    filter((notification) => !!notification.data),
    map((notification) => JSON.parse(notification.data) as PublishedMessage),
    share(),
  );
}
