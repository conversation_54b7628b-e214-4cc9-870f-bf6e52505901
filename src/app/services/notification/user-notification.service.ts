import { Injectable, inject } from '@angular/core';
import { filter, map, share } from 'rxjs';
import { NotificationService } from './notification.service';

@Injectable({
  providedIn: 'root',
})
export class UserNotificationService {
  private notification = inject(NotificationService);

  public notifications$ = this.notification.connectTo('user').pipe(
    filter((notification) => !!notification.data),
    map((notification) => JSON.parse(notification.data)),
    share(),
  );
}
