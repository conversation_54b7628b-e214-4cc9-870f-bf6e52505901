import { Injectable, inject } from '@angular/core';
import { AuthService } from '@auth0/auth0-angular';
import { EventSourceMessage, fetchEventSource } from '@microsoft/fetch-event-source';
import { Observable, filter, retry, switchMap, timer } from 'rxjs';
import { environment } from '../../../environments/environment';

class FatalError extends Error{}
class RetriableError extends Error{}
class AuthenticationError extends Error{}


@Injectable({
  providedIn: 'root',
})
export class NotificationService {
  private readonly BASE_URL = environment.dataProviderUrl;
  private auth: AuthService = inject(AuthService);

  constructor() {}

  public connectTo(channelName: string) {
    const endpoint = this.getListenEndpoint(channelName);
    return this.getConnection$(endpoint);
  }

  public getConnection$(endpoint: string) {
    return this.auth.getAccessTokenSilently().pipe(
      filter((jwt): jwt is string => !!jwt),
      switchMap((jwt) => this.getConnectionObservable$(jwt, endpoint)),
      retry({
        count: 5,
        delay: (_, attempt) => {
          const delay = Math.min(Math.pow(2, attempt - 1) * 250, 5000);
          return timer(delay);
        },
      }),
    );
  }

  private getListenEndpoint = (channelName: string) =>
    `${this.BASE_URL}/notifications/listen/${channelName}`;

  private getConnectionObservable$(token: string, endpoint: string) {
    return new Observable<EventSourceMessage>((subscriber) => {
      const controller = new AbortController();
      fetchEventSource(endpoint, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
        async onopen(response) {
          if (response.ok) {
            return;
          }
          if (response.status === 401) {
            throw new AuthenticationError();
          }
          throw new FatalError('did not open connection for notifications');
        },
        onmessage(msg: EventSourceMessage) {
          subscriber.next(msg);
        },
        onclose() {
          // server closed connection unexpectedly
          throw new RetriableError();
        },
        onerror(err: Error) {
          if (err instanceof FatalError || err instanceof AuthenticationError) {
            subscriber.error(err);
            throw err; // prevents retry
          }
          return 500; // retry in ms
        },
        openWhenHidden: true,
        signal: controller.signal,
      });

      return {
        unsubscribe: () => controller.abort(),
      };
    });
  }
}
