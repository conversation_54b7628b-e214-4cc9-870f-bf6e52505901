import { DestroyRef, inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { EscrowWaiver, RefinanceGoal } from '@rocket-logic/rl-xp-bff-models';
import { LoanControlType } from '../entity-state/loan-state/loan-form.service';
import { LoanStateService } from '../entity-state/loan-state/loan-state.service';
@Injectable()
export class HelocEscrowWaiverHandlerService {
  readonly destroyRef = inject(DestroyRef);
  stateService = inject(LoanStateService);
  addHelocEscrowListener(loanForm: FormGroup<LoanControlType>) {
    const refinanceGoalsControl = loanForm.controls.refinanceGoals;
    refinanceGoalsControl.valueChanges
      .pipe(
      takeUntilDestroyed(this.destroyRef),
    ).subscribe((newValue) => {
        const isHelocGoal = newValue?.includes(RefinanceGoal.Heloc);
          if(isHelocGoal) {
            loanForm.controls.escrowWaiver.setValue(EscrowWaiver.TaxesAndInsurance, { emitEvent: false });
            loanForm.controls.escrowWaiver.disable();
          }else{
            loanForm.controls.escrowWaiver.enable();
          }
        }
    );
  }
}
