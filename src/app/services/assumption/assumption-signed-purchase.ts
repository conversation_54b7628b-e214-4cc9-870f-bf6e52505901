import { DestroyRef, inject, Injectable } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { LoanPurpose } from '@rocket-logic/rl-xp-bff-models';
import { combineLatest, filter, startWith } from 'rxjs';
import { LoanControlType } from '../entity-state/loan-state/loan-form.service';
import { LoanStateService } from '../entity-state/loan-state/loan-state.service';
import { LeadService } from '../lead/lead.service';
@Injectable()
export class AssumptionSignedPurchaseHandlerService {
  readonly destroyRef = inject(DestroyRef);
  readonly leadService = inject(LeadService);
  stateService = inject(LoanStateService);
  addAssumptionSignedPurchaseListener(loanForm: FormGroup<LoanControlType>) {
    const loanPurposeControl = loanForm.get('loanPurpose')!;

    combineLatest([loanPurposeControl.valueChanges.pipe(startWith(loanPurposeControl.value)), this.leadService.lead$])
      .pipe(
        filter(([loanPurpose]) => loanPurpose === LoanPurpose.Purchase))
      .subscribe(([state, lead]) => {
        if (lead.isAssumptionLead) {
          loanForm.controls.hasSignedPurchaseAgreement.disable();
          loanForm.controls.hasSignedPurchaseAgreement.markAsDirty();
          if (loanForm.controls.hasSignedPurchaseAgreement.value != true) {
            loanForm.controls.hasSignedPurchaseAgreement.setValue(true);
          }
        }
      }
      );
  }
}
