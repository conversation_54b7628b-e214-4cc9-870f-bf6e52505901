import { computed, inject, Injectable } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { TaskStatus } from '@rocket-logic/rl-xp-bff-models';
import { AppIslandAction } from '../application-island/app-island-action-button/app-island-action-button.component';
import { TaskDisplayData } from '../credit-task/credit-task.component';
import { getClientIdFromTask } from '../util/get-client-id-from-task';
import { CreditService } from './credit/credit.service';
import { ClientStateService } from './entity-state/client-state/client-state.service';
import { LoanStateService } from './entity-state/loan-state/loan-state.service';
import { FormNavInputService } from './form-nav/form-nav-input.service';
import {
  FORM_SECTION_MILESTONES,
  FormNavSectionService,
} from './form-nav/form-nav-section.service';

@Injectable()
export class ActiveActionService {
  private creditService = inject(CreditService);
  private clientService = inject(ClientStateService);
  private formInputNavService = inject(FormNavInputService);
  private formNavService = inject(FormNavSectionService);
  private loanStateService = inject(LoanStateService);
  isLoanComplete = toSignal(this.loanStateService.isLoanXpCompleted$);

  creditAndTaskCheck = computed(() => {
    const allClientsHaveCredit = this.creditService.allClientsHaveCredit();
    const creditItemsComplete = this.creditItemsToComplete() === 0;
    const hasOutstandingCreditTask = this.hasOutstandingCreditTask();
    return allClientsHaveCredit && !hasOutstandingCreditTask && creditItemsComplete;
  });

  activeAction = computed(() => {
    if (this.creditAndTaskCheck() || this.isLoanComplete()) {
      return AppIslandAction.GetPricing;
    }
    return AppIslandAction.CreditRequest;
  });

  hasOutstandingCreditTask = computed(() => (this.outstandingCreditTasks()?.length ?? 0) > 0);
  creditItemsToComplete = computed(() => this.formInputNavService.availableSections().length);

  outstandingCreditTasks = computed(() => {
    return this.creditService
      .creditTasks()
      ?.filter((task) => task.status === TaskStatus.Created)
      ?.map((task) => {
        const clientInfo = this.clientService
          .stateValues()
          .find((client) => client.id === getClientIdFromTask(task))?.personalInformation;
        const taskResolutionDetails = task.input?.map((choice) => choice.choice).join(' or ');
        return {
          personalInformation: clientInfo,
          taskResolutionDetails: taskResolutionDetails,
          taskId: task.taskId,
          taskResolutionData:
            task.input !== undefined && task.input!.length > 1 ? task.input![0] : undefined,
          clientId: getClientIdFromTask(task),
        } as TaskDisplayData;
      });
  });

  navigateActionInputs() {
    switch (this.activeAction()) {
      case AppIslandAction.CreditRequest:
        this.navigateCreditInputs();
        break;
      case AppIslandAction.GetPricing:
        this.navigateProductInputs();
        break;
    }
  }

  private navigateCreditInputs() {
    this.navigateInputs(this.formInputNavService.availableSections());
  }

  private navigateProductInputs() {
    this.navigateInputs(this.formInputNavService.availableProductSections());
  }

  private navigateInputs(availableSections: string[]) {
    const firstAvailableSection = availableSections[0];
    if (!firstAvailableSection) {
      return;
    }

    const firstNavigableSection =
      this.formInputNavService.sectionElements.get(firstAvailableSection);

    if (!firstNavigableSection) {
      return;
    }

    this.formInputNavService.activateSection(firstAvailableSection);
    const formSection = Array.from(FORM_SECTION_MILESTONES.entries()).find(([_, milestone]) =>
      firstAvailableSection.startsWith(milestone),
    )?.[0];
    if (formSection) {
      this.formNavService.activateSection(formSection);
    }
  }
}
