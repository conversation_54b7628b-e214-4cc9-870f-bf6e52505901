import { Injectable } from '@angular/core';
import { Subscription } from 'rxjs';

@Injectable()
export class SubscriptionManager<T> {
  protected subscriptions = new Map<T, Subscription>();

  addSubscription(token: T, subscription: Subscription) {
    const subs = this.subscriptions.get(token) ?? new Subscription();
    subs.add(subscription);
    this.subscriptions.set(token, subs);
  }

  removeSubscriptions(token: T) {
    this.subscriptions.get(token)?.unsubscribe();
    this.subscriptions.delete(token);
  }

  removeAllSubscriptions() {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
    this.subscriptions.clear();
  }
}
