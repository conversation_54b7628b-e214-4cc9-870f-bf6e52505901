import { HttpClient, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  Address,
  Asset,
  Client,
  ClientActivationFilter,
  CompleteTaskRequest,
  ConflictingLoansResult,
  CreditPullRequest,
  CreditReport,
  CreditReportOrder,
  Deactivate,
  Income,
  IncomeType,
  JobDetails,
  Lead,
  Liability,
  LiabilityType,
  Loan,
  LoanDeactivationDetails,
  MappedAddressValidation,
  OwnedProperty,
  ServicedData,
  State,
  Task,
} from '@rocket-logic/rl-xp-bff-models';
import { CreditPullType } from '@rocket-logic/rl-xp-bff-models/dist/credit';
import { CreateCreditTransferRequest } from '@rocket-logic/rl-xp-bff-models/dist/credit/create-transfer-request';
import { LoanApplicationState } from '@rocket-logic/rl-xp-bff-models/dist/loan-application-state';
import { HomeOwnersInsuranceEstimate } from '@rocket-logic/rl-xp-bff-models/dist/property-estimates/homeowners-insurance';
import { PropertyInsurance } from '@rocket-logic/rl-xp-bff-models/dist/property-insurance/property-insurance';
import { SubjectProperty } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/subject-property';
import { Observable } from 'rxjs';
import { environment } from '../../../environments/environment';
import { County } from '../address/address-data.service';

@Injectable({
  providedIn: 'root',
})
export class DataProviderService {
  private readonly BASE_URL = environment.dataProviderUrl;
  private httpClient = inject(HttpClient);

  private standardEmployments = [
    IncomeType.Union,
    IncomeType.ActiveDuty,
    IncomeType.StandardSelfEmployment,
    IncomeType.Standard,
  ];

  getLoan$(loanId: string) {
    return this.httpClient.get<Loan>(`${this.BASE_URL}/loan/${loanId}`);
  }

  updateLoan$(loanId: string, loan: Loan) {
    return this.httpClient.put<Loan>(`${this.BASE_URL}/loan/${loanId}`, loan);
  }

  getLoanApplicationState$(loanId: string) {
    return this.httpClient.get<LoanApplicationState>(
      `${this.BASE_URL}/loanApplicationState/${loanId}`,
    );
  }

  getAssets$(loanId: string) {
    return this.httpClient.get<Asset[]>(`${this.BASE_URL}/loan/${loanId}/assets`);
  }

  updateAsset$(loanId: string, asset: Asset) {
    return this.httpClient.put<Asset>(`${this.BASE_URL}/loan/${loanId}/assets`, asset);
  }

  deleteAsset$(loanId: string, assetId: string) {
    return this.httpClient.delete<void>(`${this.BASE_URL}/loan/${loanId}/assets/${assetId}`);
  }

  getClients$(loanId: string, activationFilter?: ClientActivationFilter) {
    const url =
      activationFilter != null
        ? `${this.BASE_URL}/loan/${loanId}/clients?activationFilter=${activationFilter}`
        : `${this.BASE_URL}/loan/${loanId}/clients`;
    return this.httpClient.get<Client[]>(url);
  }

  updateClient$(loanId: string, client: Client) {
    return this.httpClient.put<Client>(`${this.BASE_URL}/loan/${loanId}/clients`, client);
  }

  deleteClient$(loanId: string, clientId: string) {
    return this.httpClient.delete<void>(`${this.BASE_URL}/loan/${loanId}/clients/${clientId}`);
  }

  updateClientRelationship$(loanId: string, clientId1: string, clientId2: string) {
    return this.httpClient.put<void>(
      `${this.BASE_URL}/loan/${loanId}/clients/spousalRelationship`,
      { rocketLogicClientId1: clientId1, rocketLogicClientId2: clientId2 },
    );
  }

  deleteClientRelationship$(loanId: string, clientId1: string, clientId2: string) {
    return this.httpClient.delete<void>(
      `${this.BASE_URL}/loan/${loanId}/clients/spousalRelationship`,
      { body: { rocketLogicClientId1: clientId1, rocketLogicClientId2: clientId2 } },
    );
  }

  getOwnedProperties$(loanId: string) {
    return this.httpClient.get<OwnedProperty[]>(`${this.BASE_URL}/loan/${loanId}/ownedProperty`);
  }

  updateOwnedProperty$(loanId: string, ownedProperty: OwnedProperty) {
    return this.httpClient.put<OwnedProperty>(
      `${this.BASE_URL}/loan/${loanId}/ownedProperty`,
      ownedProperty,
    );
  }

  deleteOwnedProperty$(loanId: string, ownedPropertyId: string) {
    return this.httpClient.delete<void>(
      `${this.BASE_URL}/loan/${loanId}/ownedProperty/${ownedPropertyId}`,
    );
  }

  getIncome$(loanId: string, clientId: string) {
    return this.httpClient.get<Income[]>(
      `${this.BASE_URL}/loan/${loanId}/clients/${clientId}/income`,
    );
  }

  updateIncome$(loanId: string, clientId: string, income: Income) {
    return this.httpClient.put<Income>(
      `${this.BASE_URL}/loan/${loanId}/clients/${clientId}/income`,
      income,
    );
  }

  deleteIncome$(loanId: string, clientId: string, incomeId: string, incomeType: IncomeType) {
    const url = this.standardEmployments.includes(incomeType)
      ? `/loan/${loanId}/clients/${clientId}/income/${incomeId}?isEmployment=true`
      : `/loan/${loanId}/clients/${clientId}/income/${incomeId}`;
    return this.httpClient.delete<void>(`${this.BASE_URL}${url}`);
  }

  getSubjectProperty$(loanId: string) {
    return this.httpClient.get<SubjectProperty>(`${this.BASE_URL}/loan/${loanId}/subjectProperty`);
  }

  updateSubjectProperty$(loanId: string, subjectProperty: SubjectProperty) {
    return this.httpClient.put(`${this.BASE_URL}/loan/${loanId}/subjectProperty`, subjectProperty);
  }

  getLiabilities$(loanId: string) {
    // Currently only fetching mortgage loans for REO
    return this.httpClient.get<Liability[]>(`${this.BASE_URL}/loan/${loanId}/liability`, {
      params: { liabilityType: LiabilityType.MortgageLoan },
    });
  }

  updateLiability$(loanId: string, liability: Liability) {
    return this.httpClient.put<Liability>(`${this.BASE_URL}/loan/${loanId}/liability`, liability);
  }

  deleteLiability$(loanId: string, liabilityId: string) {
    return this.httpClient.delete(`${this.BASE_URL}/loan/${loanId}/liability/${liabilityId}`);
  }

  deactivateLoan$(loanId: string, deactivationRequest: LoanDeactivationDetails) {
    return this.httpClient.post<LoanDeactivationDetails>(
      `${this.BASE_URL}/loan/${loanId}/deactivate`,
      deactivationRequest,
    );
  }

  pullCredit$(loanId: string, creditPullRequest: CreditPullRequest) {
    return this.httpClient.post<CreditReportOrder>(
      `${this.BASE_URL}/loan/${loanId}/credit/pullCredit`,
      creditPullRequest,
    );
  }

  transferCredit$(loanId: string, transferRequest: CreateCreditTransferRequest) {
    return this.httpClient.post<CreditReportOrder>(
      `${this.BASE_URL}/loan/${loanId}/credit/transferCredit`,
      transferRequest,
    );
  }

  getCreditPullType$(loanId: string) {
    return this.httpClient.get<{ creditPullType: CreditPullType }>(
      `${this.BASE_URL}/loan/${loanId}/credit/creditPullType`,
    );
  }

  getCreditReportOrder$(loanId: string, creditReportOrderId: string) {
    return this.httpClient.get<CreditReportOrder>(
      `${this.BASE_URL}/loan/${loanId}/credit/creditReportOrder/${creditReportOrderId}`,
    );
  }

  getCreditReportOrders$(loanId: string) {
    return this.httpClient.get<CreditReportOrder[]>(
      `${this.BASE_URL}/loan/${loanId}/credit/creditReportOrders`,
    );
  }

  getCreditReport$(loanId: string, creditReportId: string) {
    return this.httpClient.get<CreditReport>(
      `${this.BASE_URL}/loan/${loanId}/credit/creditReport/${creditReportId}`,
    );
  }

  getCreditReports$(loanId: string, includeInactive: boolean = true) {
    return this.httpClient.get<CreditReport[]>(
      `${this.BASE_URL}/loan/${loanId}/credit/creditReports?includeInactive=${includeInactive}`,
    );
  }

  completeInitialApplication$(loanId: string, formData: unknown) {
    return this.httpClient.post<void>(`${this.BASE_URL}/loan/${loanId}/complete`, formData);
  }

  createLoanFromLead$(loanNumber: string) {
    return this.httpClient.post(`${this.BASE_URL}/loan/${loanNumber}`, undefined, {
      responseType: 'text',
    });
  }

  makePrimary$(loanNumber: string, clientId: string) {
    return this.httpClient.put(
      `${this.BASE_URL}/loan/${loanNumber}/clients/${clientId}/primary`,
      undefined,
    );
  }

  deactivateClient$(loanNumber: string, clientId: string, deactivationRequest: Deactivate) {
    return this.httpClient.post(
      `${this.BASE_URL}/loan/${loanNumber}/clients/${clientId}/deactivate`,
      deactivationRequest,
    );
  }

  activateClient$(loanNumber: string, clientId: string): Observable<Client> {
    return this.httpClient.post<Client>(
      `${this.BASE_URL}/loan/${loanNumber}/clients/${clientId}/activate`,
      undefined,
    );
  }

  getTasks$(loanNumber: string) {
    return this.httpClient.get<Task[]>(`${this.BASE_URL}/loan/${loanNumber}/tasks`);
  }

  getTask$(loanNumber: string, taskId: string) {
    return this.httpClient.get<Task>(`${this.BASE_URL}/loan/${loanNumber}/tasks/${taskId}`);
  }

  completeTask$(loanNumber: string, taskId: string, completeTaskRequest: CompleteTaskRequest) {
    return this.httpClient.put(
      `${this.BASE_URL}/loan/${loanNumber}/tasks/${taskId}/complete`,
      completeTaskRequest,
    );
  }

  getLead$(loanNumber: string) {
    return this.httpClient.get<Lead>(`${this.BASE_URL}/lead/${loanNumber}`);
  }

  getZipCodes$(state: string, fipsCode: string, city: string) {
    const params = new HttpParams().set('state', state).set('fipsCode', fipsCode).set('city', city);
    return this.httpClient.get<string[]>(`${this.BASE_URL}/address/zipCodes`, { params });
  }

  getCounties$(state: string, city: string, zipCode: string) {
    const params = new HttpParams().set('state', state).set('city', city).set('zipCode', zipCode);
    return this.httpClient.get<County[]>(`${this.BASE_URL}/address/counties`, { params });
  }

  getCountiesByState$(state: string) {
    const params = new HttpParams().set('state', state);
    return this.httpClient.get<County[]>(`${this.BASE_URL}/address/counties`, { params });
  }

  getCities$(state: string, countyCode: string, zipCode: string) {
    const params = new HttpParams()
      .set('state', state)
      .set('fipsCode', countyCode)
      .set('zipCode', zipCode);
    return this.httpClient.get<string[]>(`${this.BASE_URL}/address/cities`, { params });
  }

  getStates$(zipCode: string, fipsCode?: string) {
    const params = new HttpParams().set('zipCode', zipCode);
    if (fipsCode) {
      params.set('fipsCode', fipsCode);
    }
    return this.httpClient.get<State[]>(`${this.BASE_URL}/address/states`, { params });
  }

  getLoanHistoryUrl$(loanNumber: string): Observable<string> {
    return this.httpClient.get(`${this.BASE_URL}/loan/${loanNumber}/archive`, {
      responseType: 'text',
    });
  }

  saveLoanHistory$(loanNumber: string, history: unknown): Observable<void> {
    return this.httpClient.post<void>(`${this.BASE_URL}/loan/${loanNumber}/archive`, history);
  }

  getAddressValidation$(address: Address) {
    let params = new HttpParams()
      .set('street', address.addressLine1 ?? '')
      .set('unit', address.addressLine2 ?? '')
      .set('zipCode', address.zipCode ?? '')
      .set('city', address.city ?? '')
      .set('state', address.state ?? '');

    params.keys().forEach((key) => {
      const value = params.get(key);
      if (value === '') {
        params = params.delete(key);
      }
    });

    return this.httpClient.get<MappedAddressValidation>(`${this.BASE_URL}/address/validation`, {
      params,
    });
  }

  fetchS3UrlContents$(url: string): Observable<string> {
    return this.httpClient.get<string>(url);
  }

  getPropertyInsurance$(loanNumber: string) {
    return this.httpClient.get<PropertyInsurance[]>(
      `${this.BASE_URL}/loan/${loanNumber}/propertyInsurance`,
    );
  }

  updatePropertyInsurance$(loanNumber: string, insurance: PropertyInsurance) {
    return this.httpClient.put<PropertyInsurance>(
      `${this.BASE_URL}/loan/${loanNumber}/propertyInsurance`,
      insurance,
    );
  }

  fetchConflictingLoans$(loanNumber: string): Observable<ConflictingLoansResult> {
    return this.httpClient.get<ConflictingLoansResult>(
      `${this.BASE_URL}/loan/${loanNumber}/conflictingLoans`,
    );
  }

  getIfUserIsAssignedToLead$(loanNumber: string): Observable<boolean> {
    return this.httpClient.get<boolean>(
      `${this.BASE_URL}/userInfo/loan/${loanNumber}/isUserAssignedToLead`,
    );
  }

  getUserInfo$() {
    return this.httpClient.get<Record<string, boolean>>(`${this.BASE_URL}/userInfo`);
  }

  getPrefillData$(loanNumber: string) {
    return this.httpClient.get<ServicedData>(`${this.BASE_URL}/loan/${loanNumber}/servicedData`);
  }

  getCreditPulledServicedData$(loanNumber: string) {
    return this.httpClient.get<ServicedData>(
      `${this.BASE_URL}/loan/${loanNumber}/creditPulledServicedData`,
    );
  }

  getReoLienAssignedServicedData$(loanNumber: string) {
    return this.httpClient.get<ServicedData>(
      `${this.BASE_URL}/loan/${loanNumber}/reoLienAssignedServicedData`,
    );
  }

  getSchwabClients(loanNumber: string) {
    return this.httpClient.get<string[]>(`${this.BASE_URL}/loan/${loanNumber}/clients/schwab`);
  }

  getLoanMetadata$(loanNumber: string) {
    return this.httpClient.get(`${this.BASE_URL}/loan/${loanNumber}/metadata`);
  }

  updateSchwabImportConsent$(loanNumber: string, consent: boolean): Observable<Lead> {
    const requestBody = { consent };
    return this.httpClient.post<Lead>(
      `${this.BASE_URL}/loan/${loanNumber}/schwabImportConsent`,
      requestBody,
    );
  }

  updateHasInternationalCredit$(
    loanNumber: string,
    hasInternationalCredit: boolean | null | undefined,
  ): Observable<Lead> {
    const requestBody = { hasInternationalCredit };
    return this.httpClient.post<Lead>(
      `${this.BASE_URL}/loan/${loanNumber}/hasInternationalCredit`,
      requestBody,
    );
  }

  startSchwabAssetImport$(loanNumber: string) {
    return this.httpClient.post<void>(
      `${this.BASE_URL}/loan/${loanNumber}/assets/importPartnerAssets`,
      null,
    );
  }

  getHomeOwnersInsuranceEstimate$(loanNumber: string) {
    return this.httpClient.get<HomeOwnersInsuranceEstimate>(
      `${this.BASE_URL}/loan/${loanNumber}/property-estimates/homeowners-insurance`,
    );
  }

  getQfrs(loanNumber: string) {
    return this.httpClient.get<JobDetails>(`${this.BASE_URL}/loan/${loanNumber}/stoppers`);
  }
}
