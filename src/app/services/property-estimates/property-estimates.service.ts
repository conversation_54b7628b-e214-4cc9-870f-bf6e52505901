import { Injectable, inject } from '@angular/core';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { LoanPurpose, PurchaseLoan } from '@rocket-logic/rl-xp-bff-models';
import {
  EMPTY,
  catchError,
  combineLatest,
  distinctUntilChanged,
  filter,
  map,
  of,
  shareReplay,
  switchMap,
} from 'rxjs';
import { DataProviderService } from '../data-provider/data-provider.service';
import { LoanStateService } from '../entity-state/loan-state/loan-state.service';
import { SubjectPropertyStateService } from '../entity-state/subject-property-state/subject-property-state.service';
import { LoanIdService } from '../loan-id/loan-id.service';

@Injectable()
export class PropertyEstimatesService {
  private dataProviderService = inject(DataProviderService);
  private loanStateService = inject(LoanStateService);
  private subjectPropertyStateService = inject(SubjectPropertyStateService);
  private loanId$ = inject(LoanIdService).loanId$;
  private logger = inject(SplunkLoggerService);

  // Insurance estimates estimates are based on zip/county/property type/property value combinations
  private hasInsuranceInputsChanged$ = combineLatest([
    this.subjectPropertyStateService.state$,
    this.loanStateService.state$,
  ]).pipe(
    map((states) => {
      return {
        subjectPropertyData: states[0].data,
        loanData: states[1].data,
      };
    }),
    filter((data) => {
      const loanPurpose = data.loanData?.loanPurpose;
      if (!loanPurpose) return false;

      const hasSharedInputs =
        !!data?.subjectPropertyData?.address?.county?.fipsCode &&
        !!data?.subjectPropertyData?.address?.zipCode &&
        !!data?.subjectPropertyData?.propertyType;

      if (loanPurpose === LoanPurpose.Purchase) {
        return hasSharedInputs && !!(data.loanData as PurchaseLoan)?.purchasePrice;
      }

      return hasSharedInputs && !!data?.subjectPropertyData?.estimatedPropertyValue;
    }),
    distinctUntilChanged((prev, next) => {
      const areSharedInputsEqual =
        prev?.subjectPropertyData?.address?.county?.fipsCode ===
          next?.subjectPropertyData?.address?.county?.fipsCode &&
        prev?.subjectPropertyData?.address?.zipCode ===
          next?.subjectPropertyData?.address?.zipCode &&
        prev?.subjectPropertyData?.propertyType === next?.subjectPropertyData?.propertyType;

      if (next.loanData?.loanPurpose === LoanPurpose.Purchase) {
        return (
          areSharedInputsEqual &&
          (prev?.loanData as PurchaseLoan)?.purchasePrice ===
            (next?.loanData as PurchaseLoan)?.purchasePrice
        );
      }

      return (
        areSharedInputsEqual &&
        prev?.subjectPropertyData?.estimatedPropertyValue ===
          next?.subjectPropertyData?.estimatedPropertyValue
      );
    }),
  );

  public homeOwnersInsuranceEstimate$ = combineLatest([
    this.loanId$,
    this.hasInsuranceInputsChanged$,
  ]).pipe(
    filter(([loanId]) => !!loanId),
    switchMap(([loanId]) => {
      if (!loanId) {
        return EMPTY;
      }

      return this.dataProviderService.getHomeOwnersInsuranceEstimate$(loanId);
    }),
    catchError((err) => {
      this.logger.error(`Failed to fetch homeowners insurance estimate`, err);
      return of(null);
    }),
    shareReplay(1),
  );
}
