import { Injectable, inject } from '@angular/core';
import { AssetStateService } from '../entity-state/asset-state/asset-state.service';
import { ClientStateService } from '../entity-state/client-state/client-state.service';
import { IncomeStateService } from '../entity-state/income-state/income-state.service';
import { LiabilityStateService } from '../entity-state/liability-state/liability-state.service';
import { LoanStateService } from '../entity-state/loan-state/loan-state.service';
import { OwnedPropertyStateService } from '../entity-state/owned-property-state/owned-property-state.service';
import { SubjectPropertyStateService } from '../entity-state/subject-property-state/subject-property-state.service';

@Injectable()
export class CompletedLoanFormBuilderService {
  loanStateService = inject(LoanStateService);
  clientStateService = inject(ClientStateService);
  ownedPropertyStateService = inject(OwnedPropertyStateService);
  subjectPropertyStateService = inject(SubjectPropertyStateService);
  incomeStateService = inject(IncomeStateService);
  assetStateService = inject(AssetStateService);
  liabilityStateService = inject(LiabilityStateService);

  buildCompletedLoanForm() {
    return {
      loan: this.loanStateService.state()?.data,
      clients: this.clientStateService.state()?.data,
      ownedProperties: this.ownedPropertyStateService.state()?.data,
      subjectProperty: this.subjectPropertyStateService.state()?.data,
      income: this.incomeStateService.state()?.data,
      liabilities: this.liabilityStateService.state()?.data,
    };
  }
}
