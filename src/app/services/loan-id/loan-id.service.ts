import { Injectable, inject } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { filter, map, shareReplay } from 'rxjs';

@Injectable()
export class LoanIdService {
  private activatedRoute = inject(ActivatedRoute);
  loanId$ = this.activatedRoute.paramMap.pipe(
    map((paramMap) => paramMap.get('loanId')),
    takeUntilDestroyed(),
    shareReplay(1),
  );
  loanId = toSignal(this.loanId$);
  nonNullableLoanId$ = this.loanId$.pipe(
    filter((loanNumber): loanNumber is string => !!loanNumber),
    takeUntilDestroyed(),
    shareReplay(1),
  );
}
