import { TestBed } from '@angular/core/testing';

import { LoanIdService } from './loan-id.service';
import { ActivatedRoute } from '@angular/router';
import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';

describe('LoanIdService', () => {
  let service: LoanIdService;

  beforeEach(() => MockBuilder(LoanIdService).mock(ActivatedRoute, {paramMap: NEVER}));
  beforeEach(() => {
    service = TestBed.inject(LoanIdService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
