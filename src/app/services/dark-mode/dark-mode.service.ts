import { Injectable, computed, effect, inject, signal } from '@angular/core';
import { RktDarkModeService } from '@rocketcentral/rocket-design-system-angular';

export enum DarkModePreference {
  Light = 'light',
  Dark = 'dark',
  MatchSystem = 'matchSystem'
}

@Injectable({
  providedIn: 'root',
})
export class DarkModeService {
  rktDarkModeService = inject(RktDarkModeService);
  private readonly LOCAL_STORAGE_KEY = 'rlxp.darkModePreference';
  public isDarkMode = computed(() => this.isDarkModeActive(this.darkModePreference()));
  public darkModePreference = signal(this.getDarkModePreference() ?? DarkModePreference.Light);


  darkModeEffect = effect(() => {
    if (this.isDarkMode()) {

      this.rktDarkModeService.toggleDarkModeClass(true)
      document.querySelector('body')?.classList.add('dark-mode');
    } else {
      this.rktDarkModeService.toggleDarkModeClass(false);
      document.querySelector('body')?.classList.remove('dark-mode');
    }
  });

  constructor() {
    effect(() => {
      localStorage.setItem(this.LOCAL_STORAGE_KEY, this.darkModePreference());
    });
  }

  private getDarkModePreference(): DarkModePreference | null {
    const preference = localStorage.getItem(this.LOCAL_STORAGE_KEY);
    return preference ? (preference as DarkModePreference) : null;
  }

  private isDarkModeActive(darkModePref: DarkModePreference): boolean {
    switch (darkModePref) {
      case DarkModePreference.Dark:
        return true;
      case DarkModePreference.Light:
        return false;
      case DarkModePreference.MatchSystem:
        return matchMedia('(prefers-color-scheme: dark)').matches;
    }
  }
}
