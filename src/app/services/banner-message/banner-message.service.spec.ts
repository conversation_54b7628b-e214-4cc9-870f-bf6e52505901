import { TestBed } from '@angular/core/testing';

import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { NotificationService } from '../notification/notification.service';
import { BannerMessageService } from './banner-message.service';

describe('BannerMessageService', () => {
  let service: BannerMessageService;

  beforeEach(() =>
    MockBuilder(BannerMessageService)
      .mock(NotificationService, { getConnection$: () => NEVER })
      .mock(SplunkLoggerService),
  );
  beforeEach(() => {
    service = TestBed.inject(BannerMessageService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
