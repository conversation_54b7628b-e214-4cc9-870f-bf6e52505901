import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { AbstractControl, FormControl } from '@angular/forms';
import {
  AmortizationType,
  EmploymentType,
  IncomePaymentFrequency,
  Liability,
  LiabilitySecuredByLien,
  MilitaryStatus,
  MortgageType,
  OwnedProperty,
  ServicedData
} from '@rocket-logic/rl-xp-bff-models';
import { PropertyType } from '@rocket-logic/rl-xp-bff-models/dist/enums/subject-property-type';
import {
  BehaviorSubject,
  catchError,
  combineLatest,
  EMPTY,
  exhaustMap,
  filter,
  map,
  Observable,
  shareReplay,
  startWith,
  Subject,
  switchMap,
  take,
  tap
} from 'rxjs';
import { isValidDateString } from '../../util/is-valid-date-format';
import { AuthorizationService, Pilot } from '../authorization/authorization.service';
import { CreditService } from '../credit/credit.service';
import { DataProviderService } from '../data-provider/data-provider.service';
import { Loadable } from '../entity-state/abstract-entity-state.service';
import { ClientFormService } from '../entity-state/client-state/client-form.service';
import { AllIncomeGroup } from '../entity-state/income-state/form-types';
import { IncomeFormService } from '../entity-state/income-state/income-form.service';
import { isIncomeType, MAPPABLE_INCOME_TYPES } from '../entity-state/income-state/supported-income';
import { LiabilityFormService } from '../entity-state/liability-state/liability-form.service';
import { LiabilityStateService } from '../entity-state/liability-state/liability-state.service';
import { LoanFormService } from '../entity-state/loan-state/loan-form.service';
import { OwnedPropertyFormService } from '../entity-state/owned-property-state/owned-property-form.service';
import { OwnedPropertyStateService } from '../entity-state/owned-property-state/owned-property-state.service';
import { SubjectPropertyFormRef } from '../entity-state/subject-property-state/subject-property-form.service';
import { FormNavSectionService, FormSection } from '../form-nav/form-nav-section.service';
import { LoanIdService } from '../loan-id/loan-id.service';
import {
  doesMatchEmplyomentForm,
  prefillClientEmploymentControlMap,
} from './prefill-state.helpers';

export type PrefillDataPoint = {
  group: string;
  key: string;
  value: string | PrefillDataPoint[] | unknown;
  matchesControl: boolean;
};

function hasNonNullProperty(obj: any): boolean {
  if (obj === null || obj === undefined) {
    return false;
  }

  if (Array.isArray(obj)) {
    return obj.some((value) => hasNonNullProperty(value));
  } else if (typeof obj === 'object') {
    return Object.values(obj).some((value) => hasNonNullProperty(value));
  }

  return obj != null;
}

function compareDates(date1: string, date2: string) {
  const dateObj1 = new Date(date1);
  const dateObj2 = new Date(date2);
  dateObj1.setHours(0, 0, 0, 0);
  dateObj2.setHours(0, 0, 0, 0);

  return dateObj1.getTime() === dateObj2.getTime();
}

function doesControlMatchValue(control: AbstractControl | null, value: unknown) {
  const controlValue = control?.value ?? '';

  if (isValidDateString(value as string)) {
    return compareDates(value as string, controlValue as string);
  } else if (typeof value === 'string') {
    return controlValue.toLowerCase().trim() === value.toLowerCase().trim();
  }

  return controlValue === (value ?? '');
}

function doAllControlsMatchValue(controls: (AbstractControl | null)[], addressValue: unknown) {
  return controls.every((control) => doesControlMatchValue(control, addressValue));
}

@Injectable()
export class PrefillStateService {
  private authorizationService = inject(AuthorizationService);
  private dataProviderService = inject(DataProviderService);
  private loanIdService = inject(LoanIdService);
  private loanFormService = inject(LoanFormService);
  private subjectPropertyFormRef = inject(SubjectPropertyFormRef);
  private clientFormService = inject(ClientFormService);
  private incomeFormService = inject(IncomeFormService);
  private ownedPropertyFormService = inject(OwnedPropertyFormService);
  private ownedPropertyStateService = inject(OwnedPropertyStateService);
  private liabilityStateService = inject(LiabilityStateService);
  private liabilityFormService = inject(LiabilityFormService);
  private formNavService = inject(FormNavSectionService);
  private creditService = inject(CreditService);

  private loanControls: Record<string, (AbstractControl | null)[]> = {};
  private clientControlMap: Record<string, Record<string, AbstractControl | null>> = {};
  private controlSectionMap: Record<string, FormSection> = {};
  private hoaFields = ['hoaDuesAmount', 'hoaDuesFrequency'];

  private rktCreditorNames = ['quicken loans',
    'quickenloans',
    'ql',
    'quicken loans inc',
    'quicken loans llc',
    'rocket mortgage',
    'rocket mortgage llc',
    'rocketmortgage',
    'rocket mtg',
    'rocketmort',
    'rkt',
    'rkt mtg',
    'rkt mortgage'];

  private isRocketMortgageLiability(liability: LiabilitySecuredByLien): boolean {
    if (!liability.creditorName) return false;

    const normalizedCreditorNames = liability.creditorName
      .toLowerCase()
      .replace(/[.,]/g, '');

    return (liability.liabilityType === 'MortgageLoan' &&
      this.rktCreditorNames.includes(normalizedCreditorNames));
  }

  private canFindSpaLiability(ownedProperties: OwnedProperty[], liabilities: Loadable<Map<string, Liability>>): boolean {
    const subjectProperty = ownedProperties.find((property) => property.isSubjectProperty);
    if (!subjectProperty) return false;

    const liabilityValues = liabilities.data?.values() ?? [];
    const liabilityArray = Array.from(liabilityValues) as LiabilitySecuredByLien[];

    return liabilityArray.some((liability) => liability.ownedPropertyId == subjectProperty?.id &&
      this.isRocketMortgageLiability(liability));
  }

  private getControlAndSetSectionMap(
    form: AbstractControl | undefined,
    path: string,
    controlKey: string,
    section: FormSection,
  ): AbstractControl | null {
    this.controlSectionMap[controlKey] = section;
    return form?.get(path) ?? null;
  }

  private prefillLoanControlMap = (
    subjectPropertyId?: string,
    liabilityId?: string,
  ): Record<string, (AbstractControl | null)[]> => {
    let liabilityControlMap: Record<string, (AbstractControl | null)[]> = {};
    if (liabilityId) {
      const liabilityForm = this.liabilityFormService.getLiabilityForm(liabilityId);
      liabilityControlMap = {
        currentRate: [
          this.getControlAndSetSectionMap(
            liabilityForm,
            'interestRate',
            'currentRate',
            FormSection.LoanInfo,
          ),
        ],
        isEscrowed: [
          this.getControlAndSetSectionMap(
            liabilityForm,
            'monthlyPaymentIncludesTaxesAndInsurance',
            'isEscrowed',
            FormSection.LoanInfo,
          ),
        ],
        loanType: [
          this.getControlAndSetSectionMap(
            liabilityForm,
            'mortgageType',
            'loanType',
            FormSection.LoanInfo,
          ),
        ],
        amortizationType: [
          this.getControlAndSetSectionMap(
            liabilityForm,
            'amortizationType',
            'amortizationType',
            FormSection.LoanInfo,
          ),
        ],
        unpaidPrincipalAndInterest: [
          this.getControlAndSetSectionMap(
            liabilityForm,
            'principalAndInterestMonthlyPaymentAmount',
            'unpaidPrincipalAndInterest',
            FormSection.LoanInfo,
          ),
        ],
        mortgageInsuranceAmount: [
          this.getControlAndSetSectionMap(
            liabilityForm,
            'mortgageInsuranceMonthlyPaymentAmount',
            'mortgageInsuranceAmount',
            FormSection.LoanInfo,
          ),
        ],
      };
    }

    const subjectPropertyForm = this.subjectPropertyFormRef.subjectPropertyForm;
    const subjectPropertyControlMap: Record<string, (AbstractControl | null)[]> = {
      propertyType: [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'propertyType',
          'propertyType',
          FormSection.SubjectProperty,
        ),
      ],
      'subjectPropertyAddress.addressLine1': [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'address.addressLine1',
          'subjectPropertyAddress',
          FormSection.SubjectProperty,
        ),
      ],
      'subjectPropertyAddress.addressLine2': [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'address.addressLine2',
          'subjectPropertyAddress',
          FormSection.SubjectProperty,
        ),
      ],
      'subjectPropertyAddress.city': [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'address.city',
          'subjectPropertyAddress',
          FormSection.SubjectProperty,
        ),
      ],
      'subjectPropertyAddress.state': [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'address.state',
          'subjectPropertyAddress',
          FormSection.SubjectProperty,
        ),
      ],
      'subjectPropertyAddress.zipCode': [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'address.zipCode',
          'subjectPropertyAddress',
          FormSection.SubjectProperty,
        ),
      ],
    };

    if (subjectPropertyId) {
      const ownedPropertyForm =
        this.ownedPropertyFormService.getOwnedPropertyForm(subjectPropertyId);
      if (ownedPropertyForm) {
        subjectPropertyControlMap['propertyType'].push(
          this.getControlAndSetSectionMap(
            ownedPropertyForm,
            'propertyType',
            'propertyType',
            FormSection.SubjectProperty,
          ),
        );
        subjectPropertyControlMap['subjectPropertyAddress.addressLine1'].push(
          this.getControlAndSetSectionMap(
            ownedPropertyForm,
            'address.addressLine1',
            'subjectPropertyAddress.addressLine1',
            FormSection.SubjectProperty,
          ),
        );
        subjectPropertyControlMap['subjectPropertyAddress.addressLine2'].push(
          this.getControlAndSetSectionMap(
            ownedPropertyForm,
            'address.addressLine2',
            'subjectPropertyAddress.addressLine2',
            FormSection.SubjectProperty,
          ),
        );
        subjectPropertyControlMap['subjectPropertyAddress.city'].push(
          this.getControlAndSetSectionMap(
            ownedPropertyForm,
            'address.city',
            'subjectPropertyAddress.city',
            FormSection.SubjectProperty,
          ),
        );
        subjectPropertyControlMap['subjectPropertyAddress.state'].push(
          this.getControlAndSetSectionMap(
            ownedPropertyForm,
            'address.state',
            'subjectPropertyAddress.state',
            FormSection.SubjectProperty,
          ),
        );
        subjectPropertyControlMap['subjectPropertyAddress.zipCode'].push(
          this.getControlAndSetSectionMap(
            ownedPropertyForm,
            'address.zipCode',
            'subjectPropertyAddress.zipCode',
            FormSection.SubjectProperty,
          ),
        );
      }
    }

    const loanForm = this.loanFormService.loanForm;
    return {
      ...liabilityControlMap,
      ...subjectPropertyControlMap,
      originalPurchasePrice: [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'refinanceInformation.ownershipTransferPrice',
          'originalPurchasePrice',
          FormSection.SubjectProperty,
        ),
      ],
      estimatedValue: [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'estimatedPropertyValue',
          'estimatedValue',
          FormSection.SubjectProperty,
        ),
      ],
      isTx50a6: [
        this.getControlAndSetSectionMap(
          loanForm,
          'isPreviousLoanTexas50a6',
          'isTx50a6',
          FormSection.LoanInfo,
        ),
      ],
      homeOwnersInsuranceAmount: [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'hoi.amount',
          'homeOwnersInsuranceAmount',
          FormSection.SubjectProperty,
        ),
      ],
      originalClosingDate: [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'ownershipTransferredOn',
          'originalClosingDate',
          FormSection.SubjectProperty,
        ),
      ],
      numberOfUnits: [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'numberOfUnits',
          'numberOfUnits',
          FormSection.SubjectProperty,
        ),
      ],
      hoaDuesAmount: [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'hoaPayment.amount',
          'hoa',
          FormSection.SubjectProperty,
        ),
      ],
      hoaDuesFrequency: [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'hoaPayment.frequency',
          'hoa',
          FormSection.SubjectProperty,
        ),
      ],
      floodInsuranceAmount: [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'floodInsurance.amount',
          'floodInsuranceAmount',
          FormSection.SubjectProperty,
        ),
      ],
      yearBuilt: [
        this.getControlAndSetSectionMap(
          subjectPropertyForm,
          'yearBuilt',
          'yearBuilt',
          FormSection.SubjectProperty,
        ),
      ],
    };
  };

  private prefillClientControlMap = (gcid: string): Record<string, AbstractControl | null> => {
    const clientForms = this.clientFormService.getClientFormControls();
    const clientForm = clientForms.find((formGroup) => formGroup.get('gcid')?.value === gcid);

    if (!clientForm) {
      return {};
    }

    return {
      firstName: this.getControlAndSetSectionMap(
        clientForm,
        'personalInformation.firstName',
        'firstName',
        FormSection.ClientInfo,
      ),
      lastName: this.getControlAndSetSectionMap(
        clientForm,
        'personalInformation.lastName',
        'lastName',
        FormSection.ClientInfo,
      ),
      dateOfBirth: this.getControlAndSetSectionMap(
        clientForm,
        'personalInformation.dateOfBirth',
        'dateOfBirth',
        FormSection.Credit,
      ),
      maritalStatus: this.getControlAndSetSectionMap(
        clientForm,
        'personalInformation.maritalStatus',
        'maritalStatus',
        FormSection.ClientInfo,
      ),
      'homeAddress.addressLine1': this.getControlAndSetSectionMap(
        clientForm,
        'residenceInformation.currentResidence.address.addressLine1',
        'homeAddress',
        FormSection.ClientInfo,
      ),
      'homeAddress.addressLine2': this.getControlAndSetSectionMap(
        clientForm,
        'residenceInformation.currentResidence.address.addressLine2',
        'homeAddress',
        FormSection.ClientInfo,
      ),
      'homeAddress.city': this.getControlAndSetSectionMap(
        clientForm,
        'residenceInformation.currentResidence.address.city',
        'homeAddress',
        FormSection.ClientInfo,
      ),
      'homeAddress.state': this.getControlAndSetSectionMap(
        clientForm,
        'residenceInformation.currentResidence.address.state',
        'homeAddress',
        FormSection.ClientInfo,
      ),
      'homeAddress.zipCode': this.getControlAndSetSectionMap(
        clientForm,
        'residenceInformation.currentResidence.address.zipCode',
        'homeAddress',
        FormSection.ClientInfo,
      ),
      'homeAddress.durationAtResidence.years': this.getControlAndSetSectionMap(
        clientForm,
        'residenceInformation.currentResidence.durationAtResidence.years',
        'homeAddress.durationAtResidence.years',
        FormSection.ClientInfo,
      ),
      'homeAddress.durationAtResidence.months': this.getControlAndSetSectionMap(
        clientForm,
        'residenceInformation.currentResidence.durationAtResidence.months',
        'homeAddress.durationAtResidence.months',
        FormSection.ClientInfo,
      ),
      militaryStatus: this.getControlAndSetSectionMap(
        clientForm,
        'military.militaryServices.0.status',
        'militaryStatus',
        FormSection.ClientInfo,
      ),
    };
  };

  isLoanInClientDataImportPilot$ = this.loanIdService.loanId$.pipe(
    filter((loanId): loanId is string => !!loanId),
    switchMap((loanId) =>
      this.authorizationService.getLoanAccess(loanId, [Pilot.ClientDataImport]),
    ),
    map((access) => access[Pilot.ClientDataImport] ?? false),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  private retry$ = new Subject<void>();
  private retryPrefill = () => {
    this.retry$.next();
  };

  private prefillError = new BehaviorSubject<{
    hasError: boolean;
    isFetching: boolean;
    retry: () => void;
  }>({ hasError: false, isFetching: false, retry: this.retryPrefill });
  prefillError$ = this.prefillError.asObservable();

  private prefillDataSubject = new BehaviorSubject<ServicedData | null>(null);
  public prefillData$ = this.prefillDataSubject.asObservable();
  private isLeadPrefillDataFetched$ = new Subject<boolean>();
  private canFetchCreditOrdersPrefillData$ = new Subject<boolean>();

  public hasPrefillData$ = this.prefillData$.pipe(
    tap((prefillData) => {
      if (!prefillData || !hasNonNullProperty(prefillData)) {
        this.fetchPrefillDataFromLead();
      }
    }),
    map((prefillData) => !!prefillData && hasNonNullProperty(prefillData)),
    takeUntilDestroyed()
  );

  private fetchPrefillDataFromLead() {
    combineLatest([
      this.loanIdService.loanId$,
      this.isLoanInClientDataImportPilot$,
    ]).pipe(
      filter(([loanId, isInPilot]) =>
        loanId !== null &&
        isInPilot
      ),
      switchMap(([loanId]) =>
        this.retry$.pipe(
          startWith(null),
          exhaustMap(() => {
            this.prefillError.next({ ...this.prefillError.value, isFetching: true });

            return this.dataProviderService.getPrefillData$(loanId as string).pipe(
              map((data) => {
                if (data && hasNonNullProperty(data)) {
                  this.prefillDataSubject.next(data);
                  this.isLeadPrefillDataFetched$.next(true);
                }
                else {
                  this.isLeadPrefillDataFetched$.next(false);
                }
                this.prefillError.next({ ...this.prefillError.value, hasError: false, isFetching: false });
              }),
              catchError((error) => {
                console.error('Error fetching prefill data:', error);
                this.prefillError.next({
                  hasError: true,
                  isFetching: false,
                  retry: this.retryPrefill,
                });
                this.isLeadPrefillDataFetched$.next(false);
                return EMPTY;
              }),
            );
          })
        )
      ),
      takeUntilDestroyed()
    ).subscribe();
  }

  fetchPrefillDataFromCreditOrders$ = combineLatest([
    this.loanIdService.loanId$,
    this.isLoanInClientDataImportPilot$,
    this.canFetchCreditOrdersPrefillData$,
    this.creditService.creditOrders$
  ]).pipe(
    filter(([loanId, isInPilot, canFetchCreditOrdersPrefillData, orders]) =>
      loanId !== null &&
      isInPilot &&
      canFetchCreditOrdersPrefillData &&
      (orders.data?.length ?? 0) > 0
    ),
    switchMap(([loanId]) => {
      return this.dataProviderService.getCreditPulledServicedData$(loanId as string).pipe(
        map((data) => {
          if (data && hasNonNullProperty(data)) {
            this.prefillDataSubject.next(data);
          }
        }),
        catchError((error) => {
          console.error('Error fetching prefill data from Credit Order Liabilities:', error);
          return EMPTY;
        })
      );
    }),
    takeUntilDestroyed()
  ).subscribe();

  fetchPrefillDataFromAssignedLiabilities$ = combineLatest([
    this.loanIdService.loanId$,
    toObservable(this.ownedPropertyStateService.stateValues),
    this.liabilityStateService.state$,
    this.isLoanInClientDataImportPilot$,
    this.isLeadPrefillDataFetched$
  ]).pipe(
    filter(([loanId, ownedProperties, liabilities, isInPilot, isLeadPrefillDataFetched]) =>
      loanId !== null &&
      isInPilot &&
      isLeadPrefillDataFetched === false &&
      ownedProperties?.length > 0 &&
      (liabilities.data?.size ?? 0) > 0
    ),
    switchMap(([loanId, ownedProperties, liabilities]) => {
      const spaLiabilityFound = this.canFindSpaLiability(ownedProperties, liabilities);

      if (spaLiabilityFound) {
        return this.dataProviderService.getReoLienAssignedServicedData$(loanId as string).pipe(
          map((data) => {
            if (data && hasNonNullProperty(data)) {
              this.prefillDataSubject.next(data);
            }
            else {
              this.canFetchCreditOrdersPrefillData$.next(true);
            }
          }),
          catchError((error) => {
            console.error('Error fetching prefill data FROM REO Liability:', error);
            this.canFetchCreditOrdersPrefillData$.next(true);
            return EMPTY;
          })
        );
      }
      else {
        this.canFetchCreditOrdersPrefillData$.next(true);
        return EMPTY;
      }
    }),
    takeUntilDestroyed(),
  ).subscribe();

  prefillLoanData$ = combineLatest([
    toObservable(this.ownedPropertyStateService.stateValues),
    this.liabilityStateService.state$,
  ]).pipe(
    switchMap(([ownedProperties, liabilities]) => {
      const subjectProperty = ownedProperties.find((property) => property.isSubjectProperty);

      const liabilityValues = liabilities.data?.values() ?? [];
      const liabilityArray = Array.from(liabilityValues);

      // this finds the lien in the earliest position
      const firstLiability = liabilityArray
        ?.filter((liability) => subjectProperty?.liabilityIds?.includes(liability.id))
        .sort((liabilityA, liabilityB) => {
          const aPosition =
            (liabilityA as LiabilitySecuredByLien)?.lienInformation?.lienPosition ?? 0;
          const bPosition =
            (liabilityB as LiabilitySecuredByLien)?.lienInformation?.lienPosition ?? 0;

          return aPosition - bPosition;
        })
        .find((liability) => !!liability);

      this.loanControls = this.prefillLoanControlMap(subjectProperty?.id, firstLiability?.id);

      const loanValueChanges$ = Object.values(this.loanControls)
        .flatMap((controlArray) => controlArray)
        .filter((control): control is FormControl => !!control)
        .map((control) => control.valueChanges.pipe(startWith(control.value)));

      return combineLatest([this.prefillData$, ...loanValueChanges$]).pipe(
        filter(([prefillData]) => !!prefillData),
        map(([prefillData]) => {
          const subjectPropertyAddressArray: PrefillDataPoint[] = [];
          const hoaArray: PrefillDataPoint[] = [];
          const otherLoanValuesArray: PrefillDataPoint[] = [];

          Object.entries(prefillData!)
            .filter(
              ([key]) =>
                key === 'subjectPropertyAddress' ||
                (!!this.loanControls[key] &&
                  this.loanControls[key].every((control) => control !== null)),
            )
            .forEach(([key, value]) => {
              if (key === 'subjectPropertyAddress') {
                Object.entries(value)
                  .filter(
                    ([addressKey]) => !!this.loanControls[`subjectPropertyAddress.${addressKey}`],
                  )
                  .forEach(([addressKey, addressValue]) => {
                    subjectPropertyAddressArray.push({
                      group: key,
                      key: addressKey,
                      value: addressValue,
                      matchesControl: doAllControlsMatchValue(
                        this.loanControls[`subjectPropertyAddress.${addressKey}`],
                        addressValue,
                      ),
                    });
                  });
              } else if (this.hoaFields.includes(key)) {
                hoaArray.push({
                  group: 'hoa',
                  key,
                  value: (value ?? '').toString(),
                  matchesControl: doAllControlsMatchValue(this.loanControls[`${key}`], value),
                });
              } else {
                otherLoanValuesArray.push({
                  group: '',
                  key,
                  value: (value ?? '').toString(),
                  matchesControl: doAllControlsMatchValue(this.loanControls[`${key}`], value),
                });
              }
            });

          const result: PrefillDataPoint[] = [];

          if (subjectPropertyAddressArray.length > 0) {
            result.push({
              group: 'subjectPropertyAddress',
              key: 'subjectPropertyAddress',
              value: subjectPropertyAddressArray,
              matchesControl: subjectPropertyAddressArray.every(
                (dataPoint) => dataPoint.matchesControl,
              ),
            });
          }

          if (hoaArray.length > 0) {
            result.push({
              group: 'hoa',
              key: 'hoa',
              value: hoaArray,
              matchesControl: hoaArray.every((dataPoint) => dataPoint.matchesControl),
            });
          }

          if (otherLoanValuesArray.length > 0) {
            result.push(...otherLoanValuesArray);
          }

          return result;
        }),
      );
    }),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  prefillClientData$ = combineLatest([
    this.prefillData$,
    this.clientFormService.reset$.pipe(startWith(null)),
  ]).pipe(
    filter(([prefillData]) => !!prefillData),
    switchMap(([prefillData]) => {
      // get all the client controls that match clients in the serviced-data
      this.clientControlMap =
        prefillData?.clients?.reduce<Record<string, Record<string, AbstractControl | null>>>(
          (map, client) => {
            if (!client.gcid) return map;

            const controlsForClient = this.prefillClientControlMap(client.gcid);

            if (Object.keys(controlsForClient).length !== 0) {
              map[client.gcid] = this.prefillClientControlMap(client.gcid);
            }

            return map;
          },
          {},
        ) ?? {};

      const clientValueChanges$ = Object.values(this.clientControlMap)
        .flatMap((controlMap) => Object.values(controlMap))
        .filter((control): control is FormControl => !!control)
        .map((control) => control.valueChanges.pipe(startWith(control.value)));

      // observe all the valueChanges for each control and
      // build the object for the prefill component to utilize
      return combineLatest(clientValueChanges$).pipe(
        map(() => {
          if (!prefillData?.clients) return [];

          return prefillData.clients
            .filter((client) => client.gcid && !!this.clientControlMap[client.gcid])
            .map((client) => {
              if (!client.gcid) return client;

              const clientProperties = Object.entries(client)
                .filter(
                  ([key]) => key === 'homeAddress' || !!this.clientControlMap[client.gcid!][key],
                )
                .map(([key, value]) => {
                  if (key === 'homeAddress') {
                    return this.mapHomeAddress(client.gcid!, key, value);
                  }

                  return {
                    gcid: client.gcid,
                    group: '',
                    key,
                    value: value?.toString(),
                    matchesControl: doesControlMatchValue(
                      this.clientControlMap[client.gcid!][key],
                      value,
                    ),
                  };
                });

              return { gcid: client.gcid, clientProperties };
            });
        }),
        // TODO: Clean up the typing on this
      ) as unknown as Observable<{ gcid: string; clientProperties: any }[]>;
    }),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  mapHomeAddress(gcid: string, prefix: string, address: object): any[] {
    return Object.entries(address).flatMap(([addressKey, addressValue]) => {
      if (typeof addressValue === 'object' && addressValue !== null) {
        return this.mapHomeAddress(gcid, `${prefix}.${addressKey}`, addressValue);
      }

      return {
        gcid,
        key: addressKey,
        value: addressValue,
        matchesControl: doesControlMatchValue(
          this.clientControlMap[gcid][`${prefix}.${addressKey}`],
          addressValue,
        ),
      };
    });
  }

  prefillClientEmploymentData$ = combineLatest([
    this.prefillData$,
    this.clientFormService.reset$.pipe(startWith(null)),
    toObservable(this.incomeFormService.clientIncomeMap),
  ]).pipe(
    filter(([prefillData]) => !!prefillData),
    switchMap(([prefillData]) => {
      const clientEmploymentMap =
        prefillData?.clients?.reduce<Record<string, Map<string, AllIncomeGroup>>>((map, client) => {
          if (!client.gcid) return map;

          map[client.gcid] = prefillClientEmploymentControlMap({
            gcid: client.gcid,
            clientFormService: this.clientFormService,
            incomeFormService: this.incomeFormService,
          });
          return map;
        }, {}) ?? {};

      const valueChanges$ = Object.values(clientEmploymentMap)
        .flatMap<AllIncomeGroup>((employmentMap) => [...employmentMap.values()])
        .map((incomeGroup) => incomeGroup.valueChanges.pipe(startWith(incomeGroup.value)));

      // observe all the valueChanges for all client income and
      // build the object for the prefill-employment component to utilize
      return combineLatest(valueChanges$).pipe(
        startWith([]),
        map(() => {
          return (
            prefillData?.clients?.reduce(
              (acc, client) => {
                if (!client.gcid) return acc;

                acc[client.gcid] =
                  client.employmentInfos?.map((employmentInfo) => {
                    const doesMatch = doesMatchEmplyomentForm(
                      employmentInfo,
                      clientEmploymentMap[client.gcid!],
                    );

                    return Object.entries(employmentInfo).map(
                      ([employmentKey, employmentValue], index) => {
                        return {
                          gcid: client.gcid,
                          group: `employment ${index}`,
                          key: `${employmentKey}`,
                          value: employmentValue,
                          matchesControl: doesMatch,
                        };
                      },
                    );
                  }) ?? [];

                return acc;
              },
              // string - gcid of client
              // array entry for each income which is an array of income properties
              {} as Record<string, PrefillDataPoint[][]>,
            ) ?? {}
          );
        }),
      );
    }),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  handlePrefillLoanData(key: string) {
    this.prefillData$.pipe(take(1)).subscribe((data) => {
      if (key === 'propertyType' && data?.propertyType) {
        if (data.propertyType in PropertyType) {
          const controls = this.loanControls[key];

          controls?.forEach((control) => {
            if (control && control.value !== data.propertyType) {
              control.markAsDirty();
              control.setValue(PropertyType[data.propertyType as keyof typeof PropertyType]);
            }
          });
        }
      } else if (key === 'originalClosingDate' && data?.originalClosingDate) {
        const controls = this.loanControls[key];

        controls?.forEach((control) => {
          if (control && control.value !== data.originalClosingDate) {
            control.markAsDirty();
            control.setValue(new Date(data.originalClosingDate!).toISOString());
          }
        });
      } else if (key === 'subjectPropertyAddress' && data?.subjectPropertyAddress) {
        Object.entries(data.subjectPropertyAddress).forEach(([addressKey, value]) => {
          const controls = this.loanControls[`${key}.${addressKey}`];

          controls?.forEach((control) => {
            if (control && control.value !== value) {
              control?.markAsDirty();
              control?.setValue(value);
            }
          });
        });
      } else if (key === 'hoa') {
        Object.entries(data!)
          .filter(([key, value]) => this.hoaFields.includes(key))
          .forEach(([key, value]) => {
            this.loanControls[key]?.forEach((control) => {
              if (control && control.value !== value) {
                control.markAsDirty();
                control.setValue(value);
              }
            });
          });
      } else if (key === 'amortizationType' && data?.amortizationType) {
        if (data.amortizationType in AmortizationType) {
          const controls = this.loanControls[key];

          controls?.forEach((control) => {
            if (control && control.value !== data.amortizationType) {
              control?.markAsDirty();
              control?.setValue(
                AmortizationType[data.amortizationType as keyof typeof AmortizationType],
              );
            }
          });
        }
      } else if (key === 'loanType' && data?.loanType) {
        if (data.loanType in MortgageType) {
          const controls = this.loanControls[key];

          controls?.forEach((control) => {
            if (control && control.value !== data.loanType) {
              control?.markAsDirty();
              control?.setValue(MortgageType[data.loanType as keyof typeof MortgageType]);
            }
          });
        }
      } else {
        // all the non-special cases that can have their value set without manipulation
        const controls = this.loanControls[key];

        controls?.forEach((control) => {
          if (control && control.value !== data![key as keyof typeof data]) {
            control?.markAsDirty();
            control?.setValue(data![key as keyof typeof data]);
          }
        });
      }
      this.formNavService.activateSection(this.controlSectionMap[key]);
    });
  }

  handlePrefillClientData(gcid: string, key: string) {
    this.prefillData$.pipe(take(1)).subscribe((data) => {
      const client = data?.clients?.find((client) => client.gcid === gcid);

      if (!client) return;

      if (key === 'dateOfBirth' && client.dateOfBirth) {
        const control = this.clientControlMap[gcid][key];

        if (control && control.value !== client.dateOfBirth) {
          control?.markAsDirty();
          control?.setValue(new Date(client.dateOfBirth).toISOString());
        }
      } else if (key === 'homeAddress' && client.homeAddress) {
        this.handlePrefillClientAddressData(gcid, key, client.homeAddress);
      } else if (key === 'militaryStatus' && client.militaryStatus) {
        if (client.militaryStatus ?? '' in MilitaryStatus) {
          const control = this.clientControlMap[gcid][key];

          if (control && control.value !== client.militaryStatus) {
            control?.markAsDirty();
            control?.setValue(MilitaryStatus[client.militaryStatus as keyof typeof MilitaryStatus]);
          }
        }
      } else {
        // all the non-special cases that can have their value set without manipulation
        const control = this.clientControlMap[gcid][key];

        if (control && control.value !== client[key as keyof typeof client]) {
          control?.markAsDirty();
          control?.setValue(client[key as keyof typeof client]);
        }
      }
      this.formNavService.activateSection(this.controlSectionMap[key]);
    });
  }

  handlePrefillClientAddressData(gcid: string, prefix: string, parentValue: object) {
    Object.entries(parentValue).flatMap(([addressKey, addressValue]) => {
      if (typeof addressValue === 'object' && addressValue !== null) {
        return this.handlePrefillClientAddressData(gcid, `${prefix}.${addressKey}`, addressValue);
      }
      const control = this.clientControlMap[gcid][`${prefix}.${addressKey}`];
      if (control && control.value !== addressValue) {
        control?.markAsDirty();
        control?.setValue(addressValue);
      }
    });
  }

  handlePrefillClientEmploymentInfo(gcid: string, index: number) {
    this.prefillData$.pipe(take(1)).subscribe((data) => {
      const client = data?.clients?.find((client) => client.gcid === gcid);

      if (!client || !client.employmentInfos || !client.employmentInfos[index]) return;

      const clientForms = this.clientFormService.getClientFormControls();
      const clientForm = clientForms.find((formGroup) => formGroup.value.gcid === client.gcid);

      if (!clientForm) return;

      const clientId = clientForm.get('id')?.value ?? '';
      const employmentInfo = client.employmentInfos[index];

      this.formNavService.activateSection(FormSection.Income);

      if (
        doesMatchEmplyomentForm(employmentInfo, this.incomeFormService.clientIncomeMap()[clientId])
      ) {
        return;
      }

      // non-matching income is added as a new income
      const newIncome = this.incomeFormService.addIncome(clientForm.get('id')?.value ?? '');

      // mark a field as dirty to trigger save
      newIncome.get('incomeType')?.markAsDirty();

      const employmentIncomeType = employmentInfo.incomeType;
      if (isIncomeType(employmentIncomeType)) {
        const incomeTypeValue = MAPPABLE_INCOME_TYPES.get(employmentIncomeType) || employmentIncomeType;
        newIncome.get('incomeType')?.patchValue(incomeTypeValue);
      }

      if (employmentInfo.employmentType && employmentInfo.employmentType in EmploymentType) {
        newIncome
          .get('employment.employmentType')
          ?.patchValue(
            EmploymentType[employmentInfo.employmentType as keyof typeof EmploymentType],
          );
      }

      if (employmentInfo.payFrequency && employmentInfo.payFrequency in IncomePaymentFrequency) {
        newIncome
          .get('base.frequency')
          ?.patchValue(
            IncomePaymentFrequency[
            employmentInfo.payFrequency as keyof typeof IncomePaymentFrequency
            ],
          );
      }

      newIncome
        .get('base.clientReportedAmount')
        ?.patchValue(employmentInfo.totalMonthlyQualifyingIncomeAmount);

      if (employmentInfo.employmentStartDate) {
        newIncome
          .get('employment.employmentStartDate')
          ?.patchValue(new Date(employmentInfo.employmentStartDate).toISOString());
      }

      newIncome.get('employment.jobTitle')?.patchValue(employmentInfo.jobTitle);
      newIncome.get('employment.employerName')?.patchValue(employmentInfo.employerName);
    });
  }
}
