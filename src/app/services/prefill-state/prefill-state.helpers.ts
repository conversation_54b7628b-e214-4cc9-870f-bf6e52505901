import { IncomeType, ServicedDataEmploymentInfo } from '@rocket-logic/rl-xp-bff-models';
import { ClientFormService } from '../entity-state/client-state/client-form.service';
import { AllIncomeGroup } from '../entity-state/income-state/form-types';
import { IncomeFormService } from '../entity-state/income-state/income-form.service';

export function doesMatchEmplyomentForm(
  employmentInfo: ServicedDataEmploymentInfo,
  clientEmploymentMap: Map<string, AllIncomeGroup>,
) {
  for (const employmentEntry of clientEmploymentMap) {
    const formValues = employmentEntry[1].getRawValue();

    if (employmentInfo.incomeType && employmentInfo.incomeType === formValues.incomeType) {
      if (
        [
          IncomeType.ActiveDuty,
          IncomeType.Standard,
          IncomeType.StandardSelfEmployment,
          IncomeType.Union,
        ].includes(employmentInfo.incomeType)
      ) {
        const employmentInfoStartDate = employmentInfo.employmentStartDate
          ? new Date(employmentInfo.employmentStartDate).setHours(0, 0, 0, 0)
          : null;
        const formStartDate = formValues.employment.employmentStartDate
          ? new Date(formValues.employment.employmentStartDate).setHours(0, 0, 0, 0)
          : null;

        return (
          employmentInfo.employerName === formValues.employment.employerName &&
          employmentInfoStartDate === formStartDate &&
          employmentInfo.employmentType === formValues.employment.employmentType &&
          employmentInfo.jobTitle === formValues.employment.jobTitle &&
          employmentInfo.payFrequency === formValues.base.frequency &&
          employmentInfo.totalMonthlyQualifyingIncomeAmount === formValues.base.clientReportedAmount
        );
      }

      return (
        employmentInfo.payFrequency === formValues.base.frequency &&
        employmentInfo.totalMonthlyQualifyingIncomeAmount === formValues.base.clientReportedAmount
      );
    }
  }

  return false;
}

/**
 *
 * @param gcid id of the client
 * @param clientFormService
 * @param incomeFormService
 * @returns All income forms for the client in order to monitor
 * all forms for changes in case they are updated to match
 * serviced-data.
 */
export function prefillClientEmploymentControlMap({
  gcid,
  clientFormService,
  incomeFormService,
}: {
  gcid: string;
  clientFormService: ClientFormService;
  incomeFormService: IncomeFormService;
}): Map<string, AllIncomeGroup> {
  const clientForms = clientFormService.getClientFormControls();
  const clientForm = clientForms.find((formGroup) => formGroup.value.gcid === gcid);

  if (!clientForm) {
    return new Map();
  }

  const clientId = clientForm.get('id')?.value ?? '';
  const incomeForms = incomeFormService.clientIncomeMap()[clientId];

  if (!incomeForms) {
    return new Map();
  }

  return incomeForms;
}
