import { TestBed } from '@angular/core/testing';
import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { AuthorizationService } from '../authorization/authorization.service';
import { CreditService } from '../credit/credit.service';
import { DataProviderService } from '../data-provider/data-provider.service';
import { ClientFormService } from '../entity-state/client-state/client-form.service';
import { IncomeFormService } from '../entity-state/income-state/income-form.service';
import { LiabilityFormService } from '../entity-state/liability-state/liability-form.service';
import { LiabilityStateService } from '../entity-state/liability-state/liability-state.service';
import { LoanFormService } from '../entity-state/loan-state/loan-form.service';
import { OwnedPropertyFormService } from '../entity-state/owned-property-state/owned-property-form.service';
import { OwnedPropertyStateService } from '../entity-state/owned-property-state/owned-property-state.service';
import { SubjectPropertyFormRef } from '../entity-state/subject-property-state/subject-property-form.service';
import { FormNavSectionService } from '../form-nav/form-nav-section.service';
import { LoanIdService } from '../loan-id/loan-id.service';
import { PrefillStateService } from './prefill-state.service';

describe('PrefillStateService', () => {
  let service: PrefillStateService;

  beforeEach(() =>
    MockBuilder(PrefillStateService)
      .mock(AuthorizationService)
      .mock(DataProviderService)
      .mock(LoanIdService, { loanId$: NEVER })
      .mock(LoanFormService)
      .mock(SubjectPropertyFormRef)
      .mock(ClientFormService, { reset$: NEVER })
      .mock(OwnedPropertyFormService)
      .mock(OwnedPropertyStateService)
      .mock(LiabilityStateService, { state$: NEVER })
      .mock(LiabilityFormService)
      .mock(IncomeFormService)
      .mock(FormNavSectionService)
      .mock(CreditService, {creditOrders$: NEVER})
  );
  beforeEach(() => {
    service = TestBed.inject(PrefillStateService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
