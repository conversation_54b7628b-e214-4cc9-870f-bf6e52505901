import { HttpErrorResponse } from '@angular/common/http';
import { DestroyRef, Injectable, computed, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { TaskKind } from '@rocket-logic/rl-xp-bff-models';
import {
  CreditPullRequest,
  CreditPullType,
  CreditReport,
  CreditReportOrder,
  CreditReportOrderStatus,
} from '@rocket-logic/rl-xp-bff-models/dist/credit';
import {
  Observable,
  Subject,
  catchError,
  combineLatest,
  filter,
  finalize,
  forkJoin,
  interval,
  map,
  of,
  partition,
  race,
  retry,
  scan,
  shareReplay,
  startWith,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { AvoService } from '../../analytics/avo/avo.service';
import { getCreditReportUrl } from '../../util/get-credit-report-url';
import { isReportInvalid } from '../../util/is-credit-report-invalid';
import { repeatUntil } from '../../util/repeat-until';
import { AbstractEventService } from '../abstract-event.service';
import { DataProviderService } from '../data-provider/data-provider.service';
import { ClientStateService } from '../entity-state/client-state/client-state.service';
import { LiabilityStateService } from '../entity-state/liability-state/liability-state.service';
import { LoanInfoFormListenerService } from '../entity-state/loan-state/loan-info-form-listener.service';
import { LoanStateService } from '../entity-state/loan-state/loan-state.service';
import { LeadService } from '../lead/lead.service';
import { LoanIdService } from '../loan-id/loan-id.service';
import { TaskStateService } from '../task/task-state.service';

enum CreditReportEventStatus {
  Complete = 'Complete',
  Imported = 'Imported',
}

interface CreditEvent {
  creditReportOrderId: string;
  creditReportOrderStatus: CreditReportEventStatus;
  loanNumber: string;
}

@Injectable()
export class CreditService extends AbstractEventService<CreditEvent> {
  private loanIdService = inject(LoanIdService);
  private dataProvider = inject(DataProviderService);
  private logger = inject(SplunkLoggerService);
  private taskService = inject(TaskStateService);
  private loanFormListenerService = inject(LoanInfoFormListenerService);
  loanStateService = inject(LoanStateService);
  liabilityStateService = inject(LiabilityStateService);
  clientStateService = inject(ClientStateService);
  destroyRef = inject(DestroyRef);
  avoService = inject(AvoService);
  readonly leadService = inject(LeadService);

  public isPullingCredit = signal(false);
  public isTransferringCredit = signal(false);
  public creditTransferSuccessful = signal<boolean | null>(null);
  public mostRecentReportId = signal<string | null>(null);

  refiSelected$ = toObservable(this.loanFormListenerService.refiSelected);

  leadServiceDetails = toSignal(this.leadService.lead$);

  private readonly NOTIFICATION_FALLBACK_TIME = 1 * 60 * 1000;
  override EVENT_TYPE = 'rocket-logic.credit-report-order.updated';
  private refreshSubject = new Subject<void>();

  constructor() {
    super();

    combineLatest([this.processingCreditOrder$, this.loanIdService.loanId$])
      .pipe(
        switchMap(([order, loanId]) => {
          if (!order || !loanId) {
            return of();
          }
          this.isPullingCredit.set(true);
          return this.creditNotificationListener$(loanId!, order!.orderId, order!.reportId).pipe(
            finalize(() => this.isPullingCredit.set(false)),
          );
        }),
        takeUntilDestroyed(),
      )
      .subscribe();
  }

  public creditReports$: Observable<{ data?: CreditReport[]; error?: any; fetching?: boolean }> =
    combineLatest([
      this.loanIdService.loanId$,
      this.refreshSubject.pipe(startWith(null)),
      this.loanStateService.isInitialized$.pipe(filter((isInitialized) => isInitialized)),
    ]).pipe(
      map(([loanId]) => loanId),
      filter((loanId): loanId is string => !!loanId),
      switchMap((loanId) => {
        return this.dataProvider.getCreditReports$(loanId).pipe(
          map((data) => ({ data, fetching: false, error: undefined })),
          catchError((err) => {
            this.logger.error(`CreditService: Unexpected error fetching credit reports`, err, {
              loanId,
            });
            return of({ error: err, fetching: false });
          }),
          startWith({ fetching: true }),
        );
      }),
      scan((acc, curr) => ({ ...acc, ...curr })),
      tap((creditReports) => {
        if ('data' in creditReports) {
          const mostRecentReportId = this.mostRecentReportId();
          const mostRecentReport = creditReports.data.find(
            (report) => report?.reportId === mostRecentReportId && report.isActive,
          );
          if (mostRecentReport) {
            this.mostRecentReportId.set(null);
            const creditReportUrl = getCreditReportUrl(mostRecentReport);
            window.open(creditReportUrl, '_blank');
          }
        }
      }),
      takeUntilDestroyed(),
      shareReplay(1),
    );

  public creditOrders$: Observable<{
    data?: CreditReportOrder[];
    error?: any;
    fetching?: boolean;
  }> = combineLatest([
    this.loanIdService.loanId$,
    this.refreshSubject.pipe(startWith(null)),
    this.loanStateService.isInitialized$.pipe(filter((isInitialized) => isInitialized)),
  ]).pipe(
    map(([loanId]) => loanId),
    filter((loanId): loanId is string => !!loanId),
    switchMap((loanId) => {
      return this.dataProvider.getCreditReportOrders$(loanId).pipe(
        map((data) => ({ data, fetching: false, error: undefined })),
        catchError((err) => {
          this.logger.error(`CreditService: Unexpected error fetching credit orders`, err, {
            loanId,
          });
          return of({ error: err, fetching: false });
        }),
        startWith({ fetching: true }),
      );
    }),
    scan((acc, curr) => ({ ...acc, ...curr })),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  public creditPullType$: Observable<{
    creditPullType?: CreditPullType;
    error?: any;
    fetching?: boolean;
  }> = combineLatest([
    this.loanStateService.isInitialized$,
    this.loanIdService.nonNullableLoanId$,
  ]).pipe(
    filter(([isInit, _]) => isInit),
    switchMap(([_, loanId]) =>
      this.dataProvider.getCreditPullType$(loanId).pipe(
        retry({ count: 2, delay: 1000 }),
        map(({ creditPullType }) => ({ creditPullType, fetching: false })),
        catchError((err) => {
          this.logger.error(`CreditService: Unexpected error fetching credit pull type`, err, {
            loanId,
          });
          return of({ error: err, fetching: false });
        }),
        startWith({ fetching: true }),
      ),
    ),
    switchMap((result) =>
      this.refiSelected$.pipe(
        map((refiSelected) => {
          if (refiSelected && !this.leadServiceDetails()?.isAssumptionLead) {
            // Ignore CRM response and default to soft until issue with default VA product is fixed
            return { creditPullType: CreditPullType.Soft, fetching: false };
          }
          return result;
        }),
      ),
    ),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  public processingCreditOrder$: Observable<CreditReportOrder | undefined> =
    this.creditOrders$.pipe(
      map((orders) => {
        return orders.data?.find(
          (order) => order.orderStatus === CreditReportOrderStatus.Processing,
        );
      }),
    );

  loanData = computed(() => this.loanStateService.state()?.data);
  clients = computed(() => this.clientStateService.stateValues());
  creditReports = toSignal(this.creditReports$);
  allClientsHaveCredit = computed(
    () =>
      this.clients().length > 0 &&
      this.clients().every((client) =>
        this.activeCreditReports()
          ?.filter((report) => !report.isInvalid)
          ?.some((creditReport) =>
            creditReport.creditReport.clients.some(
              (creditClient) => creditClient.clientId === client.id,
            ),
          ),
      ),
  );

  creditTasks = computed(() =>
    this.taskService
      .state()
      ?.data?.filter((task) => task.taskKind === TaskKind.CreditReportClientInformationSync),
  );

  activeCreditReports = computed(
    () =>
      this.creditReports()
        ?.data?.filter((report) => report.isActive)
        .map((activeReport) => {
          return {
            creditReport: activeReport,
            isInvalid: isReportInvalid(activeReport, this.clients(), this.creditTasks()),
          };
        }) ?? [],
  );

  public creditNotificationListener$(
    loanId: string,
    orderId: string,
    reportId: string,
  ): Observable<void> {
    const orderEvents$ = this.eventStream$.pipe(
      filter(
        (creditNotification) =>
          creditNotification.payload.creditReportOrderStatus === CreditReportEventStatus.Complete ||
          creditNotification.payload.creditReportOrderStatus === CreditReportEventStatus.Imported,
      ),
      filter(
        (creditNotification) =>
          creditNotification.payload.creditReportOrderId === orderId ||
          creditNotification.payload.creditReportOrderId === reportId,
      ),
      takeUntilDestroyed(this.destroyRef),
    );

    const [completedEventTrigger$, importedEventTrigger$] = partition(
      orderEvents$,
      (creditNotification) =>
        creditNotification.payload.creditReportOrderStatus === CreditReportEventStatus.Complete,
    );
    const creditEventFinished$ = combineLatest([
      completedEventTrigger$,
      importedEventTrigger$,
    ]).pipe(map(([completedEvent]) => completedEvent.payload.creditReportOrderId));

    const pollingFallback$ = interval(this.NOTIFICATION_FALLBACK_TIME).pipe(
      switchMap(() => this.pollCreditReportOrderStatus(loanId, orderId)),
      map((creditReportOrder) => creditReportOrder.reportId),
    );

    return race(creditEventFinished$, pollingFallback$).pipe(
      tap((creditRaceResult) => this.mostRecentReportId.set(creditRaceResult)),
      tap(() => this.refreshSubject.next()),
      map(() => undefined),
    );
  }

  public clientHasCredit(clientId: string) {
    return this.creditReports()?.data?.some((report) =>
      report.clients.some((reportClient) => reportClient.clientId === clientId),
    );
  }

  public pullCredit$(clientIds: string[], pullType?: CreditPullType) {
    return this.loanIdService.loanId$.pipe(
      switchMap((loanId) => {
        this.isPullingCredit.set(true);
        if (!loanId) throw new Error(`Trying to pull credit with no loan id`);
        if (!pullType) throw new Error(`Trying to pull credit with no credit pull type`);
        const creditPullRequest = {
          clientIds: clientIds,
          pullType,
        } as CreditPullRequest;
        return forkJoin([of(loanId), this.dataProvider.pullCredit$(loanId, creditPullRequest)]);
      }),
      switchMap(([loanId, creditReportOrder]) =>
        this.creditNotificationListener$(
          loanId,
          creditReportOrder.orderId,
          creditReportOrder.reportId,
        ),
      ),
      take(1),
      finalize(() => {
        this.liabilityStateService.refreshState();
        this.isPullingCredit.set(false);

        this.avoService.creditPull$.next({
          gcids: <string[]>this.clients()?.map((client) => client.gcid),
        });
      }),
    );
  }

  public transferCredit$(clientIds: string[]) {
    return this.loanIdService.loanId$.pipe(
      switchMap((loanId) => {
        this.isTransferringCredit.set(true);
        if (!loanId) throw new Error(`Trying to transfer credit with no loan id`);
        const transferRequest = {
          primaryBorrowerClientId: clientIds[0],
          secondaryBorrowerClientId: clientIds[1],
        };
        return forkJoin([of(loanId), this.dataProvider.transferCredit$(loanId, transferRequest)]);
      }),
      switchMap(([loanId, creditReportOrder]) =>
        this.creditNotificationListener$(
          loanId,
          creditReportOrder.orderId,
          creditReportOrder.reportId,
        ),
      ),
      take(1),
      tap(() => {
        this.creditTransferSuccessful.set(true);
      }),
      finalize(() => {
        this.isTransferringCredit.set(false);
      }),
      catchError((err) => {
        this.creditTransferSuccessful.set(false);
        if (err instanceof HttpErrorResponse && err.status === 404) {
          return of();
        }
        throw err;
      }),
    );
  }

  public pollCreditReportOrderStatus(
    loanId: string,
    orderId: string,
  ): Observable<CreditReportOrder> {
    return this.dataProvider.getCreditReportOrder$(loanId, orderId).pipe(
      repeatUntil(
        (creditReport) =>
          creditReport.orderStatus === CreditReportOrderStatus.Complete ||
          creditReport.orderStatus === CreditReportOrderStatus.Failed,
        {
          retries: 5,
        },
      ),
      map((creditReport) => creditReport),
    );
  }
}
