import { Injectable, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { Client, MaritalStatus } from '@rocket-logic/rl-xp-bff-models';
import { getClientName } from '../../util/get-client-name';
import { ClientStateService } from '../entity-state/client-state/client-state.service';
import { CreditAuthorizationService } from './credit-authorization.service';

@Injectable()
export class SelectedCreditClientService {
  private clientStateService = inject(ClientStateService);
  private creditAuthService = inject(CreditAuthorizationService);

  selectedClientsControl = new FormControl<string[] | null>(null);
  selectedClient = toSignal(this.selectedClientsControl.valueChanges);

  clients = computed(() => this.clientStateService.stateValues());

  clientCreditGroups = computed(() => {
    const groups: { client: Client; valid: boolean }[][] = [];
    this.clients().forEach((client) => {
      if (groups.some((group) => group.some((groupClient) => groupClient.client.id === client.id)))
        return;

      const spouse = this.clients().find(
        (potentialSpouse) => potentialSpouse.id === client.personalInformation?.spouseClientId,
      );

      const group = spouse
        ? [
            { client, valid: this.creditAuthService.hasClientMetConditions(client) },
            { client: spouse, valid: this.creditAuthService.hasClientMetConditions(spouse) },
          ]
        : [{ client, valid: this.creditAuthService.hasClientMetConditions(client) }];

      groups.push(group);
    });
    return groups;
  });

  clientOptions = computed(() =>
    this.clientCreditGroups().map((group) => {
      return group.reduce(
        (acc, clientObj) => {
          acc.value.push(clientObj.client.id!);

          if (acc.display.length > 0) {
            acc.display += '\n';
          }

          acc.display += getClientName(clientObj.client, true);

          if (clientObj.valid === false) {
            acc.disabled = true;
          }

          if (
            clientObj.client.personalInformation?.maritalStatus === MaritalStatus.Married &&
            group.length === 1
          ) {
            acc.description = 'Married - Spouse not on Loan';
          }

          return acc;
        },
        {
          value: [] as string[],
          display: '',
          description: '',
          disabled: false,
        },
      );
    }),
  );

  setSelectedClientGroup(clientId: string) {
    const selectedGroupIds = this.clientOptions().find((client) =>
      client.value.some((id) => id === clientId),
    )?.value;

    if (selectedGroupIds !== undefined) {
      this.selectedClientsControl.setValue(selectedGroupIds);
    }
  }
}
