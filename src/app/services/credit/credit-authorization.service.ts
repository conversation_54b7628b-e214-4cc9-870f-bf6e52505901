import { DestroyRef, Injectable, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { Client, Loan, LoanPurpose, RefinanceLoan } from '@rocket-logic/rl-xp-bff-models';
import { filter } from 'rxjs';
import { ClientStateService } from '../entity-state/client-state/client-state.service';
import { LoanStateService } from '../entity-state/loan-state/loan-state.service';
import { FormNavInputService } from '../form-nav/form-nav-input.service';
import { UserAuthorizationService } from '../user-authorization/user-authorization.service';
import { CreditService } from './credit.service';

@Injectable()
export class CreditAuthorizationService {
  userAuthorizationService = inject(UserAuthorizationService);
  creditService = inject(CreditService);
  loanStateService = inject(LoanStateService);
  clientStateService = inject(ClientStateService);
  formInputNavService = inject(FormNavInputService);
  destroyRef = inject(DestroyRef);

  isBankerLicensed = toSignal(
    this.userAuthorizationService.isBankerLicensed$.pipe(
      filter((isLicensed) => isLicensed != null),
    ),
  );

  clients = computed(() => this.clientStateService.stateValues());
  loanData = computed(() => this.loanStateService.state()?.data);

  canViewCreditRequestManager = computed(
    () =>
      this.creditRequirementsSatisfied() ||
      (this.creditService.creditReports()?.data?.length ?? 0) > 0,
  );

  public creditRequirementsSatisfied = computed(
    () =>
      this.hasLoanMetConditions(this.loanData()) &&
      this.clients().some((client) => this.hasClientMetConditions(client)) &&
      this.isBankerLicensed()?.write &&
      !this.userAuthorizationService.hasConflictingLoans(),
  );

  public hasClientMetConditions(client: Client): boolean {
    return (
      client.personalInformation?.ssn != null &&
      client.personalInformation?.dateOfBirth != null &&
      client.residenceInformation?.currentResidence?.address != null &&
      client.id != null &&
      client.gcid != null &&
      client.personalInformation?.maritalStatus != null
    );
  }

  private hasLoanMetConditions(loan: Loan | undefined): boolean {
    if (!loan) return false;

    if (loan.loanPurpose === LoanPurpose.Refinance) {
      return ((loan as RefinanceLoan)?.refinanceGoals?.length ?? 0) > 0;
    }

    return true;
  }
}
