import { inject, Injectable, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  AssetRequestStatus,
  FailureReason,
} from '@rocket-logic/rl-xp-bff-models/dist/notification/subjects';
import { tap } from 'rxjs';
import { AbstractEventService } from '../abstract-event.service';
import { AssetStateService } from '../entity-state/asset-state/asset-state.service';

@Injectable()
export class AssetDataPullService extends AbstractEventService {
  override EVENT_TYPE = 'partnership.assets.external-request.completed';
  private assetService = inject(AssetStateService);

  assetDataPullState = signal<{ status?: AssetRequestStatus; failureReasons?: FailureReason[] }>(
    {},
  );

  constructor() {
    super();

    this.eventStream$
      .pipe(
        tap((message) => {
          this.assetDataPullState.set({
            status: message.payload.status,
            failureReasons: message.payload.failureReasons ?? [],
          });
        }),
        takeUntilDestroyed(),
      )
      .subscribe({
        next: () => this.assetService.refreshState(),
      });
  }
}
