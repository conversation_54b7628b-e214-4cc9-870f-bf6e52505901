import { Injectable } from '@angular/core';
import { Subject } from 'rxjs';
import { SaveTrigger, SaveTriggerType } from './save-trigger';

@Injectable()
export class ManualSaveService implements SaveTrigger {
  private triggerSaveSubject = new Subject<SaveTriggerType.Manual>();
  triggerSave$ = this.triggerSaveSubject.asObservable();

  saveChanges() {
    this.triggerSaveSubject.next(SaveTriggerType.Manual);
  }
}
