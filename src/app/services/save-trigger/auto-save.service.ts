import { DestroyRef, Injectable, InjectionToken, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AbstractControl } from '@angular/forms';
import { Subject, Subscription, merge, switchMap, takeUntil, timer } from 'rxjs';
import { ManualSaveService } from './manual-save.service';
import { SaveTrigger, SaveTriggerType } from './save-trigger';

export const AUTO_SAVE_DELAY = new InjectionToken<number>('AUTO_SAVE_DELAY');

@Injectable()
export class AutoSaveTriggerService implements SaveTrigger {
  manualSaveService = inject(ManualSaveService);
  destroyRef = inject(DestroyRef);
  saveDelay = inject(AUTO_SAVE_DELAY);

  protected triggerSaveSubject = new Subject<SaveTriggerType>();
  triggerSave$ = this.triggerSaveSubject.asObservable();
  protected saveTimerSub?: Subscription;
  protected controls?: AbstractControl[];

  registerControls(...controls: AbstractControl[]) {
    this.controls = controls;
    this.startSaveTimer();
  }

  protected startSaveTimer() {
    // Unsubscribe from previous timer
    this.saveTimerSub?.unsubscribe();

    if (this.controls?.length) {
      this.saveTimerSub = this.getChangeTrigger$(this.controls)
        .pipe(takeUntilDestroyed(this.destroyRef))
        .subscribe((trigger) => this.triggerSaveSubject.next(trigger || SaveTriggerType.Auto));
    }
  }

  protected getChangeTrigger$(controls: AbstractControl[]) {
    const autoSaveTrigger$ = merge(...controls.map((control) => control.valueChanges)).pipe(
      switchMap(() => timer(this.saveDelay).pipe(takeUntil(this.manualSaveService.triggerSave$))),
    );
    return merge(autoSaveTrigger$, this.manualSaveService.triggerSave$);
  }
}
