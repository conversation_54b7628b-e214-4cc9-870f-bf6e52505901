import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { map, merge, Observable, shareReplay, startWith } from 'rxjs';
import { STATE_SERVICES } from '../entity-state/abstract-entity-state.service';

@Injectable()
export class LoanLastSavedService {
  private stateServices = inject(STATE_SERVICES);

  lastSavedAt$: Observable<Date> = merge(
    ...this.stateServices.map((stateService) => stateService.state$),
  ).pipe(
    map(() => new Date()),
    startWith(new Date()),
    takeUntilDestroyed(),
    shareReplay(1),
  );
}
