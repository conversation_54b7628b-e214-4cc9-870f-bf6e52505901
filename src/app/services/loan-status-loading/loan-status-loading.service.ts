import { Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { map, shareReplay } from 'rxjs';
import { LeadService } from '../lead/lead.service';

@Injectable()
export class LoanStatusLoadingService {
  private leadService = inject(LeadService);

  loanLoadingStatus$ = this.leadService.leadInitializationResult$;

  loanLoadingError$ = this.leadService.leadInitializationResult$.pipe(
    map((result) => result.error),
    shareReplay(1),
    takeUntilDestroyed(),
  );
}
