import { Injectable, computed, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Subscription, fromEvent } from 'rxjs';
import { SettingsService } from '../../settings/settings.service';
import { AbstractFormNavService } from './abstract-form-nav.service';
import { FormNavInputService, ProductMilestoneFormInput } from './form-nav-input.service';

export enum FormSection {
  LoanInfo = 'LoanInfo',
  ClientInfo = 'ClientInfo',
  PurchaseInfo = 'PurchaseInfo',
  RefinanceInfo = 'RefinanceInfo',
  SubjectProperty = 'SubjectProperty',
  Credit = 'Credit',
  Income = 'Income',
  Assets = 'Assets',
  REO = 'REO',
}

export const FORM_SECTION_MILESTONES = new Map([
  [FormSection.Income, ProductMilestoneFormInput.Income],
  [FormSection.REO, ProductMilestoneFormInput.SubjectPropertyREO],
]);

@Injectable()
export class FormNavSectionService extends AbstractFormNavService<FormSection> {
  settings = inject(SettingsService);
  inputNavService = inject(FormNavInputService);

  activeSection = signal<FormSection | null>(FormSection.LoanInfo);

  registeredSections = signal<FormSection[]>([]);

  sectionElements = new Map<FormSection, { element: HTMLElement; subs: Subscription }>();

  availableSections = computed(() => {
    if (this.settings.formSectionOrder().length > 0 && this.sectionElements.size > 0) {
      const sections = this.settings.formSectionOrder();
      return sections.sort(this.sectionSort);
    }
    return [];
  });

  override registerSection(section: FormSection, element: HTMLElement, hook: () => void): void {
    const elementObj = { element, subs: new Subscription(), hook };
    
    const milestone = FORM_SECTION_MILESTONES.get(section);
    if (milestone) {
      this.inputNavService.registerSection(milestone, element, '');
    }
    
    elementObj.subs.add(
      fromEvent(element, 'focusin')
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => this.activeSection.set(section)),
    );
    elementObj.subs.add(
      fromEvent(element, 'focusout').pipe(takeUntilDestroyed(this.destroyRef)).subscribe(),
    );
    this.sectionElements.set(section, elementObj);
    
    this.registeredSections.update((sections) => [...sections, section]);
  }

  override deregisterSection(element: HTMLElement) {
    const sectionToRemove = super.deregisterSection(element);

    if (sectionToRemove) {
      this.registeredSections.update((sections) =>
        sections.filter((section) => section !== sectionToRemove),
      );
    }

    return sectionToRemove;
  }

  sectionSort = (a: FormSection, b: FormSection) => {
    const mapA = this.settings.formSectionOrder().indexOf(a);
    const mapB = this.settings.formSectionOrder().indexOf(b);

    if (mapA < mapB) {
      return -1;
    }

    if (mapA > mapB) {
      return 1;
    }

    return 0;
  };
}