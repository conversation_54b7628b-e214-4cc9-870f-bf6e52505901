import { ScrollDispatcher } from '@angular/cdk/scrolling';
import { DestroyRef, ElementRef, Injectable, Signal, WritableSignal, inject } from '@angular/core';
import { Subscription } from 'rxjs';
import { environment } from '../../../environments/environment';

@Injectable()
export abstract class AbstractFormNavService<T> {
  protected destroyRef = inject(DestroyRef);
  protected scrollDispatcher = inject(ScrollDispatcher);
  protected appIsland?: ElementRef;

  abstract activeSection: WritableSignal<T | null>;
  abstract availableSections: Signal<T[]>;
  abstract sectionElements: Map<T, { element: HTMLElement; subs: Subscription; hook?: () => void }>;

  abstract registerSection(section: T, element: HTMLElement, ...extras: any[]): void;

  deregisterSection(element: HTMLElement): T | undefined {
    const section = Array.from(this.sectionElements.entries()).find(
      ([_, registeredElement]) => registeredElement.element === element,
    )?.[0];
    if (section) {
      const elementObj = this.sectionElements.get(section);
      this.sectionElements.delete(section);
      elementObj?.subs.unsubscribe();
    }
    return section;
  }

  activateSection(section: T) {
    const element = this.sectionElements.get(section)?.element;
    if (element) {
      this.sectionElements.get(section)?.hook?.();
      setTimeout(() => {
        this.handleElementScroll(element);
      }, 0);
      this.activeSection.set(section);
    }
  }

  registerAppIsland(ref: ElementRef) {
    this.appIsland = ref;
  }

  deregisterAppIsland() {
    this.appIsland = undefined;
  }

  private handleElementScroll(element: HTMLElement) {
    const boundingRect = element.getBoundingClientRect();
    const scrollContainer = this.scrollDispatcher
      .getAncestorScrollContainers(element)[0]
      ?.getElementRef().nativeElement;
    const scrollContainerRect = scrollContainer?.getBoundingClientRect();

    if (scrollContainerRect && boundingRect.height > scrollContainerRect.height) {
      // set scroll position manually since element is larger than scroll container
      const appIslandBoundingRect = this.appIsland?.nativeElement.getBoundingClientRect();
      if (appIslandBoundingRect) {
        // Need to offset scroll so element is not within the app island
        scrollContainer.scrollBy({
          behavior: environment.scrollBehavior,
          top: boundingRect.top - appIslandBoundingRect.bottom - 16,
        });
      } else {
        scrollContainer.scrollBy({
          behavior: environment.scrollBehavior,
          top: boundingRect.top,
        });
      }
    } else {
      element.scrollIntoView({ block: 'center', behavior: environment.scrollBehavior });
    }

    const input = element.querySelector('input');
    if (input) {
      input.focus({ preventScroll: true });
    } else {
      element.focus({ preventScroll: true });
    }
  }
}
