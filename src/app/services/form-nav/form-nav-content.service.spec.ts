import { TestBed } from '@angular/core/testing';

import { Mock<PERSON>uilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { CreditService } from '../credit/credit.service';
import { AssetStateService } from '../entity-state/asset-state/asset-state.service';
import { ClientStateService } from '../entity-state/client-state/client-state.service';
import { IncomeStateService } from '../entity-state/income-state/income-state.service';
import { LoanStateService } from '../entity-state/loan-state/loan-state.service';
import { SubjectPropertyStateService } from '../entity-state/subject-property-state/subject-property-state.service';
import { LoanIdService } from '../loan-id/loan-id.service';
import { FormNavContentService } from './form-nav-content.service';

describe('FormNavContentService', () => {
  let service: FormNavContentService;

  beforeEach(() =>
    MockBuilder(FormNavContentService)
      .mock(LoanIdService, { loanId$: NEVER })
      .mock(LoanStateService)
      .mock(ClientStateService)
      .mock(SubjectPropertyStateService)
      .mock(CreditService, { creditReports$: NEVER })
      .mock(IncomeStateService)
      .mock(AssetStateService),
  );
  beforeEach(() => {
    service = TestBed.inject(FormNavContentService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
