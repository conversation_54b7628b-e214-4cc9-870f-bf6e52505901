import { computed, inject, Injectable } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { ActiveActionService } from '../active-action.service';
import { LoanStateService } from '../entity-state/loan-state/loan-state.service';
import { FormNavInputService, Milestone } from './form-nav-input.service';
import { FORM_SECTION_MILESTONES, FormSection } from './form-nav-section.service';

@Injectable()
export class SectionMilestoneService {
  private formNavInputService = inject(FormNavInputService);
  private loanStateService = inject(LoanStateService);
  private activeActionService = inject(ActiveActionService);
  private sectionMilestoneMap = new Map<FormSection, Milestone[]>();
  private isLoanComplete = toSignal(this.loanStateService.isLoanXpCompleted$);

  private usePricingMilestones = computed(() => {
    const isLoanComplete = this.isLoanComplete();
    const creditAndTaskCheck = this.activeActionService.creditAndTaskCheck();
    return creditAndTaskCheck || isLoanComplete;
  });

  constructor() {
    this.mapFormSectionMilestones();
  }

  registerMilestone(formSection: FormSection, milestone: Milestone): void {
    const currentMilestones = this.sectionMilestoneMap.get(formSection) ?? [];
    if (!currentMilestones.includes(milestone)) {
      this.sectionMilestoneMap.set(formSection, [...currentMilestones, milestone]);
    }
  }

  getMilestoneCountForSection(formSection: FormSection): number {
    const sectionCountMap = this.usePricingMilestones()
      ? this.availableProductMilestoneCountMap()
      : this.availableCreditMilestoneCountMap();
    return sectionCountMap.get(formSection) ?? 0;
  }

  private availableCreditMilestoneCountMap = this.createAvailableMilestoneCountMap(() =>
    this.formNavInputService.availableSections(),
  );

  private availableProductMilestoneCountMap = this.createAvailableMilestoneCountMap(() =>
    this.formNavInputService.availableProductSections(),
  );

  private getSectionCountMap(availableMilestones: string[]) {
    const sectionCountMap = new Map<FormSection, number>();
    for (const [formSection, milestones] of this.sectionMilestoneMap.entries()) {
      const count = milestones.reduce(
        (acc, milestone) => acc + availableMilestones.filter((m) => m.startsWith(milestone)).length,
        0,
      );
      sectionCountMap.set(formSection, count);
    }
    return sectionCountMap;
  }

  private createAvailableMilestoneCountMap(milestoneFn: () => string[]) {
    return computed(() => {
      const availableMilestones = milestoneFn();
      return this.getSectionCountMap(availableMilestones);
    });
  }

  private mapFormSectionMilestones() {
    FORM_SECTION_MILESTONES.forEach((milestone, formSection) => {
      this.registerMilestone(formSection, milestone);
    });
  }
}
