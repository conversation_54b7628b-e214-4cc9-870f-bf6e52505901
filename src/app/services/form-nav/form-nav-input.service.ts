import { ElementRef, Injectable, Renderer2, computed, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import {
  EmploymentIncome,
  Income,
  Liability,
  LiabilitySecuredAgainstProperty,
  OwnedProperty,
} from '@rocket-logic/rl-xp-bff-models';
import { Observable, Subscription, combineLatest, map, shareReplay } from 'rxjs';
import { CreditService } from '../credit/credit.service';
import { IncomeStateService } from '../entity-state/income-state/income-state.service';
import { LiabilityStateService } from '../entity-state/liability-state/liability-state.service';
import { LoanInfoFormListenerService } from '../entity-state/loan-state/loan-info-form-listener.service';
import { OwnedPropertyStateService } from '../entity-state/owned-property-state/owned-property-state.service';
import { AbstractFormNavService } from './abstract-form-nav.service';

export enum FormInput {
  ClientFirstName = 'ClientFirstName',
  ClientLastName = 'ClientLastName',
  MaritalStatus = 'MaritalStatus',
  CurrentStreetAddress = 'CurrentStreetAddress',
  CurrentStateAddress = 'CurrentStateAddress',
  CurrentCityAddress = 'CurrentCityAddress',
  CurrentZipAddress = 'CurrentZipAddress',
  RefinanceGoals = 'RefinanceGoals',
  DrawAmount = 'DrawAmount',
  LoanAmount = 'LoanAmount',
  LienPosition = 'LienPosition',
  DateOfBirth = 'DateOfBirth',
  SSN = 'SSN',
  ConfirmSSN = 'ConfirmSSN',
  SubjectPropertyStateAddress = 'SubjectPropertyStateAddress',
  SubjectPropertyZipAddress = 'SubjectPropertyZipAddress',
}

export enum ProductMilestoneFormInput {
  PhoneNumber = 'PhoneNumber',
  OwnershipStatus = 'OwnershipStatus',
  ClosingDate = 'ClosingDate',
  PurchasePrice = 'PurchasePrice',
  DownPayment = 'DownPayment',
  CashOutAmount = 'CashOutAmount',
  SubjectPropertyStreetAddress = 'SubjectPropertyStreetAddress',
  SubjectPropertyCityAddress = 'SubjectPropertyCityAddress',
  SubjectPropertyCounty = 'SubjectPropertyCounty',
  PropertyType = 'PropertyType',
  Occupancy = 'Occupancy',
  OccupancyCurrentAddress = 'OccupancyCurrentAddress',
  EstimatedValue = 'EstimatedValue',
  HoaPaymentAmount = 'HoaPaymentAmount',
  InternationalCredit = 'InternationalCredit',
  Income = 'Income',
  AmortizationType = 'AmortizationType',
  SubjectPropertyREO = 'SubjectPropertyREO',
}

export type Milestone = FormInput | ProductMilestoneFormInput;
type MilestoneStatusMap<T extends Milestone> = Map<T, { uuid: string; isOutstanding: boolean }[]>;

@Injectable()
export class FormNavInputService extends AbstractFormNavService<string> {
  hasSignedPa = inject(LoanInfoFormListenerService).hasSignedPa;
  isRefi = inject(LoanInfoFormListenerService).refiSelected;
  isRefi$ = toObservable(this.isRefi);
  incomeStateService = inject(IncomeStateService);
  ownedPropertyStateService = inject(OwnedPropertyStateService);
  liabilityStateService = inject(LiabilityStateService);
  renderer = inject(Renderer2);
  allClientsHaveCredit = inject(CreditService).allClientsHaveCredit;

  private readonly milestoneOutstandingObservables = new Map<Milestone, Observable<boolean>>([
    [
      ProductMilestoneFormInput.Income,
      this.incomeStateService.state$.pipe(
        map(({ data }) =>
          Object.entries(data ?? {}).flatMap(([_, incomeMap]) =>
            Array.from(incomeMap.values()).filter((income) => !!income),
          ),
        ),
        map((incomes) => this.isIncomeOutstanding(incomes)),
        takeUntilDestroyed(),
        shareReplay(1),
      ),
    ],
    [
      ProductMilestoneFormInput.SubjectPropertyREO,
      combineLatest([
        this.isRefi$,
        toObservable(this.ownedPropertyStateService.stateValues),
        this.liabilityStateService.state$,
      ]).pipe(
        map(([isRefi, ownedPropertyState, liabilityState]) =>
          this.isSubjectAssociationOutstanding(
            isRefi,
            ownedPropertyState,
            Array.from(liabilityState.data?.values() ?? []),
          ),
        ),
        takeUntilDestroyed(),
        shareReplay(1),
      ),
    ],
  ]);

  activeSection = signal<Milestone | null>(null);

  productMilestonesOrder = signal(this.createMilestoneStatusMap(ProductMilestoneFormInput));
  outstandingProductMilestones = computed(() =>
    this.getOutstandingMilestones(this.productMilestonesOrder()),
  );
  availableProductSections = computed(() =>
    this.getAvailableSections(this.productMilestonesOrder(), this.outstandingProductMilestones()),
  );

  creditMilestoneOrder = signal(this.createMilestoneStatusMap(FormInput));
  outstandingCreditMilestones = computed(() =>
    this.getOutstandingMilestones(this.creditMilestoneOrder()),
  );
  availableSections = computed(() =>
    this.getAvailableSections(this.creditMilestoneOrder(), this.outstandingCreditMilestones()),
  );

  sectionElements = new Map<
    string,
    { element: HTMLElement; subs: Subscription; hook?: () => void }
  >();

  override registerSection(
    section: Milestone,
    element: HTMLElement,
    uuid: string,
    hook?: () => void,
  ): void {
    const elementObj = { element, subs: new Subscription(), hook };
    this.sectionElements.set(section + uuid, elementObj);

    if (this.isCreditInputSection(section)) {
      this.creditMilestoneOrder.update((prev) => this.addSectionStatus(prev, section, uuid));
    } else {
      this.productMilestonesOrder.update((prev) => this.addSectionStatus(prev, section, uuid));
    }

    if (this.milestoneOutstandingObservables.has(section)) {
      elementObj.subs.add(
        this.milestoneOutstandingObservables.get(section)!.subscribe((isOutstanding) => {
          this.updateIdealOrder(section, isOutstanding, uuid);
        }),
      );
    }
  }

  updateAvailableSections(
    isValid: boolean,
    shouldRegister: boolean,
    isDisabled: boolean,
    inputSection: Milestone,
    uuid: string,
  ) {
    let isOutstanding;

    if (this.isCreditInputSection(inputSection)) {
      isOutstanding = this.creditMilestoneOrder()
        .get(inputSection)
        ?.find((val) => val.uuid === uuid)?.isOutstanding;
    } else {
      isOutstanding = this.productMilestonesOrder()
        .get(inputSection)
        ?.find((val) => val.uuid === uuid)?.isOutstanding;
    }

    if (!isValid && !isOutstanding && shouldRegister && !isDisabled) {
      this.updateIdealOrder(inputSection, true, uuid);
    } else if ((isValid && isOutstanding) || isDisabled) {
      this.updateIdealOrder(inputSection, false, uuid);
    }
  }

  cleanupSections(inputSection: Milestone, elementRef: ElementRef, uuid: string) {
    if (this.sectionElements.has(inputSection + uuid)) {
      this.deregisterSection(elementRef.nativeElement);
    }

    if (this.isCreditInputSection(inputSection)) {
      this.creditMilestoneOrder.update((prev) =>
        this.removeSectionStatus(prev, inputSection, uuid),
      );
    } else {
      this.productMilestonesOrder.update((prev) =>
        this.removeSectionStatus(prev, inputSection, uuid),
      );
    }
  }

  private updateIdealOrder(sectionToUpdate: Milestone, isOutstanding: boolean, uuid: string) {
    if (this.isCreditInputSection(sectionToUpdate)) {
      this.creditMilestoneOrder.update((prev) =>
        this.updateSectionStatus(prev, sectionToUpdate, uuid, isOutstanding),
      );
    } else {
      this.productMilestonesOrder.update((prev) =>
        this.updateSectionStatus(prev, sectionToUpdate, uuid, isOutstanding),
      );
    }
  }

  private isCreditInputSection(value: Milestone): value is FormInput {
    return Object.values(FormInput).includes(value as FormInput);
  }

  private isEmploymentIncome(income: Income): income is EmploymentIncome {
    return (income as EmploymentIncome).employment !== undefined;
  }

  private addSectionStatus<T extends Milestone>(
    prev: MilestoneStatusMap<T>,
    section: T,
    uuid: string,
    isOutstanding = true,
  ): MilestoneStatusMap<T> {
    const next = new Map(prev);
    const value = next.get(section) ?? [];
    next.set(section, [...value, { uuid, isOutstanding }]);
    return next;
  }

  private removeSectionStatus<T extends Milestone>(
    prev: MilestoneStatusMap<T>,
    section: T,
    uuid: string,
  ): MilestoneStatusMap<T> {
    const next = new Map(prev);
    const value = next.get(section) ?? [];
    next.set(
      section,
      value.filter((val) => val.uuid !== uuid),
    );
    return next;
  }

  private updateSectionStatus<T extends Milestone>(
    prev: MilestoneStatusMap<T>,
    section: T,
    uuid: string,
    isOutstanding: boolean,
  ): MilestoneStatusMap<T> {
    const next = new Map(prev);

    const value = next.get(section) ?? [];
    const updatedSection = value.map((current) =>
      current.uuid === uuid ? { uuid, isOutstanding } : current,
    );
    next.set(section, updatedSection);

    return next;
  }

  private createMilestoneStatusMap(sectionEnum: typeof FormInput): MilestoneStatusMap<FormInput>;
  private createMilestoneStatusMap(
    sectionEnum: typeof ProductMilestoneFormInput,
  ): MilestoneStatusMap<ProductMilestoneFormInput>;
  private createMilestoneStatusMap(
    sectionEnum: typeof FormInput | typeof ProductMilestoneFormInput,
  ): MilestoneStatusMap<FormInput | ProductMilestoneFormInput> {
    return Object.values(sectionEnum).reduce((acc, formInput) => {
      acc.set(formInput, []);
      return acc;
    }, new Map<FormInput, { uuid: string; isOutstanding: boolean }[]>());
  }

  private getOutstandingMilestones<T extends Milestone>(milestoneMap: MilestoneStatusMap<T>): T[] {
    return Array.from(milestoneMap.keys()).filter((milestone) =>
      milestoneMap.get(milestone)?.some((value) => value.isOutstanding),
    );
  }

  private getAvailableSections<T extends Milestone>(
    milestoneMap: MilestoneStatusMap<T>,
    outstandingMilestones: T[],
  ): string[] {
    return outstandingMilestones.flatMap(
      (milestone) =>
        milestoneMap
          .get(milestone)
          ?.filter((v) => v.isOutstanding)
          .map((v) => milestone + v.uuid) ?? [],
    );
  }

  private isSubjectAssociationOutstanding(
    isRefi: boolean,
    ownedProperties: OwnedProperty[],
    liabilities: Liability[],
  ) {
    if (!isRefi) {
      return false;
    }

    const subjectOwnedProp = ownedProperties.find(
      (ownedProperty) => ownedProperty.isSubjectProperty,
    );
    if (!subjectOwnedProp || subjectOwnedProp.isOwnedFreeAndClear) {
      return false;
    }

    const hasAssociatedLiability = liabilities.some(
      (liability) =>
        (liability as LiabilitySecuredAgainstProperty).ownedPropertyId === subjectOwnedProp.id,
    );
    return !hasAssociatedLiability;
  }

  private isIncomeOutstanding(incomes: Income[]): boolean {
    const hasIncome = incomes.length > 0;
    const hasMissingStartDate = incomes.some((income) => {
      if (this.isEmploymentIncome(income)) {
        return (
          !income?.employment?.employmentStartDate ||
          income.employment.employmentStartDate.trim() === ''
        );
      }
      return false;
    });
    return !hasIncome || hasMissingStartDate;
  }
}
