import { formatCurrency } from '@angular/common';
import { Injectable, Signal, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { AssetType, PurchaseLoan, RefinanceLoan } from '@rocket-logic/rl-xp-bff-models';
import { map } from 'rxjs';
import { formatAddress } from '../../util/format-address';
import { pascalCaseSplit } from '../../util/formatting-helpers';
import { getClientName } from '../../util/get-client-name';
import { CreditService } from '../credit/credit.service';
import { AssetStateService } from '../entity-state/asset-state/asset-state.service';
import { ClientStateService } from '../entity-state/client-state/client-state.service';
import { IncomeStateService } from '../entity-state/income-state/income-state.service';
import { LoanStateService } from '../entity-state/loan-state/loan-state.service';
import { SubjectPropertyStateService } from '../entity-state/subject-property-state/subject-property-state.service';
import { LoanIdService } from '../loan-id/loan-id.service';
import { FormSection } from './form-nav-section.service';

@Injectable()
export class FormNavContentService {
  loanIdService = inject(LoanIdService);
  clientStateService = inject(ClientStateService);
  loanStateService = inject(LoanStateService);
  subjectPropertyStateService = inject(SubjectPropertyStateService);
  creditService = inject(CreditService);
  incomeStateService = inject(IncomeStateService);
  assetStateService = inject(AssetStateService);

  loanId = toSignal(this.loanIdService.loanId$);
  creditReports = toSignal(
    this.creditService.creditReports$.pipe(map((state) => state.data ?? [])),
    { initialValue: [] },
  );

  sectionContent = new Map<FormSection, Signal<string[]>>(
    (
      [
        [FormSection.LoanInfo, () => this.getLoanInfoContent()],
        [FormSection.ClientInfo, () => this.getClientInfoContent()],
        [FormSection.PurchaseInfo, () => this.getPurchaseInfoContent()],
        [FormSection.RefinanceInfo, () => this.getRefinanceInfoContent()],
        [FormSection.SubjectProperty, () => this.getSubjectPropertyContent()],
        [FormSection.Credit, () => this.getCreditContent()],
        [FormSection.Income, () => this.getIncomeContent()],
        [FormSection.Assets, () => this.getAssetsContent()],
        [FormSection.REO, () => this.getREOContent()],
      ] as [FormSection, () => (string | undefined | null)[]][]
    ).map(([key, contentFn]) => [
      key,
      computed(() => contentFn().filter((item): item is string => !!item)),
    ]),
  );

  private getLoanInfoContent(): (string | null | undefined)[] {
    return [this.loanId()];
  }

  private getClientInfoContent(): (string | null | undefined)[] {
    return [
      ...(this.clientStateService.stateValues() ?? []).map((client) => getClientName(client)),
    ];
  }

  private getPurchaseInfoContent(): (string | null | undefined)[] {
    return [
      (this.loanStateService.state()?.data as PurchaseLoan | undefined)?.purchasePrice
        ? formatCurrency(
            (this.loanStateService.state()!.data as PurchaseLoan).purchasePrice!,
            'en-US',
            '$',
          )
        : null,
    ];
  }

  private getRefinanceInfoContent(): (string | null | undefined)[] {
    const refiGoals = (this.loanStateService.state()?.data as RefinanceLoan | undefined)
      ?.refinanceGoals;
    const formattedGoals = refiGoals ? refiGoals.map((goal) => pascalCaseSplit(goal)) : null;
    return [formattedGoals ? formattedGoals.join(', ') : null];
  }

  private getSubjectPropertyContent(): (string | null | undefined)[] {
    return [
      this.subjectPropertyStateService.state()?.data?.address
        ? formatAddress(this.subjectPropertyStateService.state()!.data!.address!)
        : null,
    ];
  }

  private getCreditContent(): (string | null | undefined)[] {
    return [
      ...this.creditReports()
        .filter((report) => report.isActive && report.qualifyingScore)
        .map((report) => `FICO ${report.qualifyingScore}`),
    ];
  }

  private getAssetsContent(): (string | null | undefined)[] {
    return [
      formatCurrency(
        this.assetStateService
          .stateValues()
          ?.filter((asset) => asset?.assetType !== AssetType.EarnestMoneyDeposit)
          .reduce((total, asset) => total + (asset?.assetValue ?? 0), 0) ?? 0,
        'en-US',
        '$',
      ),
    ];
  }

  private getIncomeContent(): (string | null | undefined)[] {
    return [
      formatCurrency(
        Object.values(this.incomeStateService.state()?.data ?? {}).reduce(
          (total, incomeMap) =>
            total +
            Array.from(incomeMap.values()).reduce(
              (subtotal, income) => subtotal + (income?.totalMonthlyIncomeAmount ?? 0),
              0,
            ),
          0,
        ),
        'en-US',
        '$',
      ),
    ];
  }

  private getREOContent(): (string | null | undefined)[] {
    return [''];
  }
}
