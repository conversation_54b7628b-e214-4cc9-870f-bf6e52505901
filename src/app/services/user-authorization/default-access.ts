import { UserAccessControlList } from '@rocket-logic/rl-xp-bff-models/dist/authorization';

export enum LoanAccessId {
  IsBankerLicensed = 'IsBankerLicensed',
  RlXpDenyWithdrawVerifier = "RlXpDenyWithdrawVerifier",
  LoanIsArchived = "LoanIsArchived",
}

export enum LoanAccessVerifierReasons {
  FolderReceived = "FolderReceived"
}

export function createDefaultAccessControl(controlName: string): UserAccessControlList {
  return {
    [controlName]: {
      read: true,
      write: true,
    },
  };
}

export const DEFAULT_ACCESS: UserAccessControlList = Object.values(LoanAccessId).reduce(
  (acc, id) => {
    return { ...acc, ...createDefaultAccessControl(id) };
  },
  {},
);
