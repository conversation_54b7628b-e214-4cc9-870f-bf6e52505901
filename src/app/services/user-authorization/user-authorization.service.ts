import { Injectable, computed, inject } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import {
  EMPTY,
  catchError,
  combineLatest,
  debounceTime,
  distinctUntilChanged,
  filter,
  map,
  of,
  shareReplay,
  skip,
  switchMap,
  withLatestFrom,
} from 'rxjs';
import { AuthorizationService } from '../authorization/authorization.service';
import { LoanAuthorizationService } from '../authorization/loan-authorization.service';
import { DataProviderService } from '../data-provider/data-provider.service';
import { ClientStateService } from '../entity-state/client-state/client-state.service';
import { LoanStateService } from '../entity-state/loan-state/loan-state.service';
import { SubjectPropertyStateService } from '../entity-state/subject-property-state/subject-property-state.service';
import { LoanIdService } from '../loan-id/loan-id.service';
import { DEFAULT_ACCESS, LoanAccessId } from './default-access';

@Injectable()
export class UserAuthorizationService {
  private authorizationService = inject(AuthorizationService);
  private dataProviderService = inject(DataProviderService);
  private loanStateService = inject(LoanStateService);
  private subjectPropertyStateService = inject(SubjectPropertyStateService);
  private clientStateService = inject(ClientStateService);
  private loanAuthorizationService = inject(LoanAuthorizationService);
  private loanId$ = inject(LoanIdService).loanId$;
  private logger = inject(SplunkLoggerService);

  hasSubjectPropertyStateChanged$ = this.subjectPropertyStateService.state$.pipe(
    map((state) => state.data),
    distinctUntilChanged((prev, next) => {
      return prev?.address?.state === next?.address?.state;
    }),
  );

  hasClientAddressStateChanged$ = this.clientStateService.state$.pipe(
    map((state) => state.data),
    distinctUntilChanged((prev, next) => {
      return (
        Array.from(next?.values() ?? [])?.some((client) => {
          const prevClient = Array.from(prev?.values() ?? [])?.find(
            (other) => other.id === client.id,
          );
          if (!prevClient) {
            return false;
          }

          return (
            prevClient.residenceInformation?.currentResidence?.address?.state ===
            client.residenceInformation?.currentResidence?.address?.state
          );
        }) ?? false
      );
    }),
  );

  hasLoanPurposeChanged$ = this.loanStateService.state$.pipe(
    map((state) => state.data),
    distinctUntilChanged((prev, next) => {
      return prev?.loanPurpose === next?.loanPurpose;
    }),
  );

  hasClientGcidChanged$ = this.clientStateService.state$.pipe(
    map((state) => state.data),
    map((clients) => Array.from(clients?.values() ?? [])?.map((client) => client.gcid) ?? []),
    distinctUntilChanged((prev, next) => {
      return next.every((gcid) => prev.includes(gcid));
    }),
  );

  userScopes$ = this.dataProviderService.getUserInfo$().pipe(shareReplay(1));

  public loanAccess$ = combineLatest([
    this.loanId$,
    this.hasSubjectPropertyStateChanged$,
    this.hasClientAddressStateChanged$,
  ]).pipe(
    debounceTime(1000), // TODO: Fix dependent observables to not trigger multiple invocations on load
    filter(([loanId]) => !!loanId),
    switchMap(([loanId]) => {
      if (!loanId) {
        return EMPTY;
      }
      return this.authorizationService
        .getAuthorization(loanId, [
          LoanAccessId.IsBankerLicensed,
          LoanAccessId.RlXpDenyWithdrawVerifier,
          LoanAccessId.LoanIsArchived,
        ])
        .pipe(
          map(({ authorization }) => {
            const appAuth = authorization.applicationAuth;

            const RlXpDenyWithdrawVerifier =
              appAuth[LoanAccessId.RlXpDenyWithdrawVerifier] ??
              DEFAULT_ACCESS[LoanAccessId.RlXpDenyWithdrawVerifier];

            const IsBankerLicensed =
              appAuth[LoanAccessId.IsBankerLicensed] ??
              DEFAULT_ACCESS[LoanAccessId.IsBankerLicensed];

            const loanIsArchived =
              appAuth[LoanAccessId.LoanIsArchived] ?? DEFAULT_ACCESS[LoanAccessId.LoanIsArchived];

            const updatedLoanAccess = {
              IsBankerLicensed,
              RlXpDenyWithdrawVerifier,
              loanIsArchived,
            };
            return updatedLoanAccess;
          }),
        );
    }),
    catchError((err) => {
      this.logger.error(`Failed to fetch loan access`, err);
      return of(DEFAULT_ACCESS);
    }),
  );

  public isBankerLicensed$ = this.loanAccess$.pipe(
    map((loanAccess) => loanAccess.IsBankerLicensed),
    shareReplay(1),
  );

  public isDenyWithdrawAllowed$ = this.loanAccess$.pipe(
    map((loanAccess) => loanAccess.RlXpDenyWithdrawVerifier?.exclusion?.reason),
    shareReplay(1),
  );

  public isLoanArchived$ = this.loanAccess$.pipe(
    map((loanAccess) => {
      return !!loanAccess.loanIsArchived?.exclusion?.reason;
    }),
    shareReplay(1),
  );

  public conflictingLoans$ = combineLatest([
    this.loanId$,
    this.hasLoanPurposeChanged$,
    this.hasSubjectPropertyStateChanged$,
    this.hasClientGcidChanged$,
  ]).pipe(
    debounceTime(1000), // TODO: Fix dependent observables to not trigger multiple invocations on load
    filter(([loanId]) => !!loanId && !loanId.startsWith('999')),
    switchMap(([loanId]) => {
      if (!loanId) {
        return EMPTY;
      }
      return this.dataProviderService.fetchConflictingLoans$(loanId).pipe(
        map((response) => {
          return response.conflictingLoans;
        }),
      );
    }),
    shareReplay(1),
  );

  conflictingLoans = toSignal(this.conflictingLoans$, {
    initialValue: [],
  });

  hasConflictingLoans = computed(() => this.conflictingLoans()?.length > 0);

  constructor() {
    this.clientStateService.state$
      .pipe(
        map((state) => state?.data),
        filter((state) => state != null),
        skip(1),
        filter((next) =>
          Array.from(next?.values() ?? []).some((client) => client.personalInformation?.ssn),
        ),
        distinctUntilChanged((prev, next) => {
          const prevClients = Array.from(prev?.values() ?? []);
          const nextClients = Array.from(next?.values() ?? []);

          return !nextClients.some((client) => {
            const prevClient = prevClients.find((other) => other.id === client.id);

            if (!prevClient) {
              // If the client is new but has no SSN, do not treat it as a change
              return !!client.personalInformation?.ssn;
            }

            const prevSsn = prevClient.personalInformation?.ssn;
            const currentSsn = client.personalInformation?.ssn;

            return prevSsn !== currentSsn;
          });
        }),
        withLatestFrom(this.loanId$.pipe(filter((loanId): loanId is string => !!loanId))),
        switchMap(([_, loanId]) =>
          this.loanAuthorizationService.checkAuthorization(loanId, ['rl-xp']),
        ),
        takeUntilDestroyed(),
      )
      .subscribe();
  }
}
