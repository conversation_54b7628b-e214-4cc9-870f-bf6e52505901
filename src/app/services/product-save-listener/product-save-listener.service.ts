import { inject, Injectable } from '@angular/core';
import { catchError, of, switchMap, withLatestFrom } from 'rxjs';
import { AbstractEventService } from '../abstract-event.service';
import { DataProviderService } from '../data-provider/data-provider.service';
import { LoanIdService } from '../loan-id/loan-id.service';

@Injectable()
export class ProductSaveListenerService extends AbstractEventService {
  override EVENT_TYPE = 'origination-processing.loan.product-save';
  private dataProviderService = inject(DataProviderService);
  private loanId$ = inject(LoanIdService).nonNullableLoanId$;

  qfrs$ = this.eventStream$.pipe(
    withLatestFrom(this.loanId$),
    switchMap(([_, loanId]) => this.dataProviderService.getQfrs(loanId)),
    catchError(() => of(null)),
  );
}
