import { inject, Injectable, signal } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import {
  catchError,
  distinctUntilChanged,
  filter,
  forkJoin,
  map,
  of,
  shareReplay,
  switchMap,
} from 'rxjs';
import { getClientName } from '../../util/get-client-name';
import { loadable } from '../../util/loadable';
import { DataProviderService } from '../data-provider/data-provider.service';

@Injectable({
  providedIn: 'root',
})
export class RecentLoansService {
  readonly RECENT_LOANS_KEY = 'RECENT_LOANS';
  readonly MAX_RECENT_LOANS = 6;
  dataProviderService = inject(DataProviderService);
  recentLoans = signal<string[]>(JSON.parse(localStorage.getItem(this.RECENT_LOANS_KEY) ?? '[]'));
  recentLoansWithClients$ = toObservable(this.recentLoans).pipe(
    distinctUntilChanged((prev, curr) => prev.filter((x) => !curr.includes(x)).length === 0),
    filter((loans) => loans.length > 0),
    switchMap((loans) =>
      forkJoin(
        loans.map((loan) =>
          this.dataProviderService.getClients$(loan).pipe(
            map((clients) => {
              return {
                loan,
                client: getClientName(clients.find((client) => client.isPrimaryBorrower)),
              };
            }),
            catchError(() => of({ loan: loan, client: '' })),
          ),
        ),
      ).pipe(loadable()),
    ),
    shareReplay(1),
  );

  /**
   * Handles storing a recent loan number in local storage.
   * @param {string} loanNumber - The loan number to store.
   * @returns {string[]} - The updated list of recent loans.
   */
  handleStoreRecentLoan(loanNumber: string): string[] {
    let recentLoans: string[] = JSON.parse(localStorage.getItem(this.RECENT_LOANS_KEY) ?? '[]');
    recentLoans = recentLoans.filter((loan) => loan !== loanNumber);
    recentLoans.unshift(loanNumber);
    recentLoans = recentLoans.slice(0, this.MAX_RECENT_LOANS).filter((value) => value);
    this.recentLoans.set(recentLoans);
    localStorage.setItem(this.RECENT_LOANS_KEY, JSON.stringify(recentLoans));
    return recentLoans;
  }
}
