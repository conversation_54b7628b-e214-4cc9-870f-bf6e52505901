import { HttpErrorResponse } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Lead, LoanStatus } from '@rocket-logic/rl-xp-bff-models';
import {
  Observable,
  Subject,
  catchError,
  defer,
  filter,
  map,
  mergeWith,
  of,
  retry,
  shareReplay,
  startWith,
  switchMap,
  throwError,
  timeout,
  timer,
} from 'rxjs';
import { DataProviderService } from '../data-provider/data-provider.service';
import { LoanIdService } from '../loan-id/loan-id.service';

export type LeadInitResult = {
  initializing: boolean;
  error?: LeadInitError;
  lead?: Lead;
};

export enum LeadInitError {
  Failed = 'failed',
  RmaImportFailed = 'rma-import-failed',
  NotFound = 'lead-not-found',
  Unsupported = 'unsupported',
}

@Injectable()
export class LeadService {
  private dataProviderService = inject(DataProviderService);
  private loanIdService = inject(LoanIdService);
  static maxLoadingTime = 60000;

  private leadSubject = new Subject<Lead>();
  public set lead(lead: Lead) {
    this.leadSubject.next(lead);
  }

  leadInitializationResult$: Observable<LeadInitResult> = this.loanIdService.loanId$.pipe(
    filter((loanId): loanId is string => !!loanId),
    switchMap((loanId) => this.getLeadWithTimeout$(loanId)),
    startWith({ initializing: false }),
    shareReplay(1),
    takeUntilDestroyed(),
  );

  lead$ = this.leadInitializationResult$.pipe(
    map((result) => result.lead),
    mergeWith(this.leadSubject),
    filter((lead): lead is Lead => !!lead),
    shareReplay(1),
    takeUntilDestroyed(),
  );

  isLeadRlbCompleted$ = this.lead$.pipe(
    map((lead) => lead.rlbCompleted),
    shareReplay(1),
    takeUntilDestroyed(),
  );

  hasSchwabConsent$ = this.lead$.pipe(
    map((lead) => lead?.consentToObtainSchwabInfo ?? false),
    shareReplay(1),
    takeUntilDestroyed(),
  );

  isRelocationLead$ = this.lead$.pipe(
    map((lead) => lead?.isRelocationLead ?? false),
    shareReplay(1),
    takeUntilDestroyed(),
  );

  hasInternationalCredit$ = this.lead$.pipe(
    map((lead) => lead?.hasInternationalCreditRelo),
    shareReplay(1),
    takeUntilDestroyed(),
  );

  private getLeadWithTimeout$(loanId: string): Observable<LeadInitResult> {
    return this.dataProviderService.getLead$(loanId).pipe(
      switchMap((lead) => {
        if (lead === undefined || lead.loanStatus === LoanStatus.ApplicationInitializationPending) {
          return timer(1000).pipe(
            switchMap(() => {
              // Wrap in a defer so that the whole observable chain is retried.
              return defer(() =>
                this.dataProviderService.getLead$(loanId).pipe(
                  map((lead) => {
                    if (lead.loanStatus === LoanStatus.ApplicationInitializationPending) {
                      throw new Error('Lead is not ready yet');
                    }
                    return { initializing: false, lead, error: undefined };
                  }),
                  catchError((error) =>
                    error instanceof HttpErrorResponse && error.status === 404
                      ? of({ initializing: false, error: LeadInitError.NotFound })
                      : throwError(() => error),
                  ),
                ),
              ).pipe(
                retry({ delay: (_, retryCount) => timer(1000 * retryCount) }),
                timeout(LeadService.maxLoadingTime),
                catchError(() => of({ initializing: false, error: LeadInitError.Failed })),
                startWith({ initializing: true }),
              );
            }),
          );
        }

        if (lead.loanStatus === LoanStatus.ApplicationInitializationFailed) {
          return of({ initializing: false, error: LeadInitError.RmaImportFailed });
        }

        if (lead.isUnsupportedLeadType) {
          return of({ initializing: false, lead, error: LeadInitError.Unsupported });
        }

        return of({ initializing: false, lead, error: undefined });
      }),
      catchError((error) =>
        error instanceof HttpErrorResponse && error.status === 404
          ? of({ initializing: false, error: LeadInitError.NotFound })
          : of({ initializing: false, error: LeadInitError.Failed }),
      ),
    );
  }
}
