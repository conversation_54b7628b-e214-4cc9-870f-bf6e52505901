import { Injectable, inject } from '@angular/core';
import { Address, AddressValidationStatus } from '@rocket-logic/rl-xp-bff-models';
import { BehaviorSubject, Observable, of, tap } from 'rxjs';
import { DataProviderService } from '../data-provider/data-provider.service';

export interface County {
  fipsCode: string;
  name: string;
}
@Injectable()
export class AddressDataService {
  dataProviderService = inject(DataProviderService);

  private addressValidationStatusSubject = new BehaviorSubject<AddressValidationStatus | null>(
    null,
  );
  addressValidationStatus$ = this.addressValidationStatusSubject.asObservable();

  flushLesStatus() {
    this.addressValidationStatusSubject.next(null);
  }

  getZipCodes$(state?: string, fipsCode?: string, city?: string): Observable<string[]> {
    if (!state && !fipsCode && !city) {
      return of([]);
    }
    state = state ?? '';
    fipsCode = fipsCode ?? '';
    city = city ?? '';

    return this.dataProviderService.getZipCodes$(state, fipsCode, city);
  }

  getCounties$(state?: string, city?: string, zipCode?: string): Observable<County[]> {
    if (!state && !city && !zipCode) {
      return of([]);
    }
    state = state ?? '';
    city = city ?? '';
    zipCode = zipCode ?? '';
    return this.dataProviderService.getCounties$(state, city, zipCode);
  }

  getCities$(state?: string, fipsCode?: string, zipCode?: string): Observable<string[]> {
    if (!state && !zipCode) {
      return of([]);
    }

    state = state ?? '';
    fipsCode = fipsCode ?? '';
    zipCode = zipCode ?? '';
    return this.dataProviderService.getCities$(state, fipsCode, zipCode);
  }

  getStates$(zipCode: string, fipsCode?: string): Observable<string[]> {
    return this.dataProviderService.getStates$(zipCode, fipsCode);
  }

  getAddressValidation$(address: Address) {
    if (!Object.values(address).some((value) => !!value)) {
      return of({
        originalAddress: address,
        responseAddress: address,
        status: AddressValidationStatus.Approved,
      });
    }

    return this.dataProviderService
      .getAddressValidation$(address)
      .pipe(tap((response) => this.addressValidationStatusSubject.next(response.status)));
  }
}
