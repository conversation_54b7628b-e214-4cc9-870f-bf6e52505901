import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import {
  distinctUntilChanged,
  filter,
  fromEvent,
  map,
  merge,
  of,
  shareReplay,
  tap,
  withLatestFrom,
} from 'rxjs';
import { ActiveActionService } from '../active-action.service';
import {
  ActiveSidenavScreenService,
  SidenavScreen,
} from '../active-sidenav-screen/active-sidenav-screen.service';
import { CreditAuthorizationService } from '../credit/credit-authorization.service';
import { LoanEditingState } from '../entity-state/loan-state/loan-editing-state.service';
import { MessageCenterService } from '../message-center/message-center.service';

enum KeyAction {
  Milestone = 'm',
  Credit = 'c',
  Escape = 'Escape',
}

@Injectable()
export class KeyPressService {
  private activeActionService = inject(ActiveActionService);
  private activeSidenavService = inject(ActiveSidenavScreenService);
  private creditAuthService = inject(CreditAuthorizationService);
  private isLoanEditingDisabled = inject(LoanEditingState).isLoanEditingDisabled;
  private messageCenterService = inject(MessageCenterService);

  private keyActions = new Map<KeyAction, () => void>([
    [KeyAction.Milestone, () => this.milestoneAction()],
    [KeyAction.Credit, () => this.creditAction()],
  ]);

  private readonly insertElements = ['INPUT', 'TEXTAREA', 'MAT-SELECT'];

  private isFocusedOnTextBox$ = merge(
    of(false),
    fromEvent<FocusEvent>(document, 'focusin').pipe(
      filter((e) => this.insertElements.includes((e.target as HTMLElement).nodeName)),
      map(() => true),
    ),
    fromEvent<FocusEvent>(document, 'focusout').pipe(
      filter((e) => this.insertElements.includes((e.target as HTMLElement).nodeName)),
      map(() => false),
    ),
  ).pipe(distinctUntilChanged(), shareReplay(1));

  constructor() {
    fromEvent<KeyboardEvent>(document, 'keyup')
      .pipe(
        filter(() => !this.isLoanEditingDisabled()),
        map((event) => this.toKeyAction(event.key)),
        filter((key): key is KeyAction => !!key),
        withLatestFrom(this.isFocusedOnTextBox$),
        tap(([key, hasFocus]) =>
          // Remove focus from active element on 'Escape'
          hasFocus && key === KeyAction.Escape ? (document.activeElement as any).blur() : null,
        ),
        filter(([_, hasFocus]) => !hasFocus),
        map(([key, _]) => key),
        takeUntilDestroyed(),
      )
      .subscribe((key) => {
        const action = this.keyActions.get(key);
        if (action) {
          action();
        }
      });
  }

  private milestoneAction() {
    this.activeActionService.navigateActionInputs();
  }

  private creditAction() {
    if (
      this.creditAuthService.canViewCreditRequestManager() &&
      !this.messageCenterService.shouldDisableActions()
    ) {
      this.activeSidenavService.activate(SidenavScreen.CreditReportManager);
    }
  }

  private toKeyAction(key: string): KeyAction | null {
    if (Object.values(KeyAction).includes(key as KeyAction)) {
      return key as KeyAction;
    }

    return null;
  }
}
