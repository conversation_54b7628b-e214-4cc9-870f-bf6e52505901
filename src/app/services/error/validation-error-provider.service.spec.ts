import { TestBed } from '@angular/core/testing';

import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'ng-mocks';
import { UPDATE_HANDLERS } from '../entity-state/abstract-update-handler.service';
import { ValidationErrorProviderService } from './validation-error-provider.service';

describe('ValidationErrorProviderService', () => {
  let service: ValidationErrorProviderService;

  beforeEach(() =>
    MockBuilder(ValidationErrorProviderService).provide({ provide: UPDATE_HANDLERS, useValue: [] }),
  );
  beforeEach(() => {
    service = TestBed.inject(ValidationErrorProviderService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
