import { Injectable, inject } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { ValidationErrors } from '@angular/forms';
import { combineLatest, map } from 'rxjs';
import { getErrorMessage } from '../../util/get-error-message';
import { EntityStateName } from '../entity-state/abstract-entity-state.service';
import {
  UPDATE_HANDLERS,
  UpdateHandlerService,
} from '../entity-state/abstract-update-handler.service';

interface ValidationErrorBase {
  onDismiss: () => void;
  stateName: EntityStateName;
}

interface ValidationError extends ValidationErrorBase {
  errors: { path: string; message: string }[];
}

interface ValidationErrorDto extends ValidationErrorBase {
  path: string;
  message: string;
}

@Injectable()
export class ValidationErrorProviderService {
  private updateHandlers = inject(UPDATE_HANDLERS);

  readonly validationErrors$ = combineLatest(
    this.updateHandlers.map((handler) =>
      handler.validationErrors$.pipe(map((errors) => this.transformHandlerErrors(handler, errors))),
    ),
  ).pipe(
    map((errors) => errors.filter((error): error is ValidationError => error != null)),
    map((validationErrors) => this.toValidationDtoErrors(validationErrors)),
    takeUntilDestroyed(),
  );

  readonly errors = toSignal(this.validationErrors$, { initialValue: [] });

  private transformHandlerErrors(
    handler: UpdateHandlerService,
    errors: { [path: string]: ValidationErrors }[],
  ): ValidationError | null {
    if (!errors.length) return null;

    return {
      stateName: handler.name,
      onDismiss: handler.dismissValidationErrors.bind(handler),
      errors: errors.reduce(
        (acc, errorGroup) => {
          acc.push(
            ...Object.entries(errorGroup).map(([path, errors]) => ({
              path,
              message: getErrorMessage(errors, [], undefined, 'This field'),
            })),
          );
          return acc;
        },
        [] as { path: string; message: string }[],
      ),
    };
  }

  private toValidationDtoErrors(validationErrors: ValidationError[]): ValidationErrorDto[] {
    return validationErrors.flatMap((parentError) =>
      parentError.errors.map((innerError) => ({
        ...innerError,
        stateName: parentError.stateName,
        onDismiss: parentError.onDismiss,
      })),
    );
  }
}
