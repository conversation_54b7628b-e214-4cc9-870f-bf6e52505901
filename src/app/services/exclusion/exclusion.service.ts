import { Injectable, inject } from '@angular/core';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { ExclusionReason } from '@rocket-logic/rl-xp-bff-models/dist/authorization';
import { EMPTY, map, switchMap, take, tap } from 'rxjs';
import { environment } from '../../../environments/environment';
import { AuthorizationService } from '../authorization/authorization.service';
import { LoanIdService } from '../loan-id/loan-id.service';

@Injectable()
export class ExclusionService {
  loanIdService = inject(LoanIdService);
  authz = inject(AuthorizationService);
  logger = inject(SplunkLoggerService);

  private readonly RL_XP_APP_ID = 'rl-xp';

  createLoanPurposeExclusion$() {
    return this.handleExclusion$(
      this.RL_XP_APP_ID,
      ExclusionReason.UnsupportedLoanPurpose,
      'Loan purpose set to refinance from RL-XP UI',
      true,
    );
  }

  handleExclusion$(appId: string, reason: ExclusionReason, comment?: string, redirect = false) {
    return this.loanIdService.loanId$.pipe(
      take(1),
      switchMap((loanId) => {
        if (!loanId) {
          this.logger.error(
            `Trying to create exclusion for app id ${appId} with reason ${reason} with no loan id`,
          );
          return EMPTY;
        }

        return this.authz
          .createExclusion(loanId, {
            appId,
            reason,
            comment,
          })
          .pipe(map(() => loanId));
      }),
      tap((loanId) => {
        if (redirect) {
          window.location.href = `${environment.legacyBankingUrl}/loan/${loanId}`;
        }
      }),
    );
  }
}
