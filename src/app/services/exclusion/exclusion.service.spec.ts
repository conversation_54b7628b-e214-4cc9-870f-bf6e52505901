import { TestBed } from '@angular/core/testing';

import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { MockBuilder } from 'ng-mocks';
import { AuthorizationService } from '../authorization/authorization.service';
import { LoanIdService } from '../loan-id/loan-id.service';
import { ExclusionService } from './exclusion.service';

describe('ExclusionService', () => {
  let service: ExclusionService;

  beforeEach(() =>
    MockBuilder(ExclusionService)
      .mock(LoanIdService)
      .mock(AuthorizationService)
      .mock(SplunkLoggerService),
  );
  beforeEach(() => {
    service = TestBed.inject(ExclusionService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
