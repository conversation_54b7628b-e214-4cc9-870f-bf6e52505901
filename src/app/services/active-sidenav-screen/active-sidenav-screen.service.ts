import { Injectable, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';
import { take } from 'rxjs';

export enum SidenavScreen {
  CreditReportManager = 'CreditReportManager',
  RocketLogicAssistant = 'RocketLogicAssistant',
  Support = 'Support',
  PreFill = 'PreFill',
  MessageCenter = 'MessageCenter',
  LoanDetails = 'LoanDetails',
}

export function isSidenavScreen(value: string): value is SidenavScreen {
  return Object.values(SidenavScreen).includes(value as SidenavScreen);
}

@Injectable()
export class ActiveSidenavScreenService {
  private activatedRoute = inject(ActivatedRoute);

  activeScreen = signal<SidenavScreen | null>(null);

  constructor() {
    this.activatedRoute.queryParams.pipe(take(1), takeUntilDestroyed()).subscribe((params) => {
      const sidenav = params['sidenav'];
      if (sidenav && isSidenavScreen(sidenav)) {
        this.activate(sidenav);
      }
    });
  }

  activate(screen: SidenavScreen) {
    this.activeScreen.set(screen);
  }

  toggle(screen: SidenavScreen) {
    if (this.activeScreen() === screen) {
      this.activeScreen.set(null);
    } else {
      this.activate(screen);
    }
  }

  clear() {
    this.activeScreen.set(null);
  }
}
