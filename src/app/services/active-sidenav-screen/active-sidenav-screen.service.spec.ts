import { TestBed } from '@angular/core/testing';

import { ActivatedRoute } from '@angular/router';
import { MockBuilder, MockProvider } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { ActiveSidenavScreenService } from './active-sidenav-screen.service';

describe('ActiveSidenavScreenService', () => {
  let service: ActiveSidenavScreenService;

  beforeEach(() => MockBuilder(ActiveSidenavScreenService));
  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        MockProvider(ActivatedRoute, { queryParams: NEVER }),
      ],
    });
    service = TestBed.inject(ActiveSidenavScreenService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
