import { environment } from '../../../environments/environment';

export enum TabRestrictionConfigType {
  Default = 'Default',
  ChatBanker = 'ChatBanker',
}

export interface TabRestrictionConfig {
  appName: string;
  tabLimit: number;
  storageKey: string;
  ignorePaths?: string[];
  type: TabRestrictionConfigType;
}

export const defaultTabRestrictionConfig: TabRestrictionConfig = {
  tabLimit: environment.tabLimit,
  appName: environment.appName,
  storageKey: 'rl-tab-ids',
  ignorePaths: [],
  type: TabRestrictionConfigType.Default,
};

export const chatBankerTabRestrictionConfig: TabRestrictionConfig = {
  ...defaultTabRestrictionConfig,
  tabLimit: 3,
  type: TabRestrictionConfigType.ChatBanker,
};
