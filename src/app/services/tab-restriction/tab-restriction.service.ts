import { Inject, Injectable, OnDestroy } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';

import { map } from 'rxjs';
import { CloseTabDialogComponent } from '../../close-tab-dialog/close-tab-dialog.component';
import { UserAuthorizationService } from '../user-authorization/user-authorization.service';
import { TabRestrictionConfig, TabRestrictionConfigType } from './config';
import { TAB_RESTRICTION_CONFIG } from './token';

@Injectable()
export class TabRestrictionService implements OnDestroy {
  private config: TabRestrictionConfig | null | undefined = null;
  private openDialog: MatDialogRef<CloseTabDialogComponent> | null = null;
  private ignored = false;

  private get storageKey() {
    return this.config?.storageKey ?? '';
  }

  private get tabLimit() {
    return this.config?.tabLimit ?? 1;
  }

  private get tabs(): string[] {
    return JSON.parse(localStorage.getItem(this.storageKey) ?? '[]') || [];
  }
  private set tabs(tabs: string[]) {
    localStorage.setItem(this.storageKey, JSON.stringify(tabs));
  }

  private get sessionId() {
    return sessionStorage.getItem(this.storageKey) ?? '';
  }

  private set sessionId(id: string) {
    sessionStorage.setItem(this.storageKey, id);
  }

  constructor(
    @Inject(TAB_RESTRICTION_CONFIG) configs: TabRestrictionConfig[],
    private dialog: MatDialog,
    private userService: UserAuthorizationService,
  ) {
    this.config = configs.find((config) => config.type === TabRestrictionConfigType.Default);
    this.userService.userScopes$
      .pipe(map((user) => user['isChatBanker']))
      .subscribe((isChatBanker) => {
        if (isChatBanker) {
          this.config = configs.find(
            (config) => config.type === TabRestrictionConfigType.ChatBanker,
          );
          this.checkTabs();
        }
      });

    window.addEventListener('storage', () => {
      if (!this.ignored) {
        this.checkTabs();
      }
    });

    window.addEventListener('unload', () => {
      this.deleteTabId();
    });
  }

  ngOnDestroy() {
    this.deleteTabId();
  }

  public onNewTab(routePath?: string) {
    if (routePath && this.config?.ignorePaths && this.config.ignorePaths.includes(routePath)) {
      this.ignored = true;
      this.deleteTabId();
    } else if (this.tabLimit > 0 && !this.sessionId) {
      this.ignored = false;
      this.saveTabId();
    } else if (this.tabLimit > 0) {
      this.ignored = false;
      this.checkTabs();
    }
  }

  private checkTabs() {
    if (
      this.openDialog === null &&
      !this.tabs.includes(this.sessionId) &&
      this.tabs.length >= this.tabLimit
    ) {
      this.openDialog = this.dialog.open(CloseTabDialogComponent, {
        closeOnNavigation: false,
        data: this.config,
        disableClose: true,
      });
    } else if (!this.tabs.includes(this.sessionId) && this.tabs.length < this.tabLimit) {
      this.saveTabId();

      if (this.openDialog) {
        this.openDialog.close();
        this.openDialog = null;
      }
    }
  }

  private saveTabId() {
    const id = this.sessionId || Date.now().toString();
    const localTabs = this.tabs;
    localTabs.unshift(id);
    localTabs.splice(this.tabLimit);

    this.tabs = localTabs;
    this.sessionId = id;
  }

  private deleteTabId() {
    if (this.sessionId) {
      const localTabs = this.tabs.filter((id) => id !== this.sessionId);
      this.tabs = localTabs;
    }
  }
}
