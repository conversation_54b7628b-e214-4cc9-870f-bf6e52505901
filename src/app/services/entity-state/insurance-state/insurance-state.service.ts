import { Injectable } from '@angular/core';
import { PropertyInsurance } from '@rocket-logic/rl-xp-bff-models/dist/property-insurance/property-insurance';
import { map, Observable } from 'rxjs';
import { LoanDependentEntityStateService } from '../abstract-dependent-entity-state.service';
import { EntityStateName } from '../abstract-entity-state.service';

@Injectable()
export class InsuranceStateService extends LoanDependentEntityStateService<
  PropertyInsurance[],
  PropertyInsurance
> {
  override name = EntityStateName.Insurance;

  protected override getDependentEntityState$(loanId: string): Observable<PropertyInsurance[]> {
    return this.dataProvider.getPropertyInsurance$(loanId);
  }

  protected override updateEntityState$(
    loanId: string,
    state: PropertyInsurance,
  ): Observable<PropertyInsurance[]> {
    return this.dataProvider.updatePropertyInsurance$(loanId, state).pipe(
      map((updatedState) => {
        const newState = this.state()?.data ?? [];

        const mappedState = newState.map((insurance) =>
          insurance.policyType === state.policyType ? updatedState : insurance,
        );

        if (!mappedState.some((insurance) => insurance.policyType === updatedState.policyType)) {
          mappedState.push(updatedState);
        }

        return mappedState;
      }),
    );
  }
}
