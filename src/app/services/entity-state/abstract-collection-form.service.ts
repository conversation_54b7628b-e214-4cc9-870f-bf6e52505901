import { DestroyRef, inject, Injectable, Injector, Signal, WritableSignal } from '@angular/core';
import { AbstractControl, FormArray, FormBuilder, FormGroup } from '@angular/forms';

@Injectable()
export abstract class AbstractCollectionFormService<
  T extends { [K in keyof T]: AbstractControl<any, any> },
> {
  protected formBuilder = inject(FormBuilder);
  protected injector = inject(Injector);
  protected destroyRef = inject(DestroyRef);

  protected entityFormArray = new FormArray<FormGroup<T>>([]);
  abstract entityFormMap: WritableSignal<Map<string, FormGroup<T>>>;
  abstract entityValues: Signal<FormGroup[]>;
  entityValueChanges$ = this.entityFormArray.valueChanges;

  protected updateState(id: string, form: FormGroup) {
    const newState = this.shallowCopyState();
    newState.set(id, form);
    this.entityFormMap.set(newState);
  }

  protected deleteState(id: string) {
    const newState = this.shallowCopyState();
    newState.delete(id);
    this.entityFormMap.set(newState);
  }

  protected shallowCopyState() {
    return new Map<string, FormGroup>(this.entityFormMap());
  }
}
