import { Injectable } from '@angular/core';
import { FormArray, FormGroup, Validators } from '@angular/forms';

@Injectable()
export abstract class ValidationHandlerService {
  protected removeRequiredValidators(form: FormGroup | FormArray, paths: string[]) {
    paths.forEach((path) => {
      const control = form.get(path);
      if (control && control.hasValidator(Validators.required)) {
        control.removeValidators(Validators.required);
        control.updateValueAndValidity();
      }
    });
  }

  protected addRequiredValidators(form: FormGroup | FormArray, paths: string[]) {
    paths.forEach((path) => {
      const control = form.get(path);
      if (control && !control.hasValidator(Validators.required)) {
        control.addValidators(Validators.required);
        control.updateValueAndValidity();
      }
    });
  }
}
