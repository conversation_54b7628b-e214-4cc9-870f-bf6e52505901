import { DestroyRef, Injectable, InjectionToken, computed, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormGroup, ValidationErrors } from '@angular/forms';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import {
  BehaviorSubject,
  EMPTY,
  Observable,
  Subject,
  Subscription,
  catchError,
  concat,
  concatMap,
  defer,
  filter,
  finalize,
  map,
  switchMap,
  takeUntil,
  takeWhile,
  tap,
} from 'rxjs';
import { getDirtyPaths } from '../../util/get-dirty-paths';
import { getValidationErrors } from '../../util/get-validation-errors';
import { SaveTrigger, SaveTriggerType } from '../save-trigger/save-trigger';
import { EntityStateName } from './abstract-entity-state.service';
import { LoanEditingState } from './loan-state/loan-editing-state.service';
import { LoanStateService } from './loan-state/loan-state.service';

export type DirtyState = { dirtyPaths: string[]; form: FormGroup };
export const UPDATE_HANDLERS = new InjectionToken<UpdateHandlerService[]>('UPDATE_HANDLERS');

@Injectable()
export abstract class UpdateHandlerService {
  protected destroyRef = inject(DestroyRef);
  protected logger = inject(SplunkLoggerService);
  protected loanStateService = inject(LoanStateService);
  protected isLoanEditingDisabled = inject(LoanEditingState).isLoanEditingDisabled;

  abstract name: EntityStateName;
  protected ignoredPaths: string[] = [];
  protected dirtyState = signal<Record<string, DirtyState>>({});
  protected dirtyState$ = toObservable(this.dirtyState);
  unsavedChanges = computed(() => {
    if (this.isLoanEditingDisabled()) {
      return 0;
    }

    return Object.values(this.dirtyState()).reduce(
      (acc, { dirtyPaths }) => acc + dirtyPaths.length,
      0,
    );
  });
  protected dismissValidationSubject = new Subject<void>();
  protected saveTypeSubject = new Subject<SaveTriggerType>();
  saveType$ = this.saveTypeSubject.asObservable();
  validationErrorsSubject = new BehaviorSubject<{ [path: string]: ValidationErrors }[]>([]);
  validationErrors$ = this.validationErrorsSubject.asObservable();

  constructor(protected saveTrigger: SaveTrigger) {
    saveTrigger.triggerSave$
      .pipe(
        filter(() => this.unsavedChanges() > 0),
        tap((type) => this.saveTypeSubject.next(type)),
        concatMap(() =>
          this.handleUpdates$().pipe(
            catchError((err) => {
              this.logger.error('Unexpected error while handling updates', err);
              return EMPTY;
            }),
          ),
        ),
        takeUntilDestroyed(),
      )
      .subscribe();

    this.saveTrigger.triggerSave$
      .pipe(
        filter(() => this.unsavedChanges() > 0),
        filter(() => Object.values(this.dirtyState()).some(({ form }) => form.invalid)),
        switchMap(() =>
          this.dirtyState$.pipe(
            map((dirtyState) => this.getDirtyStateValidationErrors(Object.entries(dirtyState))),
            takeWhile((errors) => errors.length > 0),
            takeUntil(this.dismissValidationSubject),
            finalize(() => this.validationErrorsSubject.next([])),
          ),
        ),
        takeUntilDestroyed(),
      )
      .subscribe((errors) => this.validationErrorsSubject.next(errors));
  }

  public dismissValidationErrors() {
    this.dismissValidationSubject.next();
  }

  protected handleUpdates$(): Observable<any> {
    const updates: Observable<any>[] = [];
    Object.entries(this.dirtyState()).forEach(([identifier]) => {
      updates.push(
        defer(() => {
          // Fetch dirty state at time of deferred execution, and check if it's valid for update
          const dirtyState = this.dirtyState()[identifier];
          if (!this.shouldHandleUpdate([identifier, dirtyState])) {
            return EMPTY;
          }

          this.clearDirtyState(identifier, dirtyState);
          return this.handleUpdate$(identifier, dirtyState).pipe(
            tap(({ error }) => {
              if (error) {
                this.revertDirtyState(identifier, dirtyState);
              }
            }),
          );
        }),
      );
    });
    return concat(...updates);
  }

  protected shouldHandleUpdate([_, { dirtyPaths, form }]: [string, DirtyState]) {
    return dirtyPaths.length > 0 && form.valid;
  }

  protected abstract handleUpdate$(
    identifier: string,
    state: DirtyState,
  ): Observable<{ error?: any }>;

  protected listenForDirtyState(identifier: string, control: FormGroup): Subscription {
    return control.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.dirtyState.update((current) => ({
        ...current,
        [identifier]: {
          dirtyPaths: getDirtyPaths(control, this.ignoredPaths),
          form: control,
        },
      }));
    });
  }

  /**
   * Clears the dirty state for a specified form identifier.
   *
   * This function marks the form as pristine (clean) and updates the dirty state
   * object to remove all dirty paths for the given identifier.
   *
   * @param {string} identifier - The unique identifier for the form whose dirty state is to be cleared.
   * @param {DirtyState} state - The dirty state object containing the form and its dirty paths.
   */
  protected clearDirtyState(identifier: string, state: DirtyState) {
    state.form.markAsPristine();
    this.dirtyState.update((current) => ({
      ...current,
      [identifier]: { dirtyPaths: [], form: state.form },
    }));
  }

  /**
   * Clears specific paths from the dirty state for a specified form identifier.
   *
   * This function marks the specified form paths as pristine (clean) and updates the dirty state
   * object to remove the specified paths from the list of dirty paths for the given identifier.
   *
   * @param {string} identifier - The unique identifier for the form whose dirty state is to be cleared.
   * @param {string[]} pathsToClear - List of paths to clear.
   */
  protected clearDirtyPaths(identifier: string, pathsToClear: string[]) {
    const currentState = this.dirtyState()[identifier];
    if (!currentState) {
      return;
    }
    const dirtyPaths = currentState.dirtyPaths.filter((path) => !pathsToClear.includes(path));
    pathsToClear.forEach((path) => currentState.form.get(path)?.markAsPristine());
    this.dirtyState.update((current) => ({
      ...current,
      [identifier]: { dirtyPaths, form: currentState.form },
    }));
  }

  /**
   * Reverts the dirty state of a form to a previous state.
   *
   * This function marks specified paths within the form as dirty and updates the dirty state
   * to merge the new dirty paths with any existing paths for the specified identifier.
   *
   * @param {string} identifier - The unique identifier for the form whose dirty state is being reverted.
   * @param {DirtyState} state - The dirty state object containing the form and the list of dirty paths.
   */
  protected revertDirtyState(identifier: string, { form, dirtyPaths }: DirtyState) {
    dirtyPaths.forEach((path) => form.get(path)?.markAsDirty());
    this.dirtyState.update((current) => ({
      ...current,
      [identifier]: {
        dirtyPaths: Array.from(
          new Set([...dirtyPaths, ...(current[identifier]?.dirtyPaths ?? [])]),
        ),
        form,
      },
    }));
  }

  /**
   * Retrieves validation errors from the provided dirty state entries.
   *
   * This function collects validation errors from the forms within the dirty state entries
   * and returns them as an array of error objects, filtered to only include entries with errors.
   *
   * @param {Array<[string, DirtyState]>} dirtyStateEntries - An array of tuples containing form identifiers and their corresponding dirty state.
   * @returns {Array<{ [path: string]: ValidationErrors }>} - An array of validation errors keyed by form control paths.
   */
  protected getDirtyStateValidationErrors(
    dirtyStateEntries: [string, DirtyState][],
  ): { [path: string]: ValidationErrors }[] {
    return dirtyStateEntries
      .map(([_, { form }]) => getValidationErrors(form))
      .filter((errors) => Object.keys(errors).length > 0);
  }

  /**
   * Removes the dirty state for a specified form identifier.
   *
   * This function deletes the dirty state associated with the given identifier, effectively
   * removing any tracked dirty paths and form state for that identifier.
   *
   * @param {string} identifier - The unique identifier for the form whose dirty state is to be removed.
   */
  public removeDirtyState(identifier: string) {
    this.dirtyState.update((current) => {
      const next = { ...current };
      delete next[identifier];
      return next;
    });
  }
}
