import { Injectable, Injector, inject, runInInjectionContext } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { MaritalStatus } from '@rocket-logic/rl-xp-bff-models';
import { combineLatest, distinctUntilChanged, map, of, pairwise, startWith, switchMap } from 'rxjs';
import { ClientActivationHandlerService } from './client-activation-handler.service';
import { ClientSubscriptionManager } from './client-subscription-manager.service';
import { ClientControls } from './form-types';

@Injectable()
export class ClientRelationshipHandlerService {
  private clientSubManager = inject(ClientSubscriptionManager);
  private clientActivationHandlerService = inject(ClientActivationHandlerService);
  injector = inject(Injector);

  /**
   * Adds a listener to monitor and handle changes to a client's spouse ID and marital status.
   *
   * @param clientsFormArray - An array of form groups representing all clients.
   * @param clientForm - The form group of the current client.
   * @param clientKey - A unique key to manage the subscription for this client.
   *
   * This function performs the following actions:
   * 1. Sets up a combined observable that listens to changes in both the `spouseClientId` and `maritalStatus` controls of the `clientForm`.
   * 2. Uses `distinctUntilChanged` to ensure that actions are only taken when there are meaningful changes in either `spouseClientId` or `maritalStatus`.
   * 3. On each relevant change, it:
   *    - Retrieves the current `clientId` from the form.
   *    - If a new `spouseClientId` is set and the `maritalStatus` is `Married`, it invokes `handleMarriedClient` to establish the marital relationship.
   *    - If the `spouseClientId` is removed, it calls `handleUnmarriedClient` to dissolve any existing marital relationship.
   *    - If a `spouseClientId` is set but the `maritalStatus` is not `Married`, it removes the marital relationship and clears the `spouseClientId` control.
   * 4. Ensures that the subscription is properly managed and cleaned up using `takeUntilDestroyed` and `clientSubManager`.
   *
   * This listener ensures that the client's marital status and spouse relationships remain consistent across the form array.
   */
  addSpouseClientIdListener(
    clientsFormArray: FormGroup<ClientControls>[],
    clientForm: FormGroup<ClientControls>,
    clientKey: string,
  ) {
    runInInjectionContext(this.injector, () => {
      const spouseClientIdControl = clientForm.get('personalInformation.spouseClientId');
      const maritalStatusControl = clientForm.get('personalInformation.maritalStatus');
      const clientIdControl = clientForm.get('id');
      const sub = combineLatest([
        clientIdControl!.valueChanges.pipe(startWith(clientIdControl!.value)),
        spouseClientIdControl!.valueChanges.pipe(startWith(spouseClientIdControl!.value)),
        maritalStatusControl!.valueChanges.pipe(startWith(maritalStatusControl!.value)),
      ])
        .pipe(
          distinctUntilChanged(
            (
              [previousClientId, prevSpouseClientId, prevMaritalStatus],
              [currentClientId, currentSpouseClientId, currentMaritalStatus],
            ) =>
              previousClientId === currentClientId &&
              prevSpouseClientId === currentSpouseClientId &&
              this.isMarried(prevMaritalStatus) === this.isMarried(currentMaritalStatus),
          ),
          pairwise(),
          switchMap((clientChanges) => {
            const [currentClientId, spouseClientId] = clientChanges[1];
            if (spouseClientId != null && this.isSpouseDeactivated(spouseClientId)) {
              return this.clientActivationHandlerService
                .reactivateClient$(spouseClientId, currentClientId!)
                .pipe(
                  map(() => {
                    return clientChanges;
                  }),
                );
            }

            return of(clientChanges);
          }),
          takeUntilDestroyed(),
        )
        .subscribe(([[, prevSpouseClientId], [clientId, spouseClientId, maritalStatus]]) => {
          if (!clientId) {
            console.warn('Client ID not present');
            return;
          }

          if (spouseClientId && maritalStatus === MaritalStatus.Married) {
            this.handleMarriedClient(
              spouseClientId,
              clientId,
              clientsFormArray,
              prevSpouseClientId,
            );
          } else if (!spouseClientId && prevSpouseClientId) {
            this.handleUnmarriedClient(prevSpouseClientId, clientId, clientsFormArray);
          } else if (spouseClientId && !this.isMarried(maritalStatus)) {
            this.handleUnmarriedClient(spouseClientId, clientId, clientsFormArray);
            clientForm.get('personalInformation.spouseClientId')!.setValue(null);
          }
        });

      this.clientSubManager.addSubscription(clientKey, sub);
    });
  }

  /**
   * Handles the marital status updates for a married client and their spouse within a form array.
   *
   * @param spouseClientId - The ID of the spouse client to be linked.
   * @param clientId - The ID of the current client.
   * @param clientsFormArray - An array of form groups representing clients.
   * @param previousSpouseClientId - (Optional) The ID of the previous spouse client, if any.
   *
   * This function performs the following actions:
   * 1. Searches for the form group corresponding to the `spouseClientId` within `clientsFormArray`.
   * 2. If the spouse client exists and is not already linked to the current `clientId`:
   *    - Marks the spouse's `maritalStatus` control as dirty and sets its value to `MaritalStatus.Married`.
   *    - Marks the spouse's `spouseClientId` control as dirty and sets its value to the current `clientId`.
   * 3. If there is a `previousSpouseClientId` that differs from the new `spouseClientId`, it invokes
   *    `handleUnmarriedClient` to remove the marital link from the previous spouse.
   */
  private handleMarriedClient(
    spouseClientId: string,
    clientId: string,
    clientsFormArray: FormGroup<ClientControls>[],
    previousSpouseClientId?: string | null,
  ) {
    const otherClientForm = clientsFormArray.find(
      (otherClientForm) => otherClientForm.value.id === spouseClientId,
    );

    if (
      otherClientForm &&
      otherClientForm.getRawValue().personalInformation?.spouseClientId !== clientId
    ) {
      const otherSpouseClientIdControl = otherClientForm.get('personalInformation.spouseClientId')!;
      const otherMaritalStatusControl = otherClientForm.get('personalInformation.maritalStatus')!;
      otherMaritalStatusControl.markAsDirty();
      otherMaritalStatusControl.setValue(MaritalStatus.Married);
      otherSpouseClientIdControl.markAsDirty();
      otherSpouseClientIdControl.setValue(clientId);
    }

    if (previousSpouseClientId && previousSpouseClientId !== spouseClientId) {
      this.handleUnmarriedClient(previousSpouseClientId, clientId, clientsFormArray);
    }
  }

  /**
   * Handles the case where a client is marked as unmarried.
   *
   * This function searches through the clients' form array to find the spouse's form based
   * on the provided spouseClientId. If the spouse's form is found and it references the
   * client as a spouse, the function sets the spouseClientId field to null, marking it as dirty.
   *
   * @param {string} spouseClientId - The ID of the spouse client to be checked and potentially modified.
   * @param {string} clientId - The ID of the client whose marital status is being handled.
   * @param {FormGroup<ClientControls>[]} clientsFormArray - An array of form groups containing client information.
   */
  private handleUnmarriedClient(
    spouseClientId: string,
    clientId: string,
    clientsFormArray: FormGroup<ClientControls>[],
  ) {
    const otherClientForm = clientsFormArray.find(
      (otherClientForm) => otherClientForm.value.id === spouseClientId,
    );

    if (
      otherClientForm &&
      otherClientForm.getRawValue().personalInformation?.spouseClientId === clientId
    ) {
      const otherSpouseClientIdControl = otherClientForm.get('personalInformation.spouseClientId')!;
      otherSpouseClientIdControl.markAsDirty();
      otherSpouseClientIdControl.setValue(null);
    }
  }

  private isMarried(maritalStatus?: MaritalStatus | null) {
    return maritalStatus === MaritalStatus.Married;
  }

  private isSpouseDeactivated(clientId: string) {
    return (
      this.clientActivationHandlerService
        .deactivatedClients()
        ?.some((deactivated) => deactivated.id === clientId) ?? false
    );
  }
}
