import { DestroyRef, Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormArray, FormGroup } from '@angular/forms';
import {
  Address,
  CurrentResidenceDetail,
  PhoneNumber,
  ResidenceDetail,
} from '@rocket-logic/rl-xp-bff-models';
import { Subscription, distinctUntilChanged, startWith } from 'rxjs';
import { Controlify } from '../../../util/form-utility-types';
import { Nullable } from '../../../util/nullable-type';
import { ValidationHandlerService } from '../abstract-validation-handler.service';
import { ClientSubscriptionManager } from './client-subscription-manager.service';
import {
  ClientControls,
  MilitaryControls,
  PhoneNumberGroup,
  ResidenceInfoControls,
} from './form-types';

@Injectable()
export class ClientValidationHandlerService extends ValidationHandlerService {
  private subManager = inject(ClientSubscriptionManager);
  private destroyRef = inject(DestroyRef);

  private readonly MILITARY_REQUIRED_PATHS = ['militaryServices.0.branch'];

  addClientValidationListener(clientForm: FormGroup<ClientControls>, clientKey: string) {
    this.subManager.addSubscription(clientKey, this.addMilitaryValidationListener(clientForm));
    this.subManager.addSubscription(clientKey, this.addResidenceValidationListener(clientForm));
    this.subManager.addSubscription(clientKey, this.addPhoneNumbersValidationListener(clientForm));
  }

  private addPhoneNumbersValidationListener(clientForm: FormGroup<ClientControls>): Subscription {
    const phoneNumbersForm = clientForm.get(
      'contactInformation.phoneNumbers',
    ) as FormArray<PhoneNumberGroup>;

    return phoneNumbersForm.valueChanges
      .pipe(
        startWith(phoneNumbersForm.getRawValue()),
        distinctUntilChanged((prev, curr) => {
          const prevPhoneNumberWithValueCount = this.countPhoneNumbersWithValues(prev);
          const currPhoneNumbersWithValueCount = this.countPhoneNumbersWithValues(curr);

          return prevPhoneNumberWithValueCount === currPhoneNumbersWithValueCount;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => this.setPhoneNumberValidators(phoneNumbersForm));
  }

  private setPhoneNumberValidators(phoneNumberFormArray: FormArray<PhoneNumberGroup>) {
    phoneNumberFormArray.controls.forEach((phoneNumberForm) => {
      if (this.phoneNumberHasValue(phoneNumberForm.getRawValue())) {
        this.addPhoneNumberRequiredValidators(phoneNumberForm);
      } else {
        this.removePhoneNumberRequiredValidators(phoneNumberForm);
      }
    });
  }

  private addResidenceValidationListener(clientForm: FormGroup<ClientControls>): Subscription {
    const residenceInfoForm = clientForm.get(
      'residenceInformation',
    ) as FormGroup<ResidenceInfoControls>;
    return residenceInfoForm.valueChanges
      .pipe(
        startWith(residenceInfoForm.getRawValue()),
        distinctUntilChanged((prev, curr) => {
          const prevCurrentResidenceHasValue = this.currentResidenceHasValue(
            prev.currentResidence as CurrentResidenceDetail,
          );
          const currCurrentResidenceHasValue = this.currentResidenceHasValue(
            curr.currentResidence as CurrentResidenceDetail,
          );

          const prevPreviousAddressWithValueCount = this.countFormerResidencesWithValues(
            prev.formerResidences as ResidenceDetail[],
          );
          const currPreviousAddressWithValueCount = this.countFormerResidencesWithValues(
            curr.formerResidences as ResidenceDetail[],
          );

          return (
            prevCurrentResidenceHasValue === currCurrentResidenceHasValue &&
            prevPreviousAddressWithValueCount === currPreviousAddressWithValueCount
          );
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => this.setResidenceValidators(residenceInfoForm));
  }

  private setResidenceValidators(residenceInfoForm: FormGroup<ResidenceInfoControls>) {
    const currentResidence = residenceInfoForm.getRawValue()
      .currentResidence as CurrentResidenceDetail;
    const currentAddressForm = residenceInfoForm.get('currentResidence.address') as FormGroup<
      Controlify<Address>
    >;

    if (this.currentResidenceHasValue(currentResidence)) {
      this.addAddressRequiredValidators(currentAddressForm);
    } else {
      this.removeAddressRequiredValidators(currentAddressForm);
    }
  }

  private addMilitaryValidationListener(clientForm: FormGroup<ClientControls>): Subscription {
    const militaryForm = clientForm.get('military') as FormGroup<MilitaryControls>;
    return militaryForm.valueChanges
      .pipe(
        startWith(militaryForm.getRawValue()),
        distinctUntilChanged((prev, curr) => prev.hasMilitaryService === curr.hasMilitaryService),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => this.setMilitaryValidators(militaryForm));
  }

  private setMilitaryValidators(militaryForm: FormGroup<MilitaryControls>) {
    this.removeRequiredValidators(militaryForm, this.MILITARY_REQUIRED_PATHS);
    const militaryInfo = militaryForm.getRawValue();
    if (militaryInfo.hasMilitaryService) {
      this.addRequiredValidators(militaryForm, this.MILITARY_REQUIRED_PATHS);
    }
  }

  private phoneNumberHasValue(phoneNumber?: Nullable<PhoneNumber>): boolean {
    return !!phoneNumber && Object.values(phoneNumber).some((value) => !!value);
  }

  private currentResidenceHasValue(residence: CurrentResidenceDetail): boolean {
    return (
      (residence && !!residence.sameAsClientId) ||
      (residence && this.addressHasValue(residence.address)) ||
      !!residence.durationAtResidence?.years ||
      !!residence.durationAtResidence?.months ||
      !!residence.residencyType ||
      residence.taxesFiledAtThisAddress != null
    );
  }

  private formerResidenceHasValue(residence: ResidenceDetail): boolean {
    return (
      (residence && this.addressHasValue(residence.address)) ||
      !!residence.durationAtResidence?.years ||
      !!residence.durationAtResidence?.months
    );
  }

  private addressHasValue(address: Nullable<Address> | null | undefined): boolean {
    return !!address && Object.values(address).some((value) => !!value);
  }

  private countPhoneNumbersWithValues(phoneNumbers?: Nullable<PhoneNumber>[]): number {
    return (
      phoneNumbers?.reduce((count, phoneNumber) => {
        return this.phoneNumberHasValue(phoneNumber) ? count + 1 : count;
      }, 0) ?? 0
    );
  }

  private countFormerResidencesWithValues(formerResidences?: ResidenceDetail[]): number {
    return (
      formerResidences?.reduce((count, residence) => {
        return this.formerResidenceHasValue(residence) ? count + 1 : count;
      }, 0) ?? 0
    );
  }

  private removePhoneNumberRequiredValidators(form: FormGroup<Controlify<PhoneNumber>>) {
    this.removeRequiredValidators(form, ['number', 'type']);
  }

  private addPhoneNumberRequiredValidators(form: FormGroup<Controlify<PhoneNumber>>) {
    this.addRequiredValidators(form, ['number', 'type']);
  }

  private removeAddressRequiredValidators(form: FormGroup<Controlify<Address>>) {
    this.removeRequiredValidators(form, ['addressLine1', 'city', 'state', 'zipCode']);
  }

  private addAddressRequiredValidators(form: FormGroup<Controlify<Address>>) {
    this.addRequiredValidators(form, ['addressLine1', 'city', 'state', 'zipCode']);
  }
}
