import { TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { ClientActivationHandlerService } from './client-activation-handler.service';
import { ClientAddressHandlerService } from './client-address-handler.service';
import { ClientFormService } from './client-form.service';
import { ClientRelationshipHandlerService } from './client-relationship-handler.service';
import { ClientStateService } from './client-state.service';
import { ClientSubscriptionManager } from './client-subscription-manager.service';
import { ClientUpdateHandlerService } from './client-update-handler.service';
import { ClientValidationHandlerService } from './client-validation-handler.service';
import { CLIENT_AUTO_SAVE_TRIGGER } from './provide-client-state';
import { SameResidenceHandlerService } from './same-residence-handler.service';
import { UseAsSubjectHandlerService } from './use-as-subject-handler.service';

describe('ClientFormService', () => {
  let service: ClientFormService;

  beforeEach(() =>
    MockBuilder(ClientFormService)
      .mock(ClientStateService, { state$: NEVER })
      .mock(LoanEditingState, {
        isLoanEditingDisabled$: NEVER,
      })
      .mock(ClientRelationshipHandlerService)
      .mock(ClientSubscriptionManager)
      .mock(SameResidenceHandlerService)
      .mock(UseAsSubjectHandlerService)
      .mock(ClientUpdateHandlerService)
      .provide({ provide: CLIENT_AUTO_SAVE_TRIGGER, useValue: { registerControls: () => {} } })
      .mock(ClientValidationHandlerService)
      .mock(ClientAddressHandlerService)
      .mock(ClientActivationHandlerService, { deactivatedClients: signal(undefined) }),
  );
  beforeEach(() => {
    service = TestBed.inject(ClientFormService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
