import { InjectionToken, Provider } from '@angular/core';
import { AutoSaveTriggerService } from '../../save-trigger/auto-save.service';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { STATE_SERVICES } from '../abstract-entity-state.service';
import { UPDATE_HANDLERS } from '../abstract-update-handler.service';
import { ClientActivationHandlerService } from './client-activation-handler.service';
import { ClientAddressHandlerService } from './client-address-handler.service';
import { ClientFormService } from './client-form.service';
import { ClientRelationshipHandlerService } from './client-relationship-handler.service';
import { ClientStateService } from './client-state.service';
import { ClientSubscriptionManager } from './client-subscription-manager.service';
import { ClientUpdateHandlerService } from './client-update-handler.service';
import { ClientValidationHandlerService } from './client-validation-handler.service';
import { SameResidenceHandlerService } from './same-residence-handler.service';
import { UseAsSubjectHandlerService } from './use-as-subject-handler.service';

export const CLIENT_SAVE_TRIGGER = new InjectionToken<SaveTrigger>('CLIENT_SAVE_TRIGGER');
export const CLIENT_AUTO_SAVE_TRIGGER = new InjectionToken<AutoSaveTriggerService>(
  'CLIENT_AUTO_SAVE_TRIGGER',
);

export function provideClientState(): Provider[] {
  return [
    ClientStateService,
    ClientFormService,
    ClientRelationshipHandlerService,
    SameResidenceHandlerService,
    ClientSubscriptionManager,
    UseAsSubjectHandlerService,
    ClientUpdateHandlerService,
    { provide: CLIENT_AUTO_SAVE_TRIGGER, useClass: AutoSaveTriggerService },
    { provide: CLIENT_SAVE_TRIGGER, useExisting: CLIENT_AUTO_SAVE_TRIGGER },
    { provide: UPDATE_HANDLERS, useExisting: ClientUpdateHandlerService, multi: true },
    { provide: STATE_SERVICES, useExisting: ClientStateService, multi: true },
    ClientValidationHandlerService,
    ClientAddressHandlerService,
    ClientActivationHandlerService,
  ];
}
