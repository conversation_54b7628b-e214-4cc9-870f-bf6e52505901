import { DestroyRef, inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { ResidencyType } from '@rocket-logic/rl-xp-bff-models';
import { combineLatest, filter, skip, startWith } from 'rxjs';
import { LoanInfoFormListenerService } from '../loan-state/loan-info-form-listener.service';
import { ClientSubscriptionManager } from './client-subscription-manager.service';
import { ClientControls } from './form-types';

@Injectable()
export class ClientAddressHandlerService {
  private clientSubManager = inject(ClientSubscriptionManager);
  private loanInfoFormListenerService = inject(LoanInfoFormListenerService);
  private destroyRef = inject(DestroyRef);

  addAddressListener(clientForm: FormGroup<ClientControls>, clientKey: string) {
    this.clientSubManager.addSubscription(clientKey, this.loanPurposeFlipListener(clientForm));
    this.clientSubManager.addSubscription(clientKey, this.ownedResidencyListener(clientForm));
  }

  private loanPurposeFlipListener(clientForm: FormGroup<ClientControls>) {
    const monthlyRentControl =
      clientForm.controls.residenceInformation.controls.currentResidence.controls.monthlyRent;
    const residencyTypeControl =
      clientForm.controls.residenceInformation.controls.currentResidence.controls.residencyType;

    return this.loanInfoFormListenerService.purchaseSelected$
      .pipe(
        skip(1),
        filter((purchaseSelected) => purchaseSelected),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => {
        if (residencyTypeControl.value) {
          if (residencyTypeControl.value === ResidencyType.Rent) {
            monthlyRentControl.markAsDirty();
            monthlyRentControl.setValue(null);
          }

          residencyTypeControl.reset();
        }
      });
  }

  private ownedResidencyListener(clientForm: FormGroup<ClientControls>) {
    const residencyTypeControl =
      clientForm.controls.residenceInformation.controls.currentResidence.controls.residencyType;
    const useAsSubjectPropertyControl =
      clientForm.controls.residenceInformation.controls.currentResidence.controls
        .useAsSubjectProperty;
    const sameAsClientIdControl =
      clientForm.controls.residenceInformation.controls.currentResidence.controls.sameAsClientId;

    const monthlyRentControl =
      clientForm.controls.residenceInformation.controls.currentResidence.controls.monthlyRent;

    return combineLatest([
      residencyTypeControl.valueChanges.pipe(startWith(residencyTypeControl.value)),
      useAsSubjectPropertyControl.valueChanges.pipe(startWith(useAsSubjectPropertyControl.value)),
      sameAsClientIdControl.valueChanges.pipe(startWith(sameAsClientIdControl.value)),
      this.loanInfoFormListenerService.refiSelected$,
    ])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([residencyType, useAsSubjectProperty, sameAsClientId, refiSelected]) => {
        if (
          useAsSubjectProperty &&
          residencyType !== ResidencyType.Own &&
          refiSelected &&
          !sameAsClientId
        ) {
          residencyTypeControl.markAsDirty();
          residencyTypeControl.setValue(ResidencyType.Own);
        }

        if (residencyType !== ResidencyType.Rent && monthlyRentControl.value) {
          monthlyRentControl.markAsDirty();
          monthlyRentControl.setValue(null);
        }
      });
  }
}
