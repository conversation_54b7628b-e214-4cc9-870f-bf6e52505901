import { Injectable } from '@angular/core';
import { Subscription } from 'rxjs';
import { SubscriptionManager } from '../../subscription-manager/subscription-manager';

@Injectable()
export class ClientSubscriptionManager extends SubscriptionManager<string> {
  protected persistentSubs = new Map<string, Subscription>();

  /**
   * Tracks a subscription that should not be removed when state changes.
   * @param index Zero-based index of the client in the form array.
   * @param sub Subscription to be tracked.
   */
  addPersistentSubscription(key: string, sub: Subscription) {
    const subs = this.persistentSubs.get(key) ?? new Subscription();
    subs.add(sub);
    this.persistentSubs.set(key, subs);
  }

  /**
   * Removes persistent subscriptions for a client.
   * This should be called when the client is removed from the form array.
   * @param index Zero-based index of the client in the form array.
   */
  removePersistentSubscriptions(key: string) {
    this.persistentSubs.get(key)?.unsubscribe();
    this.persistentSubs.delete(key);
  }
}
