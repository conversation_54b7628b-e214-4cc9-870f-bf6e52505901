import { Injectable, computed, inject, runInInjectionContext, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import {
  AbstractControl,
  FormArray,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import {
  Client,
  Deactivate,
  DurationAtResidence,
  MaritalStatus,
  MilitaryService,
  PersonalInfo,
} from '@rocket-logic/rl-xp-bff-models';
import {
  EMPTY,
  Observable,
  Subject,
  distinctUntilChanged,
  map,
  scan,
  startWith,
  tap,
  withLatestFrom,
} from 'rxjs';
import { buildAddressForm } from '../../../util/build-address-form';
import { buildPhoneNumberForm } from '../../../util/build-phone-number-form';
import { Formify } from '../../../util/form-utility-types';
import { resetNonDirtyControls } from '../../../util/reset-non-dirty-controls';
import { syncFormArray } from '../../../util/sync-form-array';
import { AbstractCollectionFormService } from '../abstract-collection-form.service';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { ClientActivationHandlerService } from './client-activation-handler.service';
import { ClientAddressHandlerService } from './client-address-handler.service';
import { ClientRelationshipHandlerService } from './client-relationship-handler.service';
import { ClientStateService } from './client-state.service';
import { ClientSubscriptionManager } from './client-subscription-manager.service';
import { ClientUpdateHandlerService } from './client-update-handler.service';
import { ClientValidationHandlerService } from './client-validation-handler.service';
import {
  ClientControls,
  ClientForm,
  ContactInfoForm,
  CurrentResidenceControls,
  CurrentResidenceForm,
  DurationAtResidenceGroup,
  LandlordDetailsForm,
  MilitaryForm,
  MilitaryServiceGroup,
  PersonalInfoForm,
  PhoneNumberGroup,
  ResidenceDetailControls,
  ResidenceDetailForm,
  ResidenceInfoControls,
} from './form-types';
import { CLIENT_AUTO_SAVE_TRIGGER } from './provide-client-state';
import { SameResidenceHandlerService } from './same-residence-handler.service';
import { UseAsSubjectHandlerService } from './use-as-subject-handler.service';

@Injectable()
export class ClientFormService extends AbstractCollectionFormService<ClientControls> {
  public MAX_NUMBER_OF_CLIENTS = 4;
  public canAddNewClient = computed(() => this.entityValues().length < this.MAX_NUMBER_OF_CLIENTS);
  private clientStateService = inject(ClientStateService);
  private clientSubManager = inject(ClientSubscriptionManager);
  private relationshipHandler = inject(ClientRelationshipHandlerService);
  private sameResidenceHandler = inject(SameResidenceHandlerService);
  private useAsSubjectHandlerService = inject(UseAsSubjectHandlerService);
  private updateHandler = inject(ClientUpdateHandlerService);
  private clientAutoSaveTrigger = inject(CLIENT_AUTO_SAVE_TRIGGER);
  private isLoanEditingDisabled$ = inject(LoanEditingState).isLoanEditingDisabled$;
  private clientValidationHandler = inject(ClientValidationHandlerService);
  private clientAddressHandlerService = inject(ClientAddressHandlerService);
  private clientActivationHandlerService = inject(ClientActivationHandlerService);
  private readonly ssnReg = new RegExp(/^(\d{9})$/);
  private readonly emailReg = new RegExp('^[^@\\s]+@[^@\\s]+\\.[^@\\s]+$');
  private resetSubject = new Subject<Client[]>();
  public reset$ = this.resetSubject.asObservable();
  override entityFormMap = signal(new Map<string, FormGroup<ClientControls>>());
  clientsWithSubjectPropEnabled = toSignal(
    this.entityValueChanges$.pipe(
      startWith(this.entityFormArray.value),
      map(() =>
        this.entityFormArray.controls
          .filter(
            (client) =>
              client.controls.residenceInformation.controls.currentResidence.controls
                .useAsSubjectProperty.enabled,
          )
          .map((client) => client.getRawValue()),
      ),
    ),
  );

  override entityValues = computed(() => Array.from(this.entityFormMap().values()));
  sortedEntityForms = toSignal(
    this.entityValueChanges$.pipe(
      map(() =>
        Array.from(this.entityFormMap().entries()).sort(([, formA], [, formB]) => {
          const isPrimaryBorrowerA = formA.value.isPrimaryBorrower;
          const isPrimaryBorrowerB = formB.value.isPrimaryBorrower;
          if (isPrimaryBorrowerA && !isPrimaryBorrowerB) {
            return -1;
          } else if (!isPrimaryBorrowerA && isPrimaryBorrowerB) {
            return 1;
          } else {
            return 0;
          }
        }),
      ),
    ),
  );

  constructor() {
    super();

    this.clientAutoSaveTrigger.registerControls(this.entityFormArray);
    this.useAsSubjectHandlerService.addUseAsSubjectListener(this.entityFormArray, this.reset$);

    this.clientStateService.state$
      .pipe(
        map((state) => state.data),
        distinctUntilChanged((prev, current) => {
          if (prev === undefined && current === undefined) {
            return true;
          }
          if (prev?.size !== current?.size) {
            return false;
          }
          return (
            Array.from(prev?.entries() ?? [])?.every(([id, client]) => {
              return client === current?.get(id);
            }) ?? false
          );
        }),
        scan(
          (
            prev: { id: string; client: Client; updated: boolean }[],
            current: Map<string, Client> | undefined,
          ) => {
            // Determine which clients have been updated to prevent overwriting unsaved changes
            return (
              Array.from(current?.entries() ?? [])?.map(([id, client]) => {
                const updated = prev?.find(({ id }) => id === id)?.client !== client;
                return { id, client, updated };
              }) ?? []
            );
          },
          [] as { id: string; client: Client; updated: boolean }[],
        ),
        withLatestFrom(this.isLoanEditingDisabled$),
        takeUntilDestroyed(),
      )
      .subscribe(([clients, isLoanEditingDisabled]) =>
        this.handleClientStateChange(clients, isLoanEditingDisabled),
      );

    this.isLoanEditingDisabled$.pipe(takeUntilDestroyed()).subscribe((isEditingDisabled) => {
      if (isEditingDisabled) {
        this.entityValues().forEach((clientForm) => clientForm.disable());
      }
    });
  }

  /**
   *
   * @param options additional info to add with new clients. i.e. the spouse of the new client
   * @returns Form Group of the newly created client
   */
  public addClient(options?: { spouse: Client }): FormGroup<ClientControls> {
    const clientForm = this.buildClientForm();
    const clientKey = crypto.randomUUID();
    const currentLength = this.entityFormMap().size;

    if (currentLength === 0) {
      clientForm.patchValue({ isPrimaryBorrower: true }, { emitEvent: false });
    }

    if (options?.spouse) {
      clientForm.patchValue(
        {
          personalInformation: <PersonalInfo>{
            maritalStatus: MaritalStatus.Married,
            lastName: options.spouse.personalInformation?.lastName,
            numberOfDependents: options.spouse.personalInformation?.numberOfDependents,
            ageOfDependents: options.spouse.personalInformation?.ageOfDependents,
            spouseClientId: options.spouse.id,
          },
        },
        { emitEvent: true },
      );
      clientForm.controls.personalInformation.controls.spouseClientId.markAsDirty();
      clientForm.controls.personalInformation.controls.maritalStatus.markAsDirty();
    }

    this.updateState(clientKey, clientForm);
    this.entityFormArray.push(clientForm);
    this.addListeners(clientForm, clientKey);
    this.updateHandler.addClientCreateListener(clientForm, clientKey);
    return clientForm;
  }

  public addPhoneNumber(clientKey: string) {
    const clientForm = this.entityFormMap().get(clientKey)!;
    const phoneNumbersArray = clientForm.get(
      'contactInformation.phoneNumbers',
    ) as FormArray<PhoneNumberGroup>;
    phoneNumbersArray.push(buildPhoneNumberForm(this.formBuilder, { validateUniqueTypes: true }));
  }

  public addPreviousAddress(clientKey: string) {
    const clientForm = this.entityFormMap().get(clientKey)!;
    const previousAddressesArray = clientForm.get(
      'residenceInformation.formerResidences',
    ) as FormArray<FormGroup<ResidenceDetailControls>>;
    previousAddressesArray.push(this.buildResidenceDetailForm());
  }

  public addMilitaryService(clientKey: string) {
    const clientForm = this.entityFormMap().get(clientKey)!;
    const militaryServicesArray = clientForm.get(
      'military.militaryServices',
    ) as FormArray<MilitaryServiceGroup>;
    militaryServicesArray.push(this.buildMilitaryServiceForm());
  }

  public deleteClient$(clientKey: string) {
    const client = this.entityFormMap().get(clientKey)?.value;

    if (!client?.id) {
      this.findAndRemoveClient(clientKey);
      return EMPTY;
    }

    return this.clientStateService
      .deleteClient$(clientKey)
      .pipe(tap(() => this.findAndRemoveClient(clientKey)));
  }

  public deactivateClient$(clientKey: string, deactivationRequest: Deactivate) {
    const client = this.entityFormMap().get(clientKey)?.value;

    if (!client?.id) {
      this.findAndRemoveClient(clientKey);
      return EMPTY;
    }

    return this.clientStateService.deactivateClient$(clientKey, deactivationRequest).pipe(
      tap(() => {
        this.findAndRemoveClient(clientKey);
        this.clientActivationHandlerService.refreshSubject.next();
      }),
    );
  }

  /**
   * Fetch all client form controls from the client form Form Group's Form Array
   * @returns FormArray<FormGroup<ClientControls>> - FormArray of FormGroup<ClientControls>
   */
  public getClientFormControls(): FormGroup<ClientControls>[] {
    return Array.from(this.entityFormMap().values());
  }

  /**
   * Observable containing the latest values for primary borrower.
   * @returns AbstractControl - AbstractControl of the primary borrower's client form control
   */
  public getPrimaryBorrowerInfo$(): Observable<Partial<Client>> {
    return this.entityFormArray.valueChanges.pipe(
      map(
        () =>
          <Partial<Client>>(
            this.entityValues().find((control) => control.value.isPrimaryBorrower)?.value
          ) ?? {},
      ),
      distinctUntilChanged(),
      takeUntilDestroyed(this.destroyRef),
    );
  }

  private findAndRemoveClient(clientKey: string) {
    this.deleteState(clientKey);
    this.entityFormArray.removeAt(
      this.entityFormArray.controls.indexOf(this.entityFormMap().get(clientKey)!),
    );
    this.clientSubManager.removeSubscriptions(clientKey);
    this.clientSubManager.removePersistentSubscriptions(clientKey);
    this.updateHandler.removeDirtyState(clientKey);
  }

  private handleClientStateChange(
    clients: { id: string; client: Client; updated: boolean }[],
    isLoanEditingDisabled?: boolean,
  ) {
    // remove subscriptions while resetting form since clients can change position in the form array
    this.clientSubManager.removeAllSubscriptions();
    // TODO: Refactor to remove controls from FormArray not in state
    clients?.forEach(({ id, client, updated }) => {
      let clientForm = this.entityFormMap().get(id);
      if (!clientForm) {
        clientForm = this.buildClientForm();
        this.updateState(id, clientForm);
        this.entityFormArray.push(clientForm);
      }

      if (updated) {
        this.resetClient(client, clientForm);
      }

      if (isLoanEditingDisabled) {
        this.getClientFormControls().forEach((control) => control.disable());
      }
    });

    // ensure all forms are created/updated before attaching listeners
    this.entityFormMap().forEach((form, id) => this.addListeners(form, id));
    this.resetSubject.next(clients.map((c) => c.client));
  }

  private addListeners(clientForm: FormGroup<ClientControls>, clientKey: string) {
    this.ssnChangeSubscriber(clientForm, clientKey);
    this.updateHandler.addClientUpdateListener(clientForm, clientKey);
    this.relationshipHandler.addSpouseClientIdListener(
      Array.from(this.entityFormMap().values()),
      clientForm,
      clientKey,
    );
    this.sameResidenceHandler.addSameResidenceListener(this.entityFormMap(), clientKey);
    this.clientValidationHandler.addClientValidationListener(clientForm, clientKey);
    this.clientAddressHandlerService.addAddressListener(clientForm, clientKey);
  }

  private resetClient(client: Client, clientForm: FormGroup<ClientControls>) {
    this.resetPhoneNumbers(client, clientForm);
    this.resetFormerResidences(client, clientForm);
    this.resetMilitaryServices(client, clientForm);

    // Because useAsSubjectProperty is a computed field, we will not get it back from the server.
    // Need to reassign to the client object before resetting fields
    if (client.residenceInformation?.currentResidence) {
      const useAsSubjPropValue =
        clientForm.controls.residenceInformation.controls.currentResidence.controls
          .useAsSubjectProperty.value;

      client.residenceInformation.currentResidence.useAsSubjectProperty =
        useAsSubjPropValue ?? undefined;
    }

    resetNonDirtyControls(client, clientForm);

    this.addDefaultPhone(clientForm);
    this.addDefaultMilitaryService(clientForm);

    const clientSSN = client.personalInformation?.ssn;
    const formConfirmSSN = clientForm.controls.personalInformation.controls.confirmSSN;
    if (clientSSN && !formConfirmSSN?.dirty) {
      formConfirmSSN?.patchValue(clientSSN);
    }
  }

  private resetPhoneNumbers(client: Client, form: FormGroup<ClientControls>) {
    const phoneNumbersArray = form.get(
      'contactInformation.phoneNumbers',
    ) as FormArray<PhoneNumberGroup>;
    syncFormArray(
      phoneNumbersArray,
      () => buildPhoneNumberForm(this.formBuilder, { validateUniqueTypes: true }),
      client.contactInformation?.phoneNumbers,
    );
  }

  private addDefaultPhone(form: FormGroup<ClientControls>) {
    const phoneNumbersArray = form.get(
      'contactInformation.phoneNumbers',
    ) as FormArray<PhoneNumberGroup>;
    if (phoneNumbersArray.controls.length === 0) {
      phoneNumbersArray.push(
        buildPhoneNumberForm(this.formBuilder, { validateUniqueTypes: true }),
        {
          emitEvent: false,
        },
      );
    }
  }

  private resetFormerResidences(client: Client, form: FormGroup<ClientControls>) {
    const formerResidencesArray = form.get('residenceInformation.formerResidences') as FormArray<
      FormGroup<ResidenceDetailControls>
    >;
    syncFormArray(
      formerResidencesArray,
      () => this.buildResidenceDetailForm(),
      client.residenceInformation?.formerResidences,
    );
  }

  private resetMilitaryServices(client: Client, form: FormGroup<ClientControls>) {
    const militaryServicesArray = form.get(
      'military.militaryServices',
    ) as FormArray<MilitaryServiceGroup>;
    syncFormArray(
      militaryServicesArray,
      () => this.buildMilitaryServiceForm(),
      client.military?.militaryServices,
    );
  }

  private addDefaultMilitaryService(form: FormGroup<ClientControls>) {
    const militaryServicesArray = form.get(
      'military.militaryServices',
    ) as FormArray<MilitaryServiceGroup>;
    if (militaryServicesArray.controls.length === 0) {
      militaryServicesArray.push(this.buildMilitaryServiceForm(), { emitEvent: false });
    }
  }

  private validateSsnCompare(confirmControl: AbstractControl): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const validateSSN = control.value;
      const confirmSSN = confirmControl.value;
      const ssnComparisonPass = validateSSN === confirmSSN;
      if (!ssnComparisonPass) {
        control.markAsTouched();
        return { ssnComparisonFail: true };
      }
      return null;
    };
  }

  /**
   *
   * @param intialize Partial ClientForm to initialize the form with
   * @returns New Client FormGroup for a Client
   */
  private buildClientForm(): FormGroup<ClientControls> {
    const defaultFormObject = <ClientForm>{
      id: [null],
      isPrimaryBorrower: [null],
      gcid: [null],
      contactInformation: this.formBuilder.group<ContactInfoForm>({
        emailAddress: [null, [Validators.pattern(this.emailReg)]],
        phoneNumbers: this.formBuilder.array<PhoneNumberGroup>([
          buildPhoneNumberForm(this.formBuilder, { validateUniqueTypes: true }),
        ]),
        mailingAddress: buildAddressForm(this.formBuilder),
      }),
      residenceInformation: this.formBuilder.group<ResidenceInfoControls>({
        currentResidence: this.buildCurrentResidenceForm(),
        formerResidences: this.formBuilder.array<FormGroup<ResidenceDetailControls>>([]),
      }),
      personalInformation: this.formBuilder.group<PersonalInfoForm>({
        firstName: [null, [Validators.required]],
        lastName: [null, [Validators.required]],
        middleName: [null],
        preferredName: [null],
        suffix: [null],
        maritalStatus: [null],
        dateOfBirth: [null],
        ssn: [null, [Validators.pattern(this.ssnReg)]],
        confirmSSN: [null, [Validators.pattern(this.ssnReg)]],
        spouseClientId: [null],
        ageOfDependents: [null],
        numberOfDependents: [null],
      }),
      taxFilingInformation: [null],
      demographicInformation: [null],
      supplementalConsumerInformation: [null],
      military: this.formBuilder.group<MilitaryForm>({
        hasMilitaryService: [null],
        militaryServices: this.formBuilder.array<MilitaryServiceGroup>([
          this.buildMilitaryServiceForm(),
        ]),
      }),
      deactivationDetail: [null],
      declarationInformation: [null],
    };

    const clientForm = this.formBuilder.group<ClientForm>(defaultFormObject);

    return clientForm;
  }

  private ssnChangeSubscriber(clientForm: FormGroup, clientKey: string) {
    runInInjectionContext(this.injector, () => {
      const ssn = clientForm.get('personalInformation.ssn')!;
      const confirmSSN = clientForm.get('personalInformation.confirmSSN')!;

      ssn?.addValidators([this.validateClientSsnUniqueness()]);
      confirmSSN?.addValidators([this.validateSsnCompare(ssn)]);

      const ssnChangeSubscription = ssn.valueChanges
        .pipe(takeUntilDestroyed())
        .subscribe(() => confirmSSN.updateValueAndValidity());

      this.clientSubManager.addSubscription(clientKey, ssnChangeSubscription);
    });
  }

  private validateClientSsnUniqueness(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const ssns = this.entityValues()
        .map((clientForm) => clientForm.get('personalInformation.ssn')?.getRawValue())
        .filter((ssn) => !!ssn);

      return new Set(ssns).size !== ssns.length ? { ssnsUniqueFail: true } : null;
    };
  }

  private buildResidenceDetailForm(): FormGroup<ResidenceDetailControls> {
    const residenceForm = this.formBuilder.group<ResidenceDetailForm>({
      address: buildAddressForm(this.formBuilder, {
        requireCity: true,
        requireZipCode: true,
        requireState: true,
        requireStreet: true,
      }),
      residencyType: [null],
      landlordDetails: this.formBuilder.group<LandlordDetailsForm>({
        address: buildAddressForm(this.formBuilder),
        name: [null],
        phoneNumbers: this.formBuilder.array<PhoneNumberGroup>([]),
      }),
      monthlyRent: [null, [Validators.min(0), Validators.max(*********)]],
      durationAtResidence: this.buildDurationAtResidenceForm(),
    });

    this.limitYearsAtResidence(residenceForm);

    return residenceForm;
  }

  private buildCurrentResidenceForm(): FormGroup<CurrentResidenceControls> {
    const residenceForm = this.formBuilder.group<CurrentResidenceForm>({
      address: buildAddressForm(this.formBuilder),
      residencyType: [null],
      durationAtResidence: this.buildDurationAtResidenceForm(),
      landlordDetails: this.formBuilder.group<LandlordDetailsForm>({
        address: buildAddressForm(this.formBuilder),
        name: [null],
        phoneNumbers: this.formBuilder.array<PhoneNumberGroup>([]),
      }),
      monthlyRent: [null, [Validators.min(0), Validators.max(*********)]],
      sameAsClientId: [null],
      planForProperty: [null],
      sellingPrice: [null, [Validators.min(0), Validators.max(*********)]],
      useAsSubjectProperty: [null],
      taxesFiledAtThisAddress: [null],
      occupancyType: [null],
      ownedPropertyId: [null],
    });

    this.limitYearsAtResidence(residenceForm);

    return residenceForm;
  }

  private limitYearsAtResidence(residenceForm: FormGroup) {
    runInInjectionContext(this.injector, () => {
      const yearsLivedAt = residenceForm.get('durationAtResidence.years')!;

      yearsLivedAt.valueChanges.pipe(takeUntilDestroyed()).subscribe((value) => {
        if (value === null || value === undefined || value.toString() === '') {
          return;
        }

        const floatValue = parseFloat(value.toString());

        if (floatValue > 100) {
          yearsLivedAt.setValue(100);
        } else if (floatValue < 0 || isNaN(floatValue)) {
          yearsLivedAt.setValue(0);
        }
      });
    });
  }

  private buildMilitaryServiceForm(): MilitaryServiceGroup {
    return this.formBuilder.group<Formify<MilitaryService>>({
      branch: [null],
      component: [null],
      status: [null],
      isPurpleHeartRecipient: [null],
      isSurvivingSpouseOfVeteran: [null],
      expirationOfTermOfServiceDate: [null],
      vaDisabilityBenefitsStatus: [null],
      hasPriorVAHomeLoan: [null],
      wasDischargedUnderConditionsOtherThanDishonorable: [null],
      deceasedSpouseName: [null],
      deceasedSpouseSsn: [null],
      livesNearMilitaryBase: [null],
    });
  }

  private buildDurationAtResidenceForm(): DurationAtResidenceGroup {
    return this.formBuilder.group<Formify<DurationAtResidence>>({
      years: [null, [Validators.min(0), Validators.max(100)]],
      months: [null, [Validators.min(0), Validators.max(11)]],
    });
  }
}
