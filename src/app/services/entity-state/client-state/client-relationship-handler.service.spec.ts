import { TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { MockBuilder } from 'ng-mocks';
import { ClientActivationHandlerService } from './client-activation-handler.service';
import { ClientRelationshipHandlerService } from './client-relationship-handler.service';
import { ClientStateService } from './client-state.service';
import { ClientSubscriptionManager } from './client-subscription-manager.service';

describe('ClientRelationshipHandlerService', () => {
  let service: ClientRelationshipHandlerService;

  beforeEach(() =>
    MockBuilder(ClientRelationshipHandlerService)
      .mock(ClientStateService)
      .mock(ClientSubscriptionManager)
      .mock(ClientActivationHandlerService, { deactivatedClients: signal(undefined) }),
  );
  beforeEach(() => {
    service = TestBed.inject(ClientRelationshipHandlerService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
