import { Injectable, Signal, inject } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { Client, ClientActivationFilter } from '@rocket-logic/rl-xp-bff-models';
import {
  Subject,
  catchError,
  combineLatest,
  filter,
  map,
  of,
  startWith,
  switchMap,
  tap,
} from 'rxjs';
import { DataProviderService } from '../../data-provider/data-provider.service';
import { LoanIdService } from '../../loan-id/loan-id.service';
import { ClientStateService } from './client-state.service';

@Injectable()
export class ClientActivationHandlerService {
  private dataProviderService = inject(DataProviderService);
  private loanIdService = inject(LoanIdService);
  private clientStateService = inject(ClientStateService);
  private logger = inject(SplunkLoggerService);

  refreshSubject = new Subject<void>();

  public deactivatedClients: Signal<Client[] | undefined> = toSignal(
    combineLatest([this.loanIdService.loanId$, this.refreshSubject.pipe(startWith(null))]).pipe(
      map(([loanId]) => loanId),
      filter((loanId): loanId is string => !!loanId),
      switchMap((loanId) => {
        return this.dataProviderService.getClients$(loanId, ClientActivationFilter.Inactive);
      }),
      catchError((err) => {
        this.logger.error('Failed to get deactivated clients', err);
        return of([] as Client[]);
      }),
      takeUntilDestroyed(),
    ),
  );

  public reactivateClient$(clientId: string, spouseId?: string) {
    return this.loanIdService.loanId$.pipe(
      filter((loanId): loanId is string => !!loanId),
      switchMap((loanId) => {
        return this.dataProviderService.activateClient$(loanId, clientId).pipe(
          tap((client) => {
            this.refreshSubject.next();
            this.clientStateService.patchReactivatedClientState(client, spouseId);
          }),
        );
      }),
    );
  }
}
