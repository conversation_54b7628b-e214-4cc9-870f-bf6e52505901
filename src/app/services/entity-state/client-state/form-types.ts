import { FormArray, FormGroup } from '@angular/forms';
import {
  Address,
  Client,
  ContactInfo,
  CurrentResidenceDetail,
  DurationAtResidence,
  LandlordDetails,
  Military,
  MilitaryService,
  PersonalInfo,
  PhoneNumber,
  ResidenceDetail,
} from '@rocket-logic/rl-xp-bff-models';
import { Controlify, Formify } from '../../../util/form-utility-types';

export type ClientForm = Formify<ClientOmit> & ClientOverrides;
export type ClientControls = Controlify<ClientOmit> & ClientOverrides;
type ClientOmit = Omit<
  Client,
  'personalInformation' | 'contactInformation' | 'residenceInformation' | 'military'
>;
type ClientOverrides = {
  personalInformation: PersonalInfoGroup;
  contactInformation: FormGroup<ContactInfoControls>;
  residenceInformation: FormGroup<ResidenceInfoControls>;
  military: FormGroup<MilitaryControls>;
};

export type PersonalInfoForm = Formify<PersonalInfo & { confirmSSN?: string }>;
export type PersonalInfoGroup = FormGroup<Controlify<PersonalInfo & { confirmSSN?: string }>>;

export type ContactInfoForm = Formify<ContactInfoOmit> & ContactInfoOverrides;
export type ContactInfoControls = Controlify<ContactInfoOmit> & ContactInfoOverrides;
type ContactInfoOmit = Omit<ContactInfo, 'mailingAddress' | 'phoneNumbers'>;
type ContactInfoOverrides = {
  mailingAddress: FormGroup<Controlify<Address>>;
  phoneNumbers: FormArray<PhoneNumberGroup>;
};
export type PhoneNumberGroup = FormGroup<Controlify<PhoneNumber>>;

export type ResidenceInfoControls = {
  currentResidence: FormGroup<CurrentResidenceControls>;
  formerResidences: FormArray<FormGroup<ResidenceDetailControls>>;
};
export type CurrentResidenceForm = Formify<ResidenceDetailOmit & CurrentResidenceOmit> &
  ResidenceDetailOverrides;
export type CurrentResidenceControls = Controlify<ResidenceDetailOmit & CurrentResidenceOmit> &
  ResidenceDetailOverrides;
type CurrentResidenceOmit = Omit<CurrentResidenceDetail, keyof ResidenceDetail>;
export type ResidenceDetailForm = Formify<ResidenceDetailOmit> & ResidenceDetailOverrides;
export type ResidenceDetailControls = Controlify<ResidenceDetailOmit> & ResidenceDetailOverrides;
type ResidenceDetailOmit = Omit<
  ResidenceDetail,
  'address' | 'landlordDetails' | 'durationAtResidence'
>;
type ResidenceDetailOverrides = {
  address: FormGroup<Controlify<Address>>;
  landlordDetails: FormGroup<LandlordDetailsControls>;
  durationAtResidence?: DurationAtResidenceGroup;
};
export type LandlordDetailsForm = Formify<LandlordDetailsOmit> & LandlordDetailsOverrides;
export type LandlordDetailsControls = Controlify<LandlordDetailsOmit> & LandlordDetailsOverrides;
type LandlordDetailsOmit = Omit<LandlordDetails, 'address' | 'phoneNumbers'>;
type LandlordDetailsOverrides = {
  address: FormGroup<Controlify<Address>>;
  phoneNumbers: FormArray<PhoneNumberGroup>;
};

export type DurationAtResidenceGroup = FormGroup<Controlify<DurationAtResidence>>;

export type MilitaryForm = Formify<MilitaryOmit> & MilitaryOverrides;
export type MilitaryControls = Controlify<MilitaryOmit> & MilitaryOverrides;
type MilitaryOmit = Omit<Military, 'militaryServices'>;
type MilitaryOverrides = {
  militaryServices: FormArray<MilitaryServiceGroup>;
};
export type MilitaryServiceGroup = FormGroup<Controlify<MilitaryService>>;
