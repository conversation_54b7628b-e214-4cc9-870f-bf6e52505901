import { DestroyRef, Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormArray, FormControl, FormGroup } from '@angular/forms';
import { Address, Client } from '@rocket-logic/rl-xp-bff-models';
import { SubjectProperty } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/subject-property';
import {
  EMPTY,
  Observable,
  combineLatest,
  distinctUntilChanged,
  exhaustMap,
  filter,
  map,
  pairwise,
  startWith,
  switchMap,
  take,
  takeUntil,
} from 'rxjs';
import { areAddressesEqual } from '../../../util/address-equal';
import { rawValueChanges$ } from '../../../util/raw-value-changes';
import { removeEmptyValues } from '../../../util/remove-empty-values';
import { updateSubjectPropertyForm } from '../../../util/update-subject-property-form';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { SubjectPropertyFormRef } from '../subject-property-state/subject-property-form.service';
import { SubjectPropertyStateService } from '../subject-property-state/subject-property-state.service';
import { ClientControls } from './form-types';

@Injectable()
export class UseAsSubjectHandlerService {
  private subjectPropertyFormRef = inject(SubjectPropertyFormRef);
  private subjectPropertyStateService = inject(SubjectPropertyStateService);
  private isLoanEditingDisabled$ = inject(LoanEditingState).isLoanEditingDisabled$;
  private subjectPropertyAddress = this.subjectPropertyFormRef.subjectPropertyForm.controls.address;
  private destroyRef = inject(DestroyRef);

  useAsSubjectDropdownControl = new FormControl<Client | null>(null);

  public addUseAsSubjectListener(
    clientsFormArray: FormArray<FormGroup<ClientControls>>,
    reset$: Observable<Client[]>,
  ) {
    this.addSetClientStateListener(clientsFormArray);
    this.addSyncSubjectPropertyListener(clientsFormArray);
    this.addResetSubjectPropertyListener();
    this.addInitUseAsSubjectPropListener(clientsFormArray, reset$);
  }

  /**
   * Listens for `useAsSubjectDropdownControl` changes and updates the `useAsSubjectProperty` control on the matching client form.
   * @param clientsFormArray
   */
  private addSetClientStateListener(clientsFormArray: FormArray<FormGroup<ClientControls>>) {
    this.useAsSubjectDropdownControl.valueChanges
      .pipe(distinctUntilChanged(), takeUntilDestroyed(this.destroyRef))
      .subscribe((clientToUseAsSubjProp) => {
        clientsFormArray.controls.forEach((client) => {
          const useAsSubjectControl =
            client.controls.residenceInformation.controls.currentResidence.controls
              .useAsSubjectProperty;
          const sameAsClientId =
            client.value.residenceInformation?.currentResidence?.sameAsClientId;
          const isSelected =
            client.value.id === clientToUseAsSubjProp?.id ||
            (sameAsClientId && sameAsClientId === clientToUseAsSubjProp?.id);
          if (
            (!isSelected && useAsSubjectControl.value) ||
            (isSelected && !useAsSubjectControl.value)
          ) {
            useAsSubjectControl.markAsDirty();
            useAsSubjectControl.setValue(isSelected ? true : null);
          }
        });
      });
  }

  /**
   * Listens for `useAsSubjectDropdownControl` changes and `clientsFormArray` changes to update the subject property form when the selected client's address changes.
   * @param clientsFormArray
   */
  private addSyncSubjectPropertyListener(clientsFormArray: FormArray<FormGroup<ClientControls>>) {
    combineLatest([
      this.useAsSubjectDropdownControl.valueChanges.pipe(
        startWith(this.useAsSubjectDropdownControl.value),
      ),
      clientsFormArray.valueChanges.pipe(
        startWith(clientsFormArray.value),
        map(() => clientsFormArray.controls),
      ),
    ])
      .pipe(
        map(([clientToUseAsSubjProp, clientForms]) =>
          clientForms.find((clientForm) => clientForm.value.id === clientToUseAsSubjProp?.id),
        ),
        distinctUntilChanged(),
        switchMap((clientForm) => {
          if (!clientForm) {
            return EMPTY;
          }

          const addressForm =
            clientForm.controls.residenceInformation.controls.currentResidence.controls.address;
          return rawValueChanges$(addressForm);
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((address) => {
        const equalAddresses = areAddressesEqual(
          address,
          this.subjectPropertyAddress.getRawValue() ?? {},
        );

        if (!equalAddresses) {
          updateSubjectPropertyForm(
            address as Address,
            this.subjectPropertyFormRef.subjectPropertyForm,
          );
        }
      });
  }

  /**
   * Listens for `useAsSubjectDropdownControl` changes and resets the subject property form when the selected client is deselected.
   */
  private addResetSubjectPropertyListener() {
    this.useAsSubjectDropdownControl.valueChanges
      .pipe(
        pairwise(),
        takeUntil(this.isLoanEditingDisabled$.pipe(filter((deactivated) => deactivated === true))),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(([prev, next]) => {
        if (prev && !next) {
          this.subjectPropertyAddress.reset();
        }
      });
  }

  /**
   * Listens for `reset$` and initializes the `useAsSubjectDropdownControl` with the client that has the same address as the subject property from the state service.
   * @param clientsFormArray
   * @param reset$
   */
  private addInitUseAsSubjectPropListener(
    clientsFormArray: FormArray<FormGroup<ClientControls>>,
    reset$: Observable<Client[]>,
  ) {
    reset$
      .pipe(
        exhaustMap(() =>
          this.subjectPropertyStateService.state$.pipe(
            map((state) => state.data),
            filter((subjectProp): subjectProp is SubjectProperty => !!subjectProp),
            take(1),
          ),
        ),
        map((subjectPropState, index) =>
          index === 0
            ? subjectPropState
            : (this.subjectPropertyFormRef.subjectPropertyForm.getRawValue() as SubjectProperty),
        ),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((subjectProp) => this.initUseAsSubjectProp(clientsFormArray, subjectProp));
  }

  private initUseAsSubjectProp(
    clientsFormArray: FormArray<FormGroup<ClientControls>>,
    subjectProp?: SubjectProperty,
  ) {
    const clonedSubjPropAddress = { ...subjectProp?.address };
    const hasSubjectPropertyAddress =
      clonedSubjPropAddress && Object.keys(removeEmptyValues(clonedSubjPropAddress)).length > 0;
    if (
      !hasSubjectPropertyAddress ||
      clientsFormArray.controls.some(
        (clientForm) =>
          clientForm.value.residenceInformation?.currentResidence?.useAsSubjectProperty,
      )
    ) {
      // We already have some client hooked up to the subject prop or there is no subject address
      return;
    }

    let useAsSubjectClient: Client | null = null;
    clientsFormArray.controls.forEach((clientForm) => {
      const currentResidenceControl =
        clientForm.controls.residenceInformation.controls.currentResidence;
      const sameAsClientId = currentResidenceControl.value.sameAsClientId;

      if (!useAsSubjectClient && currentResidenceControl.value.address) {
        const useAsSubjectProp = areAddressesEqual(
          currentResidenceControl.value.address,
          clonedSubjPropAddress,
        );

        if (useAsSubjectProp) {
          currentResidenceControl.patchValue({ useAsSubjectProperty: true }, { emitEvent: false });
        }

        if (useAsSubjectProp && !sameAsClientId) {
          useAsSubjectClient = clientForm.getRawValue() as Client;
        }
      }

      if (useAsSubjectClient && sameAsClientId && useAsSubjectClient.id === sameAsClientId) {
        currentResidenceControl.patchValue({ useAsSubjectProperty: true }, { emitEvent: false });
      }
    });

    if (useAsSubjectClient) {
      this.useAsSubjectDropdownControl.setValue(useAsSubjectClient);
    }
  }
}
