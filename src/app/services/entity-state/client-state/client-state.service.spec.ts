import { TestBed } from '@angular/core/testing';

import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { MockBuilder } from 'ng-mocks';
import { DataProviderService } from '../../data-provider/data-provider.service';
import { LeadService } from '../../lead/lead.service';
import { LoanApplicationStateService } from '../../loan-application-state/loan-application-state.service';
import { LoanIdService } from '../../loan-id/loan-id.service';
import { OwnedPropertyStateService } from '../owned-property-state/owned-property-state.service';
import { ClientStateService } from './client-state.service';

describe('ClientStateService', () => {
  let service: ClientStateService;

  beforeEach(() =>
    MockBuilder(ClientStateService)
      .mock(LoanIdService)
      .mock(DataProviderService)
      .mock(SplunkLoggerService)
      .mock(LeadService)
      .mock(LoanApplicationStateService)
      .mock(OwnedPropertyStateService),
  );
  beforeEach(() => {
    service = TestBed.inject(ClientStateService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
