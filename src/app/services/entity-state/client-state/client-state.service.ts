import { Injectable, computed, inject } from '@angular/core';
import { Client, Deactivate, MaritalStatus, ResidencyType } from '@rocket-logic/rl-xp-bff-models';
import { EMPTY, Observable, finalize, map, of, switchMap, take, tap } from 'rxjs';
import { RecentLoansService } from '../../recent-loans/recent-loans.service';
import { AbstractCollectionEntityStateService } from '../abstract-collection-entity-state.service';
import { EntityStateName } from '../abstract-entity-state.service';
import { OwnedPropertyStateService } from '../owned-property-state/owned-property-state.service';

@Injectable()
export class ClientStateService extends AbstractCollectionEntityStateService<
  Client,
  { client: Client; clientKey: string }
> {
  override identifier: keyof Client = 'id';
  name = EntityStateName.Client;
  ownedPropertyStateService = inject(OwnedPropertyStateService);
  recentLoanService = inject(RecentLoansService);
  override stateValues = computed(() =>
    Array.from(this.state()?.data?.values() ?? [])
      .slice()
      .sort((a, b) => {
        if (a.isPrimaryBorrower) return -1;
        if (b.isPrimaryBorrower) return 1;
        return 0;
      }),
  );

  readonly showVaResequencingMessage = computed(() =>
    this.stateValues().some(
      (client) =>
        client.isPrimaryBorrower &&
        !client.military?.hasMilitaryService &&
        this.nonPrimaryClientsWithVa()?.length,
    ),
  );

  readonly nonPrimaryClientsWithVa = computed(
    () =>
      this.stateValues().filter(
        (client) => !client?.isPrimaryBorrower && client?.military?.hasMilitaryService,
      ) ?? [],
  );

  protected override getDependentEntityState$(loanId: string): Observable<Map<string, Client>> {
    return this.dataProvider
      .getClients$(loanId)
      .pipe(map((clients) => this.generateState(clients)));
  }

  protected override updateEntityState$(
    loanId: string,
    state: { client: Client; clientKey: string },
  ): Observable<Map<string, Client>> {
    return this.dataProvider.updateClient$(loanId, state.client).pipe(
      tap((updatedClient) => {
        if (
          updatedClient.residenceInformation?.currentResidence?.residencyType ===
            ResidencyType.Own ||
          this.state()?.data?.get(state.clientKey)?.residenceInformation?.currentResidence
            ?.residencyType === ResidencyType.Own
        ) {
          this.ownedPropertyStateService.refreshState();
        }
      }),
      map((updatedClient) => this.updateStateMap(updatedClient, state.clientKey)),
    );
  }
  public deleteClient$(clientKey: string): Observable<void> {
    const client = this.state()?.data?.get(clientKey);
    if (!client?.id) {
      return of();
    }

    return this.loanIdService.loanId$.pipe(
      take(1),
      switchMap((loanId) => {
        if (!loanId) throw new Error(`Trying to delete client with id ${client} with no loan id`);

        if (client.isPrimaryBorrower) {
          const newPrimaryClientId = this.stateValues().find(
            (client) => !client.isPrimaryBorrower,
          )?.id;

          if (!newPrimaryClientId) {
            throw Error(`No new client ID found when deactivating client: ${client.id}`);
          }

          return this.makePrimary$(newPrimaryClientId).pipe(
            switchMap(() => this.dataProvider.deleteClient$(loanId, client.id!)),
          );
        } else {
          return this.dataProvider.deleteClient$(loanId, client.id!);
        }
      }),
      tap(() => this.manualStateSubject.next({ data: this.deleteFromStateMap(clientKey) })),
    );
  }

  public deactivateClient$(clientKey: string, deactivationRequest: Deactivate) {
    const client = this.state()?.data?.get(clientKey);
    if (!client?.id) {
      return of();
    }

    return this.loanIdService.loanId$.pipe(
      take(1),
      switchMap((loanId) => {
        if (!loanId) {
          throw new Error(`Trying to deactivate client with id ${client} with no loan id`);
        }

        if (client.isPrimaryBorrower) {
          const newPrimaryClientId = this.stateValues().find(
            (client) => !client.isPrimaryBorrower,
          )?.id;

          if (!newPrimaryClientId) {
            throw Error(`No new client ID found when deactivating client: ${client.id}`);
          }

          deactivationRequest.newPrimaryRocketLogicClientId = newPrimaryClientId;
          return this.dataProvider.deactivateClient$(loanId, client.id!, deactivationRequest);
        } else {
          return this.dataProvider.deactivateClient$(loanId, client.id!, deactivationRequest);
        }
      }),
      tap(() => {
        const primaryClientId = deactivationRequest?.newPrimaryRocketLogicClientId
          ? deactivationRequest?.newPrimaryRocketLogicClientId
          : this.stateValues().find((client) => client.isPrimaryBorrower)?.id;

        const newMap = this.deepCopyState();
        const newState = this.swapPrimaryClients(newMap, primaryClientId!);
        this.removeSpouseId(newState, clientKey);
        newState.delete(clientKey);
        this.manualStateSubject.next({ data: newState });
      }),
    );
  }

  public patchReactivatedClientState(reactivatedClient: Client, spouseId?: string) {
    const clientKey = crypto.randomUUID();
    if (spouseId) {
      reactivatedClient.personalInformation!.spouseClientId = spouseId;
      reactivatedClient.personalInformation!.maritalStatus = MaritalStatus.Married;
    }

    const newMap = this.deepCopyState();
    newMap.set(clientKey, reactivatedClient);
    this.manualStateSubject.next({ data: newMap });
  }

  public updateRelationship$(clientId1: string, clientId2: string): Observable<void> {
    return this.loanIdService.loanId$.pipe(
      take(1),
      switchMap((loanId) => {
        if (!loanId)
          throw new Error(
            `Trying to update relationship for clients ${clientId1}, ${clientId2} with no loan id`,
          );

        const client1 = this.findClientById(clientId1);
        const client2 = this.findClientById(clientId2);
        if (!client1 || !client2) {
          throw new Error(
            `Trying to update relationship with a client that doesn't exist. ${clientId1}, ${clientId2}`,
          );
        }
        if (
          client1.personalInformation?.spouseClientId === clientId2 &&
          client2.personalInformation?.spouseClientId === clientId1
        ) {
          // Relationship already exists, skip update
          return EMPTY;
        }

        this.manualStateSubject.next({ updating: true });
        return this.dataProvider
          .updateClientRelationship$(loanId, clientId1, clientId2)
          .pipe(finalize(() => this.manualStateSubject.next({ updating: false })));
      }),
      tap(() => this.handleClientsRelationshipUpdate(clientId1, clientId2)),
    );
  }

  public deleteRelationship$(clientId1: string, clientId2: string): Observable<void> {
    return this.loanIdService.loanId$.pipe(
      take(1),
      switchMap((loanId) => {
        if (!loanId)
          throw new Error(
            `Trying to delete relationship for clients ${clientId1}, ${clientId2} with no loan id`,
          );
        const client1 = this.findClientById(clientId1);
        const client2 = this.findClientById(clientId2);
        if (!client1 || !client2) {
          // Trying to delete a relationship with a client that was deleted, emit immediately to update state
          return of(undefined);
        }
        if (
          client1.personalInformation?.spouseClientId !== clientId2 &&
          client2.personalInformation?.spouseClientId !== clientId1
        ) {
          // Relationship doesn't exist, skip delete
          return EMPTY;
        }

        this.manualStateSubject.next({ updating: true });
        return this.dataProvider
          .deleteClientRelationship$(loanId, clientId1, clientId2)
          .pipe(finalize(() => this.manualStateSubject.next({ updating: false })));
      }),
      tap(() => this.handleClientsRelationshipUpdate(clientId1, clientId2, true)),
    );
  }

  public makePrimary$(clientId: string) {
    return this.loanIdService.loanId$.pipe(
      take(1),
      switchMap((loanId) => {
        if (!loanId) {
          throw new Error(`Trying to make client ${clientId} primary with no loan id`);
        }

        return this.dataProvider.makePrimary$(loanId, clientId);
      }),
      tap(() => {
        const newMap = this.deepCopyState();
        const newState = this.swapPrimaryClients(newMap, clientId);
        this.manualStateSubject.next({ data: newState });
      }),
    );
  }

  private swapPrimaryClients(clients: Map<string, Client>, clientId: string) {
    clients.forEach((client, _) => {
      if (client.id === clientId) {
        client.isPrimaryBorrower = true;
      } else {
        client.isPrimaryBorrower = false;
      }
    });

    return clients;
  }

  private removeSpouseId(clients: Map<string, Client>, clientKey: string) {
    const deactivatedClientId = clients.get(clientKey)?.id;
    if (deactivatedClientId != null) {
      clients.forEach((client, _) => {
        if (client.personalInformation?.spouseClientId === deactivatedClientId) {
          client.personalInformation!.spouseClientId = undefined;
        }
      });
    }
    return clients;
  }

  private handleClientsRelationshipUpdate(
    clientId1: string,
    clientId2: string,
    deleteRelationship = false,
  ) {
    const client1Key =
      Array.from(this.state()?.data?.entries() ?? []).find(
        ([_, client]) => client.id === clientId1,
      )?.[0] ?? '';

    const client2Key =
      Array.from(this.state()?.data?.entries() ?? []).find(
        ([_, client]) => client.id === clientId2,
      )?.[0] ?? '';

    const newState = this.deepCopyState();
    [
      { clientKey: client1Key, spouseId: clientId2 },
      { clientKey: client2Key, spouseId: clientId1 },
    ].forEach(({ clientKey, spouseId }) => {
      if (!clientKey) return;

      const existingClient = newState.get(clientKey);
      const existingSpouseId = existingClient?.personalInformation?.spouseClientId;

      if (!deleteRelationship && existingSpouseId) {
        const existingSpouseKey =
          Array.from(this.state()?.data?.entries() ?? [])?.find(
            ([_, client]) => client.id === existingSpouseId,
          )?.[0] ?? '';

        if (existingSpouseKey) {
          let existingSpouse = newState.get(existingSpouseKey) ?? {};
          existingSpouse = this.handleRelationshipUpdate(existingSpouse, undefined);
          newState.set(existingSpouseKey, existingSpouse);
        }
      }

      let client = newState.get(clientKey) ?? {};
      client = this.handleRelationshipUpdate(client, deleteRelationship ? undefined : spouseId);
      newState.set(clientKey, client);
    });

    this.manualStateSubject.next({ data: newState });
  }

  private handleRelationshipUpdate(client: Client, spouseClientId: string | undefined): Client {
    return {
      ...client,
      personalInformation: {
        ...client.personalInformation,
        spouseClientId,
      },
    };
  }

  private findClientById(clientId: string): Client | undefined {
    return this.stateValues().find((client) => client.id === clientId);
  }
}
