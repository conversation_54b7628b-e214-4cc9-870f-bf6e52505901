import { Inject, Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import {
  Address,
  AddressValidationStatus,
  Client,
  MappedAddressValidation,
} from '@rocket-logic/rl-xp-bff-models';
import {
  EMPTY,
  Observable,
  catchError,
  concat,
  defer,
  exhaustMap,
  filter,
  iif,
  of,
  switchMap,
  takeWhile,
  tap,
} from 'rxjs';
import { AddressValidationDialogComponent } from '../../../address-validation-dialog/address-validation-dialog.component';
import { AvoService } from '../../../analytics/avo/avo.service';
import { areAddressesEqual } from '../../../util/address-equal';
import { removeEmptyValues } from '../../../util/remove-empty-values';
import { AddressDataService } from '../../address/address-data.service';
import { DataProviderService } from '../../data-provider/data-provider.service';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { EntityStateName } from '../abstract-entity-state.service';
import { DirtyState, UpdateHandlerService } from '../abstract-update-handler.service';
import { SubjectPropertyFormRef } from '../subject-property-state/subject-property-form.service';
import { ClientStateService } from './client-state.service';
import { ClientSubscriptionManager } from './client-subscription-manager.service';
import { ClientControls } from './form-types';
import { CLIENT_SAVE_TRIGGER } from './provide-client-state';

@Injectable()
export class ClientUpdateHandlerService extends UpdateHandlerService {
  clientSubManager = inject(ClientSubscriptionManager);
  stateService = inject(ClientStateService);
  dataProviderService = inject(DataProviderService);
  addressDataService = inject(AddressDataService);
  subjectPropertyFormRef = inject(SubjectPropertyFormRef);
  private dialog = inject(MatDialog);

  avoService = inject(AvoService);

  override name = EntityStateName.Client;

  private readonly residenceInfoPath = 'residenceInformation.currentResidence.address';

  constructor(@Inject(CLIENT_SAVE_TRIGGER) saveTrigger: SaveTrigger) {
    super(saveTrigger);
  }
  /**
   * Perform initial update for a client that exists in the form but not as a remote resource.
   * @param clientForm Form group representing the current client.
   * @param clientKey Key to the form map for the current client.
   */
  addClientCreateListener(clientForm: FormGroup<ClientControls>, clientKey: string) {
    const sub = clientForm.valueChanges
      .pipe(
        takeWhile((client) => !this.doesClientExistAsRemoteResource(client)),
        filter((client) => this.hasMinimumData(client)),
        exhaustMap(() => {
          const currentState = this.dirtyState()[clientKey];

          // An update will be performed with first/last name, so clear the dirty state for those paths.
          if (currentState) {
            this.clearDirtyPaths(clientKey, [
              'personalInformation.firstName',
              'personalInformation.lastName',
            ]);
          }

          const formValue = clientForm.getRawValue();
          return this.stateService
            .updateState({
              client: {
                personalInformation: {
                  firstName: formValue.personalInformation.firstName!,
                  lastName: formValue.personalInformation.lastName!,
                },
              },
              clientKey,
            })
            .pipe(
              tap(({ error }) => {
                // Restore previous dirty state on error.
                if (error && currentState) {
                  this.revertDirtyState(clientKey, currentState);
                }
              }),
            );
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.clientSubManager.addPersistentSubscription(clientKey, sub);
  }

  addClientUpdateListener(clientForm: FormGroup<ClientControls>, clientKey: string) {
    const sub = this.listenForDirtyState(clientKey.toString(), clientForm);
    this.clientSubManager.addSubscription(clientKey, sub);
  }

  protected override shouldHandleUpdate([_, { dirtyPaths, form }]: [string, DirtyState]): boolean {
    return (
      super.shouldHandleUpdate([_, { dirtyPaths, form }]) &&
      this.doesClientExistAsRemoteResource(form.value)
    );
  }

  protected override handleUpdates$(): Observable<any> {
    return concat(this.handleRelationships$(), super.handleUpdates$());
  }

  protected override handleUpdate$(
    identifier: string,
    state: DirtyState,
  ): Observable<{ error?: any }> {
    const clientAddress = state.form.get(this.residenceInfoPath)?.value;
    const sameAsClientId = (state.form as FormGroup<ClientControls>).controls.residenceInformation
      .controls.currentResidence.controls.sameAsClientId.value;

    const addressValidation$ = this.addressDataService.getAddressValidation$(clientAddress).pipe(
      catchError(() => {
        return of({
          originalAddress: clientAddress,
          status: AddressValidationStatus.NotFound,
        } as MappedAddressValidation);
      }),
      tap((response) => {
        if (response.status !== AddressValidationStatus.NotFound) {
          state.form
            .get(`${this.residenceInfoPath}.censusTractCode`)
            ?.setValue(response.responseAddress?.censusTractCode, { emitEvent: false });
          state.form
            .get(`${this.residenceInfoPath}.plus4`)
            ?.setValue(response.responseAddress?.plus4, { emitEvent: false });
        }
      }),
      switchMap((response) =>
        response.status === AddressValidationStatus.NotFound ||
        areAddressesEqual(response.originalAddress, response?.responseAddress ?? {})
          ? of(null)
          : this.dialog
              .open(AddressValidationDialogComponent, {
                data: [
                  { isSuggested: false, address: response.originalAddress },
                  { isSuggested: true, address: response?.responseAddress ?? {} },
                ],
                panelClass: 'rkt-Dialog',
                minWidth: '50%',
                minHeight: '50%',
                backdropClass: 'rkt-Backdrop',
              })
              .afterClosed(),
      ),
      switchMap((address: Address | null) => {
        const clientValue = state.form.getRawValue();

        if (address) {
          clientValue.residenceInformation.currentResidence.address = address;
        }

        return this.updateState$(clientValue, identifier);
      }),
    );

    return iif(
      () =>
        state.dirtyPaths.some((path) => path.includes(this.residenceInfoPath)) && !sameAsClientId,
      addressValidation$,
      defer(() => this.updateState$(state.form.getRawValue(), identifier)),
    );
  }

  private updateState$(state: any, clientKey: string) {
    if (
      state.personalInformation.firstName &&
      state.personalInformation.lastName &&
      (state.contactInformation.phoneNumbers.length > 0 ||
        state.contactInformation.emailAddresses.length > 0)
    ) {
      this.avoService.clientUpdate$.next(state);
    }

    return this.stateService.updateState({
      client: removeEmptyValues(state),
      clientKey,
    });
  }

  private hasMinimumData(client: {
    personalInformation?: { firstName?: string | null; lastName?: string | null } | null;
  }): boolean {
    return (
      !!client.personalInformation?.firstName?.trim() &&
      !!client.personalInformation?.lastName?.trim()
    );
  }

  private handleRelationships$(): Observable<any> {
    const dirtyStateValues = Object.values(this.dirtyState());
    const { deleteSpouse, updateSpouse } = dirtyStateValues.reduce(
      (acc, state) => {
        const spouseChanged = state.dirtyPaths.some(
          (path) => path === 'personalInformation.spouseClientId',
        );
        if (!spouseChanged) {
          return acc;
        }

        const spouseId = state.form.value.personalInformation?.spouseClientId;
        if (spouseId) {
          acc.updateSpouse.push(state.form.value);
        } else {
          acc.deleteSpouse.push(state.form.value);
        }

        return acc;
      },
      { deleteSpouse: [] as Client[], updateSpouse: [] as Client[] },
    );
    if (!deleteSpouse.length && !updateSpouse.length) {
      return EMPTY;
    }

    const deleteRelationships = this.getUniqueRelationships(deleteSpouse);
    const updateRelationships = this.getUniqueRelationships(updateSpouse);

    return concat(
      ...deleteRelationships.map(({ id1, id2 }) =>
        this.stateService.deleteRelationship$(id1, id2).pipe(
          catchError((err) => {
            this.logger.error(`Failed to delete relationship for ids: ${id1}, ${id2}`, err);
            return EMPTY;
          }),
        ),
      ),
      ...updateRelationships.map(({ id1, id2 }) =>
        this.stateService.updateRelationship$(id1, id2).pipe(
          catchError((err) => {
            this.logger.error(`Failed to update relationship for ids: ${id1}, ${id2}`, err);
            return EMPTY;
          }),
        ),
      ),
    );
  }

  private getUniqueRelationships(clients: Client[]): { id1: string; id2: string }[] {
    return clients.reduce(
      (acc, client) => {
        if (
          !acc.some(
            (relationship) => relationship.id1 === client.id || relationship.id2 === client.id,
          )
        ) {
          acc.push({ id1: client.id!, id2: this.getSpouseId(client) });
        }
        return acc;
      },
      [] as { id1: string; id2: string }[],
    );
  }

  private getSpouseId(client: Client): string {
    const spouseId = client.personalInformation?.spouseClientId;
    if (spouseId) {
      return spouseId;
    }

    return (
      this.stateService.stateValues().find((stateClient) => stateClient.id === client.id)
        ?.personalInformation?.spouseClientId ?? ''
    );
  }

  private doesClientExistAsRemoteResource(client: { id?: string | null }): boolean {
    return !!client.id;
  }
}
