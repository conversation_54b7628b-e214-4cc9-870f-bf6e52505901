import { TestBed } from '@angular/core/testing';

import { <PERSON><PERSON><PERSON>uilder } from 'ng-mocks';
import { NEVER } from "rxjs";
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { ClientSubscriptionManager } from './client-subscription-manager.service';
import { SameResidenceHandlerService } from './same-residence-handler.service';

describe('SameResidenceHandlerService', () => {
  let service: SameResidenceHandlerService;

  beforeEach(() =>
    MockBuilder(SameResidenceHandlerService)
      .mock(ClientSubscriptionManager)
      .mock(LoanEditingState, { isLoanEditingDisabled$: NEVER }),
    );
  beforeEach(() => {
    service = TestBed.inject(SameResidenceHandlerService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
