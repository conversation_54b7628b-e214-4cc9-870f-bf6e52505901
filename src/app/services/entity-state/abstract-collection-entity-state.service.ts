import { Injectable, Signal } from '@angular/core';
import { LoanDependentEntityStateService } from './abstract-dependent-entity-state.service';

@Injectable()
export abstract class AbstractCollectionEntityStateService<
  T,
  U,
> extends LoanDependentEntityStateService<Map<string, T>, U> {
  abstract identifier: keyof T;
  abstract stateValues: Signal<T[]>;

  protected generateState(values: T[]) {
    if (values?.length === 0) {
      return new Map<string, T>();
    }

    const currentState = this.deepCopyState();
    return this.matchKeyInState(values, currentState);
  }

  protected updateStateMap(updatedValue: T, key: string) {
    const newState = this.deepCopyState();
    newState.set(key, updatedValue);
    return newState;
  }

  protected deleteFromStateMap(key: string) {
    const newState = this.deepCopyState();
    newState.delete(key);
    return newState;
  }

  protected deepCopyState() {
    const newMap = new Map<string, T>();

    this.state()?.data?.forEach((value, key) => {
      newMap.set(key, { ...value });
    });

    return newMap;
  }

  private matchKeyInState(state: T[], stateMap: Map<string, T>) {
    const newState = new Map<string, T>();

    state.forEach((value) => {
      const existingEntry = Array.from(stateMap.entries()).find(
        ([, currentValue]) => currentValue[this.identifier] === value[this.identifier],
      );

      const key = existingEntry ? existingEntry[0] : crypto.randomUUID();
      newState.set(key, value);
    });

    return newState;
  }
}
