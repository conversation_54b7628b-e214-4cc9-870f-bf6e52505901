import { TestBed } from '@angular/core/testing';

import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { DataProviderService } from '../../data-provider/data-provider.service';
import { LeadService } from '../../lead/lead.service';
import { LoanApplicationStateService } from '../../loan-application-state/loan-application-state.service';
import { LoanIdService } from '../../loan-id/loan-id.service';
import { SubjectPropertyStateService } from '../subject-property-state/subject-property-state.service';
import { OwnedPropertyStateService } from './owned-property-state.service';

describe('OwnedPropertyStateService', () => {
  let service: OwnedPropertyStateService;

  beforeEach(() =>
    MockBuilder(OwnedPropertyStateService)
      .mock(SubjectPropertyStateService, { state$: NEVER })
      .mock(LoanIdService, { loanId$: NEVER })
      .mock(DataProviderService)
      .mock(SplunkLoggerService)
      .mock(LeadService)
      .mock(LoanApplicationStateService),
  );
  beforeEach(() => {
    service = TestBed.inject(OwnedPropertyStateService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
