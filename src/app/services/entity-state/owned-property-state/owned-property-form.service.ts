import { computed, inject, Injectable, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { PaymentAmount } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/payment-amount';
import { distinctUntilChanged, map, withLatestFrom } from 'rxjs';
import { buildAddressForm } from '../../../util/build-address-form';
import { Controlify, Formify } from '../../../util/form-utility-types';
import { resetNonDirtyControls } from '../../../util/reset-non-dirty-controls';
import { AbstractCollectionFormService } from '../abstract-collection-form.service';
import { LiabilityFormRef } from '../liability-state/liability-form.service';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { OwnedPropertyControls, OwnedPropertyForm, OwnedPropertyGroup } from './form-types';
import { OwnedPropertyStateService } from './owned-property-state.service';
import { OwnedPropertySubscriptionManager } from './owned-property-subscription-manager.service';
import { OwnedPropertyUpdateHandlerService } from './owned-property-update-handler.service';
import { OWNED_PROPERTY_AUTO_SAVE_TRIGGER } from './provide-owned-property-state';

@Injectable()
export class OwnedPropertyFormService extends AbstractCollectionFormService<OwnedPropertyControls> {
  private ownedPropertyStateService = inject(OwnedPropertyStateService);
  private updateHandler = inject(OwnedPropertyUpdateHandlerService);
  private autoSaveTrigger = inject(OWNED_PROPERTY_AUTO_SAVE_TRIGGER);
  private ownedPropertySubManager = inject(OwnedPropertySubscriptionManager);
  private isLoanEditingDisabled$ = inject(LoanEditingState).isLoanEditingDisabled$;
  private liabilityFormRef = inject(LiabilityFormRef);

  override entityFormMap = signal(new Map<string, OwnedPropertyGroup>());
  override entityValues = computed(() => Array.from(this.entityFormMap().values()));

  constructor() {
    super();

    this.autoSaveTrigger.registerControls(this.entityFormArray);

    this.ownedPropertyStateService.state$
      .pipe(
        map((state) => state.data),
        distinctUntilChanged(),
        withLatestFrom(this.isLoanEditingDisabled$),
        takeUntilDestroyed(),
      )
      .subscribe(([ownedPropertyMap, isLoanEditingDisabled]) => {
        const newFormKeys: string[] = [];
        ownedPropertyMap?.forEach((ownedProperty, ownedPropKey) => {
          let ownedPropertyForm = this.entityFormMap().get(ownedPropKey);
          if (!ownedPropertyForm) {
            ownedPropertyForm = this.buildOwnedPropertyForm();
            newFormKeys.push(ownedPropKey);
            this.updateState(ownedPropKey, ownedPropertyForm);
            this.entityFormArray.push(ownedPropertyForm);
          }

          if (ownedProperty) {
            resetNonDirtyControls(ownedProperty, ownedPropertyForm);
          }

          if (ownedProperty?.isSubjectProperty || ownedProperty?.isCurrentResidence) {
            this.disableProperty(ownedPropertyForm);
          } else {
            this.enableProperty(ownedPropertyForm);
          }
        });

        if (ownedPropertyMap?.size != null) {
          if (ownedPropertyMap.size < this.entityValues().length) {
            const currentKeys = new Set(this.entityFormMap().keys());
            const newKeys = new Set(ownedPropertyMap.keys());
            const removedKeys = [...currentKeys].filter((key) => !newKeys.has(key));

            removedKeys.forEach((removedFormKey) => {
              this.deleteState(removedFormKey);
              this.entityFormArray.removeAt(
                this.entityFormArray.controls.indexOf(this.entityFormMap().get(removedFormKey)!),
              );
              this.ownedPropertySubManager.removeSubscriptions(removedFormKey);
              this.updateHandler.onDeleteOwnedProperty(removedFormKey);
            });
          }
        }

        newFormKeys.forEach((key) => {
          this.updateHandler.addOwnedPropertyUpdateListener(this.entityFormMap().get(key)!, key);
        });

        if (isLoanEditingDisabled) {
          this.entityFormArray.disable();
        }
      });

    this.isLoanEditingDisabled$.pipe(takeUntilDestroyed()).subscribe((isInactive:boolean) => {
      if (isInactive) {
        this.entityFormArray.disable();
      }
    });
  }

  public getOwnedPropertyForm(ownedPropertyId: string): OwnedPropertyGroup | undefined {
    return this.entityValues().find((control) => control.value.id === ownedPropertyId);
  }

  public addOwnedProperty() {
    const ownedPropertyForm = this.buildOwnedPropertyForm();
    const ownedPropKey = crypto.randomUUID();

    this.updateState(ownedPropKey, ownedPropertyForm);
    this.entityFormArray.push(ownedPropertyForm);
    this.updateHandler.addOwnedPropertyUpdateListener(ownedPropertyForm, ownedPropKey);
  }

  public deleteOwnedProperty(ownedPropKey: string) {
    const ownedPropForm = this.entityFormMap().get(ownedPropKey);

    if (ownedPropForm?.value.id) {
      // removing form state/subscriptions is handled after state emits
      this.ownedPropertyStateService
        .deleteOwnedProperty$(ownedPropKey)
        .subscribe(() => this.unassociatedLiens(ownedPropForm.value.id!));
    } else {
      this.entityFormArray.removeAt(
        this.entityFormArray.controls.indexOf(this.entityFormMap().get(ownedPropKey)!),
      );
      this.ownedPropertySubManager.removeSubscriptions(ownedPropKey);
      this.updateHandler.onDeleteOwnedProperty(ownedPropKey);
      this.deleteState(ownedPropKey);
    }
  }

  private unassociatedLiens(ownedPropertyId: string) {
    this.liabilityFormRef.formArray.controls.forEach((liabilityForm) => {
      const liability = liabilityForm.getRawValue();
      if (liability.ownedPropertyId === ownedPropertyId) {
        const ownedPropertyIdControl = liabilityForm.controls.ownedPropertyId;
        ownedPropertyIdControl.markAsDirty();
        ownedPropertyIdControl.patchValue(null);
      }
    });
  }

  private buildOwnedPropertyForm(): OwnedPropertyGroup {
    return this.formBuilder.group<OwnedPropertyForm>({
      occupancyType: [null],
      estimatedPropertyValue: [null],
      isManufactured: [null],
      address: buildAddressForm(this.formBuilder, {
        requireStreet: true,
        requireCity: true,
        requireState: true,
        requireZipCode: true,
        allowPOBoxes: false,
      }),
      id: [null],
      isSubjectProperty: [null],
      isCurrentResidence: [null],
      isOwnedFreeAndClear: [null],
      propertyType: [null],
      ownerClientIds: [[]],
      dispositionStatus: [null],
      liabilityIds: [[]],
      incomeIds: [[]],
      assetIds: [[]],
      insurancePayment: this.buildPaymentForm(),
      taxPayment: this.buildPaymentForm(),
      hoaPayment: this.buildPaymentForm(),
      pendingSaleClosingDate: [""],
    });
  }

  private buildPaymentForm(): FormGroup<Controlify<PaymentAmount>> {
    return this.formBuilder.group<Formify<PaymentAmount>>({
      amount: [null],
      frequency: [null],
    });
  }

  private disableProperty(ownedPropertyForm: OwnedPropertyGroup) {
    ownedPropertyForm.controls.address.disable();
    ownedPropertyForm.controls.propertyType.disable();
    ownedPropertyForm.controls.occupancyType.disable();
  }

  private enableProperty(ownedPropertyForm: OwnedPropertyGroup) {
    ownedPropertyForm.controls.address.enable();
    ownedPropertyForm.controls.propertyType.enable();
    ownedPropertyForm.controls.occupancyType.enable();
  }
}
