import { FormGroup } from '@angular/forms';
import { Address, OwnedProperty } from '@rocket-logic/rl-xp-bff-models';
import { PaymentAmount } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/payment-amount';
import { Controlify, Formify } from '../../../util/form-utility-types';

type OwnedPropertyOmit = Omit<
  OwnedProperty,
  'insurancePayment' | 'taxPayment' | 'hoaPayment' | 'address'
>;
type OwnedPropertyOverrides = {
  insurancePayment: FormGroup<Controlify<PaymentAmount>>;
  taxPayment: FormGroup<Controlify<PaymentAmount>>;
  hoaPayment: FormGroup<Controlify<PaymentAmount>>;
  address: FormGroup<Controlify<Address>>;
};
export type OwnedPropertyForm = Formify<OwnedPropertyOmit> & OwnedPropertyOverrides;
export type OwnedPropertyGroup = FormGroup<OwnedPropertyControls>;
export type OwnedPropertyControls = Controlify<OwnedPropertyOmit> & OwnedPropertyOverrides;
