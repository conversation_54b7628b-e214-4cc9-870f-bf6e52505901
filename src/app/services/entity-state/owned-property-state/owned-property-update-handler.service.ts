import { Inject, Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { removeEmptyValues } from '../../../util/remove-empty-values';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { EntityStateName } from '../abstract-entity-state.service';
import { DirtyState, UpdateHandlerService } from '../abstract-update-handler.service';
import { OwnedPropertyGroup } from './form-types';
import { OwnedPropertyStateService } from './owned-property-state.service';
import { OwnedPropertySubscriptionManager } from './owned-property-subscription-manager.service';
import { OWNED_PROPERTY_SAVE_TRIGGER } from './provide-owned-property-state';

@Injectable()
export class OwnedPropertyUpdateHandlerService extends UpdateHandlerService {
  stateService = inject(OwnedPropertyStateService);
  private ownedPropertySubManager = inject(OwnedPropertySubscriptionManager);

  override name = EntityStateName.OwnedProperty;

  constructor(@Inject(OWNED_PROPERTY_SAVE_TRIGGER) saveTrigger: SaveTrigger) {
    super(saveTrigger);
  }

  public addOwnedPropertyUpdateListener(form: OwnedPropertyGroup, ownedPropKey: string) {
    const sub = this.listenForDirtyState(ownedPropKey, form);
    this.ownedPropertySubManager.addSubscription(ownedPropKey, sub);
  }

  public onDeleteOwnedProperty(ownedPropKey: string) {
    this.removeDirtyState(ownedPropKey);
  }

  protected override handleUpdate$(
    identifier: string,
    state: DirtyState,
  ): Observable<{ error?: any }> {
    return this.stateService.updateState({
      ownedPropertyKey: identifier,
      property: removeEmptyValues(state.form.getRawValue()),
    });
  }
}
