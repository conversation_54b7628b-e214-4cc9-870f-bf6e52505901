import { computed, Injectable } from '@angular/core';
import { OwnedProperty } from '@rocket-logic/rl-xp-bff-models';
import { map, of, switchMap, tap, timer } from 'rxjs';
import { AbstractCollectionEntityStateService } from '../abstract-collection-entity-state.service';
import { EntityStateName } from '../abstract-entity-state.service';

@Injectable()
export class OwnedPropertyStateService extends AbstractCollectionEntityStateService<
  OwnedProperty,
  { ownedPropertyKey: string; property: OwnedProperty }
> {
  name = EntityStateName.OwnedProperty;
  override identifier: keyof OwnedProperty = 'id';
  override stateValues = computed(() => Array.from(this.state()?.data?.values() ?? []));

  refreshWithDelay(delayInMs: number) {
    timer(delayInMs).subscribe(() => this.refreshState());
  }

  protected override getDependentEntityState$(loanId: string) {
    return this.dataProvider
      .getOwnedProperties$(loanId)
      .pipe(map((ownedProps) => this.generateState(ownedProps)));
  }

  protected override updateEntityState$(
    loanId: string,
    state: { ownedPropertyKey: string; property: OwnedProperty },
  ) {
    return this.dataProvider
      .updateOwnedProperty$(loanId, state.property)
      .pipe(map((updatedProperty) => this.updateStateMap(updatedProperty, state.ownedPropertyKey)));
  }

  public deleteOwnedProperty$(ownedPropertyKey: string) {
    const ownedProp = this.state()?.data?.get(ownedPropertyKey);
    if (!ownedProp?.id) {
      this.logger.info('No id found for owned property', ownedProp);
      return of();
    }

    return this.loanIdService.loanId$.pipe(
      switchMap((loanId) => {
        if (!loanId) {
          throw new Error(
            `Trying to delete owned property with id ${ownedProp.id} with no loan id`,
          );
        }

        return this.dataProvider.deleteOwnedProperty$(loanId, ownedProp.id);
      }),
      tap(() => {
        this.manualStateSubject.next({ data: this.deleteFromStateMap(ownedPropertyKey) });
      }),
    );
  }
}
