import { InjectionToken, Provider } from '@angular/core';
import { AutoSaveTriggerService } from '../../save-trigger/auto-save.service';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { STATE_SERVICES } from '../abstract-entity-state.service';
import { UPDATE_HANDLERS } from '../abstract-update-handler.service';
import { OwnedPropertyFormService } from './owned-property-form.service';
import { OwnedPropertyStateService } from './owned-property-state.service';
import { OwnedPropertySubscriptionManager } from './owned-property-subscription-manager.service';
import { OwnedPropertyUpdateHandlerService } from './owned-property-update-handler.service';

export const OWNED_PROPERTY_SAVE_TRIGGER = new InjectionToken<SaveTrigger>(
  'OWNED_PROPERTY_SAVE_TRIGGER',
);
export const OWNED_PROPERTY_AUTO_SAVE_TRIGGER = new InjectionToken<AutoSaveTriggerService>(
  'OWNED_PROPERTY_AUTO_SAVE_TRIGGER',
);

export function provideOwnedPropertyState(): Provider[] {
  return [
    OwnedPropertyStateService,
    OwnedPropertyFormService,
    OwnedPropertyUpdateHandlerService,
    OwnedPropertySubscriptionManager,
    { provide: OWNED_PROPERTY_AUTO_SAVE_TRIGGER, useClass: AutoSaveTriggerService },
    { provide: OWNED_PROPERTY_SAVE_TRIGGER, useExisting: OWNED_PROPERTY_AUTO_SAVE_TRIGGER },
    { provide: STATE_SERVICES, useExisting: OwnedPropertyStateService, multi: true },
    { provide: UPDATE_HANDLERS, useExisting: OwnedPropertyUpdateHandlerService, multi: true },
  ];
}
