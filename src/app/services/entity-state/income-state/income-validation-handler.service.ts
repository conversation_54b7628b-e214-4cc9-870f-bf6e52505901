import { DestroyRef, Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AbstractControl, Validators } from '@angular/forms';
import {
  EmploymentStatus,
  Income,
  isEmploymentIncome,
  isSelfEmploymentIncome,
} from '@rocket-logic/rl-xp-bff-models';
import { distinctUntilChanged, startWith } from 'rxjs';
import { ValidationHandlerService } from '../abstract-validation-handler.service';
import { AllIncomeGroup } from './form-types';
import { IncomeSubscriptionManager } from './income-subscription-manager.service';

@Injectable()
export class IncomeValidationHandlerService extends ValidationHandlerService {
  subManager = inject(IncomeSubscriptionManager);
  destroyRef = inject(DestroyRef);

  private readonly EMPLOYMENT_REQUIRED_PATHS = [
    'employment.employmentStartDate',
    'employment.employerName',
    'employment.jobTitle',
  ];
  private readonly NON_EMPLOYMENT_REQUIRED_PATHS = [];
  protected readonly VARIABLE_REQUIRED_PATHS = [
    ...this.EMPLOYMENT_REQUIRED_PATHS,
    ...this.NON_EMPLOYMENT_REQUIRED_PATHS,
  ];

  private readonly PREVIOUS_EMPLOYMENT_PATHS = ['base.clientReportedAmount', 'base.frequency'];
  private readonly PREVIOUS_EMPLOYMENT_REQUIRED_PATHS = [
    'employment.employerName',
    'employment.jobTitle',
    'employment.employmentStartDate',
    'employment.employmentEndDate',
  ];

  addIncomeValidationListener(incomeForm: AllIncomeGroup, clientId: string, incomeId: string) {
    const incomeTypeControl = incomeForm.get('incomeType')!;
    const sub = incomeTypeControl.valueChanges
      .pipe(
        startWith(incomeTypeControl.value),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((incomeType) => {
        if (incomeType) {
          this.setIncomeValidators(incomeForm);
        }
      });

    const employmentEndDateControl = incomeForm.controls.employment.controls.employmentStatus;
    const sub2 = employmentEndDateControl.valueChanges
      .pipe(
        startWith(employmentEndDateControl.value),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((endDate) => {
        incomeForm.controls.employment.controls.isExpectedToContinue.patchValue(!!endDate);
        this.setIncomeValidators(incomeForm);
      });

    sub.add(sub2);
    this.subManager.addIncomeSubscription(clientId, incomeId, sub);
  }

  private setIncomeValidators(incomeForm: AllIncomeGroup) {
    const income = incomeForm.getRawValue() as Income;
    this.removeRequiredValidators(incomeForm, this.VARIABLE_REQUIRED_PATHS);
    if (isEmploymentIncome(income) || isSelfEmploymentIncome(income)) {
      this.addEmploymentValidators(incomeForm);
    }
  }

  private addEmploymentValidators(incomeForm: AllIncomeGroup) {
    if (incomeForm?.value?.employment?.employmentStatus === EmploymentStatus.Previous) {
      this.updateValidators(incomeForm, this.PREVIOUS_EMPLOYMENT_PATHS, (control) =>
        control.removeValidators(Validators.required),
      );

      this.updateValidators(incomeForm, this.PREVIOUS_EMPLOYMENT_REQUIRED_PATHS, (control) =>
        control.addValidators(Validators.required),
      );
    } else {
      this.updateValidators(incomeForm, this.PREVIOUS_EMPLOYMENT_REQUIRED_PATHS, (control) =>
        control.removeValidators(Validators.required),
      );

      this.updateValidators(
        incomeForm,
        [...this.EMPLOYMENT_REQUIRED_PATHS, ...this.PREVIOUS_EMPLOYMENT_PATHS],
        (control) => control.addValidators(Validators.required),
      );
    }
  }

  private updateValidators(
    incomeForm: AllIncomeGroup,
    paths: string[],
    action: (control: AbstractControl) => void,
  ) {
    paths.forEach((path) => {
      const control = incomeForm.get(path);
      if (control) {
        action(control);
        control.updateValueAndValidity();
      }
    });
  }
}
