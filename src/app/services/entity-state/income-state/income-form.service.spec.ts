import { TestBed } from '@angular/core/testing';

import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { ClientStateService } from '../client-state/client-state.service';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { IncomeFormService } from './income-form.service';
import { IncomeStateService } from './income-state.service';
import { IncomeSubscriptionManager } from './income-subscription-manager.service';
import { IncomeUpdateHandlerService } from './income-update-handler.service';
import { IncomeValidationHandlerService } from './income-validation-handler.service';
import { INCOME_AUTO_SAVE_TRIGGER } from './provide-income-state';

describe('IncomeFormService', () => {
  let service: IncomeFormService;

  beforeEach(() =>
    MockBuilder(IncomeFormService)
      .mock(IncomeStateService, { state$: NEVER })
      .mock(ClientStateService, { state$: NEVER })
      .mock(LoanEditingState, { isLoanEditingDisabled$: NEVER })
      .mock(IncomeSubscriptionManager)
      .mock(IncomeUpdateHandlerService)
      .mock(IncomeValidationHandlerService)
      .provide({ provide: INCOME_AUTO_SAVE_TRIGGER, useValue: { registerControls: () => {} } }),
  );
  beforeEach(() => {
    service = TestBed.inject(IncomeFormService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
