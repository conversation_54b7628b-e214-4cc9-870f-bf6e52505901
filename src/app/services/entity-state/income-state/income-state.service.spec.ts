import { TestBed } from '@angular/core/testing';

import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { MockBuilder } from 'ng-mocks';
import { DataProviderService } from '../../data-provider/data-provider.service';
import { LeadService } from '../../lead/lead.service';
import { LoanApplicationStateService } from '../../loan-application-state/loan-application-state.service';
import { LoanIdService } from '../../loan-id/loan-id.service';
import { ClientStateService } from '../client-state/client-state.service';
import { IncomeStateService } from './income-state.service';

describe('IncomeStateService', () => {
  let service: IncomeStateService;

  beforeEach(() =>
    MockBuilder(IncomeStateService)
      .mock(LoanIdService)
      .mock(DataProviderService)
      .mock(SplunkLoggerService)
      .mock(ClientStateService)
      .mock(LoanApplicationStateService)
      .mock(LeadService),
  );
  beforeEach(() => {
    service = TestBed.inject(IncomeStateService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
