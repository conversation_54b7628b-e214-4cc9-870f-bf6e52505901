import { Income, IncomeType } from '@rocket-logic/rl-xp-bff-models';
import { pascalCaseToSentence } from '../../../util/formatting-helpers';

export function isIncomeType(thing: any): thing is IncomeType {
  return Object.values(IncomeType).includes(thing) || MAPPABLE_INCOME_TYPES.has(thing);
}

export const MAPPABLE_INCOME_TYPES = new Map<IncomeType, IncomeType>([
  [IncomeType.Corporation, IncomeType.StandardSelfEmployment],
  [IncomeType.Farm, IncomeType.StandardSelfEmployment],
  [IncomeType.Partnership, IncomeType.StandardSelfEmployment],
  [IncomeType.SCorporation, IncomeType.StandardSelfEmployment],
  [IncomeType.SoleProprietorship, IncomeType.StandardSelfEmployment],
]);

export const SUPPORTED_INCOME_TYPES = [
  IncomeType.Standard,
  IncomeType.SocialSecurity,
  IncomeType.StandardSelfEmployment,
  IncomeType.Pension,
  IncomeType.ActiveDuty,
  IncomeType.VABenefits,
  IncomeType.ChildSupport,
  IncomeType.Alimony,
  IncomeType.Adoption,
  IncomeType.AlaskanDividend,
  IncomeType.Annuity,
  IncomeType.AssetDepletion,
  IncomeType.Boarder,
  IncomeType.CapitalGains,
  IncomeType.DefinedContributionPlan,
  IncomeType.DividendsAndInterest,
  IncomeType.FosterCare,
  IncomeType.GamblingWinnings,
  IncomeType.LongTermDisability,
  IncomeType.MortgageDifferentialPayments,
  IncomeType.NotesReceivableInstallment,
  IncomeType.PublicAssistance,
  IncomeType.Royalties,
  IncomeType.Severance,
  IncomeType.TemporaryLeave,
  IncomeType.Tribal,
  IncomeType.Trust,
  IncomeType.Union,
  IncomeType.Unemployment,
];

export const SUPPORTED_INCOME_OPTIONS = SUPPORTED_INCOME_TYPES.map((incomeType) => {
  return {
    value: incomeType,
    display: mapIncomeTypeToString(incomeType),
  };
});

export function mapIncomeTypeToString(incomeType: IncomeType): string {
  switch (incomeType) {
    case IncomeType.Standard:
      return 'Employment';
    case IncomeType.StandardSelfEmployment:
      return 'Self Employment';
    case IncomeType.VABenefits:
      return 'VA Benefits';
    case IncomeType.ActiveDuty:
      return 'Active Duty Military';
    case IncomeType.DefinedContributionPlan:
      return '401(k)/IRA';
    default:
      return pascalCaseToSentence(incomeType);
  }
}

export function isSupportedIncome(income: Income): boolean {
  return SUPPORTED_INCOME_TYPES.includes(income.incomeType);
}
