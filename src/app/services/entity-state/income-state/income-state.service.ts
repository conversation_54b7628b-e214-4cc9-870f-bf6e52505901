import { Injectable, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { Client, Income, IncomeType } from '@rocket-logic/rl-xp-bff-models';
import { Observable, distinctUntilChanged, forkJoin, map, of, switchMap, tap } from 'rxjs';
import { matchKeyInState } from '../../../util/match-key-in-state';
import { LoanDependentEntityStateService } from '../abstract-dependent-entity-state.service';
import { EntityStateName } from '../abstract-entity-state.service';
import { ClientStateService } from '../client-state/client-state.service';
import { isSupportedIncome } from './supported-income';

@Injectable()
export class IncomeStateService extends LoanDependentEntityStateService<
  { [clientId: string]: Map<string, Income> },
  { income: Income; incomeId: string; clientId: string }
> {
  name = EntityStateName.Income;
  protected clientStateService = inject(ClientStateService);

  // Redeclare the state signal so it's initialized after sub-class dependencies are injected
  public override state = toSignal(this.state$);

  public deleteIncome$(clientId: string, incomeId: string, incomeType: IncomeType) {
    const currentData = this.state()?.data;
    const clientIncomeMap = currentData?.[clientId] ?? new Map<string, Income>();
    const { id } = clientIncomeMap.get(incomeId) ?? {};

    if (!id) {
      return of();
    }

    return this.loanIdService.loanId$.pipe(
      switchMap((loanId) => {
        if (!loanId)
          throw new Error(
            `Trying to delete income with id ${id} and client id ${clientId} with no loan id`,
          );
        return this.dataProvider.deleteIncome$(loanId, clientId, id, incomeType);
      }),
      tap(() => {
        const newState = new Map(clientIncomeMap);
        newState.delete(incomeId);
        this.manualStateSubject.next({
          data: { ...currentData, [clientId]: newState },
        });
      }),
    );
  }

  protected override getDependentEntityState$(
    loanId: string,
  ): Observable<{ [clientId: string]: Map<string, Income> }> {
    return this.clientStateService.state$.pipe(
      map(({ data }) => data ?? new Map<string, Client>()),
      distinctUntilChanged((prev, curr) => {
        if (prev.size !== curr.size) {
          return false;
        }
        return Array.from(curr.values()).every((client) =>
          Array.from(prev.values()).some(({ id }) => id === client.id),
        );
      }),
      switchMap((data) => {
        if (data.size === 0) {
          return of([]);
        }

        return forkJoin(
          Array.from(data.values())
            .filter((client) => client.id)
            .map((client) =>
              this.dataProvider.getIncome$(loanId, client.id!).pipe(
                map((incomes) => {
                  const supportedIncomes = incomes.filter(isSupportedIncome);

                  const newState = matchKeyInState(
                    supportedIncomes,
                    this.state()?.data?.[client.id!] ?? new Map<string, Income>(),
                  );

                  return {
                    [client.id!]: newState,
                  };
                }),
              ),
            ),
        );
      }),
      map((clientIncomes) =>
        clientIncomes.reduce((acc, clientIncome) => Object.assign(acc, clientIncome), {}),
      ),
    );
  }

  protected override updateEntityState$(
    loanId: string,
    state: { income: Income; incomeId: string; clientId: string },
  ): Observable<{ [clientId: string]: Map<string, Income> }> {
    return this.dataProvider.updateIncome$(loanId, state.clientId, state.income).pipe(
      map((updatedIncome) => {
        const currentData = this.state()?.data;
        const clientIncome = currentData?.[state.clientId] ?? new Map<string, Income>();
        const newState = new Map(clientIncome);
        newState.set(state.incomeId, updatedIncome);
        return { ...currentData, [state.clientId]: newState };
      }),
    );
  }
}
