import { FormGroup } from '@angular/forms';
import {
  ActiveDutyMilitaryIncome,
  Address,
  Alimony,
  EmployerContactInfo,
  Employment,
  EmploymentIncome,
  EmploymentPayment,
  IncomePaymentAmount,
  LeaveOfAbsence,
  NonEmploymentIncome,
  ReceivedFromIncome,
  RecipientNameIncome,
  Rental,
  SelfEmployment,
  StandardEmployment,
  StandardEmploymentIncome,
  Tribal,
} from '@rocket-logic/rl-xp-bff-models';
import { Controlify, Formify } from '../../../util/form-utility-types';

export type AllIncome = EmploymentIncome &
  Omit<ActiveDutyMilitaryIncome, 'incomeType'> &
  Omit<StandardEmploymentIncome, 'incomeType'> &
  NonEmploymentIncome &
  ReceivedFromIncome &
  RecipientNameIncome &
  Omit<Alimony, 'incomeType'> &
  Omit<Rental, 'incomeType'> &
  Omit<Tribal, 'incomeType'>;

export type AllIncomeForm = Formify<IncomeOmit> & IncomeOverrides;
export type AllIncomeGroup = FormGroup<Controlify<IncomeOmit> & IncomeOverrides>;
type IncomeOmit = Omit<AllIncome, 'employment' | 'base' | 'overtime' | 'commission' | 'bonus'>;
type IncomeOverrides = {
  employment: AllEmploymentGroup;
  base: AllPaymentGroup;
  overtime: AllPaymentGroup;
  commission: AllPaymentGroup;
  bonus: AllPaymentGroup;
};

export type AllEmployment = Employment &
  Omit<SelfEmployment, 'employmentType'> &
  Omit<StandardEmployment, 'employmentType'>;
export type AllEmploymentForm = Formify<EmploymentOmit> & EmploymentOverrides;
export type AllEmploymentGroup = FormGroup<Controlify<EmploymentOmit> & EmploymentOverrides>;
type EmploymentOmit = Omit<AllEmployment, 'employerContactInfo'>;
type EmploymentOverrides = {
  employerContactInfo: EmployerContactInfoGroup;
};
export type LeaveOfAbsenceGroup = FormGroup<Controlify<LeaveOfAbsence>>;
export type EmployerContactInfoForm = Formify<EmployerContactInfoOmit> &
  EmployerContactInfoOverrides;
export type EmployerContactInfoGroup = FormGroup<
  Controlify<EmployerContactInfoOmit> & EmployerContactInfoOverrides
>;
type EmployerContactInfoOmit = Omit<EmployerContactInfo, 'address'>;
type EmployerContactInfoOverrides = {
  address: FormGroup<Controlify<Address>>;
};

export type AllPayment = IncomePaymentAmount & EmploymentPayment;
export type AllPaymentForm = Formify<AllPayment>;
export type AllPaymentGroup = FormGroup<Controlify<AllPayment>>;
