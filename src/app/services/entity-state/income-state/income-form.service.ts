import { Injectable, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormArray, FormBuilder, Validators } from '@angular/forms';
import { Income, IncomeType } from '@rocket-logic/rl-xp-bff-models';
import { distinctUntilChanged, map, withLatestFrom } from 'rxjs';
import { buildAddressForm } from '../../../util/build-address-form';
import { resetNonDirtyControls } from '../../../util/reset-non-dirty-controls';
import { ClientStateService } from '../client-state/client-state.service';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import {
  AllEmploymentForm,
  AllIncomeForm,
  AllIncomeGroup,
  AllPaymentForm,
  AllPaymentGroup,
  EmployerContactInfoForm,
} from './form-types';
import { IncomeStateService } from './income-state.service';
import { IncomeSubscriptionManager } from './income-subscription-manager.service';
import { IncomeUpdateHandlerService } from './income-update-handler.service';
import { IncomeValidationHandlerService } from './income-validation-handler.service';
import { INCOME_AUTO_SAVE_TRIGGER } from './provide-income-state';

@Injectable()
export class IncomeFormService {
  private stateService = inject(IncomeStateService);
  private clientStateService = inject(ClientStateService);
  private formBuilder = inject(FormBuilder);
  private updateHandler = inject(IncomeUpdateHandlerService);
  private validationHandler = inject(IncomeValidationHandlerService);
  private subManager = inject(IncomeSubscriptionManager);
  private autoSaveTrigger = inject(INCOME_AUTO_SAVE_TRIGGER);
  private isLoanEditingDisabled$ = inject(LoanEditingState).isLoanEditingDisabled$;

  public clientIncomeMap = signal<{
    [clientId: string]: Map<string, AllIncomeGroup>;
  }>({});
  private incomeFormArray = new FormArray<AllIncomeGroup>([]);

  constructor() {
    this.stateService.state$
      .pipe(
        map((state) => state.data),
        distinctUntilChanged((prev, current) => this.hasIncomeChanged(prev, current)),
        withLatestFrom(this.isLoanEditingDisabled$),
        takeUntilDestroyed(),
      )
      .subscribe(([clientIncomes, isLoanEditingDisabled]) => {
        Object.entries(clientIncomes ?? {}).forEach(([clientId, incomeMap]) => {
          if (!this.clientIncomeMap()[clientId]) {
            this.clientIncomeMap.update((prev) => {
              return {
                ...prev,
                [clientId]: new Map<string, AllIncomeGroup>(),
              };
            });
          }
          const incomeFormMap = this.clientIncomeMap()[clientId];
          const newFormIds: string[] = [];
          incomeMap.forEach((income, incomeId) => {
            let incomeForm = incomeFormMap.get(incomeId);
            if (!incomeForm) {
              incomeForm = this.buildIncomeForm();
              incomeFormMap.set(incomeId, incomeForm);
              this.incomeFormArray.push(incomeForm);
              newFormIds.push(incomeId);
            }

            if (income) {
              this.clientIncomeMap.update((prev) => {
                const clientIncomeMap = prev[clientId];
                if (incomeForm) {
                  clientIncomeMap.set(incomeId, incomeForm);
                }

                return {
                  ...prev,
                  [clientId]: clientIncomeMap,
                };
              });
              // income saved out of order can create empty records in the state array
              // skip resetting the form if the income doesn't exist
              resetNonDirtyControls(income, incomeForm);
            }

            if (isLoanEditingDisabled) {
              incomeForm.disable();
            }
          });

          newFormIds.forEach((incomeId) => {
            this.addListeners(incomeFormMap.get(incomeId)!, clientId, incomeId);
          });
        });
      });

    this.clientStateService.state$
      .pipe(
        map((state) => state.data),
        takeUntilDestroyed(),
      )
      .subscribe((clients) => {
        // Remove income form map when client is removed
        const incomeMapEntries = Object.entries(this.clientIncomeMap());
        if ((!clients || clients.size === 0) && incomeMapEntries.length > 0) {
          this.clientIncomeMap.update(() => ({}));
          incomeMapEntries.forEach(([clientId]) => {
            this.subManager.removeClientSubscriptions(clientId);
            this.updateHandler.onDeleteClient(clientId);
          });
        } else {
          incomeMapEntries.forEach(([clientId]) => {
            if (!Array.from(clients?.values() ?? [])?.some((client) => client.id === clientId)) {
              this.clientIncomeMap.update((prev) => {
                delete prev[clientId];
                return { ...prev };
              });
              const clientIncomes = this.clientIncomeMap()[clientId];
              clientIncomes?.forEach((form, _) =>
                this.incomeFormArray.removeAt(this.incomeFormArray.controls.indexOf(form)),
              );
              this.clientIncomeMap.update((prev) => {
                const newState = { ...prev };
                delete newState[clientId];
                return newState;
              });
              this.subManager.removeClientSubscriptions(clientId);
              this.updateHandler.onDeleteClient(clientId);
            }
          });
        }
      });

    this.isLoanEditingDisabled$.pipe(takeUntilDestroyed()).subscribe((isEditingDisabled:boolean) => {
      if (isEditingDisabled) {
        Object.values(this.clientIncomeMap()).forEach((incomeMap) =>
          Array.from(incomeMap.values()).forEach((incomeForm) => incomeForm.disable()),
        );
      }
    });

    this.autoSaveTrigger.registerControls(this.incomeFormArray);
  }

  public addIncome(clientId: string): AllIncomeGroup {
    const incomeId = crypto.randomUUID();
    const incomeForm = this.buildIncomeForm();
    this.incomeFormArray.push(incomeForm);
    incomeForm.patchValue({ clientId });
    this.addListeners(incomeForm, clientId, incomeId);
    this.clientIncomeMap.update((prev) => {
      const clientIncomeMap = prev[clientId];
      clientIncomeMap.set(incomeId, incomeForm);
      return {
        ...prev,
        [clientId]: clientIncomeMap,
      };
    });
    this.clientIncomeMap()[clientId].set(incomeId, incomeForm);
    return incomeForm;
  }

  public deleteIncome(clientId: string, incomeId: string, incomeType: IncomeType) {
    const incomeFormMap = this.clientIncomeMap()[clientId];
    const incomeForm = incomeFormMap.get(incomeId) ?? this.buildIncomeForm();
    const income = incomeForm?.value;
    this.updateHandler.onDeleteIncome(clientId, incomeId);
    if (income?.id) {
      this.stateService.deleteIncome$(clientId, incomeId, incomeType).subscribe(() => {
        this.deleteIncomeAndUpdateClientIncomeMap(incomeFormMap, incomeId, clientId);
      });
    } else {
      this.deleteIncomeAndUpdateClientIncomeMap(incomeFormMap, incomeId, clientId);
    }

    this.incomeFormArray.removeAt(this.incomeFormArray.controls.indexOf(incomeForm));
  }

  private deleteIncomeAndUpdateClientIncomeMap(
    incomeFormMap: Map<string, AllIncomeGroup>,
    incomeId: string,
    clientId: string,
  ) {
    this.clientIncomeMap.update((prev) => {
      const newIncomeFormMap = new Map(incomeFormMap);
      newIncomeFormMap.delete(incomeId);
      return {
        ...prev,
        [clientId]: newIncomeFormMap,
      };
    });
    this.subManager.removeIncomeSubscriptions(clientId, incomeId);
  }

  private addListeners(incomeForm: AllIncomeGroup, clientId: string, incomeId: string) {
    this.validationHandler.addIncomeValidationListener(incomeForm, clientId, incomeId);
    this.updateHandler.addIncomeUpdateListener(incomeForm, clientId, incomeId);
  }

  private buildIncomeForm() {
    return this.formBuilder.group<AllIncomeForm>({
      id: [null],
      incomeType: [null],
      clientId: [null],
      qualifiedMonthlyIncomeAmount: [null],
      totalMonthlyIncomeAmount: [null],
      employment: this.formBuilder.group<AllEmploymentForm>({
        id: [null],
        employmentType: [null],
        hasSpecialRelationship: [null],
        employmentStartDate: [null],
        jobTitle: [null],
        employmentEndDate: [null],
        isExpectedToContinue: [null],
        employmentStatus: [null],
        monthsPaidPerYear: [null],
        yearsEmployedInThisLineOfWork: [null],
        leavesOfAbsence: [null],
        employerName: [null],
        employerContactInfo: this.formBuilder.group<EmployerContactInfoForm>({
          address: buildAddressForm(this.formBuilder, {
            allowPOBoxes: true,
          }),
          phone: [null],
        }),
        businessStructure: [null],
        ownershipInterestType: [null],
        isCurrentlyOnLeave: [null],
        isEmployedAbroad: [null],
        isPartTime: [null],
        isSeasonalIncome: [null],
        isTemporaryOrEmployedByStaffingAgency: [null],
      }),
      base: this.buildAllPaymentForm(true),
      overtime: this.buildAllPaymentForm(),
      commission: this.buildAllPaymentForm(),
      bonus: this.buildAllPaymentForm(),
      isOfficer: [null],
      grossedUpQualifiedMonthlyIncome: [null],
      receivedFrom: [null],
      recipientName: [null],
      exSpouseName: [null],
      ownedPropertyId: [null],
      tribeName: [null],
    });
  }

  private buildAllPaymentForm(required = false): AllPaymentGroup {
    return this.formBuilder.group<AllPaymentForm>({
      isIncludedInRatios: [null],
      clientReportedAmount: [null, required ? [Validators.required] : []],
      qualifiedMonthlyAmount: [null],
      frequency: [null, required ? [Validators.required] : []],
    });
  }

  private hasIncomeChanged(
    previous: { [clientId: string]: Map<string, Income> } | undefined,
    current: { [clientId: string]: Map<string, Income> } | undefined,
  ) {
    if (Object.keys(previous ?? {}).length !== Object.keys(current ?? {}).length) {
      return false;
    }
    for (const entry of Object.entries(current ?? {})) {
      const [clientId, currentIncomeMap] = entry;
      if (previous?.[clientId]?.size !== currentIncomeMap.size) {
        return false;
      }

      if (
        !new Array(currentIncomeMap.values()).every((income) =>
          new Array(previous?.[clientId].values()).some((prevIncome) => income === prevIncome),
        )
      ) {
        return false;
      }
    }
    return true;
  }
}
