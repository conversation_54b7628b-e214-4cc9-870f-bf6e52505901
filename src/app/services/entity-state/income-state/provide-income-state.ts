import { InjectionToken, Provider } from '@angular/core';
import { AutoSaveTriggerService } from '../../save-trigger/auto-save.service';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { STATE_SERVICES } from '../abstract-entity-state.service';
import { UPDATE_HANDLERS } from '../abstract-update-handler.service';
import { IncomeFormService } from './income-form.service';
import { IncomeStateService } from './income-state.service';
import { IncomeSubscriptionManager } from './income-subscription-manager.service';
import { IncomeUpdateHandlerService } from './income-update-handler.service';
import { IncomeValidationHandlerService } from './income-validation-handler.service';

export const INCOME_SAVE_TRIGGER = new InjectionToken<SaveTrigger>('INCOME_SAVE_TRIGGER');
export const INCOME_AUTO_SAVE_TRIGGER = new InjectionToken<AutoSaveTriggerService>(
  'INCOME_AUTO_SAVE_TRIGGER',
);

export function provideIncomeState(): Provider[] {
  return [
    IncomeStateService,
    IncomeFormService,
    IncomeSubscriptionManager,
    IncomeValidationHandlerService,
    IncomeUpdateHandlerService,
    { provide: INCOME_AUTO_SAVE_TRIGGER, useClass: AutoSaveTriggerService },
    { provide: INCOME_SAVE_TRIGGER, useExisting: INCOME_AUTO_SAVE_TRIGGER },
    { provide: UPDATE_HANDLERS, useExisting: IncomeUpdateHandlerService, multi: true },
    { provide: STATE_SERVICES, useExisting: IncomeStateService, multi: true },
  ];
}
