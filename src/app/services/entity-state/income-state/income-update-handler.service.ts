import { Inject, Injectable, inject } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Observable } from 'rxjs';
import { removeEmptyValues } from '../../../util/remove-empty-values';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { EntityStateName } from '../abstract-entity-state.service';
import { DirtyState, UpdateHandlerService } from '../abstract-update-handler.service';
import { IncomeStateService } from './income-state.service';
import { IncomeSubscriptionManager } from './income-subscription-manager.service';
import { INCOME_SAVE_TRIGGER } from './provide-income-state';

@Injectable()
export class IncomeUpdateHandlerService extends UpdateHandlerService {
  stateService = inject(IncomeStateService);
  subManager = inject(IncomeSubscriptionManager);

  override name = EntityStateName.Income;

  constructor(@Inject(INCOME_SAVE_TRIGGER) saveTrigger: SaveTrigger) {
    super(saveTrigger);
  }

  addIncomeUpdateListener(incomeForm: FormGroup, clientId: string, incomeId: string) {
    const sub = this.listenForDirtyState(`${clientId}.${incomeId}`, incomeForm);
    this.subManager.addIncomeSubscription(clientId, incomeId, sub);
  }

  onDeleteIncome(clientId: string, incomeId: string) {
    this.removeDirtyState(`${clientId}.${incomeId}`);
  }

  onDeleteClient(clientId: string) {
    this.dirtyState.update((curr) => {
      const next = { ...curr };
      Object.keys(next).forEach((key) => {
        if (key.startsWith(clientId)) {
          delete next[key];
        }
      });
      return next;
    });
  }

  protected override handleUpdate$(
    identifier: string,
    state: DirtyState,
  ): Observable<{ error?: any }> {
    const [clientId, incomeId] = identifier.split('.');
    return this.stateService.updateState({
      incomeId,
      clientId,
      income: removeEmptyValues(state.form.getRawValue()),
    });
  }
}
