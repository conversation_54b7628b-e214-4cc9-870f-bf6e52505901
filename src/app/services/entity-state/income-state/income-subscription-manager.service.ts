import { Injectable } from '@angular/core';
import { Subscription } from 'rxjs';
import { SubscriptionManager } from '../../subscription-manager/subscription-manager';

@Injectable()
export class IncomeSubscriptionManager extends SubscriptionManager<string> {
  addIncomeSubscription(clientId: string, incomeId: string, subscription: Subscription) {
    this.addSubscription(`${clientId}.${incomeId}`, subscription);
  }

  removeClientSubscriptions(clientId: string) {
    Array.from(this.subscriptions.keys())
      .filter((key) => key.startsWith(clientId))
      .forEach((key) => this.removeSubscriptions(key));
  }
  removeIncomeSubscriptions(clientId: string, incomeId: string) {
    this.removeSubscriptions(`${clientId}.${incomeId}`);
  }
}
