import { DestroyRef, Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { EMPTY, distinctUntilChanged, merge, switchMap, withLatestFrom } from 'rxjs';
import { ClientStateService } from '../client-state/client-state.service';
import { EmdFormControls } from './asset-form.service';
import { AssetStateService } from './asset-state.service';

@Injectable()
export class EmdHandlerService {
  clientStateService = inject(ClientStateService);
  stateService = inject(AssetStateService);
  destroyRef = inject(DestroyRef);

  addEmdListener(emdForm: FormGroup) {
    const assetValue = emdForm.get('assetValue')!;
    const noEmdControl = emdForm.get('noEmd')!;
    const ownersControl = emdForm.get('rocketLogicClientIds')!;

    merge(
      assetValue.valueChanges.pipe(distinctUntilChanged()),
      ownersControl.valueChanges.pipe(distinctUntilChanged()),
    )
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        const doesEmdHaveAnyValues = assetValue.value || ownersControl.value?.length;

        if (doesEmdHaveAnyValues && noEmdControl.value) {
          noEmdControl.reset(false);
        } else if (!doesEmdHaveAnyValues && !noEmdControl.value) {
          noEmdControl.reset(true);
        }
      });

    noEmdControl.valueChanges
      .pipe(
        distinctUntilChanged(),
        withLatestFrom(this.clientStateService.state$),
        switchMap(([noEmd, clientState]) => {
          if (noEmd == null) {
            return EMPTY;
          }

          if (noEmd) {
            emdForm.get('assetIdentifier')!.reset(null);
            assetValue.reset(null);
            noEmdControl.reset(true);
            ownersControl.reset(null);
            return this.stateService.deleteEmdAsset$();
          }

          if (!ownersControl.value?.length) {
            const primaryClientId = Array.from(clientState.data?.values() ?? []).find(
              (client) => client.isPrimaryBorrower,
            )?.id;
            ownersControl.reset(primaryClientId ? [primaryClientId] : []);
          }

          return EMPTY;
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }

  mirrorEmdFormToReadonlyForm({
    emdForm,
    readonlyForm,
  }: {
    emdForm: FormGroup<EmdFormControls>;
    readonlyForm: FormGroup<EmdFormControls>;
  }) {
    emdForm.valueChanges.pipe(takeUntilDestroyed()).subscribe((value) => {
      readonlyForm.patchValue(value);
    });
  }
}
