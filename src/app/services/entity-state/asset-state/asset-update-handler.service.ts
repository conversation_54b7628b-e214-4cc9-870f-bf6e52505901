import { Inject, Injectable, inject } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Observable } from 'rxjs';
import { removeEmptyValues } from '../../../util/remove-empty-values';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { EntityStateName } from '../abstract-entity-state.service';
import { DirtyState, UpdateHandlerService } from '../abstract-update-handler.service';
import { AssetStateService } from './asset-state.service';
import { AssetSubscriptionManager } from './asset-subscription-manager.service';
import { ASSET_SAVE_TRIGGER } from './provide-asset-state';

@Injectable()
export class AssetUpdateHandlerService extends UpdateHandlerService {
  static LUMP_SUM_IDENTIFIER = 'lumpSum';
  static EMD_IDENTIFIER = 'emd';
  stateService = inject(AssetStateService);
  subManager = inject(AssetSubscriptionManager);

  override name = EntityStateName.Assets;

  constructor(@Inject(ASSET_SAVE_TRIGGER) saveTrigger: SaveTrigger) {
    super(saveTrigger);
  }

  addAssetUpdateListener(assetForm: FormGroup, assetKey: string) {
    const sub = this.listenForDirtyState(assetKey, assetForm);
    this.subManager.addSubscription(assetKey, sub);
  }

  onDeleteAsset(assetKey: string) {
    this.dirtyState.update((curr) => {
      const next = { ...curr };
      delete next[assetKey];
      return next;
    });
  }

  addLumpSumUpdateListener(lumpSumForm: FormGroup) {
    this.listenForDirtyState(crypto.randomUUID(), lumpSumForm);
  }

  addEmdUpdateListener(emdForm: FormGroup) {
    this.listenForDirtyState(crypto.randomUUID(), emdForm);
  }

  protected override handleUpdate$(
    assetKey: string,
    state: DirtyState,
  ): Observable<{ error?: any }> {
    const cleanedState = removeEmptyValues(state.form.getRawValue());
    const update = { assetKey, asset: cleanedState };
    return this.stateService.updateState(update);
  }
}
