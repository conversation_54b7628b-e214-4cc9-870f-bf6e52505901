import { TestBed, fakeAsync, flush } from '@angular/core/testing';
import { FormControl, FormGroup } from '@angular/forms';
import { MockBuilder } from 'ng-mocks';
import { NEVER, of } from 'rxjs';
import { ClientStateService } from '../client-state/client-state.service';
import { AssetStateService } from './asset-state.service';
import { EmdHandlerService } from './emd-handler.service';

describe('EmdHandlerService', () => {
  let service: EmdHandlerService;

  beforeEach(() =>
    MockBuilder(EmdHandlerService)
      .provide({
        provide: AssetStateService,
        useValue: {
          deleteEmdAsset$: () => NEVER,
        },
      })
      .provide({
        provide: ClientStateService,
        useValue: { state$: of({ data: [{ isPrimaryBorrower: true, id: 'clientId' }] }) },
      }),
  );
  beforeEach(() => {
    service = TestBed.inject(EmdHandlerService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should autofill primary client when EMD is being provided', fakeAsync(() => {
    const formGroup = new FormGroup({
      assetIdentifier: new FormControl(null),
      assetValue: new FormControl<number | null>(null),
      noEmd: new FormControl<boolean | null>(true),
      rocketLogicClientIds: new FormControl<string[] | null>(null),
    });

    service.addEmdListener(formGroup);

    // allow emd
    formGroup.controls.noEmd.setValue(false);
    flush();

    // expect primary client autofilled
    const emdValues = formGroup.getRawValue();
    expect(emdValues.noEmd).toBeFalse();
    expect(emdValues.assetValue).toBeNull();
    expect(emdValues.rocketLogicClientIds).toHaveSize(1);
  }));

  it('should remove emd values when EMD is not being provided', fakeAsync(() => {
    const formGroup = new FormGroup({
      assetIdentifier: new FormControl(null),
      assetValue: new FormControl<number | null>(10),
      noEmd: new FormControl<boolean | null>(false),
      rocketLogicClientIds: new FormControl<string[] | null>(['clientId']),
    });

    service.addEmdListener(formGroup);

    // disallow emd
    formGroup.controls.noEmd.setValue(true);
    flush();

    // expect nulls
    const emdValues = formGroup.getRawValue();
    expect(emdValues.noEmd).toBeTrue();
    expect(emdValues.assetValue).toBeNull();
    expect(emdValues.rocketLogicClientIds).toBeNull();
  }));

  it('should autofill primary client and allow EMD when an asset value is provided', fakeAsync(() => {
    const formGroup = new FormGroup({
      assetIdentifier: new FormControl(null),
      assetValue: new FormControl<number | null>(null),
      noEmd: new FormControl<boolean | null>(true),
      rocketLogicClientIds: new FormControl<string[] | null>(null),
    });

    service.addEmdListener(formGroup);

    // provide value
    formGroup.controls.assetValue.setValue(10);
    flush();

    // expect noEmd to switch and primary client autofilled
    const emdValues = formGroup.getRawValue();
    expect(emdValues.noEmd).toBeFalse();
    expect(emdValues.assetValue).toBe(10);
    expect(emdValues.rocketLogicClientIds).toHaveSize(1);
  }));

  it('should allow EMD when a client is provided', fakeAsync(() => {
    const formGroup = new FormGroup({
      assetIdentifier: new FormControl(null),
      assetValue: new FormControl<number | null>(null),
      noEmd: new FormControl<boolean | null>(true),
      rocketLogicClientIds: new FormControl<string[] | null>(null),
    });

    service.addEmdListener(formGroup);

    // provide client
    formGroup.controls.rocketLogicClientIds.setValue(['clientId']);
    flush();

    // expect noEmd to switch
    const emdValues = formGroup.getRawValue();
    expect(emdValues.noEmd).toBeFalse();
    expect(emdValues.assetValue).toBeNull();
    expect(emdValues.rocketLogicClientIds).toHaveSize(1);
  }));

  it('should have no EMD when value and clients are removed', fakeAsync(() => {
    const formGroup = new FormGroup({
      assetIdentifier: new FormControl(null),
      assetValue: new FormControl<number | null>(10),
      noEmd: new FormControl<boolean | null>(false),
      rocketLogicClientIds: new FormControl<string[] | null>(['clientId']),
    });

    service.addEmdListener(formGroup);

    // remove value and clients
    formGroup.controls.assetValue.setValue(null);
    flush();

    formGroup.controls.rocketLogicClientIds.setValue(null);
    flush();

    // expect noEmd to switch
    const emdValues = formGroup.getRawValue();
    expect(emdValues.noEmd).toBeTrue();
    expect(emdValues.assetValue).toBeNull();
    expect(emdValues.rocketLogicClientIds).toBeNull();
  }));
});
