import { DestroyRef, Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { DestinationType } from '@rocket-logic/rl-xp-bff-models';
import { tap } from 'rxjs';
import { ValidationHandlerService } from '../abstract-validation-handler.service';

const destinationAccountId = ['accountId'];

@Injectable()
export class AssetValidationHandlerService extends ValidationHandlerService {
  private destroyRef = inject(DestroyRef);

  addGiftOfCashAssetValidationListener(giftOfCashForm: FormGroup) {
    giftOfCashForm
      .get('giftDestinationType')
      ?.valueChanges.pipe(
        tap((value) => {
          if (value === DestinationType.ClientBankAccount) {
            this.addRequiredValidators(giftOfCashForm, destinationAccountId);
          } else {
            this.removeRequiredValidators(giftOfCashForm, destinationAccountId);
          }
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();
  }
}
