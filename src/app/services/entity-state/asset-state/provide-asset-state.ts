import { InjectionToken, Provider } from '@angular/core';
import { AutoSaveTriggerService } from '../../save-trigger/auto-save.service';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { STATE_SERVICES } from '../abstract-entity-state.service';
import { UPDATE_HANDLERS } from '../abstract-update-handler.service';
import { AssetFormService } from './asset-form.service';
import { AssetStateService } from './asset-state.service';
import { AssetSubscriptionManager } from './asset-subscription-manager.service';
import { AssetUpdateHandlerService } from './asset-update-handler.service';
import { AssetValidationHandlerService } from './asset-validation-handler.service';
import { EmdHandlerService } from './emd-handler.service';

export const ASSET_SAVE_TRIGGER = new InjectionToken<SaveTrigger>('ASSET_SAVE_TRIGGER');
export const ASSET_AUTO_SAVE_TRIGGER = new InjectionToken<AutoSaveTriggerService>(
  'ASSET_AUTO_SAVE_TRIGGER',
);

export function provideAssetState(): Provider[] {
  return [
    AssetStateService,
    AssetFormService,
    AssetSubscriptionManager,
    AssetUpdateHandlerService,
    { provide: ASSET_AUTO_SAVE_TRIGGER, useClass: AutoSaveTriggerService },
    { provide: ASSET_SAVE_TRIGGER, useExisting: ASSET_AUTO_SAVE_TRIGGER },
    { provide: UPDATE_HANDLERS, useExisting: AssetUpdateHandlerService, multi: true },
    { provide: STATE_SERVICES, useExisting: AssetStateService, multi: true },
    EmdHandlerService,
    AssetValidationHandlerService,
  ];
}
