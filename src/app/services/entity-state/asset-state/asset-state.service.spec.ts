import { TestBed } from '@angular/core/testing';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { DataProviderService } from '../../data-provider/data-provider.service';
import { LeadService } from '../../lead/lead.service';
import { LoanApplicationStateService } from '../../loan-application-state/loan-application-state.service';
import { LoanIdService } from '../../loan-id/loan-id.service';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { LoanStateService } from '../loan-state/loan-state.service';
import { AssetFormService } from './asset-form.service';
import { AssetStateService } from './asset-state.service';

describe('AssetStateService', () => {
  let service: AssetStateService;

  beforeEach(() =>
    MockBuilder(AssetStateService)
      .mock(LoanIdService)
      .mock(DataProviderService)
      .mock(SplunkLoggerService)
      .mock(LoanApplicationStateService)
      .mock(LeadService)
      .mock(LoanStateService)
      .mock(LoanEditingState, {
        isLoanEditingDisabled$: NEVER,
      })
      .mock(AssetFormService)
  );
  beforeEach(() => {
    service = TestBed.inject(AssetStateService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
