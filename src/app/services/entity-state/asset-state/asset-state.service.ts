import { Injectable, computed } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { Asset, AssetType } from '@rocket-logic/rl-xp-bff-models';
import { BehaviorSubject, EMPTY, of } from 'rxjs';
import { catchError, filter, finalize, map, switchMap, take, tap } from 'rxjs/operators';
import { AbstractCollectionEntityStateService } from '../abstract-collection-entity-state.service';
import { EntityStateName, Loadable } from '../abstract-entity-state.service';

@Injectable()
export class AssetStateService extends AbstractCollectionEntityStateService<
  Asset,
  { asset: Asset; assetKey: string }
> {
  override identifier: keyof Asset = 'assetIdentifier';
  private deletionStateSubject = new BehaviorSubject<Loadable<void>>({ updating: false });
  public deletionState = toSignal(this.deletionStateSubject);
  stateValues = computed(() => Array.from(this.state()?.data?.values() ?? []));

  name = EntityStateName.Assets;

  lumpSum = computed(() =>
    this.stateValues().find((asset) => asset.assetType === AssetType.BorrowerEstimatedTotalAssets),
  );

  protected override getDependentEntityState$(loanId: string) {
    return this.dataProvider.getAssets$(loanId).pipe(map((assets) => this.generateState(assets)));
  }

  protected override updateEntityState$(loanId: string, state: { asset: Asset; assetKey: string }) {
    const { asset, assetKey } = state;

    return this.dataProvider
      .updateAsset$(loanId, asset)
      .pipe(map((updatedAsset) => this.updateStateMap(updatedAsset, assetKey)));
  }

  public deleteAsset$(assetKey: string) {
    const asset = this.state()?.data?.get(assetKey);
    if (!asset?.assetIdentifier) {
      return of();
    }

    this.deletionStateSubject.next({ updating: true });
    return this.loanIdService.loanId$.pipe(
      switchMap((loanId) => {
        if (!loanId)
          throw new Error(
            `Trying to delete asset with id ${asset.assetIdentifier} with no loan id`,
          );

        return this.dataProvider.deleteAsset$(loanId, asset.assetIdentifier!);
      }),
      catchError(() => {
        this.deletionStateSubject.next({ updating: false });
        return of();
      }),
      tap(() => this.manualStateSubject.next({ data: this.deleteFromStateMap(assetKey) })),
      take(1),
      finalize(() => this.deletionStateSubject.next({ updating: false })),
    );
  }
  public deleteLumpSumAsset$() {
    return this.deleteAssetByType$(AssetType.BorrowerEstimatedTotalAssets);
  }

  public deleteEmdAsset$() {
    return this.deleteAssetByType$(AssetType.EarnestMoneyDeposit);
  }

  private deleteAssetByType$(assetType: AssetType) {
    const assetKey = Array.from(this.state()?.data?.entries() ?? []).find(
      ([, asset]) => asset.assetType === assetType,
    )?.[0];

    if (!assetKey) {
      return EMPTY;
    }

    return this.deleteAsset$(assetKey);
  }
}
