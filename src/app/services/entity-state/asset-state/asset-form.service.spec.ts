import { TestBed } from '@angular/core/testing';

import { Mock<PERSON>uilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { LoanStateService } from '../loan-state/loan-state.service';
import { AssetFormService } from './asset-form.service';
import { AssetStateService } from './asset-state.service';
import { AssetSubscriptionManager } from './asset-subscription-manager.service';
import { AssetUpdateHandlerService } from './asset-update-handler.service';
import { AssetValidationHandlerService } from './asset-validation-handler.service';
import { EmdHandlerService } from './emd-handler.service';
import { ASSET_AUTO_SAVE_TRIGGER } from './provide-asset-state';

describe('AssetFormService', () => {
  let service: AssetFormService;

  beforeEach(() =>
    MockBuilder(AssetFormService)
      .mock(AssetStateService, { state$: NEVER })
      .mock(LoanEditingState, {
        isLoanEditingDisabled$: NEVER,
      })
      .mock(LoanStateService)
      .mock(AssetUpdateHandlerService)
      .mock(AssetSubscriptionManager)
      .mock(EmdHandlerService)
      .mock(AssetValidationHandlerService)
      .provide({ provide: ASSET_AUTO_SAVE_TRIGGER, useValue: { registerControls: () => {} } }),
  );

  beforeEach(() => {
    service = TestBed.inject(AssetFormService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
