import { computed, inject, Injectable, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Asset, AssetType } from '@rocket-logic/rl-xp-bff-models';
import { distinctUntilChanged, map, scan, withLatestFrom } from 'rxjs';
import { resetNonDirtyControls } from '../../../util/reset-non-dirty-controls';
import { AbstractCollectionFormService } from '../abstract-collection-form.service';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { LoanStateService } from '../loan-state/loan-state.service';
import { AssetStateService } from './asset-state.service';
import { AssetSubscriptionManager } from './asset-subscription-manager.service';
import { AssetUpdateHandlerService } from './asset-update-handler.service';
import { AssetValidationHandlerService } from './asset-validation-handler.service';
import { EmdHandlerService } from './emd-handler.service';
import { ASSET_AUTO_SAVE_TRIGGER } from './provide-asset-state';

export type EmdFormControls = {
  assetIdentifier: FormControl<null>;
  assetType: FormControl<AssetType | null>;
  assetValue: FormControl<null>;
  includeInQualification: FormControl<null>;
  isActive: FormControl<null>;
  isClientEstimated: FormControl<null>;
  qualifyingAmount: FormControl<null>;
  rocketLogicClientIds: FormControl<null>;
  noEmd: FormControl<boolean | null>;
};

@Injectable()
export class AssetFormService extends AbstractCollectionFormService<any> {
  override entityFormMap = signal(new Map<string, FormGroup>());
  override entityValues = computed(() => Array.from(this.entityFormMap().values() ?? []));
  private assetStateService = inject(AssetStateService);
  private subManager = inject(AssetSubscriptionManager);
  private updateHandler = inject(AssetUpdateHandlerService);
  private emdHandler = inject(EmdHandlerService);
  private autoSaveTrigger = inject(ASSET_AUTO_SAVE_TRIGGER);
  private loanStateService = inject(LoanStateService);
  private loanEditingState = inject(LoanEditingState);
  private assetValidationHandler = inject(AssetValidationHandlerService);
  // These forms exist outside the stateMap
  lumpSumForm: FormGroup = this.buildAssetForm(AssetType.BorrowerEstimatedTotalAssets);
  emdForm = this.buildEmdForm(AssetType.EarnestMoneyDeposit);
  readonlyEmdForm = this.buildEmdForm(AssetType.EarnestMoneyDeposit, { disabled: true });

  constructor() {
    super();

    this.updateHandler.addLumpSumUpdateListener(this.lumpSumForm);
    this.updateHandler.addEmdUpdateListener(this.emdForm);
    this.emdHandler.addEmdListener(this.emdForm);
    this.emdHandler.mirrorEmdFormToReadonlyForm({
      emdForm: this.emdForm,
      readonlyForm: this.readonlyEmdForm,
    });
    this.autoSaveTrigger.registerControls(this.entityFormArray, this.lumpSumForm, this.emdForm);
    this.assetStateService.state$
      .pipe(
        map((state) => state.data),
        distinctUntilChanged((prev, current) => {
          if (prev === undefined && current === undefined) {
            return true;
          }
          if (prev?.size !== current?.size) {
            return false;
          }
          return (
            Array.from(prev?.entries() ?? [])?.every(([assetKey, asset]) => {
              return asset === current?.get(assetKey);
            }) ?? false
          );
        }),
        scan(
          (prev, current) => {
            return (
              Array.from(current?.entries() ?? []).map(([assetKey, asset]) => {
                const updated = prev?.find(({ id }) => id === assetKey)?.asset !== asset;
                return { asset, updated, id: assetKey };
              }) ?? []
            );
          },
          [] as { asset: Asset; updated: boolean; id: string }[],
        ),
        withLatestFrom(this.loanEditingState.isLoanEditingDisabled$),
        takeUntilDestroyed(),
      )
      .subscribe(([assets, isLoanEditingDisabled]) => {
        const newFormKeys: string[] = [];
        assets
          .filter(({ asset }) => {
            if (asset.assetType === AssetType.BorrowerEstimatedTotalAssets) {
              resetNonDirtyControls(asset, this.lumpSumForm, true);
              return false;
            }

            if (asset.assetType === AssetType.EarnestMoneyDeposit) {
              resetNonDirtyControls(asset, this.emdForm, true);
              return false;
            }

            return true;
          })
          .forEach(({ asset, updated, id }) => {
            let assetForm = this.entityFormMap().get(id);
            if (!assetForm) {
              newFormKeys.push(id);
              assetForm = this.buildAssetForm(asset.assetType);

              this.updateState(id, assetForm);
              this.entityFormArray.push(assetForm);
            }

            if (updated) {
              resetNonDirtyControls(asset, assetForm, true);
            }
          });

        newFormKeys.forEach((id) => {
          this.updateHandler.addAssetUpdateListener(this.entityFormMap().get(id)!, id);
        });

        if (isLoanEditingDisabled) {
          this.entityFormArray.disable();
          this.lumpSumForm.disable();
          this.emdForm.disable();
        }
      });

    this.loanEditingState.isLoanEditingDisabled$
      .pipe(takeUntilDestroyed())
      .subscribe((isEditingDisabled) => {
        if (isEditingDisabled) {
          this.entityFormArray.disable();
          this.lumpSumForm.disable();
          this.emdForm.disable();
        }
      });
  }

  public deleteAsset(assetKey: string) {
    const asset = this.entityFormMap().get(assetKey);

    if (asset!.value.assetIdentifier) {
      this.assetStateService.deleteAsset$(assetKey).subscribe(() => { });
    }

    this.deleteState(assetKey);
    this.entityFormArray.removeAt(
      this.entityFormArray.controls.indexOf(this.entityFormMap().get(assetKey)!),
    );
    this.updateHandler.onDeleteAsset(assetKey);
    this.subManager.removeSubscriptions(assetKey);
  }

  public addAsset(value: AssetType): FormGroup {
    const assetForm = this.buildAssetForm(value);
    const assetKey = crypto.randomUUID();

    this.updateState(assetKey, assetForm);
    this.updateHandler.addAssetUpdateListener(assetForm, assetKey);
    this.entityFormArray.push(assetForm);
    return assetForm;
  }

  public buildAssetForm(assetType: AssetType) {
    switch (assetType) {
      case AssetType.CheckingAccount:
      case AssetType.SavingsAccount:
      case AssetType.MoneyMarket:
      case AssetType.CertificateOfDeposit:
      case AssetType._401K:
      case AssetType.IndividualRetirementArrangement:
      case AssetType._403B:
      case AssetType._457Plan:
      case AssetType.ThriftSavingsPlan:
      case AssetType.SimplifiedEmployeePension:
      case AssetType.Annuity:
      case AssetType.Keogh:
      case AssetType.BrokerageAccount:
      case AssetType.MutualFund:
      case AssetType._529CollegeSavingsPlan:
      case AssetType.LifeInsurance:
      case AssetType.TrustAccount:
      case AssetType.PledgedAssetAccount:
        return this.buildAccountAssetForm(assetType);
      case AssetType.GiftOfCash:
        return this.buildGiftOfCashAssetForm(assetType);
      case AssetType.GiftOfEquity:
        return this.buildGiftAssetForm(assetType);
      case AssetType.Grant:
        return this.buildSubsidyAsset(assetType);
      case AssetType.BorrowerEstimatedTotalAssets:
        return this.buildBorrowerEstimatedAsset(assetType);
      case AssetType.EarnestMoneyDeposit:
        return this.buildEmdForm(assetType);
      default:
        return this.buildStandardAssetForm(assetType);
    }
  }

  private standardAssetControls({
    assetType,
    requireAssetValue = true,
    requireClientIds = true,
  }: {
    assetType: AssetType;
    requireAssetValue?: boolean;
    requireClientIds?: boolean;
  }) {
    return {
      assetIdentifier: [null],
      assetType: [assetType],
      assetValue: [null, requireAssetValue ? Validators.required : []],
      includeInQualification: [null],
      isActive: [null],
      isClientEstimated: [null],
      qualifyingAmount: [null],
      rocketLogicClientIds: [null, requireClientIds ? Validators.required : []],
    };
  }

  private buildStandardAssetForm(assetType: AssetType): FormGroup {
    return this.formBuilder.group(
      this.standardAssetControls({ assetType, requireClientIds: false }),
    );
  }

  private buildBorrowerEstimatedAsset(assetType: AssetType): FormGroup {
    return this.formBuilder.group({
      assetIdentifier: [null],
      assetType: [assetType],
      assetValue: [null],
    });
  }

  private buildAccountAssetForm(assetType: AssetType) {
    return this.formBuilder.group({
      ...this.standardAssetControls({ assetType }),
      financialInstitution: [
        null,
        assetType === AssetType.TrustAccount ? [Validators.required] : [],
      ],
      accountIdentifier: [null],
    });
  }

  private buildGiftAssetForm(assetType: AssetType): FormGroup {
    return this.formBuilder.group({
      ...this.standardAssetControls({ assetType }),
      giftSource: [null],
      nameOfGiftGiver: [null],
    });
  }

  private buildGiftOfCashAssetForm(assetType: AssetType): FormGroup {
    const giftOfCashForm = this.formBuilder.group({
      ...this.standardAssetControls({ assetType }),
      giftSource: [null],
      nameOfGiftGiver: [null],
      accountId: [null],
      giftDestinationType: [null],
    });

    this.assetValidationHandler.addGiftOfCashAssetValidationListener(giftOfCashForm);

    return giftOfCashForm;
  }

  private buildSubsidyAsset(assetType: AssetType): FormGroup {
    return this.formBuilder.group({
      ...this.standardAssetControls({ assetType }),
      accountId: [null],
      grantDestinationType: [null],
      nameOfGrantor: [null],
      source: [null, [Validators.required]],
    });
  }

  private buildEmdForm(
    assetType: AssetType,
    { disabled } = { disabled: false },
  ): FormGroup<EmdFormControls> {
    const form = this.formBuilder.group({
      ...this.standardAssetControls({
        assetType,
        requireAssetValue: false,
        requireClientIds: false,
      }),
      noEmd: [true],
    });

    if (disabled) {
      form.disable();
    }

    return form;
  }
}
