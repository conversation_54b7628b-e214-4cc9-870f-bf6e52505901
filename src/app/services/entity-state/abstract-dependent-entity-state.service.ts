import { Injectable } from '@angular/core';
import { Observable, filter, switchMap } from 'rxjs';
import { EntityStateService } from './abstract-entity-state.service';
import { LoanStateService } from './loan-state/loan-state.service';

@Injectable()
export abstract class LoanDependentEntityStateService<T, U = T> extends EntityStateService<T, U> {
  protected loanStateService?: LoanStateService;

  protected override getEntityState$(loanId: string): Observable<T> {
    if (!this.loanStateService) {
      // We aren't guaranteed to have this class fully constructed before we start fetching data
      // So we need to inject the loan state service on demand
      this.loanStateService = this.injector.get(LoanStateService);
    }

    return this.loanStateService.isInitialized$.pipe(
      filter((isInitialized) => isInitialized),
      switchMap(() => this.getDependentEntityState$(loanId)),
    );
  }

  protected abstract getDependentEntityState$(loanId: string): Observable<T>;
}
