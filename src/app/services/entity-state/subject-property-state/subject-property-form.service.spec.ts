import { signal } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { PropertyEstimatesService } from '../../property-estimates/property-estimates.service';
import { UserAuthorizationService } from '../../user-authorization/user-authorization.service';
import { InsuranceStateService } from '../insurance-state/insurance-state.service';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { LoanInfoFormListenerService } from '../loan-state/loan-info-form-listener.service';
import { PropertyAttachmentTypeHandlerService } from './property-attachment-type-handler';
import { SUBJECT_PROPERTY_AUTO_SAVE_TRIGGER } from './provide-subject-property-state';
import {
    SubjectPropertyFormRef,
    SubjectPropertyFormService,
} from './subject-property-form.service';
import { SubjectPropertyStateService } from './subject-property-state.service';
import { SubjectPropertyUpdateHandlerService } from './subject-property-update-handler.service';
import { SubjectPropertyValidationHandlerService } from './subject-property-validation-handler-service';

describe('SubjectPropertyFormService', () => {
  let service: SubjectPropertyFormService;

  beforeEach(() =>
    MockBuilder(SubjectPropertyFormService)
      .mock(SubjectPropertyStateService, {
        state: signal({}),
        state$: NEVER,
      })
      .mock(UserAuthorizationService)
      .mock(SubjectPropertyFormRef)
      .mock(LoanEditingState, { isLoanEditingDisabled$: NEVER })
      .mock(LoanInfoFormListenerService)
      .mock(PropertyEstimatesService, {
        homeOwnersInsuranceEstimate$: NEVER,
      })
      .mock(SubjectPropertyValidationHandlerService)
      .mock(SubjectPropertyUpdateHandlerService)
      .mock(PropertyAttachmentTypeHandlerService)
      .mock(InsuranceStateService, { state$: NEVER })
      .provide({
        provide: SUBJECT_PROPERTY_AUTO_SAVE_TRIGGER,
        useValue: { registerControls: () => {} },
      }),
  );
  beforeEach(() => {
    service = TestBed.inject(SubjectPropertyFormService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
