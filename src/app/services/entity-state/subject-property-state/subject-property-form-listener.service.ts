import { Injectable, computed, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { State } from '@rocket-logic/rl-xp-bff-models';
import { PropertyType } from '@rocket-logic/rl-xp-bff-models/dist/enums/subject-property-type';
import { map, merge, startWith } from 'rxjs';
import { isAttachmentType } from '../../../util/is-attachment-type';
import {
  SubjectPropertyFormRef,
  SubjectPropertyFormService,
} from './subject-property-form.service';

@Injectable()
export class SubjectPropertyFormListenerService {
  private subjectPropertyFormService = inject(SubjectPropertyFormService);
  private subjectPropertyFormRef = inject(SubjectPropertyFormRef);

  public propertyTypeSignal = toSignal(
    merge(
      this.subjectPropertyFormRef.subjectPropertyForm.valueChanges,
      this.subjectPropertyFormService.reset$,
    ).pipe(
      map((formChange) => formChange?.propertyType),
      startWith(this.subjectPropertyFormRef.subjectPropertyForm.value.propertyType),
    ),
  );

  propertyLocation = toSignal(
    this.subjectPropertyFormRef.subjectPropertyForm.valueChanges.pipe(
      map((formChange) => formChange?.address),
    ),
  );

  public isTwoToFourFamily = computed(
    () => this.propertyTypeSignal() === PropertyType.TwoToFourFamily,
  );
  public isMobileManufactured = computed(
    () =>
      this.propertyTypeSignal() === PropertyType.ManufacturedPUD ||
      this.propertyTypeSignal() === PropertyType.ManufacturedSingleFamily ||
      this.propertyTypeSignal() === PropertyType.ManufacturedCondominium,
  );
  public isAttachmentType = computed(() =>
    isAttachmentType(this.propertyTypeSignal() ?? undefined),
  );

  public isTexasProperty = computed(() => {
    return this.propertyLocation()?.state === State.Texas;
  });
}
