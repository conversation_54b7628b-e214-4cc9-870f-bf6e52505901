import { DestroyRef, Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { AttachmentType } from '@rocket-logic/rl-xp-bff-models/dist/enums/attachment-type';
import { PropertyType } from '@rocket-logic/rl-xp-bff-models/dist/enums/subject-property-type';
import { Subscription, combineLatest, distinctUntilChanged, filter, skip, takeUntil } from 'rxjs';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { SubjectPropertyControls } from './form-types';
import { SubjectPropertyStateService } from './subject-property-state.service';

@Injectable()
export class PropertyAttachmentTypeHandlerService {
  private subjectPropertyStateService = inject(SubjectPropertyStateService);
  private isLoanEditingDisabled$ = inject(LoanEditingState).isLoanEditingDisabled$;
  private destroyRef = inject(DestroyRef);

  protected subjectPropertyListener?: Subscription;

  public addInitListener(subjectPropertyForm: FormGroup<SubjectPropertyControls>) {
    const stateFormChanges = subjectPropertyForm.controls.propertyType.valueChanges;
    this.subjectPropertyListener?.unsubscribe();
    this.subjectPropertyListener = combineLatest([
      this.subjectPropertyStateService.state$,
      stateFormChanges,
    ])
      .pipe(
        skip(1),
        distinctUntilChanged(
          ([prevSubjectPropState, prevSubjectPropType], [subjectPropState, subjectPropType]) => {
            return (
              prevSubjectPropState.data?.propertyType === subjectPropState.data?.propertyType &&
              prevSubjectPropType === subjectPropType
            );
          },
        ),
        takeUntil(this.isLoanEditingDisabled$.pipe(filter((deactivated) => deactivated === true))),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(([subjectPropertyState, propertyType]) => {
        const statePropertyType = subjectPropertyState.data?.propertyType;
        this.handleAttachment(subjectPropertyForm, propertyType, statePropertyType);
      });
  }

  private handleAttachment(
    subjectPropertyForm: FormGroup<SubjectPropertyControls>,
    formPropertyType?: PropertyType | null,
    statePropertyType?: PropertyType,
  ) {
    if (formPropertyType == null || formPropertyType !== statePropertyType) {
      const attachmentTypeControl = subjectPropertyForm.controls.attachmentType;
      const defaultAttachmentType = this.getDefaultAttachment(formPropertyType);
      if (attachmentTypeControl.getRawValue() !== defaultAttachmentType) {
        attachmentTypeControl.setValue(defaultAttachmentType);
      }
    }
  }

  private getDefaultAttachment(propertyType?: PropertyType | null) {
    switch (propertyType) {
      case PropertyType.Condominium:
      case PropertyType.ManufacturedCondominium:
      case PropertyType.SiteCondo:
      case PropertyType.Coop:
        return AttachmentType.Attached;
      case PropertyType.PUD:
      case PropertyType.ManufacturedPUD:
      case PropertyType.SingleFamily:
      case PropertyType.TwoToFourFamily:
      case PropertyType.ManufacturedSingleFamily:
        return AttachmentType.Detached;
      default:
        return null;
    }
  }
}
