import { Inject, Injectable, inject } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatDialog } from '@angular/material/dialog';
import {
  Address,
  AddressValidationStatus,
  InsurancePolicyType,
  MappedAddressValidation,
} from '@rocket-logic/rl-xp-bff-models';
import { PropertyInsurance } from '@rocket-logic/rl-xp-bff-models/dist/property-insurance/property-insurance';
import { SubjectProperty } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/subject-property';
import { Observable, catchError, defer, forkJoin, iif, map, of, switchMap, tap } from 'rxjs';
import { AddressValidationDialogComponent } from '../../../address-validation-dialog/address-validation-dialog.component';
import { areAddressesEqual } from '../../../util/address-equal';
import { removeEmptyValues } from '../../../util/remove-empty-values';
import { AddressDataService } from '../../address/address-data.service';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { EntityStateName, Loadable } from '../abstract-entity-state.service';
import { DirtyState, UpdateHandlerService } from '../abstract-update-handler.service';
import { ClientFormService } from '../client-state/client-form.service';
import { InsuranceStateService } from '../insurance-state/insurance-state.service';
import { SubjectPropertyControls } from './form-types';
import { SUBJECT_PROPERTY_SAVE_TRIGGER } from './provide-subject-property-state';
import { SubjectPropertyStateService } from './subject-property-state.service';

@Injectable()
export class SubjectPropertyUpdateHandlerService extends UpdateHandlerService {
  static IDENTIFIER = 'subjectProperty';
  stateService = inject(SubjectPropertyStateService);
  addressDataService = inject(AddressDataService);
  insuranceStateService = inject(InsuranceStateService);
  clientFormService = inject(ClientFormService);
  private dialog = inject(MatDialog);
  private readonly HOI_IDENTIFIER = 'hoi';
  private readonly FLOOD_INSURANCE_IDENTIFIER = 'floodInsurance';

  override name = EntityStateName.SubjectProperty;

  constructor(@Inject(SUBJECT_PROPERTY_SAVE_TRIGGER) saveTrigger: SaveTrigger) {
    super(saveTrigger);
  }

  addUpdateListener(subjectPropertyForm: FormGroup<SubjectPropertyControls>) {
    this.listenForDirtyState(SubjectPropertyUpdateHandlerService.IDENTIFIER, subjectPropertyForm);
  }

  protected override handleUpdate$(_: string, state: DirtyState): Observable<{ error?: any }> {
    const subjProp = state.form.getRawValue();
    const subjPropAddress = subjProp.address as Address;
    const isAddressDirty = state.dirtyPaths.some((path) => path.includes('address'));
    const doesAddressLine1HaveValue = !!subjPropAddress?.addressLine1;
    const useAsSubjectProperty = this.clientFormService
      .entityValues()
      .find((client) => client.value.residenceInformation?.currentResidence?.useAsSubjectProperty);

    if (!useAsSubjectProperty) {
      const subjectPropertyValidation$ = this.addressDataService
        .getAddressValidation$(subjPropAddress)
        .pipe(
          catchError((err) => {
            return of({
              originalAddress: subjPropAddress,
              status: AddressValidationStatus.NotFound,
            } as MappedAddressValidation);
          }),
          tap((response) => {
            if (response.status !== AddressValidationStatus.NotFound) {
              state.form
                .get('address.censusTractCode')
                ?.setValue(response.responseAddress?.censusTractCode, { emitEvent: false });
              state.form
                .get('address.plus4')
                ?.setValue(response.responseAddress?.plus4, { emitEvent: false });
            }
          }),
          switchMap((response) => {
            if (
              response.status === AddressValidationStatus.NotFound ||
              areAddressesEqual(response.originalAddress, response?.responseAddress ?? {})
            ) {
              return of(null);
            }

            const currentAddress = state.form.getRawValue().address;
            return this.dialog
              .open(AddressValidationDialogComponent, {
                data: [
                  { isSuggested: false, address: currentAddress },
                  { isSuggested: true, address: response?.responseAddress ?? {} },
                ],
                panelClass: 'rkt-Dialog',
                minWidth: '50%',
                minHeight: '50%',
                backdropClass: 'rkt-Backdrop',
              })
              .afterClosed();
          }),
          switchMap((address: Address | null) => {
            const subjPropValue = state.form.getRawValue();
            if (address) {
              subjPropValue.address = address;
            }
            return this.updateState$(subjPropValue);
          }),
        );
      return iif(
        () => isAddressDirty && doesAddressLine1HaveValue,
        subjectPropertyValidation$,
        defer(() => this.updateState$(state.form.getRawValue(), state.dirtyPaths)),
      );
    }

    return this.updateState$(state.form.getRawValue(), state.dirtyPaths);
  }

  private updateState$(state: any, dirtyPaths?: string[]) {
    const isHoiDirty = dirtyPaths?.some((path) => path.includes(this.HOI_IDENTIFIER)) ?? false;
    const isFloodDirty =
      dirtyPaths?.some((path) => path.includes(this.FLOOD_INSURANCE_IDENTIFIER)) ?? false;
    const cleanedState = removeEmptyValues(state);

    const onlyInsuranceIsDirty = dirtyPaths?.every(
      (path) =>
        path.includes(this.HOI_IDENTIFIER) || path.includes(this.FLOOD_INSURANCE_IDENTIFIER),
    );

    const insuranceUpdate$ = defer(() =>
      this.updateInsuranceState$(cleanedState, isHoiDirty, isFloodDirty),
    ).pipe(map(() => ({ data: state }) as Loadable<SubjectProperty>));

    const fullUpdate$ = defer(() =>
      forkJoin([
        this.stateService.updateState(cleanedState),
        this.updateInsuranceState$(cleanedState, isHoiDirty, isFloodDirty),
      ]).pipe(map(([subjProp]) => subjProp)),
    );

    const finalUpdate$ = onlyInsuranceIsDirty ? insuranceUpdate$ : fullUpdate$;

    return iif(
      () => isHoiDirty || isFloodDirty,
      finalUpdate$,
      defer(() => this.stateService.updateState(cleanedState)),
    );
  }

  private updateInsuranceState$(state: any, isHoiDirty: boolean, isFloodDirty: boolean) {
    const updates: PropertyInsurance[] = [];
    if (isHoiDirty) {
      updates.push({ amount: state.hoi.amount, policyType: InsurancePolicyType.HOI });
    }
    if (isFloodDirty) {
      updates.push({ amount: state.floodInsurance.amount, policyType: InsurancePolicyType.Flood });
    }

    return updates.map((insurance) =>
      this.insuranceStateService.updateState(insurance).pipe(
        catchError((err) => {
          return of({ error: err } as Loadable<InsurancePolicyType>);
        }),
      ),
    );
  }
}
