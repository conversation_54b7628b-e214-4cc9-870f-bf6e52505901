import { FormGroup } from '@angular/forms';
import { Address } from '@rocket-logic/rl-xp-bff-models/dist/address';
import { PropertyInsurance } from '@rocket-logic/rl-xp-bff-models/dist/property-insurance/property-insurance';
import { ManufacturedInformation } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/manufactured-info';
import { PaymentAmount } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/payment-amount';
import { SubjectProperty } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/subject-property';
import { SubjectPropertyRefinanceInformation } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/subject-property-refinance-info';
import { Controlify, Formify } from '../../../util/form-utility-types';

export type SubjectPropertyForm = Formify<SubjectPropertyOmit> & SubjectPropertyOverrides;
export type SubjectPropertyControls = Controlify<SubjectPropertyOmit> & SubjectPropertyOverrides;
type SubjectPropertyOmit = Omit<
  SubjectProperty,
  'address' | 'manufacturedInformation' | 'hoaPayment' | 'units' | 'refinanceInformation'
>;
type SubjectPropertyOverrides = {
  address: FormGroup<Controlify<Address>>;
  manufacturedInformation: FormGroup<Controlify<ManufacturedInformation>>;
  hoaPayment: FormGroup<Controlify<PaymentAmount>>;
  hoi: FormGroup<Controlify<PropertyInsurance>>;
  floodInsurance: FormGroup<Controlify<PropertyInsurance>>;
  refinanceInformation: FormGroup<Controlify<SubjectPropertyRefinanceInformation>>;
};
