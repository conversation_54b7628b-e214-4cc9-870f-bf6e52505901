import { Injectable, effect, inject } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { InsurancePolicyType } from '@rocket-logic/rl-xp-bff-models';
import { PropertyInsurance } from '@rocket-logic/rl-xp-bff-models/dist/property-insurance/property-insurance';
import { ManufacturedInformation } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/manufactured-info';
import { PaymentAmount } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/payment-amount';
import { SubjectProperty } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/subject-property';
import { SubjectPropertyRefinanceInformation } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/subject-property-refinance-info';
import { Subject, combineLatestWith, distinctUntilChanged, filter, map } from 'rxjs';
import { buildAddressForm } from '../../../util/build-address-form';
import { Formify } from '../../../util/form-utility-types';
import { resetNonDirtyControls } from '../../../util/reset-non-dirty-controls';
import { PropertyEstimatesService } from '../../property-estimates/property-estimates.service';
import { InsuranceStateService } from '../insurance-state/insurance-state.service';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { SubjectPropertyControls, SubjectPropertyForm } from './form-types';
import { PropertyAttachmentTypeHandlerService } from './property-attachment-type-handler';
import { SUBJECT_PROPERTY_AUTO_SAVE_TRIGGER } from './provide-subject-property-state';
import { SubjectPropertyStateService } from './subject-property-state.service';
import { SubjectPropertyUpdateHandlerService } from './subject-property-update-handler.service';
import { SubjectPropertyValidationHandlerService } from './subject-property-validation-handler-service';

@Injectable()
export class SubjectPropertyFormRef {
  private formBuilder = inject(FormBuilder);

  public readonly subjectPropertyForm: FormGroup<SubjectPropertyControls> =
    this.formBuilder.group<SubjectPropertyForm>({
      occupancyType: [null],
      estimatedPropertyValue: [null],
      insurancePayment: [null],
      isManufactured: [null],
      taxPayment: [null],
      hoaPayment: this.formBuilder.group<Formify<PaymentAmount>>({
        amount: [null, [Validators.min(0), Validators.max(99_999.99)]],
        frequency: [null],
      }),
      address: buildAddressForm(this.formBuilder, {
        requireState: true,
        requireZipCode: true,
      }),
      condoInformation: [null],
      refinanceInformation: this.formBuilder.group<Formify<SubjectPropertyRefinanceInformation>>({
        ownershipTransferPrice: [null],
        rocketLogicOwnedPropertyId: [null],
      }),
      ownershipTransferredOn: [null],
      floodInformation: [null],
      propertyTaxIds: [null],
      attachmentType: [null],
      isLocatedInAgeRestrictedCommunity: [null],
      isLocatedInDisasterArea: [null],
      isNativeAmericanLand: [null],
      appraisalRequired: [null],
      legalDescription: [null],
      propertyType: [null],
      yearBuilt: [null],
      squareFootage: [null],
      numberOfUnits: [null],
      pestInspectionCompletedOn: [null],
      waterQualityInspectionCompletedOn: [null],
      manufacturedInformation: this.formBuilder.group<Formify<ManufacturedInformation>>({
        numberOfSections: [null],
      }),
      titleHeldAs: [null],
      floodInsurance: this.formBuilder.group<Formify<PropertyInsurance>>({
        policyType: [InsurancePolicyType.Flood],
        amount: [null],
        startDate: [null],
        endDate: [null],
      }),
      hoi: this.formBuilder.group<Formify<PropertyInsurance>>({
        policyType: [InsurancePolicyType.HOI],
        amount: [null],
        startDate: [null],
        endDate: [null],
      }),
    });
}
@Injectable()
export class SubjectPropertyFormService {
  private subjectPropertyRef = inject(SubjectPropertyFormRef);
  private subjectPropertyStateService = inject(SubjectPropertyStateService);
  private insuranceStateService = inject(InsuranceStateService);
  private LoanEditingState = inject(LoanEditingState);
  private validationHandler = inject(SubjectPropertyValidationHandlerService);
  private updateHandler = inject(SubjectPropertyUpdateHandlerService);
  private handleAttachmentHandlerService = inject(PropertyAttachmentTypeHandlerService);
  propertyEstimatesService = inject(PropertyEstimatesService);
  private autoSaveTrigger = inject(SUBJECT_PROPERTY_AUTO_SAVE_TRIGGER);

  private resetSubject = new Subject<SubjectProperty | undefined>();
  public reset$ = this.resetSubject.asObservable();

  homeOwnersInsuranceEstimateChanged = toSignal(
    this.propertyEstimatesService.homeOwnersInsuranceEstimate$,
  );

  constructor() {
    this.autoSaveTrigger.registerControls(this.subjectPropertyRef.subjectPropertyForm);
    this.addListeners();

    const insurance$ = this.insuranceStateService.state$.pipe(
      map((state) => state.data),
      filter((data) => data != null),
      distinctUntilChanged(),
      map((data) => {
        return data?.reduce(
          (acc, insurance) => {
            if (insurance.policyType === InsurancePolicyType.HOI) {
              Object.assign(acc.hoi, insurance);
            } else if (insurance.policyType === InsurancePolicyType.Flood) {
              Object.assign(acc.floodInsurance, insurance);
            }
            return acc;
          },
          { hoi: {}, floodInsurance: {} },
        );
      }),
      takeUntilDestroyed(),
    );

    this.subjectPropertyStateService.state$
      .pipe(
        map((state) => state.data),
        combineLatestWith(insurance$),
        filter(([subjectProperty, insurance]) => subjectProperty != null && insurance != null),
        distinctUntilChanged(
          ([prevSubjectProperty, prevInsurance], [subjectProperty, insurance]) =>
            prevSubjectProperty === subjectProperty && prevInsurance === insurance,
        ),
        takeUntilDestroyed(),
      )
      .subscribe(([subjectProperty, insurance]) => {
        const joinedData = { ...subjectProperty, ...insurance };
        resetNonDirtyControls(joinedData, this.subjectPropertyRef.subjectPropertyForm);
        this.resetSubject.next(subjectProperty);
      });

    this.LoanEditingState.isLoanEditingDisabled$
      .pipe(takeUntilDestroyed())
      .subscribe((isLoanEditingDisabled) =>
        isLoanEditingDisabled ? this.subjectPropertyRef.subjectPropertyForm.disable() : null,
      );

    this.handleAttachmentHandlerService.addInitListener(
      this.subjectPropertyRef.subjectPropertyForm,
    );

    effect(() => {
      const estimate = this.homeOwnersInsuranceEstimateChanged();
      const hoiFormGroup = this.subjectPropertyRef.subjectPropertyForm.controls.hoi;

      if (estimate?.annualAmount && !hoiFormGroup.controls.amount.getRawValue()) {
        hoiFormGroup.controls.amount.markAsDirty();
        hoiFormGroup.controls.amount.setValue(estimate.annualAmount);
      }
    });
  }

  private addListeners() {
    this.updateHandler.addUpdateListener(this.subjectPropertyRef.subjectPropertyForm);
    this.validationHandler.addPropertyTypeListener(this.subjectPropertyRef.subjectPropertyForm);
    this.validationHandler.addEstimatedValueListener(this.subjectPropertyRef.subjectPropertyForm);
  }
}
