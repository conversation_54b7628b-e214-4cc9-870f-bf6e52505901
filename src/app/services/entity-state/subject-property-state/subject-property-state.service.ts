import { Injectable, inject } from '@angular/core';
import { SubjectProperty } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/subject-property';
import { Observable, tap } from 'rxjs';
import { LoanDependentEntityStateService } from '../abstract-dependent-entity-state.service';
import { EntityStateName } from '../abstract-entity-state.service';
import { OwnedPropertyStateService } from '../owned-property-state/owned-property-state.service';

@Injectable()
export class SubjectPropertyStateService extends LoanDependentEntityStateService<SubjectProperty> {
  ownedPropertyStateService = inject(OwnedPropertyStateService);

  name = EntityStateName.SubjectProperty;

  protected override getDependentEntityState$(loanId: string): Observable<SubjectProperty> {
    return this.dataProvider.getSubjectProperty$(loanId);
  }

  protected override updateEntityState$(
    loanId: string,
    state: SubjectProperty,
  ): Observable<SubjectProperty> {
    return this.dataProvider
      .updateSubjectProperty$(loanId, state)
      .pipe(tap(() => this.ownedPropertyStateService.refreshWithDelay(1000)));
  }
}
