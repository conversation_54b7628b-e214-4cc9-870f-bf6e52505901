import { DestroyRef, Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup, Validators } from '@angular/forms';
import { LoanPurpose } from '@rocket-logic/rl-xp-bff-models';
import { PropertyType } from '@rocket-logic/rl-xp-bff-models/dist/enums/subject-property-type';
import { ValidationHandlerService } from '../abstract-validation-handler.service';
import { LoanInfoFormListenerService } from '../loan-state/loan-info-form-listener.service';
import { SubjectPropertyControls } from './form-types';

@Injectable()
export class SubjectPropertyValidationHandlerService extends ValidationHandlerService {
  private destroyRef = inject(DestroyRef);
  private loanFormListener = inject(LoanInfoFormListenerService);

  addPropertyTypeListener(subjectProperty: FormGroup<SubjectPropertyControls>) {
    subjectProperty.controls.propertyType.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((propertyType) => {
        const numberOfUnitsControl = subjectProperty.controls.numberOfUnits;
        if (propertyType === PropertyType.TwoToFourFamily) {
          numberOfUnitsControl?.addValidators([
            Validators.min(1),
            Validators.max(4),
            Validators.required,
          ]);
        } else {
          numberOfUnitsControl?.clearValidators();
        }
        numberOfUnitsControl?.updateValueAndValidity();
      });
  }

  addEstimatedValueListener(subjectProperty: FormGroup<SubjectPropertyControls>) {
    this.loanFormListener.loanPurpose$
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((loanPurpose) => {
        const estimatedPropertyValueControl = subjectProperty.controls.estimatedPropertyValue;
        if (loanPurpose === LoanPurpose.Purchase) {
          estimatedPropertyValueControl.clearValidators();
        } else {
          estimatedPropertyValueControl.setValidators([Validators.min(10_000)]);
        }
        estimatedPropertyValueControl.updateValueAndValidity();
      });
  }
}
