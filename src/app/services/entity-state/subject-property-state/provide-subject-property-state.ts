import { InjectionToken } from '@angular/core';
import { AutoSaveTriggerService } from '../../save-trigger/auto-save.service';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { STATE_SERVICES } from '../abstract-entity-state.service';
import { UPDATE_HANDLERS } from '../abstract-update-handler.service';
import { PropertyAttachmentTypeHandlerService } from './property-attachment-type-handler';
import { SubjectPropertyFormListenerService } from './subject-property-form-listener.service';
import {
  SubjectPropertyFormRef,
  SubjectPropertyFormService,
} from './subject-property-form.service';
import { SubjectPropertyStateService } from './subject-property-state.service';
import { SubjectPropertyUpdateHandlerService } from './subject-property-update-handler.service';
import { SubjectPropertyValidationHandlerService } from './subject-property-validation-handler-service';

export const SUBJECT_PROPERTY_SAVE_TRIGGER = new InjectionToken<SaveTrigger>(
  'SUBJECT_PROPERTY_SAVE_TRIGGER',
);
export const SUBJECT_PROPERTY_AUTO_SAVE_TRIGGER = new InjectionToken<AutoSaveTriggerService>(
  'SUBJECT_PROPERTY_AUTO_SAVE_TRIGGER',
);

export function provideSubjectPropertyState() {
  return [
    SubjectPropertyStateService,
    SubjectPropertyValidationHandlerService,
    SubjectPropertyFormService,
    SubjectPropertyFormRef,
    SubjectPropertyFormListenerService,
    SubjectPropertyUpdateHandlerService,
    PropertyAttachmentTypeHandlerService,
    { provide: SUBJECT_PROPERTY_AUTO_SAVE_TRIGGER, useClass: AutoSaveTriggerService },
    { provide: SUBJECT_PROPERTY_SAVE_TRIGGER, useExisting: SUBJECT_PROPERTY_AUTO_SAVE_TRIGGER },
    { provide: UPDATE_HANDLERS, useExisting: SubjectPropertyUpdateHandlerService, multi: true },
    { provide: STATE_SERVICES, useExisting: SubjectPropertyStateService, multi: true },
  ];
}
