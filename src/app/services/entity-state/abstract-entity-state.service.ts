import { HttpErrorResponse } from '@angular/common/http';
import { Injectable, InjectionToken, Injector, computed, inject } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import {
  Observable,
  Subject,
  catchError,
  combineLatest,
  concatMap,
  defer,
  filter,
  map,
  merge,
  of,
  scan,
  shareReplay,
  startWith,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { DataProviderService } from '../data-provider/data-provider.service';
import { LeadService } from '../lead/lead.service';
import { LoanApplicationStateService } from '../loan-application-state/loan-application-state.service';
import { LoanIdService } from '../loan-id/loan-id.service';

export const STATE_SERVICES = new InjectionToken<EntityStateService<any>[]>('STATE_SERVICES');

export interface Loadable<T> {
  data?: T;
  fetching?: boolean;
  updating?: boolean;
  error?: EntityStateErrorType;
}

export enum EntityStateErrorType {
  FetchError = 'FetchError',
  UpdateError = 'UpdateError',
}

export enum EntityStateName {
  Loan = 'LoanState',
  Client = 'ClientState',
  Assets = 'AssetsState',
  Income = 'IncomeState',
  Liability = 'LiabilityState',
  OwnedProperty = 'OwnedPropertyState',
  SubjectProperty = 'SubjectPropertyState',
  Task = 'TaskState',
  Insurance = 'Insurance',
}

@Injectable()
export abstract class EntityStateService<T, U = T> {
  abstract name: EntityStateName;
  protected loanIdService = inject(LoanIdService);
  protected dataProvider = inject(DataProviderService);
  protected leadService = inject(LeadService);
  protected loanApplicationStateService = inject(LoanApplicationStateService);
  protected logger = inject(SplunkLoggerService);
  protected injector = inject(Injector);

  protected updateQueueSubject = new Subject<Observable<Loadable<T>>>();
  protected refreshSubject = new Subject<void>();
  protected manualStateSubject = new Subject<Loadable<T>>();
  protected stateFetch$: Observable<Loadable<T>> = combineLatest([
    this.loanIdService.loanId$,
    this.refreshSubject.pipe(startWith(null)),
  ]).pipe(
    map(([loanId]) => loanId),
    filter((loanId): loanId is string => !!loanId),
    switchMap((loanId) => {
      return this.getEntityState$(loanId).pipe(
        map((data) => ({ data, fetching: false, error: undefined })),
        catchError((error) => {
          if (error instanceof HttpErrorResponse && error.status === 404) {
            return this.handleNotFound$();
          }
          this.logger.error(`${this.name}: Unexpected error fetching entity state`, error, {
            loanId,
          });
          return of({ error: EntityStateErrorType.FetchError, fetching: false });
        }),
        startWith({ fetching: true }),
      );
    }),
  );
  protected stateUpdate$ = this.updateQueueSubject.pipe(
    concatMap((update$) => {
      return update$;
    }),
  );

  public state$ = merge(this.stateFetch$, this.stateUpdate$, this.manualStateSubject).pipe(
    scan((acc, value) => ({ ...acc, ...value })),
    takeUntilDestroyed(),
    shareReplay(1),
  );
  public state = toSignal(this.state$);
  public isUpdating = computed(() => this.state()?.updating ?? false);
  public isFetching = computed(() => this.state()?.fetching ?? false);

  protected abstract getEntityState$(loanId: string): Observable<T>;
  protected abstract updateEntityState$(loanId: string, state: U): Observable<T>;
  public refreshState() {
    this.refreshSubject.next();
  }

  public dismissError() {
    this.manualStateSubject.next({ error: undefined });
  }

  public updateState(state: U): Observable<Loadable<T>> {
    const resultSubject = new Subject<Loadable<T>>();
    const update$ = this.handleUpdate$(state).pipe(tap(resultSubject));
    this.updateQueueSubject.next(update$);
    return resultSubject.asObservable();
  }

  protected handleUpdate$(update: U): Observable<Loadable<T>> {
    return defer(() => {
      return this.loanIdService.loanId$.pipe(
        take(1),
        map((loanId) => {
          if (loanId) {
            return loanId;
          }

          const message = `${this.name}: No loanId provided for update`;
          this.logger.error(message);
          throw new Error(message);
        }),
        switchMap((loanId) => this.updateEntityState$(loanId, update)),
        map((data) => ({ data, updating: false, error: undefined })),
        catchError((error) => {
          this.logger.error(`${this.name}: Unexpected error updating entity state`, error);
          return of({ updating: false, error: EntityStateErrorType.UpdateError });
        }),
        startWith({ updating: true }),
      );
    });
  }

  protected handleNotFound$(): Observable<Loadable<T>> {
    return of({ data: undefined, fetching: false, error: undefined });
  }
}
