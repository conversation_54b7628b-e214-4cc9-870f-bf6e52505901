import { Injectable, Signal, computed } from '@angular/core';
import { Liability } from '@rocket-logic/rl-xp-bff-models';
import { Observable, catchError, map, of, switchMap, take, tap } from 'rxjs';
import { EntityStateName } from '../abstract-entity-state.service';
import { AbstractCollectionEntityStateService } from '../abstract-collection-entity-state.service';

@Injectable()
export class LiabilityStateService extends AbstractCollectionEntityStateService<
  Liability,
  { liabilityKey: string; liability: Liability }
> {
  override identifier: keyof Liability = 'id';
  override stateValues: Signal<Liability[]> = computed(() =>
    Array.from(this.state()?.data?.values() ?? []),
  );

  name = EntityStateName.Liability;

  protected override getDependentEntityState$(loanId: string): Observable<Map<string, Liability>> {
    return this.dataProvider
      .getLiabilities$(loanId)
      .pipe(map((liabilities) => this.generateState(liabilities)));
  }

  protected override updateEntityState$(
    loanId: string,
    state: { liabilityKey: string; liability: Liability },
  ) {
    const { liability, liabilityKey } = state;
    return this.dataProvider
      .updateLiability$(loanId, liability)
      .pipe(map((updatedLiability) => this.updateStateMap(updatedLiability, liabilityKey)));
  }

  public deleteLiability$(liabilityKey: string) {
    const liability = this.state()?.data?.get(liabilityKey);

    if (!liability?.id) {
      return of({});
    }

    return this.loanIdService.loanId$.pipe(
      switchMap((loanId) => {
        if (!loanId) {
          throw new Error(`Trying to delete liability with id ${liability.id} with no loan id`);
        }
        return this.dataProvider.deleteLiability$(loanId, liability.id);
      }),
      catchError(() => of()),
      tap(() => {
        this.manualStateSubject.next({ data: this.deleteFromStateMap(liabilityKey) });
      }),
      take(1),
    );
  }
}
