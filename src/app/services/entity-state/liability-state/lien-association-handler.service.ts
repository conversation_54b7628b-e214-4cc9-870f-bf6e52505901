import { DestroyRef, inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { distinctUntilChanged, pairwise, startWith } from 'rxjs';
import { OwnedPropertyFormService } from '../owned-property-state/owned-property-form.service';
import { LiabilitySubscriptionManager } from './liability-subscription-manager.service';
import { LiabilityFormRef } from './liability-form.service';

@Injectable()
export class LienAssociationHandlerService {
  private destroyRef = inject(DestroyRef);
  private ownedPropertyFormService = inject(OwnedPropertyFormService);
  private subManager = inject(LiabilitySubscriptionManager);
  private liabilityFormRef = inject(LiabilityFormRef);

  addLienAssociationListener(liabilityFormKey: string) {
    const liabilityForm = this.liabilityFormRef.entityFormMap().get(liabilityFormKey);

    const ownedPropertyIdControl = liabilityForm?.controls.ownedPropertyId;
    const sub = ownedPropertyIdControl?.valueChanges
      .pipe(
        startWith(ownedPropertyIdControl.value),
        distinctUntilChanged(),
        pairwise(),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(([prevOwnedPropertyId, currentOwnedPropertyId]) => {
        if (currentOwnedPropertyId) {
          const ownedPropertyForm =
            this.ownedPropertyFormService.getOwnedPropertyForm(currentOwnedPropertyId);
          if (ownedPropertyForm?.value.isSubjectProperty) {
            liabilityForm?.controls.isPayingOffUnpaidBalanceAsPartOfTransaction.markAsDirty();
            liabilityForm?.patchValue({ isPayingOffUnpaidBalanceAsPartOfTransaction: true });
          } else if (liabilityForm?.value.isPayingOffUnpaidBalanceAsPartOfTransaction) {
            liabilityForm?.controls.isPayingOffUnpaidBalanceAsPartOfTransaction.markAsDirty();
            liabilityForm?.patchValue({ isPayingOffUnpaidBalanceAsPartOfTransaction: false });
          }
        } else if (prevOwnedPropertyId) {
          if (liabilityForm?.value.isPayingOffUnpaidBalanceAsPartOfTransaction) {
            liabilityForm?.patchValue({ isPayingOffUnpaidBalanceAsPartOfTransaction: false });
            liabilityForm?.controls.isPayingOffUnpaidBalanceAsPartOfTransaction.markAsDirty();
          }
        }
      });
    if (sub) {
      this.subManager.addSubscription(liabilityFormKey, sub);
    }
  }
}
