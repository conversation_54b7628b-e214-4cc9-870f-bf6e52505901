import { TestBed } from '@angular/core/testing';

import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { DataProviderService } from '../../data-provider/data-provider.service';
import { LeadService } from '../../lead/lead.service';
import { LoanApplicationStateService } from '../../loan-application-state/loan-application-state.service';
import { LoanIdService } from '../../loan-id/loan-id.service';
import { LiabilityStateService } from './liability-state.service';

describe('LiabilityStateService', () => {
  let service: LiabilityStateService;

  beforeEach(() =>
    MockBuilder(LiabilityStateService)
      .mock(LoanIdService, { loanId$: NEVER })
      .mock(DataProviderService)
      .mock(SplunkLoggerService)
      .mock(LoanApplicationStateService)
      .mock(LeadService),
  );
  beforeEach(() => {
    service = TestBed.inject(LiabilityStateService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
