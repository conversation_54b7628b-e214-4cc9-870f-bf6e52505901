import { DestroyRef, Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { debounceTime } from 'rxjs/operators';
import { AllLiabilityGroup } from './form-types';
import { LiabilityFormRef } from './liability-form.service';

@Injectable()
export class LienPositionHandlerService {
  private destroyRef = inject(DestroyRef);
  private liabilityFormRef = inject(LiabilityFormRef);

  addLienPositionListener() {
    this.liabilityFormRef.formMapChanges$
      .pipe(
        // Avoids rapid updates when form state is being initialized
        debounceTime(10),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((liabilities) => this.handleLiabilityChange(liabilities));
  }

  private handleLiabilityChange(liabilities: [string, AllLiabilityGroup][]) {
    const { associated, unassociated } = this.groupLiabilitiesByProperty(liabilities);

    unassociated.forEach(({ liability }) => {
      if (liability.value.lienInformation?.lienPosition != null) {
        const lienPositionControl = liability.controls.lienInformation.controls.lienPosition;
        lienPositionControl.setValue(null);
      }
    });

    for (const liabilityGroup of associated.values()) {
      liabilityGroup.sort(this.sortByLienPosition).forEach(({ liability }, index) => {
        const lienPosition = index + 1;
        if (liability.value.lienInformation?.lienPosition === lienPosition) {
          return;
        }

        const lienPositionControl = liability.controls.lienInformation.controls.lienPosition;
        lienPositionControl.setValue(lienPosition);
      });
    }
  }

  private groupLiabilitiesByProperty(liabilities: [string, AllLiabilityGroup][]): {
    associated: Map<string, { liability: AllLiabilityGroup }[]>;
    unassociated: { liability: AllLiabilityGroup }[];
  } {
    return liabilities.reduce(
      (acc, [, liability]) => {
        if (!liability.value.ownedPropertyId) {
          acc.unassociated.push({ liability });
          return acc;
        }

        const propertyLiabilities = acc.associated.get(liability.value.ownedPropertyId) ?? [];
        propertyLiabilities.push({ liability });
        acc.associated.set(liability.value.ownedPropertyId, propertyLiabilities);
        return acc;
      },
      {
        associated: new Map<string, { liability: AllLiabilityGroup }[]>(),
        unassociated: [] as { liability: AllLiabilityGroup }[],
      },
    );
  }

  private sortByLienPosition(
    a: { liability: AllLiabilityGroup },
    b: { liability: AllLiabilityGroup },
  ) {
    const { liability: liabilityA } = a;
    const { liability: liabilityB } = b;
    if (
      liabilityA.value.lienInformation?.lienPosition &&
      liabilityB.value.lienInformation?.lienPosition
    ) {
      return (
        liabilityA.value.lienInformation.lienPosition -
        liabilityB.value.lienInformation.lienPosition
      );
    }

    if (liabilityA.value.lienInformation?.lienPosition) {
      return -1;
    }

    if (liabilityB.value.lienInformation?.lienPosition) {
      return 1;
    }

    return 0;
  }
}
