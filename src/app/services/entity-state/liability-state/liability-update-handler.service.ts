import { Inject, Injectable, inject } from '@angular/core';
import { Observable } from 'rxjs';
import { removeEmptyValues } from '../../../util/remove-empty-values';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { EntityStateName } from '../abstract-entity-state.service';
import { DirtyState, UpdateHandlerService } from '../abstract-update-handler.service';
import { AllLiabilityGroup } from './form-types';
import { LiabilityStateService } from './liability-state.service';
import { LiabilitySubscriptionManager } from './liability-subscription-manager.service';
import { LIABILITY_SAVE_TRIGGER } from './provide-liability-state';

@Injectable()
export class LiabilityUpdateHandlerService extends UpdateHandlerService {
  private stateService = inject(LiabilityStateService);
  private subManager = inject(LiabilitySubscriptionManager);

  override name = EntityStateName.Liability;

  constructor(@Inject(LIABILITY_SAVE_TRIGGER) saveTrigger: SaveTrigger) {
    super(saveTrigger);
  }

  addLiabilityUpdateListener(liabilityForm: AllLiabilityGroup, liabilityKey: string) {
    const sub = this.listenForDirtyState(liabilityKey, liabilityForm);
    this.subManager.addSubscription(liabilityKey, sub);
  }

  onDeleteLiability(key: string) {
    this.dirtyState.update((curr) => {
      const next = { ...curr };
      delete next[key];
      return next;
    });
  }

  protected override handleUpdate$(
    liabilityKey: string,
    state: DirtyState,
  ): Observable<{ error?: any }> {
    const cleanedState = removeEmptyValues(state.form.getRawValue());
    const update = { liabilityKey, liability: cleanedState };
    return this.stateService.updateState(update);
  }
}
