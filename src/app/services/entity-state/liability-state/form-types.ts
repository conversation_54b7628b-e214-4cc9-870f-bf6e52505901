import { FormGroup } from '@angular/forms';
import {
  AlimonyLiability,
  AutoLease,
  AutoLoan,
  ChildSupportLiability,
  CollectionAccount,
  HomeEquityLineOfCredit,
  LandContract,
  Liability,
  LiabilitySecuredByLien,
  LienInformation,
  MechanicsLien,
  MiscInstallment,
  MonetaryJudgement,
  MonthlyRentPayment,
  MortgageLoan,
  Open30DayChargeAccount,
  PropertyAssessedCleanEnergy,
  RealEstateLiability,
  Revolving,
  SolarPanelPayment,
  StudentLoan,
  TaxesOwed,
  UnknownLiability,
} from '@rocket-logic/rl-xp-bff-models';
import { Controlify, Formify } from '../../../util/form-utility-types';

type OmitLiabilityType<T extends Liability> = Omit<T, 'liabilityType'>;
export type AllLiabilityType = Liability &
  LiabilitySecuredByLien &
  RealEstateLiability &
  OmitLiabilityType<AlimonyLiability> &
  OmitLiabilityType<AutoLease> &
  OmitLiabilityType<AutoLoan> &
  OmitLiabilityType<ChildSupportLiability> &
  OmitLiabilityType<CollectionAccount> &
  OmitLiabilityType<MiscInstallment> &
  OmitLiabilityType<MonetaryJudgement> &
  OmitLiabilityType<MonthlyRentPayment> &
  OmitLiabilityType<Open30DayChargeAccount> &
  OmitLiabilityType<Revolving> &
  OmitLiabilityType<StudentLoan> &
  OmitLiabilityType<UnknownLiability> &
  Omit<HomeEquityLineOfCredit, 'liabilityType' | 'amortizationType'> &
  OmitLiabilityType<LandContract> &
  OmitLiabilityType<MechanicsLien> &
  OmitLiabilityType<MortgageLoan> &
  OmitLiabilityType<PropertyAssessedCleanEnergy> &
  OmitLiabilityType<SolarPanelPayment> &
  OmitLiabilityType<TaxesOwed>;

export type AllLiabilityControls = Controlify<AllLiabilityOmit> & AllLiabilityOverrides;

export type AllLiabilityForm = Formify<AllLiabilityOmit> & AllLiabilityOverrides;
export type AllLiabilityGroup = FormGroup<Controlify<AllLiabilityOmit> & AllLiabilityOverrides>;
type AllLiabilityOmit = Omit<AllLiabilityType, 'lienInformation'>;
type AllLiabilityOverrides = {
  lienInformation: LienInformationGroup;
};

export type LienInformationForm = Formify<LienInformation>;
export type LienInformationGroup = FormGroup<Controlify<LienInformation>>;
