import { TestBed } from '@angular/core/testing';

import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { LoanEditingState } from '../loan-state/loan-editing-state.service';
import { LiabilityFormRef, LiabilityFormService } from './liability-form.service';
import { LiabilityStateService } from './liability-state.service';
import { LiabilitySubscriptionManager } from './liability-subscription-manager.service';
import { LiabilityUpdateHandlerService } from './liability-update-handler.service';
import { LienAssociationHandlerService } from './lien-association-handler.service';
import { LienPositionHandlerService } from './lien-position-handler.service';
import { LIABILITY_AUTO_SAVE_TRIGGER } from './provide-liability-state';

describe('LiabilityFormService', () => {
  let service: LiabilityFormService;

  beforeEach(() =>
    MockBuilder(LiabilityFormService)
      .mock(LiabilityStateService, { state$: NEVER })
      .mock(LoanEditingState, { isLoanEditingDisabled$: NEVER })
      .mock(LiabilityUpdateHandlerService)
      .mock(LienPositionHandlerService)
      .mock(LienAssociationHandlerService)
      .mock(LiabilitySubscriptionManager)
      .provide({ provide: LIABILITY_AUTO_SAVE_TRIGGER, useValue: { registerControls: () => {} } })
      .provide(LiabilityFormRef),
  );
  beforeEach(() => {
    service = TestBed.inject(LiabilityFormService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
