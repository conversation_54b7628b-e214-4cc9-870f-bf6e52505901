import { InjectionToken, Provider } from '@angular/core';
import { AutoSaveTriggerService } from '../../save-trigger/auto-save.service';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { STATE_SERVICES } from '../abstract-entity-state.service';
import { UPDATE_HANDLERS } from '../abstract-update-handler.service';
import { LiabilityFormRef, LiabilityFormService } from './liability-form.service';
import { LiabilityStateService } from './liability-state.service';
import { LiabilitySubscriptionManager } from './liability-subscription-manager.service';
import { LiabilityUpdateHandlerService } from './liability-update-handler.service';
import { LienAssociationHandlerService } from './lien-association-handler.service';
import { LienPositionHandlerService } from './lien-position-handler.service';

export const LIABILITY_SAVE_TRIGGER = new InjectionToken<SaveTrigger>('LIABILITY_SAVE_TRIGGER');
export const LIABILITY_AUTO_SAVE_TRIGGER = new InjectionToken<AutoSaveTriggerService>(
  'LIABILITY_AUTO_SAVE_TRIGGER',
);

export function provideLiabilityState(): Provider[] {
  return [
    LiabilityStateService,
    LiabilityFormService,
    LiabilityUpdateHandlerService,
    { provide: LIABILITY_AUTO_SAVE_TRIGGER, useClass: AutoSaveTriggerService },
    { provide: LIABILITY_SAVE_TRIGGER, useExisting: LIABILITY_AUTO_SAVE_TRIGGER },
    { provide: UPDATE_HANDLERS, useExisting: LiabilityUpdateHandlerService, multi: true },
    { provide: STATE_SERVICES, useExisting: LiabilityStateService, multi: true },
    LienPositionHandlerService,
    LienAssociationHandlerService,
    LiabilitySubscriptionManager,
    LiabilityFormRef,
  ];
}
