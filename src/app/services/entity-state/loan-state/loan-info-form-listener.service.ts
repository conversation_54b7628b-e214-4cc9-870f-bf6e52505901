import { Injectable, Signal, computed, inject } from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { LoanPurpose, PurchaseLoan } from '@rocket-logic/rl-xp-bff-models';
import { map, merge, shareReplay, startWith } from 'rxjs';
import { LeadService } from '../../lead/lead.service';
import { LoanFormRef } from './loan-form.service';

@Injectable()
export class LoanInfoFormListenerService {
  private loanFormRef = inject(LoanFormRef);
  readonly leadService = inject(LeadService);
  leadServiceDetails = toSignal(this.leadService.lead$);
  isAssumptionLead = computed(() => {
    return this.leadServiceDetails()?.isAssumptionLead;
  });

  public readonly loanPurpose$ = merge(
    this.loanFormRef.loanForm.valueChanges,
    this.loanFormRef.reset$,
  ).pipe(
    startWith(this.loanFormRef.loanForm.getRawValue().loanPurpose),
    map(() => this.loanFormRef.loanForm.getRawValue().loanPurpose),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  public loanPurposeSignal = toSignal(this.loanPurpose$);

  public purchasePrice: Signal<number | undefined> = toSignal(
    merge(this.loanFormRef.loanForm.valueChanges, this.loanFormRef.reset$).pipe(
      map((formChange) => {
        if (this.loanFormRef.loanForm.getRawValue().loanPurpose === LoanPurpose.Purchase) {
          const purchaseLoan = formChange as PurchaseLoan;
          return purchaseLoan.purchasePrice ?? 0;
        }
        return 0;
      }),
      startWith(this.loanFormRef.loanForm.value.purchasePrice ?? 0),
    ),
  );

  public hasSignedPa = toSignal(
    this.loanFormRef.loanForm.valueChanges.pipe(
      map(() => this.loanFormRef.loanForm.getRawValue()),
      map((loan) => {
        if (loan?.loanPurpose === LoanPurpose.Purchase) {
          return loan?.hasSignedPurchaseAgreement ?? false;
        }
        return false;
      }),
      startWith(this.loanFormRef.loanForm.getRawValue().hasSignedPurchaseAgreement ?? false),
    ),
  );

  public refiSelected = computed(() => this.loanPurposeSignal() === LoanPurpose.Refinance);
  public refiSelected$ = toObservable(this.refiSelected);
  public purchaseSelected = computed(() => this.loanPurposeSignal() === LoanPurpose.Purchase);
  public purchaseSelected$ = toObservable(this.purchaseSelected);
}
