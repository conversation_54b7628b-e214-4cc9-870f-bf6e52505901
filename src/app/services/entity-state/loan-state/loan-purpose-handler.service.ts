import { DestroyRef, Injectable, Injector, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { LoanPurpose } from '@rocket-logic/rl-xp-bff-models';
import { exhaustMap, filter, map, of, switchMap, tap, withLatestFrom } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { ConfirmationDialogComponent } from '../../../confirmation-dialog/confirmation-dialog.component';
import { ExclusionService } from '../../exclusion/exclusion.service';
import { LoanControlType } from './loan-form.service';
import { LoanStateService } from './loan-state.service';

@Injectable()
export class LoanPurposeHandlerService {
  stateService = inject(LoanStateService);
  dialog = inject(MatDialog);
  destroyRef = inject(DestroyRef);
  injector = inject(Injector);
  exclusionService = inject(ExclusionService);
  allowRefiAccess = environment.featureFlags?.allowRefiAccess;

  dialogRef?: MatDialogRef<ConfirmationDialogComponent>;

  addLoanPurposeListener(loanForm: FormGroup<LoanControlType>) {
    const loanPurposeControl = loanForm.get('loanPurpose')!;

    loanPurposeControl.valueChanges
      .pipe(
        withLatestFrom(this.stateService.state$),
        tap(console.log),
        filter(([newValue, loan]) => newValue !== null),
        filter(([newValue, loan]) => newValue !== loan.data?.loanPurpose),
        exhaustMap(([newValue, loan]) => {
          const oldValue = loan.data?.loanPurpose;

          if (!oldValue) {
            return of({ didConfirm: true, oldValue });
          }

          this.dialogRef = this.dialog.open(ConfirmationDialogComponent, {
            data: { newLoanPurpose: newValue, oldLoanPurpose: oldValue },
            panelClass: 'rkt-Dialog',
            backdropClass: 'rkt-Backdrop',
            injector: this.injector,
            autoFocus: 'dialog',
          });

          return this.dialogRef.afterClosed().pipe(
            tap(() => (this.dialogRef = undefined)),
            map((didConfirm: boolean) => ({ didConfirm, oldValue })),
          );
        }),
        takeUntilDestroyed(),
      )
      .subscribe(({ didConfirm, oldValue }) => {
        if (didConfirm) {
          this.saveLoanPurpose(loanForm);
        } else {
          loanPurposeControl.reset(oldValue);
        }
      });
  }

  private saveLoanPurpose(loanForm: FormGroup<LoanControlType>) {
    const loan = this.stateService.state();
    const loanPurposeControl = loanForm.get('loanPurpose')!;
    const newLoanPurpose = loanPurposeControl.value;

    if (newLoanPurpose) {
      loanPurposeControl.markAsPristine();
      loanPurposeControl.updateValueAndValidity();
      this.stateService
        .updateState({
          ...loan?.data,
          loanPurpose: newLoanPurpose,
        })
        .pipe(
          switchMap((res) => {
            if (res.error || newLoanPurpose === LoanPurpose.Purchase || this.allowRefiAccess) {
              return of(res);
            }

            return this.exclusionService.createLoanPurposeExclusion$().pipe(map(() => res));
          }),
        )
        .subscribe(({ error }) => {
          if (error) {
            loanPurposeControl.reset(loan?.data?.loanPurpose);
          }
        });
    }
  }
}
