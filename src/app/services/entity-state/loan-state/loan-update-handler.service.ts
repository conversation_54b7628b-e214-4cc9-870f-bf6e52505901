import { Inject, Injectable, inject } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { Observable } from 'rxjs';
import { removeEmptyValues } from '../../../util/remove-empty-values';
import { SaveTrigger } from '../../save-trigger/save-trigger';
import { EntityStateName } from '../abstract-entity-state.service';
import { DirtyState, UpdateHandlerService } from '../abstract-update-handler.service';
import { LoanControlType } from './loan-form.service';
import { LoanStateService } from './loan-state.service';
import { LOAN_SAVE_TRIGGER } from './provide-loan-state';

@Injectable()
export class LoanUpdateHandlerService extends UpdateHandlerService {
  static IDENTIFIER = 'loan';
  stateService = inject(LoanStateService);

  override name = EntityStateName.Loan;

  constructor(@Inject(LOAN_SAVE_TRIGGER) saveTrigger: SaveTrigger) {
    super(saveTrigger);
  }

  addUpdateListener(loanForm: FormGroup<LoanControlType>) {
    this.listenForDirtyState(LoanUpdateHandlerService.IDENTIFIER, loanForm);
  }

  protected override handleUpdate$(_: string, state: DirtyState): Observable<{ error?: any }> {
    return this.stateService.updateState(removeEmptyValues(state.form.getRawValue()));
  }
}
