import { DestroyRef, inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup, Validators } from '@angular/forms';
import { LoanPurpose, RefinanceGoal } from '@rocket-logic/rl-xp-bff-models';
import { combineLatest, distinctUntilChanged, map, startWith } from 'rxjs';
import { requireNonZeroValidator } from '../../../util/require-non-zero-validator';
import { ValidationHandlerService } from '../abstract-validation-handler.service';
import { ClientFormService } from '../client-state/client-form.service';
import { LoanControlType } from './loan-form.service';

@Injectable()
export class LoanValidationHandlerService extends ValidationHandlerService {
  private destroyRef = inject(DestroyRef);
  private clientFormService = inject(ClientFormService);

  addValidationListeners(loanForm: FormGroup<LoanControlType>) {
    this.addHelocValidationListener(loanForm);
    this.addDownPaymentValidationListener(loanForm);
    this.addCashOutPurchasePriceValidationListener(loanForm);
  }

  private addHelocValidationListener(loanForm: FormGroup<LoanControlType>) {
    const helocGroup = loanForm.controls.helocDetails;
    const helocControls = ['loanAmount', 'drawDownAmount', 'lienPosition'];

    loanForm.controls.refinanceGoals.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((goals) => {
        const hasHelocGoal = goals?.includes(RefinanceGoal.Heloc);
        if (!hasHelocGoal) {
          this.removeRequiredValidators(helocGroup, helocControls);
          helocGroup.reset();
          helocGroup.disable();
        } else {
          this.addRequiredValidators(helocGroup, helocControls);
          helocGroup.enable();
        }
      });
  }

  private addDownPaymentValidationListener(loanForm: FormGroup<LoanControlType>): void {
    const loanPurposeControl = loanForm.controls.loanPurpose;
    const downPaymentControl = loanForm.controls.downPaymentAmount;

    const primaryBorrowerInfo$ = this.clientFormService.getPrimaryBorrowerInfo$();

    /**
     * Only Primary Borrower's Military Status is Relevant
     */
    const hasMilitaryService$ = primaryBorrowerInfo$.pipe(
      distinctUntilChanged(),
      map((client) => client?.military?.hasMilitaryService ?? false),
    );

    const loanPurpose$ = loanPurposeControl.valueChanges.pipe(distinctUntilChanged());

    /**
     * Track changes in VA Status and Loan Purpose, add or remove validator accordingly.
     */
    combineLatest([hasMilitaryService$, loanPurpose$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([hasMilitaryService, loanPurpose]) => {
        if (loanPurpose === LoanPurpose.Refinance || hasMilitaryService) {
          downPaymentControl.removeValidators(requireNonZeroValidator);
        } else {
          downPaymentControl.addValidators(requireNonZeroValidator);
        }
        downPaymentControl.updateValueAndValidity();
      });
  }

  private addCashOutPurchasePriceValidationListener(loanForm: FormGroup<LoanControlType>): void {
    const loanPurposeControl = loanForm.controls.loanPurpose;
    const cashOutAmountControl = loanForm.controls.desiredCashOutAmount;
    const purchasePriceControl = loanForm.controls.purchasePrice;
    loanPurposeControl.valueChanges
      .pipe(
        startWith(loanPurposeControl.value),
        map(() => loanPurposeControl.getRawValue()),
        distinctUntilChanged(),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((loanPurpose) => {
        if (loanPurpose === LoanPurpose.Purchase) {
          cashOutAmountControl.clearValidators();
          purchasePriceControl.addValidators([Validators.min(10_000), Validators.max(10_000_000)]);
          cashOutAmountControl.updateValueAndValidity();
          purchasePriceControl.updateValueAndValidity();
        } else if (loanPurpose === LoanPurpose.Refinance) {
          purchasePriceControl.clearValidators();
          cashOutAmountControl.addValidators([Validators.min(1_000), Validators.max(3_000_000)]);
          purchasePriceControl.updateValueAndValidity();
          cashOutAmountControl.updateValueAndValidity();
        }
      });
  }
}
