import { Injectable, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import {
  Loan,
  LoanPurpose,
  NewConstructionDetails,
  PurchaseLoan,
  ReasonsForPurchase,
  RefinanceLoan,
} from '@rocket-logic/rl-xp-bff-models';
import { HelocDetails } from '@rocket-logic/rl-xp-bff-models/dist/refinance/heloc-details';
import { Subject, distinctUntilChanged, filter, map, withLatestFrom } from 'rxjs';
import { buildHelocDetailsForm } from '../../../util/build-heloc-details-form';
import { Controlify, Formify } from '../../../util/form-utility-types';
import { resetNonDirtyControls } from '../../../util/reset-non-dirty-controls';
import { AssumptionSignedPurchaseHandlerService } from '../../assumption/assumption-signed-purchase';
import { HelocEscrowWaiverHandlerService } from '../../heloc/heloc-escrow-waiver.service';
import { NewConstructionListenerService } from '../../new-construction/new-construction-listener.service';
import { LoanEditingState } from './loan-editing-state.service';
import { LoanPurposeHandlerService } from './loan-purpose-handler.service';
import { LoanStateService } from './loan-state.service';
import { LoanUpdateHandlerService } from './loan-update-handler.service';
import { LoanValidationHandlerService } from './loan-validation-handler.service';
import { LOAN_AUTO_SAVE_TRIGGER } from './provide-loan-state';

export type CombinedLoanType = PurchaseLoan & RefinanceLoan;
type LoanFormType = Formify<
  Omit<CombinedLoanType, 'reasonsForPurchase' | 'newConstructionDetails' | 'helocDetails'>
> & {
  reasonsForPurchase: FormGroup<Controlify<ReasonsForPurchase>>;
  newConstructionDetails: FormGroup<Controlify<NewConstructionDetails>>;
  helocDetails: FormGroup<Controlify<HelocDetails>>;
};
export type LoanControlType = Controlify<
  Omit<CombinedLoanType, 'reasonsForPurchase' | 'newConstructionDetails' | 'helocDetails'>
> & {
  reasonsForPurchase: FormGroup<Controlify<ReasonsForPurchase>>;
  newConstructionDetails: FormGroup<Controlify<NewConstructionDetails>>;
  helocDetails: FormGroup<Controlify<HelocDetails>>;
};

@Injectable()
export class LoanFormRef {
  private formBuilder = inject(FormBuilder);
  public resetSubject = new Subject<Loan | undefined>();
  public reset$ = this.resetSubject.asObservable();

  public isNonArmsLength = signal(false);

  public readonly loanForm: FormGroup<LoanControlType> = this.formBuilder.group<LoanFormType>({
    loanPurpose: [null, [Validators.required]],
    // REFI
    desiredCashOutAmount: [null],
    refinanceGoals: [null],
    reasonsForCashOut: [null],
    hasReceivedCashBackOnPreviousLoan: [null],
    isPreviousLoanTexas50a6: [null],
    isCEMA: [null],
    hasNetEscrows: [null],
    // PURCHASE
    hasSignedPurchaseAgreement: [null],
    isForSaleByOwner: [null],
    sellerConcessions: [null],
    contractClosingDate: [null],
    clientRequestedClosingDate: [null],
    maximumClientWantsToPayAtClosing: [null],
    reasonsForPurchase: this.formBuilder.group<Formify<ReasonsForPurchase>>({
      isBetterArea: [null],
      isChangeInHouseSize: [null],
      isFirstTimeHomebuyer: [null],
      isInvestment: [null],
      isMoveCloserToFamily: [null],
      isNewFinancialPosition: [null],
      isPurchasingForFamilyOrFriend: [null],
      isPurchasingSecondHome: [null],
      isRelocatingForWork: [null],
      isUpsizingOrDownSizing: [null],
    }),
    sellerRelationshipToBorrower: [null],
    downPaymentAmount: [null],
    purchasePrice: [null],
    loanDeactivationDetails: [null],
    escrowWaiver: [null],
    newConstructionDetails: this.formBuilder.group<Formify<NewConstructionDetails>>({
      buildingStatus: [null],
      isNewConstruction: [null],
    }),
    helocDetails: buildHelocDetailsForm(this.formBuilder),
  });
}

@Injectable()
export class LoanFormService {
  private loanFormRef = inject(LoanFormRef);
  private loanStateService = inject(LoanStateService);
  private LoanEditingState = inject(LoanEditingState);
  private updateHandler = inject(LoanUpdateHandlerService);
  private loanPurposeHandler = inject(LoanPurposeHandlerService);
  private newConstructionListenerService = inject(NewConstructionListenerService);
  private validationHandler = inject(LoanValidationHandlerService);
  private helocEscrowWaiverHandler = inject(HelocEscrowWaiverHandlerService);
  private assumptionSignedPurchaseHandler = inject(AssumptionSignedPurchaseHandlerService);
  private autoSaveTrigger = inject(LOAN_AUTO_SAVE_TRIGGER);

  public readonly loanForm: FormGroup<LoanControlType> = this.loanFormRef.loanForm;

  constructor() {
    this.autoSaveTrigger.registerControls(this.loanForm);
    this.addListeners();
    this.loanStateService.state$
      .pipe(
        map((state) => state.data),
        filter((data) => data != null),
        distinctUntilChanged(),
        withLatestFrom(this.LoanEditingState.isLoanEditingDisabled$),
        takeUntilDestroyed(),
      )
      .subscribe(([data, isLoanEditingDisabled]) => {
        resetNonDirtyControls(data, this.loanForm);
        this.loanFormRef.resetSubject.next(data);
        if (isLoanEditingDisabled) {
          this.loanForm.disable();
        }

        const loanPurpose = data?.loanPurpose;
        const hasNonArmsLengthConditions =
          (data as PurchaseLoan)?.sellerRelationshipToBorrower != null;
        if (loanPurpose === LoanPurpose.Refinance) {
          this.loanFormRef.isNonArmsLength.set(false);
        } else if (hasNonArmsLengthConditions) {
          this.loanFormRef.isNonArmsLength.set(true);
        }
      });
  }

  private addListeners() {
    this.updateHandler.addUpdateListener(this.loanForm);
    this.loanPurposeHandler.addLoanPurposeListener(this.loanForm);
    this.newConstructionListenerService.addNewConstructionListener(this.loanForm);
    this.validationHandler.addValidationListeners(this.loanForm);
    this.helocEscrowWaiverHandler.addHelocEscrowListener(this.loanForm);
    this.assumptionSignedPurchaseHandler.addAssumptionSignedPurchaseListener(this.loanForm);
  }
}
