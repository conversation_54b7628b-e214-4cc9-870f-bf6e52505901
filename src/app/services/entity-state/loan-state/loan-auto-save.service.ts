import { Injectable, inject } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { Observable, delay, of, switchMap } from 'rxjs';
import { AutoSaveTriggerService } from '../../save-trigger/auto-save.service';
import { LoanPurposeHandlerService } from './loan-purpose-handler.service';

@Injectable()
export class LoanAutoSaveTriggerService extends AutoSaveTriggerService {
  private loanPurposeHandler = inject(LoanPurposeHandlerService);

  protected override getChangeTrigger$(controls: AbstractControl[]): Observable<any> {
    return super.getChangeTrigger$(controls).pipe(
      switchMap((value) => {
        if (this.loanPurposeHandler.dialogRef) {
          // Wait for loan purpose change to be handled then start the save timer again
          return this.loanPurposeHandler.dialogRef.afterClosed().pipe(delay(this.saveDelay));
        }

        return of(value);
      }),
    );
  }
}
