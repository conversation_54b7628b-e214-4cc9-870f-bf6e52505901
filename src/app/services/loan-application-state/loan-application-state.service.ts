import { Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { LoanApplicationState } from '@rocket-logic/rl-xp-bff-models/dist/loan-application-state';
import { Observable, catchError, filter, map, of, shareReplay, switchMap } from 'rxjs';
import { DataProviderService } from '../data-provider/data-provider.service';
import { LoanIdService } from '../loan-id/loan-id.service';

@Injectable()
export class LoanApplicationStateService {
  private dataProviderService = inject(DataProviderService);
  private loanIdService = inject(LoanIdService);

  loanAppState$: Observable<LoanApplicationState> = this.loanIdService.loanId$.pipe(
    filter((loanId): loanId is string => !!loanId),
    switchMap((loanId) => this.getLoanApplicationState$(loanId)),
    catchError(() => of(null)),
    filter((loanAppState): loanAppState is LoanApplicationState => !!loanAppState),
    filter((loanAppState) => Object.keys(loanAppState).length > 0),
    shareReplay(1),
    takeUntilDestroyed(),
  );

  isXpCompleted$ = this.loanAppState$.pipe(
    map((loanAppState) => loanAppState.isXpComplete),
    shareReplay(1),
    takeUntilDestroyed(),
  );

  private getLoanApplicationState$(loanId: string): Observable<LoanApplicationState> {
    return this.dataProviderService.getLoanApplicationState$(loanId);
  }
}
