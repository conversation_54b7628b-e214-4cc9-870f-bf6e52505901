import { Injectable, inject } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { TaskStatus } from '@rocket-logic/rl-xp-bff-models';
import { EMPTY, combineLatest, filter, merge, switchMap } from 'rxjs';
import { repeatUntil } from '../../util/repeat-until';
import { CreditService } from '../credit/credit.service';
import { DataProviderService } from '../data-provider/data-provider.service';
import { LoanIdService } from '../loan-id/loan-id.service';
import { TaskStateService } from './task-state.service';

@Injectable()
export class PollCreditTasksListenerService {
  private taskState = inject(TaskStateService);
  private creditService = inject(CreditService);
  private loanIdService = inject(LoanIdService);
  private dataProvider = inject(DataProviderService);

  constructor() {
    this.startListener();
  }

  private creditTasks$ = toObservable(this.creditService.creditTasks);

  private startListener() {
    combineLatest([
      this.creditTasks$,
      this.loanIdService.loanId$.pipe(filter((loanNumber): loanNumber is string => !!loanNumber)),
    ])
      .pipe(
        switchMap(([creditTasks, loanNumber]) => {
          const createdTasks = creditTasks?.filter((task) => task.status === TaskStatus.Created);
          if (createdTasks?.length ?? false) {
            return merge(
              ...createdTasks!.map((task) => {
                return this.dataProvider
                  .getTask$(loanNumber, task.taskId)
                  .pipe(
                    repeatUntil(
                      (polledTask) =>
                        polledTask.status === TaskStatus.Removed ||
                        polledTask.status === TaskStatus.Completed,
                    ),
                  );
              }),
            );
          }
          return EMPTY;
        }),
        takeUntilDestroyed(),
      )
      .subscribe((completedTask) => {
        this.taskState.updateState(completedTask);
      });
  }
}
