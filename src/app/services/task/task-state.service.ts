import { Injectable } from '@angular/core';
import { CompleteTaskRequest, Task } from '@rocket-logic/rl-xp-bff-models';
import { NEVER, Observable, map, switchMap, take } from 'rxjs';
import { EntityStateName, EntityStateService } from '../entity-state/abstract-entity-state.service';

@Injectable({
  providedIn: 'root',
})
export class TaskStateService extends EntityStateService<Task[], Task> {
  name = EntityStateName.Task;

  protected override getEntityState$(loanId: string): Observable<Task[]> {
    return this.dataProvider.getTasks$(loanId);
  }

  protected override updateEntityState$(_loanId: string, task: Task): Observable<Task[]> {
    // for now tasks are considered readonly, we don't push updates to the backend
    return this.state$.pipe(
      take(1),
      map((state) => [...(state?.data || [])]),
      map((state) => {
        const idx = state.findIndex((v) => v.taskId === task.taskId);
        if (idx !== -1) {
          state[idx] = task;
        } else {
          state.push(task);
        }
        return state;
      }),
    );
  }

  public completeTask$(taskId: string, completeTaskRequest: CompleteTaskRequest) {
    return this.loanIdService.loanId$.pipe(
      take(1),
      switchMap((loanId) =>
        loanId ? this.dataProvider.completeTask$(loanId, taskId, completeTaskRequest) : NEVER,
      ),
    );
  }
}
