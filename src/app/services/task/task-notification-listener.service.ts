import { Injectable, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { TaskKind, notificationSubjects } from '@rocket-logic/rl-xp-bff-models';
import { filter, map, switchMap } from 'rxjs';
import { DataProviderService } from '../data-provider/data-provider.service';
import { ClientStateService } from '../entity-state/client-state/client-state.service';
import { LoanNotificationService } from '../notification/loan-notification.service';
import { TaskStateService } from './task-state.service';

const TASK_CREATED_EVENT_TYPE = 'rocket-logic.task.created';
const TASK_UPDATED_EVENT_TYPE = 'rocket-logic.task.updated';

@Injectable()
export class TaskNotificationListenerService {
  private dataProvider = inject(DataProviderService);
  private notificationService = inject(LoanNotificationService);
  private taskState = inject(TaskStateService);
  private clientState = inject(ClientStateService);

  private expectedTaskKinds: string[] = Object.values(TaskKind);

  private taskEvents$ = this.notificationService.notifications$.pipe(
    filter(
      (notification) =>
        notification.type === TASK_CREATED_EVENT_TYPE ||
        notification.type === TASK_UPDATED_EVENT_TYPE,
    ),
    map((notification) => notification.payload as notificationSubjects.RocketLogicTaskEvent),
    filter((payload) => this.isExpectedTaskKind(payload.taskKind)),
  );

  constructor() {
    this.startListener();
  }

  private startListener() {
    this.taskEvents$
      .pipe(
        switchMap((event) => this.dataProvider.getTask$(event.loanNumber, event.taskId)),
        takeUntilDestroyed(),
      )
      .subscribe((task) => {
        this.taskState.updateState(task);
      });
  }

  private isExpectedTaskKind(kind: string) {
    return this.expectedTaskKinds.includes(kind);
  }
}
