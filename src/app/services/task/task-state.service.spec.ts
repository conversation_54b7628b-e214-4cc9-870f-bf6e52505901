import { TestBed } from '@angular/core/testing';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { MockBuilder } from 'ng-mocks';
import { DataProviderService } from '../data-provider/data-provider.service';
import { LeadService } from '../lead/lead.service';
import { LoanApplicationStateService } from '../loan-application-state/loan-application-state.service';
import { LoanIdService } from '../loan-id/loan-id.service';
import { TaskStateService } from './task-state.service';

describe('TaskStateService', () => {
  let service: TaskStateService;

  beforeEach(() =>
    MockBuilder(TaskStateService)
      .mock(LoanIdService)
      .mock(DataProviderService)
      .mock(SplunkLoggerService)
      .mock(LoanApplicationStateService)
      .mock(LeadService),
  );
  beforeEach(() => {
    service = TestBed.inject(TaskStateService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
