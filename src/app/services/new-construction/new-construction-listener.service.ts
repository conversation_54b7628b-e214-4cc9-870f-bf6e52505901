import { DestroyRef, inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { distinctUntilChanged, filter, startWith } from 'rxjs';
import { LoanControlType } from '../entity-state/loan-state/loan-form.service';

@Injectable()
export class NewConstructionListenerService {
  private destroyRef = inject(DestroyRef);

  addNewConstructionListener(loanForm: FormGroup<LoanControlType>) {
    const newConstructionControl =
      loanForm.controls.newConstructionDetails.controls.isNewConstruction;
    const hasSignedPaControl = loanForm.controls.hasSignedPurchaseAgreement;

    newConstructionControl.valueChanges
      .pipe(
        startWith(newConstructionControl.value),
        distinctUntilChanged(),
        filter((isNewConstruction) => !!isNewConstruction),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((isNewConstruction) => hasSignedPaControl.setValue(isNewConstruction));
  }
}
