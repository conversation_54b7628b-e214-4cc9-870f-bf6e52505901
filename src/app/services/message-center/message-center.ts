import { Client } from '@rocket-logic/rl-xp-bff-models';
import { AssetRequestStatus } from '@rocket-logic/rl-xp-bff-models/dist/notification/subjects';
import { TaskDisplayData } from '../../credit-task/credit-task.component';
import {
  EntityStateErrorType,
  EntityStateName,
} from '../entity-state/abstract-entity-state.service';
import { LeadInitError } from '../lead/lead.service';

export enum MesageCenterWarningType {
  VaResequencingWarning = 'vaResequencingWarning',
  AssetDataPullWarning = 'assetDataPullWarning',
  AddressNotFound = 'addressNotFound',
}

export enum MessageCenterInfoType {
  AssetDataPull = 'assetDataPull',
  HasPrefillData = 'hasPrefillData',
}

export enum MessageCenterErrorType {
  ConflictingLoans = 'conflictingLoans',
  BankerNotLicensed = 'bankerNotLicensed',
  BankerNotAssignedToLead = 'bankerNotAssignedToLead',
  AssetDataPull = 'assetDataPull',
  CreditTask = 'creditTask',
}

export interface MessageCenterWarning {
  type: MesageCenterWarningType;
}

export interface MessageCenterInfo {
  type: MessageCenterInfoType;
}

export interface MessageCenterError {
  type: MessageCenterErrorType | LeadInitError | EntityStateErrorType;
  shouldLock?: boolean;
  onDismiss?: () => void; // TODO: Do we need ?
}

export interface MessageCenterErrorWithContext<T> extends MessageCenterError {
  context: T;
}

export interface EntityStateError
  extends MessageCenterErrorWithContext<{ name: EntityStateName }> {}

export interface BankerLicensedError
  extends MessageCenterErrorWithContext<{ comment?: string; commonId?: string }> {}

export interface OutstandingCreditTaskError
  extends MessageCenterErrorWithContext<{ task: TaskDisplayData }> {}

export interface ConflictingLoansError
  extends MessageCenterErrorWithContext<{
    preferredFirstname?: string;
    preferredLastName?: string;
    loanNumber?: string;
    client?: Client;
  }> {}

export interface AssetDataPullWarning extends MessageCenterWarning {
  failureReasons: string[];
  status: AssetRequestStatus;
}

export function isAssetDataPullWarning(
  error: MessageCenterWarning | AssetDataPullWarning | null,
): error is AssetDataPullWarning {
  return error?.type === MesageCenterWarningType.AssetDataPullWarning;
}

export function isBankerLicensedError(
  error: MessageCenterError | null,
): error is BankerLicensedError {
  return error?.type === MessageCenterErrorType.BankerNotLicensed;
}

export function isConflictingLoansError(
  error: MessageCenterError | null,
): error is ConflictingLoansError {
  return error?.type === MessageCenterErrorType.ConflictingLoans;
}

export function isCreditTaskError(
  error: MessageCenterError | null,
): error is OutstandingCreditTaskError {
  return error?.type === MessageCenterErrorType.CreditTask;
}

export function isEntityStateError(error: MessageCenterError): error is EntityStateError {
  return (
    error.type === EntityStateErrorType.FetchError ||
    error.type === EntityStateErrorType.UpdateError
  );
}
