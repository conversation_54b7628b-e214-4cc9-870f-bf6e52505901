import { computed, inject, Injectable } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { AddressValidationStatus } from '@rocket-logic/rl-xp-bff-models';
import { AssetRequestStatus } from '@rocket-logic/rl-xp-bff-models/dist/notification/subjects';
import { combineLatest, filter, map, startWith, tap } from 'rxjs';
import { ActiveActionService } from '../active-action.service';
import {
  ActiveSidenavScreenService,
  SidenavScreen,
} from '../active-sidenav-screen/active-sidenav-screen.service';
import { AddressDataService } from '../address/address-data.service';
import { AssetDataPullService } from '../asset-data-pull/asset-data-pull.service';
import { BankerAssignmentService } from '../banker-assignment/banker-assignment.service';
import { STATE_SERVICES } from '../entity-state/abstract-entity-state.service';
import { ClientStateService } from '../entity-state/client-state/client-state.service';
import { LeadInitError, LeadService } from '../lead/lead.service';
import { LoanStatusLoadingService } from '../loan-status-loading/loan-status-loading.service';
import { PrefillStateService } from '../prefill-state/prefill-state.service';
import { UserAuthorizationService } from '../user-authorization/user-authorization.service';
import {
  AssetDataPullWarning,
  BankerLicensedError,
  ConflictingLoansError,
  MesageCenterWarningType,
  MessageCenterError,
  MessageCenterErrorType,
  MessageCenterInfo,
  MessageCenterInfoType,
  MessageCenterWarning,
  OutstandingCreditTaskError,
} from './message-center';

@Injectable()
export class MessageCenterService {
  readonly clientStateService = inject(ClientStateService);
  readonly hasPrefillData$ = inject(PrefillStateService).hasPrefillData$;
  private userAuthorizationService = inject(UserAuthorizationService);
  private addressDataService = inject(AddressDataService);
  private bankerAssignmentService = inject(BankerAssignmentService);
  private stateServices = inject(STATE_SERVICES);
  private loanStatusLoadingService = inject(LoanStatusLoadingService);
  private activeSidenavScreenService = inject(ActiveSidenavScreenService);

  private outstandingCreditTasks$ = toObservable(
    inject(ActiveActionService).outstandingCreditTasks,
  );
  private assetDataPullState = inject(AssetDataPullService).assetDataPullState;
  private assetDataPullState$ = toObservable(inject(AssetDataPullService).assetDataPullState);
  private leadInfo = toSignal(inject(LeadService).lead$);
  private firstClient = computed(() => this.clientStateService.stateValues()[0]);

  readonly errorCount = computed(() => this.errors()?.length ?? 0);
  readonly warningCount = computed(() => this.warnings()?.length ?? 0);
  readonly infoCount = computed(() => this.info()?.length ?? 0);

  readonly shouldDisableActions = computed(() =>
    this.errors().some((error) => error?.shouldLock && error.type === LeadInitError.NotFound),
  );

  // Errors
  readonly loanInitError$ = this.loanStatusLoadingService.loanLoadingError$.pipe(
    filter((error): error is LeadInitError => !!error),
    tap((error) => {
      if (error === LeadInitError.NotFound || error === LeadInitError.Failed) {
        this.activeSidenavScreenService.activate(SidenavScreen.Support);
      }
    }),
    map((error) => ({ type: error, shouldLock: true })),
  );
  readonly stateErrors$ = combineLatest(
    this.stateServices.map((service) =>
      service.state$.pipe(
        map((state) => ({
          ...state,
          name: service.name,
          onDismiss: service.dismissError.bind(service),
        })),
      ),
    ),
  ).pipe(
    map((allState) =>
      allState
        .filter((state) => state.error != null)
        .map((state) => ({
          type: state.error!,
          context: {
            name: state.name,
          },
        })),
    ),
  );
  readonly outstandingCreditTaskErrors$ = this.outstandingCreditTasks$.pipe(
    map((tasks) =>
      tasks?.map(
        (task) =>
          ({
            type: MessageCenterErrorType.CreditTask,
            context: {
              task,
            },
          }) as OutstandingCreditTaskError,
      ),
    ),
  );
  readonly assetDataPullError$ = this.assetDataPullState$.pipe(
    map((state) =>
      state.status === AssetRequestStatus.Failure
        ? ({
            type: MessageCenterErrorType.AssetDataPull,
            onDismiss: () => this.assetDataPullState.set({}), // TODO: Do we need ?
          } as MessageCenterError)
        : null,
    ),
  );
  readonly conflictingLoansError$ = this.userAuthorizationService.conflictingLoans$.pipe(
    map((loans) =>
      loans && loans.length > 0
        ? ({
            type: MessageCenterErrorType.ConflictingLoans,
            dismissable: true,
            context: {
              client: this.firstClient(),
              loanNumber: loans[0].loanNumber,
              preferredFirstname: loans[0].assignedTo.preferredFirstName,
              preferredLastName: loans[0].assignedTo.preferredLastName,
            },
          } as ConflictingLoansError)
        : null,
    ),
  );
  readonly bankerLicenseError$ = this.userAuthorizationService.isBankerLicensed$.pipe(
    filter((isLicensed) => isLicensed != null),
    map((isLicensed) =>
      !isLicensed.write
        ? ({
            type: MessageCenterErrorType.BankerNotLicensed,
            context: {
              comment: isLicensed.exclusion?.comment,
              commonId: this.leadInfo()?.assignedToCommonId,
            },
          } as BankerLicensedError)
        : null,
    ),
  );

  readonly bankerAssignmentError$ = this.bankerAssignmentService.userIsAssignedToLead$.pipe(
    map((isAssigned) =>
      !isAssigned
        ? {
            type: MessageCenterErrorType.BankerNotAssignedToLead,
          }
        : null,
    ),
  );

  // Info
  readonly prefillDataInfo$ = this.hasPrefillData$.pipe(
    map((data) =>
      data
        ? ({
            type: MessageCenterInfoType.HasPrefillData,
          } as MessageCenterInfo)
        : null,
    ),
  );
  readonly assetDataPullInfo$ = this.assetDataPullState$.pipe(
    map((state) =>
      state.failureReasons?.length === 0 && state.status !== AssetRequestStatus.Failure
        ? ({ type: MessageCenterInfoType.AssetDataPull } as MessageCenterInfo)
        : null,
    ),
  );

  // Warnings
  readonly addressNotFoundWarning$ = this.addressDataService.addressValidationStatus$.pipe(
    map((response) =>
      response === AddressValidationStatus.NotFound
        ? ({
            type: MesageCenterWarningType.AddressNotFound,
            onDismiss: () => this.addressDataService.flushLesStatus(),
          } as MessageCenterWarning)
        : null,
    ),
  );
  readonly showVaResequencingMessage$ = toObservable(
    this.clientStateService.showVaResequencingMessage,
  ).pipe(
    map((showVaMessage) =>
      showVaMessage
        ? ({
            type: MesageCenterWarningType.VaResequencingWarning,
          } as MessageCenterWarning)
        : null,
    ),
  );
  readonly assetDataPullWarning$ = this.assetDataPullState$.pipe(
    map((state) =>
      state.failureReasons?.length
        ? ({
            type: MesageCenterWarningType.AssetDataPullWarning,
            failureReasons: state?.failureReasons?.map((reason) => reason.errorMessage) ?? [],
            status: state.status,
          } as AssetDataPullWarning)
        : null,
    ),
  );

  readonly errors$ = combineLatest([
    this.conflictingLoansError$.pipe(startWith(null)),
    this.bankerLicenseError$.pipe(startWith(null)),
    this.bankerAssignmentError$.pipe(startWith(null)),
    this.assetDataPullError$.pipe(startWith(null)),
    this.outstandingCreditTaskErrors$.pipe(startWith(null)),
    this.stateErrors$.pipe(startWith(null)),
    this.loanInitError$.pipe(startWith(null)),
  ]).pipe(
    map(
      (errors) =>
        errors
          .flat()
          .filter((error) => !!error)
          .sort((a, b) =>
            a?.type === LeadInitError.NotFound || a?.type === LeadInitError.Failed ? -1 : 0,
          ) as MessageCenterError[],
    ),
  );

  readonly warnings$ = combineLatest([
    this.showVaResequencingMessage$,
    this.assetDataPullWarning$,
    this.addressNotFoundWarning$.pipe(startWith(null)),
  ]).pipe(map((warnings) => warnings.flat().filter((warning) => !!warning)));

  readonly info$ = combineLatest([this.assetDataPullInfo$, this.prefillDataInfo$]).pipe(
    map((info) => info.flat().filter((info) => !!info)),
  );

  readonly errors = toSignal(this.errors$, { initialValue: [] });
  readonly warnings = toSignal(this.warnings$, { initialValue: [] });
  readonly info = toSignal(this.info$, { initialValue: [] });
}
