import { inject, Injectable } from '@angular/core';
import { filter } from 'rxjs';
import {
  LoanNotificationService,
  PublishedMessage,
} from './notification/loan-notification.service';

@Injectable()
export abstract class AbstractEventService<T = any> {
  private notificationService = inject(LoanNotificationService);
  protected abstract EVENT_TYPE: string;

  protected eventStream$ = this.notificationService.notifications$.pipe(
    filter<PublishedMessage<T>>((message) => message.type === this.EVENT_TYPE),
  );
}
