import { Injectable, inject } from '@angular/core';
import {
  AssetRequestStatus,
  FailureEntity,
} from '@rocket-logic/rl-xp-bff-models/dist/notification/subjects';
import { catchError, map, of, startWith, switchMap } from 'rxjs';
import { environment } from '../../../environments/environment';
import { AssetDataPullService } from '../asset-data-pull/asset-data-pull.service';
import { DataProviderService } from '../data-provider/data-provider.service';
import { LoanIdService } from '../loan-id/loan-id.service';

export const SCHWAB_LOAN_CHANNEL = 'Cadillac';

@Injectable()
export class SchwabService {
  dataProviderService = inject(DataProviderService);
  assetDataPullService = inject(AssetDataPullService);
  loanIdService = inject(LoanIdService);
  private enableSchwab = environment.featureFlags?.enableSchwab ?? false;

  isSchwab$ = this.loanIdService.loanId$.pipe(
    switchMap((loanId) => this.dataProviderService.getLead$(loanId!)),
    map((lead) => lead.leadSourceCategory === SCHWAB_LOAN_CHANNEL && this.enableSchwab),
    startWith(false),
  );

  public startSchwabAssetImport$ = this.loanIdService.loanId$.pipe(
    switchMap((loanId) => this.dataProviderService.startSchwabAssetImport$(loanId!)),
    catchError(() => {
      this.assetDataPullService.assetDataPullState.set({
        status: AssetRequestStatus.Failure,
        failureReasons: [
          {
            entity: FailureEntity.Assets,
            errorMessage: 'Something went wrong, please submit a support ticket.',
          },
        ],
      });
      return of(null);
    }),
  );
}
