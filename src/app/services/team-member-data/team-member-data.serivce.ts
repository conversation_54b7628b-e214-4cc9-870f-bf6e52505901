import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import {
  TeamMemberDataResponse
} from '@rocket-logic/rl-xp-bff-models';
import { catchError, map, Observable, of, shareReplay } from 'rxjs';
import { environment } from '../../../environments/environment';

export interface TeamMemberRoles {
  isSchwabBanker: boolean;
  isFranchiseBanker: boolean;
}

export enum SpecialtyCode {
  SchwabCode = 'LC_Schwab',
  PartnershipCode = 'LC_Partnership',
  RelocationCode = 'LC_Relocation',
  FranchiseCode = 'Franchise_Banker'
}

@Injectable({
  providedIn: 'root',
})
export class TeamMemberDataService {
  private readonly BASE_URL = environment.dataProviderUrl;
  private httpClient = inject(HttpClient);

  teamMemberData$: Observable<TeamMemberDataResponse | null> = this.getTeamMemberData$().pipe(
    shareReplay(1),
  );

  teamMemberRoles$: Observable<TeamMemberRoles> = this.teamMemberData$.pipe(
    map((teamMemberDataResponse) => {
      const isSchwabBanker = teamMemberDataResponse?.data.teamMember.teamMemberJobs
        .some(job => job.skills.some(skill => skill.code === SpecialtyCode.SchwabCode ||
          skill.code === SpecialtyCode.PartnershipCode ||
          skill.code === SpecialtyCode.RelocationCode)) ?? false;
      const isFranchiseBanker = teamMemberDataResponse?.data.teamMember.teamMemberJobs
        .some(job => job.skills.some(skill => skill.code === SpecialtyCode.FranchiseCode)) ?? false;
      return {
        isSchwabBanker,
        isFranchiseBanker
      }
    }),
    catchError(() => of({isSchwabBanker: false, isFranchiseBanker: false})),
    shareReplay(1)
  );

  getTeamMemberData$() {
    return this.httpClient.get<TeamMemberDataResponse>(`${this.BASE_URL}/teamMemberData`).pipe(
      catchError(() => of(null))
    );
  }
}
