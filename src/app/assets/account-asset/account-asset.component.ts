import { Component, computed } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { TextFieldComponent } from '../../_shared/components/text-field/text-field.component';
import { RlaFormFieldSuffixComponent } from '../../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component';
import { RlaHighlightDirective } from '../../assistant/directives/rla-highlight.directive';
import { FormattedNumberInputComponent } from '../../question-input/formatted-number-input/formatted-number-input.component';
import { getAccountIdentifierLabel, getFinancialInstitutionLabel } from '../asset-label-lookup';
import { BaseAssetComponent } from '../base-asset/base-asset.component';
import { ClientSelectComponent } from '../client-select/client-select.component';

@Component({
  selector: 'app-account-asset',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatSlideToggleModule,
    ClientSelectComponent,
    FormattedNumberInputComponent,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    TextFieldComponent,
    MatMenuModule,
  ],
  templateUrl: './account-asset.component.html',
  styleUrl: './account-asset.component.scss',
})
export class AccountAssetComponent extends BaseAssetComponent {
  accountIdentifierLabel = computed(() => getAccountIdentifierLabel(this.assetType()));
  financialInstitutionLabel = computed(() => getFinancialInstitutionLabel(this.assetType()));
}
