import { AsyncPipe } from '@angular/common';
import {
  Component,
  DestroyRef,
  ElementRef,
  OnInit,
  Signal,
  ViewChild,
  computed,
  effect,
  inject,
  input,
  signal,
} from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import {
  MatAutocompleteModule,
  MatAutocompleteSelectedEvent,
} from '@angular/material/autocomplete';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { MatChipGrid, MatChipInputEvent, MatChipsModule } from '@angular/material/chips';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { Client } from '@rocket-logic/rl-xp-bff-models';
import {
  Observable,
  combineLatest,
  distinctUntilChanged,
  filter,
  map,
  skip,
  startWith,
  switchMap,
  take,
  tap,
} from 'rxjs';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { ClientNamePipe } from '../../util/client-name.pipe';
import { getErrorMessage } from '../../util/get-error-message';

@Component({
  selector: 'app-client-select',
  standalone: true,
  imports: [
    MatCheckboxModule,
    MatSelectModule,
    MatChipsModule,
    ClientNamePipe,
    MatAutocompleteModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    AsyncPipe,
  ],
  templateUrl: './client-select.component.html',
  styleUrl: './client-select.component.scss',
})
export class ClientSelectComponent implements OnInit {
  private clientStateService = inject(ClientStateService);
  private destroyRef = inject(DestroyRef);
  isLoanEditingDisabled = inject(LoanEditingState).isLoanEditingDisabled;

  control = input.required<FormControl<string[] | null | undefined>>();
  label = input<string>('');

  isControlDisabled$ = toObservable(this.control).pipe(
    switchMap((control) => control.statusChanges.pipe(startWith(control.status))),
    map((status) => status === 'DISABLED'),
    takeUntilDestroyed(),
  );
  isControlDisabled = toSignal(this.isControlDisabled$, { initialValue: false });
  isDisabled = computed(() => this.isLoanEditingDisabled() || this.isControlDisabled());
  clients = computed(() => this.clientStateService.stateValues());
  chosenClients = new Set<Client>();
  filteredClients: Observable<Client[]> | null = null;
  ownerControl = new FormControl('');
  ownerIds: Signal<string[]> = computed(() => this.control().value ?? []);
  clients$ = toObservable(this.clients);
  clientIdFormValid = signal(false);
  isRequired = computed(() => this.control()?.hasValidator(Validators.required));

  @ViewChild(MatChipGrid) grid: MatChipGrid | null = null;
  @ViewChild('ownerInput') ownerInput: ElementRef<HTMLInputElement> | null = null;

  constructor() {
    this.clients$
      .pipe(
        distinctUntilChanged(
          (prev, curr) =>
            prev.length === curr.length &&
            prev.every((client) => curr.some((currentClient) => currentClient.id === client.id)),
        ),
        skip(1),
      )
      .subscribe((clients) => {
        // Clients have been added/deleted, need to update the chosenClients
        const clientMap = Array.from(this.chosenClients).reduce((acc, curr) => {
          if (!acc.has(curr?.id ?? '')) {
            acc.set(curr?.id ?? '', curr);
          }

          return acc;
        }, new Map<string, Client>());

        this.chosenClients = new Set(clients.filter((client) => clientMap.has(client?.id ?? '')));
      });

    effect(() => (this.isDisabled() ? this.ownerControl.disable() : this.ownerControl.enable()));
  }

  ngOnInit(): void {
    this.control()!
      .statusChanges.pipe(
        map((status) => status === 'VALID'),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe((isValid) => {
        this.clientIdFormValid.set(isValid);
        this.updateGridErrorState();
      });

    this.control()!
      .valueChanges.pipe(
        distinctUntilChanged(),
        tap((clientIds) => {
          this.chosenClients.clear();

          if (clientIds) {
            this.updateChosenClients(clientIds);
          }
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe();

    this.filteredClients = combineLatest([this.clients$, this.ownerControl.valueChanges]).pipe(
      map(([_, clientName]: [Client[], string | null]) => {
        return clientName && typeof clientName === 'string'
          ? this.filter(clientName)
          : this.clients()?.slice();
      }),
      takeUntilDestroyed(this.destroyRef),
    ) as Observable<Client[]>;

    if (this.ownerIds().length) {
      // Wait for client state to be initialized before setting up initial values
      this.clients$
        .pipe(
          filter((clients) => clients.length > 0),
          take(1),
          takeUntilDestroyed(this.destroyRef),
        )
        .subscribe(() => {
          this.updateChosenClients(this.ownerIds());
        });
    }
  }

  /**
   * Refresh the mat-chip-grid error state.
   * Utilized by the ownerControl focus event to update before any
   * values change.
   * Utilized by the clientIdForm valueChanges subscription.
   */
  updateGridErrorState() {
    this.grid?.updateErrorState();
  }

  /**
   * Error state matcher for the mat-chip-grid.
   * mat-chip-grid controls the error border of the mat-form-field.
   * @returns ErrorStateMatcher
   */
  errorStateMatcher() {
    return {
      isErrorState: () => !this.clientIdFormValid(),
    };
  }

  getErrorMessage() {
    return this.control() && getErrorMessage(this.control()!);
  }

  remove(client: Client) {
    this.chosenClients.delete(client);
    const clientIds = Array.from(this.chosenClients).map((client) => client.id ?? '');
    this.control()?.markAsDirty();
    this.control()?.setValue(clientIds);
  }

  selected(event: MatAutocompleteSelectedEvent): void {
    const client = event.option.value;
    this.chosenClients.add(client);
    const clientIds = Array.from(this.chosenClients).map((client) => client.id ?? '');
    this.control()?.markAsDirty();
    this.control()?.setValue(clientIds);
    this.ownerControl.setValue(null);

    if (this.ownerInput) {
      this.ownerInput.nativeElement.value = '';
    }
  }

  add(event: MatChipInputEvent): void {
    event.chipInput.clear();
    this.ownerControl.setValue(null);
  }

  private updateChosenClients(updatedClients: string[]) {
    const clients = this.clients()?.filter((client) => updatedClients.includes(client?.id ?? ''));

    clients?.forEach((client) => {
      if (!this.chosenClients.has(client)) {
        this.chosenClients.add(client);
      }
    });
  }

  private filter(clientName: string): Client[] {
    const filterName = clientName.toLowerCase();
    return (
      this.clients()?.filter((client) => {
        return `${client.personalInformation?.preferredName ?? client.personalInformation?.firstName} ${client.personalInformation?.lastName} ${client.personalInformation?.suffix ?? ''}`
          .toLowerCase()
          .includes(filterName);
      }) ?? []
    );
  }
}
