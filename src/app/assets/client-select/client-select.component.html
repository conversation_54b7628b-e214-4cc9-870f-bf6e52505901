<mat-form-field class="rkt-Autocomplete rkt-FormField">
  <mat-label id="clientSelectLabel">{{ label() }}</mat-label>
  <div class="flex">
    <mat-chip-grid
      #chipGrid
      aria-labelledby="clientSelectLabel"
      [required]="isRequired()"
      [errorStateMatcher]="errorStateMatcher()"
      [disabled]="isDisabled()"
    >
      @for (client of chosenClients; track client.id) {
        <mat-chip-row
          [disabled]="isDisabled()"
          class="rkt-Chip rkt-Chip--light rkt-Caption-12"
          (removed)="remove(client)"
        >
          {{ client | clientName }}
          <button matChipRemove>
            <mat-icon svgIcon="cancel-outlined"></mat-icon>
          </button>
        </mat-chip-row>
      }
    </mat-chip-grid>
    <input
      class="rkt-Input"
      #ownerInput
      matInput
      [formControl]="ownerControl"
      [matChipInputFor]="chipGrid"
      [matAutocomplete]="auto"
      [matAutocompleteDisabled]="isDisabled()"
      (matChipInputTokenEnd)="add($event)"
      (focus)="updateGridErrorState()"
      
    />
  </div>

  <mat-autocomplete
    autoActiveFirstOption
    #auto="matAutocomplete"
    (optionSelected)="selected($event)"
  >
    @for (client of filteredClients | async; track client.id) {
      <mat-option class="rkt-Autocomplete__option" [value]="client">{{
        client | clientName
      }}</mat-option>
    }
  </mat-autocomplete>

  @if (!clientIdFormValid() && ownerControl.touched) {
    <mat-error>{{ getErrorMessage() }}</mat-error>
  }
</mat-form-field>
