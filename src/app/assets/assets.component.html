<app-form-section
  [title]="'Asset(s)'"
  [isCollapsible]="assetsFormService.entityValues().length > 0"
  [formSection]="FormSection.Assets"
>
  @if (!assetStateService.isFetching()) {
    @if (!earnestMoneyFormGroup.controls.noEmd.value) {
      <app-earnest-money-deposit [isReadonly]="true" />
    }

    @for (assetData of assetsFormService.entityFormMap(); track assetData[0]) {
      @if (assetData[1].value.assetType !== AssetType.BorrowerEstimatedTotalAssets) {
        @switch (mapAssetBucket(assetData[1].value.assetType)) {
          @case (AssetBucket.Account) {
            <app-account-asset
              (deleteFormEmitter)="onDeleteClick(assetData[0])"
              [formGroup]="assetData[1]"
            />
          }
          @case (AssetBucket.Gift) {
            <app-gift-asset
              (deleteFormEmitter)="onDeleteClick(assetData[0])"
              [formGroup]="assetData[1]"
            />
          }
          @case (AssetBucket.Subsidy) {
            <app-subsidy-asset
              [formGroup]="assetData[1]"
              (deleteFormEmitter)="onDeleteClick(assetData[0])"
            />
          }
          @default {
            <app-base-asset
              [formGroup]="assetData[1]"
              (deleteFormEmitter)="onDeleteClick(assetData[0])"
            />
          }
        }
      }
    }
    <div class="flex flex-col" [class.gap-3]="isSchwab()">
      <div class="row">
        <app-create-asset-button
          [buttonLabel]="
            assetsFormService.entityValues().length ? 'Add Additional Assets' : 'Add Assets'
          "
          (createTypeSelected)="onAddClick($event)"
        />
        @if (!assetsFormService.entityValues().length && isInAssetsPilot()) {
          <span>or</span>

          <ng-container [formGroup]="lumpSumFormGroup">
            <app-formatted-number-input
              class="asset-value"
              [control]="lumpSumAssetValueControl"
              [allowNegative]="false"
              prefix="$"
              label="Lump Sum Amount"
              subscriptSizing="dynamic"
            />
          </ng-container>
        }
      </div>

      @if (isSchwab() && areCreditMilestoneComplete()) {
        <app-schwab-assets-import />
      }
    </div>
  } @else {
    <app-assets-skeleton />
  }
  @if (!assetStateService.isFetching()) {
    <div section-summary class="row flex-wrap">
      @for (asset of assetsFormService.entityValues(); track asset) {
        <app-tile
          [label]="asset.value.assetType | pascalCaseSplit"
          [content]="asset.value.assetValue | currency"
          (tileClick)="openFormSection()"
        ></app-tile>
      }
    </div>
  } @else {
    <div section-summary class="row flex-wrap mt-2">
      <app-tile-skeleton></app-tile-skeleton>
    </div>
  }
</app-form-section>
