import { Component, computed, EventEmitter, inject, input, Output } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { RlaFormFieldSuffixComponent } from '../../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component';
import { RlaHighlightDirective } from '../../assistant/directives/rla-highlight.directive';
import { FormattedNumberInputComponent } from '../../question-input/formatted-number-input/formatted-number-input.component';
import { AssetFormService } from '../../services/entity-state/asset-state/asset-form.service';
import { AssetStateService } from '../../services/entity-state/asset-state/asset-state.service';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { getAssetName, getAssetOwnersLabel, getAssetValueLabel } from '../asset-label-lookup';
import { ClientSelectComponent } from '../client-select/client-select.component';

@Component({
  selector: 'app-base-asset',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    ClientSelectComponent,
    FormattedNumberInputComponent,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    MatMenuModule,
  ],
  templateUrl: './base-asset.component.html',
  styleUrl: './base-asset.component.scss',
})
export class BaseAssetComponent {
  @Output() deleteFormEmitter = new EventEmitter<FormGroup>();
  isDisabled = inject(LoanEditingState).isLoanEditingDisabled;
  assetStateService = inject(AssetStateService);
  isFetchingOrUpdating = computed(() => this.assetStateService.deletionState()?.updating);
  assetType = computed(() => this.formGroup().value.assetType);
  assetsFormService = inject(AssetFormService);
  formGroup = input.required<FormGroup>();
  assetName = computed(() => getAssetName(this.assetType()));
  assetValueLabel = computed(() => getAssetValueLabel(this.assetType()));
  assetValueControl = computed(() => this.formGroup().get('assetValue') as FormControl);
  assetOwnersLabel = computed(() => getAssetOwnersLabel(this.formGroup().value.assetType));
  ownersControl = computed(
    () => this.formGroup().get('rocketLogicClientIds') as FormControl<string[]>,
  );

  onDeleteClick() {
    this.deleteFormEmitter.emit(this.formGroup());
  }
}
