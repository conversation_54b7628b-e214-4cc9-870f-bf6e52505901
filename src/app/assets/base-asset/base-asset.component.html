<div class="header">
  <span class="rkt-Label-16 rkt-FontWeight--700">{{ assetName() }}</span>
  <button
    mat-icon-button
    [matMenuTriggerFor]="assetOptions"
    class="rkt-ButtonIcon"
    [disabled]="isDisabled() || isFetchingOrUpdating()"
    [class.rkt-ButtonIcon--is-disabled]="isDisabled() || isFetchingOrUpdating()"
    data-testid="asset-options-menu-trigger"
  >
    <mat-icon class="rkt-Icon" color="accent">more_vert</mat-icon>
  </button>
  <mat-menu #assetOptions class="rkt-Menu" data-testid="asset-options-menu">
    <button
      [disabled]="isDisabled() || isFetchingOrUpdating()"
      mat-menu-item
      class="rkt-Menu__item"
      (click)="onDeleteClick()"
      data-testid="delete-asset-button"
    >
      <mat-icon class="rkt-Icon" svgIcon="delete-outlined"></mat-icon>
      <span class="rkt-Menu__item-text">Delete</span>
    </button>
  </mat-menu>
</div>
<div class="grid-container" [formGroup]="formGroup()">
  <app-formatted-number-input
    class="asset-value"
    [control]="assetValueControl()"
    [allowNegative]="false"
    prefix="$"
    [label]="assetValueLabel()"
    appRlaHighlight
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-formatted-number-input>

  <app-client-select [label]="assetOwnersLabel()" class="clients" [control]="ownersControl()" />
</div>
