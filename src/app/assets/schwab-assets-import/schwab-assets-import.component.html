<div class="row">
  <button
    #schwabDataPullButton
    mat-button
    class="rkt-Button rkt-Button--large rkt-Button--tertiary rkt-Button--has-icon"
    color="accent"
    [disabled]="isDisabled()"
    [class.rkt-Button--is-disabled]="isDisabled()"
    (click)="onSchwabAssetsImport()"
  >
    @if (isLoading()) {
      <mat-icon class="rkt-Icon rkt-Spacing--mr8">
        <mat-spinner
          class="rkt-ProgressSpinner rkt-ProgressSpinner--enterprise"
          diameter="90"
        ></mat-spinner>
      </mat-icon>
    } @else {
      <mat-icon class="rkt-Icon" color="primary" svgIcon="file_download-outlined"></mat-icon>
    }
    {{ isLoading() ? 'Importing' : 'Import Schwab Assets' }}
  </button>

  <mat-checkbox class="rkt-Checkbox rkt-Checkbox--enterprise" [formControl]="checkboxFormControl">
    The client(s) consented to asset(s) pull.
  </mat-checkbox>
</div>
