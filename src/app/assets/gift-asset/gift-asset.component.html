<div class="header">
  <span class="rkt-Label-16 rkt-FontWeight--700">{{ assetName() }}</span>
  <button
    mat-icon-button
    [matMenuTriggerFor]="assetOptions"
    class="rkt-ButtonIcon"
    [disabled]="isDisabled() || isFetchingOrUpdating()"
    [class.rkt-ButtonIcon--is-disabled]="isDisabled() || isFetchingOrUpdating()"
    data-testid="asset-options-menu-trigger"
  >
    <mat-icon class="rkt-Icon" color="accent">more_vert</mat-icon>
  </button>
  <mat-menu #assetOptions class="rkt-Menu" data-testid="asset-options-menu">
    <button
      [disabled]="isDisabled() || isFetchingOrUpdating()"
      mat-menu-item
      class="rkt-Menu__item"
      (click)="onDeleteClick()"
      data-testid="delete-asset-button"
    >
      <mat-icon class="rkt-Icon" svgIcon="delete-outlined"></mat-icon>
      <span class="rkt-Menu__item-text">Delete</span>
    </button>
  </mat-menu>
</div>
@if (assetType() === AssetType.GiftOfCash) {
  <div class="grid-container" [formGroup]="formGroup()">
    <app-formatted-number-input
      class="asset-value"
      [control]="assetValueControl()"
      [allowNegative]="false"
      prefix="$"
      [label]="assetValueLabel()"
      appRlaHighlight
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-formatted-number-input>

    <app-text-field
      class="name"
      appRlaHighlight
      [label]="FieldLabel.GiftSourceName"
      [control]="$any(formGroup().controls['nameOfGiftGiver'])"
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-text-field>

    <app-select-field
      class="giver-source"
      appRlaHighlight
      [label]="FieldLabel.GiftDonorType"
      [control]="$any(formGroup().controls['giftSource'])"
      [options]="giftSource"
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-select-field>

    <app-client-select [label]="assetOwnersLabel()" class="clients" [control]="ownersControl()" />

    <app-select-field
      [label]="FieldLabel.GiftDestination"
      [control]="$any(formGroup().controls['giftDestinationType'])"
      [options]="giftDestinationType"
      appRlaHighlight
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-select-field>

    @if (formGroup().get('giftDestinationType')?.value === DestinationType.ClientBankAccount) {
      <mat-form-field class="rkt-FormField">
        <mat-label>{{ FieldLabel.DestinationAccount }}</mat-label>
        <mat-select class="rkt-Input" formControlName="accountId">
          <mat-option [value]="null">None</mat-option>
          @for (account of destinationAccounts(); track account.assetIdentifier) {
            <mat-option [value]="account.assetIdentifier">
              {{ account.assetType | pascalCaseSplit }}
              {{ account.financialInstitution ? ' - ' + account.financialInstitution : '' }}
              {{ account.accountIdentifier ? ' - ' + account.accountIdentifier : '' }}
            </mat-option>
          }
        </mat-select>
      </mat-form-field>
    }
  </div>
} @else {
  <div class="grid-container" [formGroup]="formGroup()">
    <app-formatted-number-input
      class="asset-value"
      [control]="assetValueControl()"
      [allowNegative]="false"
      prefix="$"
      [label]="assetValueLabel()"
      appRlaHighlight
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-formatted-number-input>

    <app-text-field
      class="source"
      [label]="FieldLabel.GiftSourceName"
      [control]="$any(formGroup().controls['nameOfGiftGiver'])"
      appRlaHighlight
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-text-field>

    <app-select-field
      class="giver-source"
      [control]="$any(formGroup().controls['giftSource'])"
      [label]="FieldLabel.GiftDonorType"
      [options]="giftSource"
      appRlaHighlight
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-select-field>

    <app-client-select [label]="assetOwnersLabel()" class="clients" [control]="ownersControl()" />
  </div>
}
