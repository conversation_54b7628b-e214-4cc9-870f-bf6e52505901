import { Component, computed } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  AccountAsset,
  Asset,
  AssetType,
  DestinationType,
  FundsSourceType,
} from '@rocket-logic/rl-xp-bff-models';
import { SelectFieldComponent } from '../../_shared/components/select-field/select-field.component';
import { TextFieldComponent } from '../../_shared/components/text-field/text-field.component';
import { FieldLabel } from '../../_shared/models/field-label';
import { RlaFormFieldSuffixComponent } from '../../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component';
import { RlaHighlightDirective } from '../../assistant/directives/rla-highlight.directive';
import { FormattedNumberInputComponent } from '../../question-input/formatted-number-input/formatted-number-input.component';
import { pascalCaseSplit } from '../../util/formatting-helpers';
import { PascalCaseSplitPipe } from '../../util/pascal-case-split.pipe';
import { BaseAssetComponent } from '../base-asset/base-asset.component';
import { ClientSelectComponent } from '../client-select/client-select.component';

function isDestinationAccountType(asset: Asset): asset is AccountAsset {
  switch (asset.assetType) {
    case AssetType.CheckingAccount:
    case AssetType.SavingsAccount:
    case AssetType.MoneyMarket:
    case AssetType.CertificateOfDeposit:
      return true;
    default:
      return false;
  }
}

@Component({
  selector: 'app-gift-asset',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatSlideToggleModule,
    PascalCaseSplitPipe,
    ClientSelectComponent,
    FormattedNumberInputComponent,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    TextFieldComponent,
    SelectFieldComponent,
    MatMenuModule,
  ],
  templateUrl: './gift-asset.component.html',
  styleUrl: './gift-asset.component.scss',
})
export class GiftAssetComponent extends BaseAssetComponent {
  AssetType = AssetType;
  DestinationType = DestinationType;
  FieldLabel = FieldLabel;

  destinationAccounts = computed(() =>
    this.assetStateService.stateValues().filter(isDestinationAccountType),
  );

  giftSource = Object.values(FundsSourceType).map((value) => ({
    value,
    display: pascalCaseSplit(value),
  }));

  giftDestinationType = Object.values(DestinationType).map((value) => ({
    value,
    display: pascalCaseSplit(value),
  }));
}
