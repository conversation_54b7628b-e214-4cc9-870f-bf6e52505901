import { AssetType } from '@rocket-logic/rl-xp-bff-models';
import { pascalCaseSplit } from '../util/formatting-helpers';

export function getAssetName(assetType: AssetType) {
  if (assetType == null) {
    return '';
  }

  switch (assetType) {
    case AssetType.IndividualRetirementArrangement:
      return 'Individual Retirement Account';
    default: {
      let assetName = pascalCaseSplit(assetType);

      if (assetName.startsWith('_')) {
        assetName = assetName.substring(1);
      }

      return assetName;
    }
  }
}

export function getAccountIdentifierLabel(assetType: AssetType) {
  switch (assetType) {
    case AssetType._401K:
    case AssetType._403B:
    case AssetType._457Plan:
    case AssetType._529CollegeSavingsPlan:
    case AssetType.Annuity:
    case AssetType.CertificateOfDeposit:
    case AssetType.CheckingAccount:
    case AssetType.IndividualRetirementArrangement:
    case AssetType.Keogh:
    case AssetType.LifeInsurance:
    case AssetType.MoneyMarket:
    case AssetType.MutualFund:
    case AssetType.SavingsAccount:
    case AssetType.SimplifiedEmployeePension:
    case AssetType.TrustAccount:
    case AssetType.ThriftSavingsPlan:
    case AssetType.BrokerageAccount:
    case AssetType.PledgedAssetAccount:
      return 'Account Number';
    default:
      return 'Account Identifier';
  }
}

export function getAssetOwnersLabel(assetType: AssetType) {
  switch (assetType) {
    case AssetType._401K:
    case AssetType._403B:
    case AssetType._457Plan:
    case AssetType._529CollegeSavingsPlan:
    case AssetType.Annuity:
    case AssetType.BrokerageAccount:
    case AssetType.CertificateOfDeposit:
    case AssetType.CheckingAccount:
    case AssetType.EarnestMoneyDeposit:
    case AssetType.IndividualRetirementArrangement:
    case AssetType.Keogh:
    case AssetType.LifeInsurance:
    case AssetType.MoneyMarket:
    case AssetType.MutualFund:
    case AssetType.PledgedAssetAccount:
    case AssetType.SavingsAccount:
    case AssetType.SimplifiedEmployeePension:
    case AssetType.ThriftSavingsPlan:
      return 'Owner';
    case AssetType._1031Exchange:
    case AssetType.BridgeLoan:
      return 'Property Owner';
    case AssetType.Grant:
      return 'Grant Recipient';
    case AssetType.TrustAccount:
      return 'Account Holder';
    case AssetType.GiftOfCash:
    case AssetType.GiftOfEquity:
      return 'Gift Recipient';
    default:
      return 'Asset Owners';
  }
}

export function getAssetValueLabel(assetType: AssetType) {
  switch (assetType) {
    case AssetType.CertificateOfDeposit:
    case AssetType.CheckingAccount:
    case AssetType.MoneyMarket:
    case AssetType.SavingsAccount:
      return 'Statement Balance';
    case AssetType._401K:
    case AssetType._403B:
    case AssetType._457Plan:
    case AssetType.Annuity:
    case AssetType.IndividualRetirementArrangement:
    case AssetType.Keogh:
    case AssetType.SimplifiedEmployeePension:
    case AssetType.ThriftSavingsPlan:
      return 'Amount';
    case AssetType.BrokerageAccount:
    case AssetType.MutualFund:
    case AssetType.PledgedAssetAccount:
      return 'Market Value';
    case AssetType.BridgeLoan:
    case AssetType.LifeInsurance:
    case AssetType._1031Exchange:
      return 'Cash Value';
    case AssetType._529CollegeSavingsPlan:
    case AssetType.TrustAccount:
      return 'Account Balance';
    case AssetType.Grant:
      return 'Grant Amount';
    case AssetType.EarnestMoneyDeposit:
      return 'Deposit Amount';
    case AssetType.GiftOfCash:
    case AssetType.GiftOfEquity:
      return 'Gift Amount';
    default:
      return 'Asset Value';
  }
}

export function getFinancialInstitutionLabel(assetType: AssetType) {
  switch (assetType) {
    case AssetType.CertificateOfDeposit:
    case AssetType.CheckingAccount:
    case AssetType.MoneyMarket:
    case AssetType.SavingsAccount:
    case AssetType.PledgedAssetAccount:
      return 'Bank Name';
    case AssetType._401K:
    case AssetType._403B:
    case AssetType._457Plan:
    case AssetType.Annuity:
    case AssetType.IndividualRetirementArrangement:
    case AssetType.Keogh:
    case AssetType.SimplifiedEmployeePension:
    case AssetType.ThriftSavingsPlan:
      return 'Institution Name';
    case AssetType.BrokerageAccount:
    case AssetType.MutualFund:
      return 'Broker';
    case AssetType._529CollegeSavingsPlan:
      return 'Savings Plan';
    case AssetType.LifeInsurance:
      return 'Life Insurance Company';
    case AssetType.TrustAccount:
      return 'Trust Holder';
    default:
      return 'Financial Institution';
  }
}
