import { <PERSON><PERSON><PERSON>cyPipe } from '@angular/common';
import { Component, computed, inject, viewChild } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormControl, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { AssetType } from '@rocket-logic/rl-xp-bff-models';
import { EMPTY, filter, map, switchMap } from 'rxjs';
import { FormSectionComponent } from '../form-section/form-section.component';
import { FormattedNumberInputComponent } from '../question-input/formatted-number-input/formatted-number-input.component';
import { ActiveActionService } from '../services/active-action.service';
import { AuthorizationService, Pilot } from '../services/authorization/authorization.service';
import { AssetFormService } from '../services/entity-state/asset-state/asset-form.service';
import { AssetStateService } from '../services/entity-state/asset-state/asset-state.service';
import { LoanStateService } from '../services/entity-state/loan-state/loan-state.service';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { LoanIdService } from '../services/loan-id/loan-id.service';
import { SchwabService } from '../services/schwab/schwab.service';
import { TileSkeletonComponent } from '../tile/tile-skeleton/tile-skeleton.component';
import { TileComponent } from '../tile/tile.component';
import { openFormSection } from '../util/open-form-section';
import { PascalCaseSplitPipe } from '../util/pascal-case-split.pipe';
import { AccountAssetComponent } from './account-asset/account-asset.component';
import { AssetsSkeletonComponent } from './assets-skeleton/assets-skeleton.component';
import { BaseAssetComponent } from './base-asset/base-asset.component';
import { CreateAssetButtonComponent } from './create-asset-button/create-asset-button.component';
import { EarnestMoneyDepositComponent } from './earnest-money-deposit/earnest-money-deposit.component';
import { GiftAssetComponent } from './gift-asset/gift-asset.component';
import { LumpSumDialogComponent } from './lump-sum-dialog/lump-sum-dialog.component';
import { SchwabAssetsImportComponent } from './schwab-assets-import/schwab-assets-import.component';
import { SubsidyAssetComponent } from './subsidy-asset/subsidy-asset.component';

export enum AssetBucket {
  Base = 'Base',
  Account = 'Account',
  Gift = 'Gift',
  Subsidy = 'Subsidy',
}

@Component({
  selector: 'app-assets',
  standalone: true,
  imports: [
    FormSectionComponent,
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatSlideToggleModule,
    CreateAssetButtonComponent,
    AccountAssetComponent,
    GiftAssetComponent,
    BaseAssetComponent,
    SubsidyAssetComponent,
    MatCardModule,
    MatExpansionModule,
    PascalCaseSplitPipe,
    FormattedNumberInputComponent,
    AssetsSkeletonComponent,
    EarnestMoneyDepositComponent,
    TileComponent,
    TileSkeletonComponent,
    CurrencyPipe,
    SchwabAssetsImportComponent,
  ],
  templateUrl: './assets.component.html',
  styleUrl: './assets.component.scss',
})
export class AssetsComponent {
  assetsFormService = inject(AssetFormService);
  assetStateService = inject(AssetStateService);
  formNavSectionService = inject(FormNavSectionService);
  private authService = inject(AuthorizationService);
  private loanStateService = inject(LoanStateService);
  private loanIdService = inject(LoanIdService);
  private schwabService = inject(SchwabService);
  formSectionComponentRef = viewChild.required(FormSectionComponent);

  private activeActionService = inject(ActiveActionService);
  readonly AssetBucket = AssetBucket;
  readonly AssetType = AssetType;
  readonly FormSection = FormSection;
  private dialog = inject(MatDialog);

  isInAssetsPilot = toSignal(
    this.loanStateService.isInitialized$.pipe(
      filter((isInit) => isInit),
      switchMap(() => this.loanIdService.loanId$),
      filter((loanNumber): loanNumber is NonNullable<typeof loanNumber> => !!loanNumber),
      switchMap((loanNumber) => {
        return this.authService.getLoanAccess(loanNumber!, [Pilot.RocketLogicAssets]);
      }),
      map((response) => {
        return response[Pilot.RocketLogicAssets];
      }),
    ),
  );

  isSchwab = toSignal(this.schwabService.isSchwab$);

  areCreditMilestoneComplete = computed(
    () => this.activeActionService.creditItemsToComplete() === 0,
  );

  lumpSumFormGroup = this.assetsFormService.lumpSumForm;
  earnestMoneyFormGroup = this.assetsFormService.emdForm;

  get lumpSumAssetValueControl() {
    return this.lumpSumFormGroup.get('assetValue') as FormControl;
  }

  onAddClick(value: AssetType) {
    if (!this.assetStateService.lumpSum()) {
      this.assetsFormService.addAsset(value);
      this.resetLumpSumFormGroup();
      return;
    }

    const dialogRef = this.dialog.open(LumpSumDialogComponent, {
      panelClass: 'rkt-Dialog',
      minWidth: '50%',
      minHeight: '50%',
      backdropClass: 'rkt-Backdrop',
    });

    dialogRef
      .afterClosed()
      .pipe(
        switchMap((result) => {
          if (result) {
            this.resetLumpSumFormGroup();
            return this.assetStateService.deleteLumpSumAsset$();
          } else {
            return EMPTY;
          }
        }),
      )
      .subscribe(() => {
        this.assetsFormService.addAsset(value);
      });
  }

  onDeleteClick(assetKey: string) {
    this.assetsFormService.deleteAsset(assetKey);
  }

  mapAssetBucket(assetType: AssetType) {
    switch (assetType) {
      case AssetType.CheckingAccount:
      case AssetType.SavingsAccount:
      case AssetType.MoneyMarket:
      case AssetType.CertificateOfDeposit:
      case AssetType._401K:
      case AssetType.IndividualRetirementArrangement:
      case AssetType._403B:
      case AssetType._457Plan:
      case AssetType.ThriftSavingsPlan:
      case AssetType.SimplifiedEmployeePension:
      case AssetType.Annuity:
      case AssetType.Keogh:
      case AssetType.BrokerageAccount:
      case AssetType.MutualFund:
      case AssetType._529CollegeSavingsPlan:
      case AssetType.LifeInsurance:
      case AssetType.TrustAccount:
      case AssetType.PledgedAssetAccount:
        return AssetBucket.Account;
      case AssetType.GiftOfCash:
      case AssetType.GiftOfEquity:
        return AssetBucket.Gift;
      case AssetType.Grant:
        return AssetBucket.Subsidy;
      default:
        return AssetBucket.Base;
    }
  }

  openFormSection() {
    openFormSection(() => this.formSectionComponentRef());
  }

  private resetLumpSumFormGroup() {
    this.lumpSumFormGroup?.reset({
      assetValue: null,
      assetType: AssetType.BorrowerEstimatedTotalAssets,
    });
  }
}
