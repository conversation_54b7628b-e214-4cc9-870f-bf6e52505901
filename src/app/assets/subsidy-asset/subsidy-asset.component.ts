import { Component } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatMenuModule } from '@angular/material/menu';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { DestinationType, GrantSourceType } from '@rocket-logic/rl-xp-bff-models';
import { SelectFieldComponent } from '../../_shared/components/select-field/select-field.component';
import { TextFieldComponent } from '../../_shared/components/text-field/text-field.component';
import { FieldLabel } from '../../_shared/models/field-label';
import { RlaFormFieldSuffixComponent } from '../../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component';
import { RlaHighlightDirective } from '../../assistant/directives/rla-highlight.directive';
import { FormattedNumberInputComponent } from '../../question-input/formatted-number-input/formatted-number-input.component';
import { pascalCaseSplit } from '../../util/formatting-helpers';
import { BaseAssetComponent } from '../base-asset/base-asset.component';
import { ClientSelectComponent } from '../client-select/client-select.component';

@Component({
  selector: 'app-subsidy-asset',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatSlideToggleModule,
    ClientSelectComponent,
    FormattedNumberInputComponent,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    TextFieldComponent,
    SelectFieldComponent,
    MatMenuModule,
  ],
  templateUrl: './subsidy-asset.component.html',
  styleUrl: './subsidy-asset.component.scss',
})
export class SubsidyAssetComponent extends BaseAssetComponent {
  sourceType = Object.values(GrantSourceType).map((value) => ({
    value,
    display: pascalCaseSplit(value),
  }));
  grantDestinationType = Object.values(DestinationType)
    .filter((destination) => destination !== DestinationType.TitleCompany)
    .map((value) => ({
      value,
      display: pascalCaseSplit(value),
    }));
  FieldLabel = FieldLabel;
}
