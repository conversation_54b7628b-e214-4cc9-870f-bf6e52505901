import { Component, computed, inject, input, signal } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { AssetType } from '@rocket-logic/rl-xp-bff-models';
import { FormattedNumberInputComponent } from '../../question-input/formatted-number-input/formatted-number-input.component';
import { AssetFormService } from '../../services/entity-state/asset-state/asset-form.service';
import { AssetStateService } from '../../services/entity-state/asset-state/asset-state.service';
import { getAssetOwnersLabel, getAssetValueLabel } from '../asset-label-lookup';
import { ClientSelectComponent } from '../client-select/client-select.component';

@Component({
  selector: 'app-earnest-money-deposit',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    ReactiveFormsModule,
    ClientSelectComponent,
    FormattedNumberInputComponent,
  ],
  templateUrl: './earnest-money-deposit.component.html',
  styleUrl: './earnest-money-deposit.component.scss',
})
export class EarnestMoneyDepositComponent {
  isReadonly = input(false);
  assetStateService = inject(AssetStateService);
  assetFormService = inject(AssetFormService);
  assetValueLabel = signal(getAssetValueLabel(AssetType.EarnestMoneyDeposit));
  assetFormGroup = computed(() =>
    this.isReadonly() ? this.assetFormService.readonlyEmdForm : this.assetFormService.emdForm,
  );
  assetOwnersLabel = computed(() => getAssetOwnersLabel(AssetType.EarnestMoneyDeposit));
  ownersControl = computed(() => this.assetFormGroup().controls.rocketLogicClientIds);
}
