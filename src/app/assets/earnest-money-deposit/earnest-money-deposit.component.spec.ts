import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MockBuilder, MockInstance } from 'ng-mocks';
import { FormattedNumberInputComponent } from '../../question-input/formatted-number-input/formatted-number-input.component';
import { AssetFormService } from '../../services/entity-state/asset-state/asset-form.service';
import { AssetStateService } from '../../services/entity-state/asset-state/asset-state.service';
import { EarnestMoneyDepositComponent } from './earnest-money-deposit.component';

describe('EarnestMoneyDepositComponent', () => {
  let component: EarnestMoneyDepositComponent;
  let fixture: ComponentFixture<EarnestMoneyDepositComponent>;

  beforeEach(() =>
    MockBuilder(EarnestMoneyDepositComponent)
      .provide({
        provide: AssetFormService,
        useValue: {
          emdForm: new FormGroup({
            assetValue: new FormControl<number | null>(null),
            rocketLogicClientIds: new FormControl<string[] | null>(null),
          }),
        },
      })
      .provide({
        provide: AssetStateService,
        useValue: {
          isFetching: () => false,
        },
      }),
  );

  beforeEach(() => {
    MockInstance(FormattedNumberInputComponent, 'matFormField', signal(undefined));
    fixture = TestBed.createComponent(EarnestMoneyDepositComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
