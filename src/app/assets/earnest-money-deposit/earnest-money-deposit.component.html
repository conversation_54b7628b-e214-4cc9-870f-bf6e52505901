@if (isReadonly()) {
  <div class="header">
    <span class="rkt-Label-16 rkt-FontWeight--700">Earnest Money Deposit</span>
  </div>
}

@if (!assetStateService.isFetching()) {
  <div class="grid-container" [formGroup]="assetFormGroup()">
    <app-formatted-number-input
      class="asset-value"
      [control]="assetFormGroup().controls.assetValue"
      [allowNegative]="false"
      prefix="$"
      [label]="assetValueLabel()"
    />
    <app-client-select [label]="assetOwnersLabel()" class="clients" [control]="ownersControl()" />
  </div>
}
