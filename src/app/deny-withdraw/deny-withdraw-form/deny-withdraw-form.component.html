<h1 mat-dialog-title>{{ deactivationTitle }} Loan</h1>
<mat-dialog-content [formGroup]="deactivationGroup">
  <mat-form-field class="rkt-FormField" color="accent">
    <mat-label>{{ data.deactivationType }} Reason</mat-label>
    <mat-select formControlName="reason" class="rkt-Input">
      <mat-option [value]="null">None</mat-option>
      @for (reason of deactivationOptions; track $index) {
        <mat-option [value]="reason.bucket">{{ reason.display }}</mat-option>
      }
    </mat-select>
  </mat-form-field>
  <mat-form-field class="rkt-FormField" color="accent">
    <mat-label>Additional Option</mat-label>
    <mat-select formControlName="additionalOption" class="rkt-Input">
      <mat-option [value]="null">None</mat-option>
      @for (option of additionalOptions(); track $index) {
        <mat-option [value]="option.value">{{ option.display }}</mat-option>
      }
    </mat-select>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button
    mat-flat-button
    class="rkt-Button rkt-Button--secondary"
    color="accent"
    (click)="onCancel()"
  >
    Cancel
  </button>
  <button
    mat-flat-button
    class="rkt-Button"
    [class.rkt-Button--is-disabled]="deactivationGroup.invalid || deactivationGroup.pristine"
    [disabled]="deactivationGroup.invalid || deactivationGroup.pristine"
    color="primary"
    (click)="onSubmit()"
  >
    Ok
  </button>
</mat-dialog-actions>
