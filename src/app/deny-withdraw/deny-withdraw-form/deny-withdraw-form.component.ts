import { CommonModule } from '@angular/common';
import { Component, DestroyRef, Inject, OnInit, inject } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import {
  Form<PERSON>uilder,
  FormControl,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { DeactivationType } from '@rocket-logic/rl-xp-bff-models';
import { finalize, map } from 'rxjs';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import {
  DenyBucket,
  WithdrawBucket,
  denyReasonOptions,
  withdrawReasonOptions,
} from '../deny-withdraw';

type ReasonControlType = DenyBucket | WithdrawBucket | null;

@Component({
  selector: 'app-deny-withdraw-form',
  standalone: true,
  imports: [
    MatDialogModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    ReactiveFormsModule,
    CommonModule,
    MatButtonModule,
  ],
  templateUrl: './deny-withdraw-form.component.html',
  styleUrl: './deny-withdraw-form.component.scss',
})
export class DenyWithdrawFormComponent implements OnInit {
  private fb = inject(FormBuilder);
  private dialogRef = inject(MatDialogRef<DenyWithdrawFormComponent>);
  private destroyRef = inject(DestroyRef);
  private loanStateService = inject(LoanStateService);

  deactivationOptions =
    this.data.deactivationType === DeactivationType.Denied
      ? denyReasonOptions
      : withdrawReasonOptions;

  deactivationGroup = this.fb.group<{
    reason: FormControl<ReasonControlType>;
    additionalOption: FormControl<string | null>;
  }>({
    reason: this.fb.control({ value: null, disabled: false }, Validators.required),
    additionalOption: this.fb.control({ value: '', disabled: true }, Validators.required),
  });

  additionalOptions = toSignal(
    this.reasonControl.valueChanges.pipe(map((reason) => this.getAdditionalOptions(reason))),
  );

  deactivationTitle = this.data.deactivationType === DeactivationType.Denied ? 'Deny' : 'Withdraw';

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      deactivationType: DeactivationType;
    },
  ) {}

  get additionalOptionControl(): FormControl<string | null> {
    return this.deactivationGroup.controls.additionalOption;
  }

  get reasonControl(): FormControl<ReasonControlType> {
    return this.deactivationGroup.controls.reason;
  }

  ngOnInit() {
    this.deactivationGroup.valueChanges
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe((changes) => {
        if (!changes?.reason) {
          this.additionalOptionControl.disable({ emitEvent: false });
        } else {
          this.additionalOptionControl.enable({ emitEvent: false });
        }
      });
  }

  onSubmit() {
    this.loanStateService
      .deactivateLoan$(this.data.deactivationType, this.additionalOptionControl.value ?? '')
      .pipe(finalize(() => this.dialogRef.close()))
      .subscribe();
  }

  onCancel() {
    this.dialogRef.close();
  }

  private getAdditionalOptions(reason: ReasonControlType) {
    if (this.data.deactivationType === DeactivationType.Denied) {
      return denyReasonOptions.find((option) => option.bucket === reason)?.additionalOptions;
    } else {
      return withdrawReasonOptions.find((option) => option.bucket === reason)?.additionalOptions;
    }
  }
}
