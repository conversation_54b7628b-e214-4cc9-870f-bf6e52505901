import { DenyReason, WithdrawReason } from '@rocket-logic/rl-xp-bff-models';

export interface DeactivationWrapper {
  display: string;
  bucket: DenyBucket | WithdrawBucket;
  additionalOptions: DeactivationOption[];
}

export interface DeactivationOption {
  display: string;
  value: DenyReason | WithdrawReason;
}

export enum DenyBucket {
  ProductNotAvailable = 'productNotAvailable',
  CreditIssues = 'creditIssues',
  GuidelineRestrictions = 'guidelineRestrictions',
  IncomeIssues = 'incomeIssues',
  PropertyIssues = 'propertyIssues',
  AssetIssues = 'assetIssues',
}

export enum WithdrawBucket {
  ChooseAnotherLender = 'chooseAnotherLender',
  ClientNotInterested = 'clientNotInterested',
  GuidelineIssues = 'guidelineIssues',
  FutureOpportunity = 'futureOpportunity',
}

export const ASSET_OPTIONS: DeactivationOption[] = [
  { display: 'Assets Do Not Qualify', value: DenyReason.AssetsDoNotQualify },
  { display: 'Insufficient Funds to Close', value: DenyReason.InsufficientFundsToClose },
  { display: 'Reserve Requirement Not Met', value: DenyReason.ReserveRequirementNotMet },
];

export const CREDIT_OPTIONS: DeactivationOption[] = [
    { display: 'Credit Needs Improving', value: DenyReason.CreditNeedsImproving },
    { display: 'Bankruptcy', value: DenyReason.Bankruptcy },
    { display: 'Judgments or Tax Liens', value: DenyReason.JudgmentsLiens },
    { display: 'Collections', value: DenyReason.Collections },
    { display: 'Mortgage Lates', value: DenyReason.MortgageLates },
    { display: 'Foreclosure/Short Sale', value: DenyReason.ForeclosureShortSale },
    { display: 'Insufficient Credit', value: DenyReason.InsufficientCredit },
];

export const GUIDELINE_RESTRICTIONS_OPTIONS: DeactivationOption[] = [

  // {display: 'Cook County', value: DenyReason.Cook}, NOT SUPPORTED BY RLAPI
  // {display: 'Client Has More Than 4 Properties', value: DenyReason.ClientHasMoreThan4Properties}, NOT SUPPORTED BY RLAPI
  { display: 'Condo Restrictions', value: DenyReason.CondoRestrictions },
  { display: 'Seasoning', value: DenyReason.Seasoning },
  // {display: 'Same Service P&I Increase Greater Than 20%', value: DenyReason.SameServicerPiIncreaseGreaterThan20Percentage}, // NOT SUPPORTED
  {
    display: 'New P&I Greater Than Current P&I',
    value: DenyReason.PrincipalAndInterestIncrease,
  },
  { display: 'High Cost/Safe Harbor Issue', value: DenyReason.HighCostSafeHarborIssue },
  { display: 'State Restrictions', value: DenyReason.StateRestrictions },
  {
    display: 'Unable to Provide Residency Or Citizenship',
    value: DenyReason.UnableToProvideResidencyOrCitizenship,
  },
  {
    display: 'Number of Properties Exceeded',
    value: DenyReason.TooManyInvestmentProperties,
  },
  { display: 'Cash Out in Texas', value: DenyReason.CashOutInTexas },
  { display: 'Cannot Subordinate', value: DenyReason.CannotSubordinate },
  { display: 'No Tangible Net Benefit', value: DenyReason.NoTangibleNetBenefit },
  { display: 'Follow-Up After Delisted', value: DenyReason.FollowUpAfterDelisted },
  { display: 'FHA Lending Limit Exceeded', value: DenyReason.FhaLendingLimitExceeded },
  { display: 'Illinois Regulations', value: DenyReason.IllinoisRegulations },
];

export const INCOME_OPTIONS: DeactivationOption[] = [

  // { display: 'Unemployed', value: DenyReason.Unemployed }, // NOT SUPPORTED
  { display: 'Length of Time Self-Employed', value: DenyReason.LengthOfTimeEmployed },
  { display: 'No Verifiable Income', value: DenyReason.NoVerifiableIncome },
  {
    display: 'Self-Employment Income Not Supported',
    value: DenyReason.SelfEmploymentIncomeNotSupported,
  },
  { display: 'Credit Score OK - DTI Excessive', value: DenyReason.DtiExcessive },
  {
    display: 'DTI Excessive - Client Pays Child Support',
    value: DenyReason.DtiExcessiveChildSupport,
  },
  { display: 'Length of Time Employed', value: DenyReason.LengthOfTimeEmployed },
  {
    display: 'Continuance of Non-Employment Income',
    value: DenyReason.ContinuanceOfNonEmploymentIncome,
  },
  // { display: 'Client Pays Child Support', value: DenyReason.PaysChildSupport }, // NOT SUPPORTED
  { display: 'No Verifiable Income', value: DenyReason.NoVerifiableIncome },
  { display: 'Employment Status Changed', value: DenyReason.EmploymentStatusChanged },
];

export const PROPERTY_OPTIONS: DeactivationOption[] = [
  { display: 'Insufficient Equity', value: DenyReason.InsufficientEquity },
  {
    display: 'Collateral: Unacceptable Property Type',
    value: DenyReason.UnacceptablePropertyType,
  },
];

export const LENDER_OPTIONS: DeactivationOption[] = [
  {
    display: 'Already Closed with Another Lender',
    value: WithdrawReason.AnotherLenderAlreadyClosed,
  },
  { display: 'In Process With Another Lender', value: WithdrawReason.AnotherLenderInProcess },
  {
    display: 'Chose Another Lender - Better Program/Process',
    value: WithdrawReason.AnotherLenderBetterProgram,
  },
  {
    display: 'Chose Another Lender - Better Rate Fees',
    value: WithdrawReason.AnotherLenderBetterRateFees,
  },
  {
    display: 'Only Wants a Brick and Mortar Lender',
    value: WithdrawReason.OnlyWantsBrickAndMortarLender,
  },
  {
    display: 'Realtor/Builder Referral',
    value: WithdrawReason.AnotherLenderRealtorBuilderReferral,
  },
  { display: 'Client Prefers TPO Broker', value: WithdrawReason.ClientPrefersTpoBroker },
];

export const NOT_INTERESTED_OPTIONS: DeactivationOption[] = 
[
  { display: 'Client Shopping Rates', value: WithdrawReason.ClientShoppingRates },
  { display: 'Chose Not to Relocate', value: WithdrawReason.ChoseNotToRelocate },
  { display: 'No Reason Given', value: WithdrawReason.NoReasonGiven },
  {
    display: 'Loan Does Not Make Sense for the Client',
    value: WithdrawReason.LoanDoesNotMakeSenseToClient,
  },
  { display: 'Unable to Re-connect with the Client', value: WithdrawReason.UnresponsiveClient },
  // {display: 'Inquiry About Product', value: WithdrawReason.InquiryAboutProduct}, // NOT SUPPORTED
  { display: 'Personal Loan', value: WithdrawReason.PersonalLoan },
  { display: 'Unresponsive Client', value: WithdrawReason.UnresponsiveClient },
  { display: 'Auto Expired Due To Age', value: WithdrawReason.AutoExpiredDueToAge },
];

export const FUTURE_OPTIONS: DeactivationOption[] = [
  { display: 'Renting', value: WithdrawReason.FutureRenting },
  { display: 'New Construction/Rehab', value: WithdrawReason.FutureNewConstruction },
  { display: 'Purchase', value: WithdrawReason.FuturePurchase },
  { display: 'Refinance', value: WithdrawReason.FutureRefinance },
];

export const GUIDELINE_ISSUE_OPTIONS: DeactivationOption[] = [
  { display: 'Prepayment Penalty', value: WithdrawReason.PrepaymentPenalty }
];

export const denyReasonOptions: DeactivationWrapper[] = [
  {
    display: 'Credit Issues',
    bucket: DenyBucket.CreditIssues,
    additionalOptions: CREDIT_OPTIONS,
  },
  {
    display: 'Asset Issues',
    bucket: DenyBucket.AssetIssues,
    additionalOptions: ASSET_OPTIONS,
  },
  {
    display: 'Guideline Restrictions',
    bucket: DenyBucket.GuidelineRestrictions,
    additionalOptions: GUIDELINE_RESTRICTIONS_OPTIONS,
  },
  {
    display: 'Income Issues',
    bucket: DenyBucket.IncomeIssues,
    additionalOptions: INCOME_OPTIONS,
  },
  {
    display: 'Product Not Available',
    bucket: DenyBucket.ProductNotAvailable,
    additionalOptions: [
      { display: 'Product Not Available', value: DenyReason.ProductNotAvailable },
    ],
  },
  {
    display: 'Property Issues',
    bucket: DenyBucket.PropertyIssues,
    additionalOptions: PROPERTY_OPTIONS,
  },
];

export const withdrawReasonOptions: DeactivationWrapper[] = [
  {
    display: 'Chose Another Lender',
    bucket: WithdrawBucket.ChooseAnotherLender,
    additionalOptions: LENDER_OPTIONS,
  },
  {
    display: 'Client Not Interested',
    bucket: WithdrawBucket.ClientNotInterested,
    additionalOptions: NOT_INTERESTED_OPTIONS,
  },
  {
    display: 'Future Opportunity',
    bucket: WithdrawBucket.FutureOpportunity,
    additionalOptions: FUTURE_OPTIONS,
  },
  {
    display: 'Guideline Issues',
    bucket: WithdrawBucket.GuidelineIssues,
    additionalOptions: GUIDELINE_ISSUE_OPTIONS,
  },
];
