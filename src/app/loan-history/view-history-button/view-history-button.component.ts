import { Component, ViewContainerRef, inject } from '@angular/core';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { LoanHistoryComponent } from '../loan-history.component';
@Component({
  selector: 'app-view-history-button',
  standalone: true,
  imports: [MatDialogModule, LoanHistoryComponent, MatMenuModule, MatIconModule],
  templateUrl: './view-history-button.component.html',
  styleUrl: './view-history-button.component.scss',
})
export class ViewHistoryButtonComponent {
  private dialog = inject(MatDialog);
  private viewContainerRef = inject(ViewContainerRef);
  openDialog() {
    this.dialog.open(LoanHistoryComponent, {
      data: {},
      panelClass: 'rkt-Dialog',
      minWidth: '50%',
      minHeight: '50%',
      maxHeight: '95vh',
      backdropClass: 'rkt-Backdrop',
      viewContainerRef: this.viewContainerRef,
    });
  }
}
