<div class="header">
  <h1 mat-dialog-title>Loan History</h1>
  <button mat-icon-button (click)="closeDialog()">
    <mat-icon>close</mat-icon>
  </button>
</div>

<div class="content-wrapper">
  <mat-card class="rkt-Card--enterprise section-container">
    @if (errorContent()) {
      <ng-container *ngTemplateOutlet="errorTemplate"></ng-container>
    }

    @if (loading()) {
      <ng-container *ngTemplateOutlet="loadingTemplate"></ng-container>
    } @else if (content()) {
      <ng-container>
        <pre>{{ content() | json }}</pre>
      </ng-container>
    } @else {
      <ng-container *ngTemplateOutlet="noContentTemplate"></ng-container>
    }
  </mat-card>

  <ng-template #noContentTemplate><div class="no-history">No History Available</div> </ng-template>

  <ng-template #loadingTemplate>
    <div class="rkt-ProgressBarContainer rkt-ProgressBarContainer--enterprise">
      <div class="rkt-ProgressBarLabel rkt-Spacing--mt0 rkt-Spacing--mb8">Loading Loan History</div>
      <mat-progress-bar class="rkt-ProgressBar" mode="indeterminate"></mat-progress-bar>
      <div class="rkt-ProgressBarHelperText rkt-Spacing--mt8">Gathering form data...</div>
    </div>
  </ng-template>

  <ng-template #errorTemplate>
    <rkt-alert variant="warn" [isDismissible]="false">
      <p class="rkt-Alert__text">{{ errorContent() }}</p>
    </rkt-alert>
  </ng-template>
</div>
