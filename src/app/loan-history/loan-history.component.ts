import { CommonModule, JsonPipe } from '@angular/common';
import { Component, Signal, WritableSignal, computed, inject, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { RktAlertModule } from '@rocketcentral/rocket-design-system-angular';
import { Observable, catchError, of } from 'rxjs';
import { LoanStateService } from '../services/entity-state/loan-state/loan-state.service';
import { ErrorUtilities } from '../util/error-utilities';

@Component({
  selector: 'app-loan-history',
  standalone: true,
  imports: [
    MatCardModule,
    JsonPipe,
    MatDialogModule,
    MatIconModule,
    MatButtonModule,
    CommonModule,
    RktAlertModule,
    MatProgressBarModule,
    MatProgressSpinnerModule,
  ],
  templateUrl: './loan-history.component.html',
  styleUrl: './loan-history.component.scss',
})
export class LoanHistoryComponent {
  private loanStateService: LoanStateService = inject(LoanStateService);
  private dialogRef = inject(MatDialogRef<LoanHistoryComponent>);

  public errorContent: WritableSignal<string | null> = signal(null);
  public content$: Observable<string | null | undefined> = this.loanStateService.loanHistory$.pipe(
    catchError((e) => {
      this.errorContent.set(ErrorUtilities.httpErrorToMessage(e));
      return of(null);
    }),
  );
  public content: Signal<string | null | undefined> = toSignal(this.content$);
  public loading: Signal<boolean | null> = computed(
    () => !this.content() && this.content !== null && !this.errorContent(),
  );

  closeDialog() {
    this.dialogRef.close();
  }
}
