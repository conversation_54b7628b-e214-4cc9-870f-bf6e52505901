.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

mat-card {
  min-height: 20vh;
  display: flex;
  align-items: flex-start;
  justify-content: stretch;
}

.header {
  margin-bottom: 0.5rem;

  h1 {
    margin-bottom: 0;
  }
}

.content-wrapper {
  overflow-y: auto;
  overflow-x: hidden;
}

.rkt-ProgressBarContainer,
.no-history {
  flex: 1 1 100%;
  justify-content: center;
  display: flex;
  flex-direction: column;
  align-self: center;
  justify-self: center;
  height: 100%;
}

rkt-alert {
  display: flex;
  flex-direction: column;
  justify-self: flex-start;
  align-self: center;
  width: 100%;
  margin-bottom: 1rem;
  overflow: hidden;
  text-overflow: ellipsis;
}