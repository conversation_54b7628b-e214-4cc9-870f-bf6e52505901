@use 'sass:map';
@use '@angular/material' as mat;


@mixin color($theme) {

    @if mat.get-theme-type($theme)==dark {
        .app-header {
            background-color: mat.get-theme-color($theme, background, app-bar);
            border-bottom: 1px solid mat.get-theme-color($theme, foreground, divider);
            color: mat.get-theme-color($theme, accent, 900);
        }
    }

    @else {
        .app-header {
            background-color: var(--rlxp-header-background-color);
            border-bottom: 1px solid mat.get-theme-color($theme, foreground, divider);
        }
    }
}

@mixin typography($theme) {}

@mixin theme($theme) {
    @if mat.theme-has($theme, color) {
        @include color($theme);
    }

    @if mat.theme-has($theme, typography) {
        @include typography($theme);
    }
}