import { NgTemplateOutlet } from '@angular/common';
import { Component, computed, inject, input, TemplateRef } from '@angular/core';
import { MatIconModule } from '@angular/material/icon';
import { DarkModeService } from '../services/dark-mode/dark-mode.service';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [MatIconModule, MatIconModule, NgTemplateOutlet],
  templateUrl: './header.component.html',
  styleUrl: './header.component.scss',
  providers: [DarkModeService],
})
export class HeaderComponent {
  darkModeService = inject(DarkModeService);

  iconTemplate = input.required<TemplateRef<any>>();
  actionsTemplate = input.required<TemplateRef<any>>();

  iconTemplateContext = computed(() => {
    return { isDarkMode: this.darkModeService.isDarkMode() };
  });
}
