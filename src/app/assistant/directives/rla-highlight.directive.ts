import { AfterRenderPhase, Directive, ElementRef, inject, Injector } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormControl, NgControl } from '@angular/forms';
import { AnalyticsBrowser } from '@segment/analytics-next';
import { BehaviorSubject, combineLatest, concat, distinctUntilChanged, filter, firstValueFrom, fromEvent, map, Observable, of, pairwise, scan, shareReplay, startWith, Subject, switchMap, take, takeWhile, tap, throwError, timeout, withLatestFrom } from 'rxjs';
import { CONTROL_PROVIDER } from '../../_shared/tokens/control-provider';
import { FIELD_VALUE_PREVIEWER } from '../../_shared/tokens/field-value-previewer';
import { afterNextRender$ } from '../../_shared/util/after-next-render';
import { LoanIdService } from '../../services/loan-id/loan-id.service';
import { SuggestionsService } from '../../suggestions/suggestions.service';
import { rawValueChanges$ } from '../../util/raw-value-changes';
import { AssistantService } from '../services/assistant.service';

@Directive({
  selector: '[appRlaHighlight]',
  standalone: true,
  exportAs: 'rlaHighlight',
})
export class RlaHighlightDirective {
  private suggestionsService = inject(SuggestionsService);
  private injector = inject(Injector);
  private assistantService = inject(AssistantService);
  private fieldValuePreviewer = inject(FIELD_VALUE_PREVIEWER, { optional: true });
  private elementRef = inject(ElementRef);
  private control = inject(NgControl, { optional: true });
  private controlProvider = inject(CONTROL_PROVIDER, { optional: true })
  private analyticsBrowser = inject(AnalyticsBrowser, { optional: true });
  private loanIdService = inject(LoanIdService, { optional: true });

  private resolvedControl$: Observable<FormControl> = afterNextRender$({ phase: AfterRenderPhase.Write, injector: this.injector }).pipe(
    switchMap(() => {
      if (this.control?.control) {
        return of(this.control.control as FormControl);
      }
      if (this.controlProvider) {
        return toObservable(this.controlProvider.control, { injector: this.injector }).pipe(
          filter((control): control is FormControl => !!control),
          timeout({
            first: 2_000,
            with: () => throwError(() => new Error(`${RlaHighlightDirective.name}: CONTROL_PROVIDER was provided but did not emit a value`)),
          }),
        );
      }
      throw new Error(`${RlaHighlightDirective.name}: Could not find NgControl or CONTROL_PROVIDER`);
    }),
  );

  private suggestion$ = this.resolvedControl$.pipe(
    switchMap((control) => this.suggestionsService.suggestionsForControl$(control)),
    map(suggestions => suggestions.at(0)),
    shareReplay(1),
    takeUntilDestroyed(),
  );

  applySuggestionIfExistsAction$ = new Subject<{ applyType: 'on-load' | 'manual' }>();

  isOnLoadSuggestionPhase$ = combineLatest({
    controlValueHasRemainedEmptySinceLoad: this.resolvedControl$.pipe(
      switchMap((control) => {
        return rawValueChanges$(control).pipe(
          scan((everyValueHasBeenEmpty, value) => {
            if (!everyValueHasBeenEmpty) {
              return false;
            }
            if (value === null || value === '') {
              return true;
            }
            return false;
          }, true),
        );
      }),
    ),
    hasSuggestionBeenConsumed: this.applySuggestionIfExistsAction$.pipe(
      map(() => true),
      startWith(false),
    ),
  }).pipe(
    map(({ controlValueHasRemainedEmptySinceLoad, hasSuggestionBeenConsumed }) => {
      if (hasSuggestionBeenConsumed) {
        return false;
      }
      return controlValueHasRemainedEmptySinceLoad;
    }),
    distinctUntilChanged(),
    shareReplay(1),
    takeUntilDestroyed(),
  );

  manualShouldShowSuggestionIfExists$ = new BehaviorSubject<boolean>(false);
  private shouldShowSuggestionIfExists$: Observable<boolean> = concat(
    this.isOnLoadSuggestionPhase$.pipe(
      takeWhile(isOnLoadSuggestionPhase => isOnLoadSuggestionPhase),
      map(() => true),
    ),
    // Manual updates will not apply until the "on load" suggestion phase is finished.
    this.manualShouldShowSuggestionIfExists$,
  ).pipe(
    distinctUntilChanged(),
    shareReplay(1),
    takeUntilDestroyed(),
  );

  constructor() {
    // Apply suggestion if it exists.
    this.applySuggestionIfExistsAction$
      .pipe(
        withLatestFrom(this.suggestion$, this.resolvedControl$),
        takeUntilDestroyed(),
      )
      .subscribe(async ([{ applyType }, suggestion, control]) => {
        if (suggestion) {
          this.manualShouldShowSuggestionIfExists$.next(false);
          control.markAsDirty();
          control.setValue(suggestion.value);

          const description = applyType === 'on-load'
            ? 'RLA: Applied on-load suggestion'
            : 'RLA: Applied suggestion'
          await Promise.allSettled([
            firstValueFrom(this.assistantService.recordUsage$()),
            firstValueFrom(this.assistantService.recordMetrics$({ fieldsAppliedCount: 1 })),
            this.analyticsBrowser?.trackClick(this.elementRef.nativeElement, description, {
              loanNumber: this.loanIdService?.loanId(),
            }),
          ]);
        }
      });

    // Add/remove click handler that applies the suggestion when in "on load" phase.
    this.isOnLoadSuggestionPhase$
      .pipe(
        switchMap((isOnLoadSuggestionPhase) => {
          this.elementRef.nativeElement.title = isOnLoadSuggestionPhase ? 'Click to apply suggestion' : '';
          if (isOnLoadSuggestionPhase) {
            return fromEvent<Event>(this.elementRef.nativeElement, 'click').pipe(
              take(1),
              tap(() => {
                this.applySuggestionIfExistsAction$.next({ applyType: 'on-load' });
              }),
              map(() => isOnLoadSuggestionPhase),
            );
          }
          return of(isOnLoadSuggestionPhase);
        }),
        takeWhile(isOnLoadSuggestionPhase => isOnLoadSuggestionPhase),
        takeUntilDestroyed(),
      )
      .subscribe();

    const showSuggestionCssClass = 'rla-show-suggestion';

    // Handles showing the suggestion.
    combineLatest({
      suggestion: this.suggestion$,
      shouldShowSuggestionIfExists: this.shouldShowSuggestionIfExists$,
    })
      .pipe(
        filter(({ shouldShowSuggestionIfExists }) => shouldShowSuggestionIfExists),
        takeUntilDestroyed(),
      )
      .subscribe(({ suggestion }) => {
        if (suggestion) {
          this.elementRef.nativeElement.classList.add(showSuggestionCssClass);
          if (this.fieldValuePreviewer) {
            this.fieldValuePreviewer.preview.set(suggestion.formattedValue ?? suggestion.value);
          } else {
            console.error('fieldValuePreviewer does not exist', this.elementRef.nativeElement);
          }
        }
      });


    // Handles hiding the suggestion.
    this.shouldShowSuggestionIfExists$
      .pipe(
        filter((shouldShowSuggestionIfExists) => !shouldShowSuggestionIfExists),
        takeUntilDestroyed(),
      )
      .subscribe(() => {
        this.elementRef.nativeElement.classList.remove(showSuggestionCssClass);
        if (this.fieldValuePreviewer) {
          this.fieldValuePreviewer.preview.set(null);
        } else {
          console.error('fieldValuePreviewer does not exist', this.elementRef.nativeElement);
        }
      });

    // Handle hiding the suggestion when it flips to undefined.
    this.suggestion$
      .pipe(
        pairwise(),
        filter(([prev, next]) => !!prev && !next),
        takeUntilDestroyed()
      )
      .subscribe(() => {
        this.elementRef.nativeElement.classList.remove(showSuggestionCssClass);
        if (this.fieldValuePreviewer) {
          this.fieldValuePreviewer.preview.set(null);
        } else {
          console.error('fieldValuePreviewer does not exist', this.elementRef.nativeElement);
        }
      });
  }
}
