@use '@rocketcentral/rocket-design-system-styles/web/scss/_color.scss' as *;

:host {
  height: 100%;
  overflow: auto;
}

.formatted-inner-html {
  ::ng-deep {
    ul {
      list-style-type: none;
      padding: 0;
      font-weight: normal;
      display: flex;
      flex-direction: column;
      gap: 0.5rem;
    }

    li {
      padding: 0;
    }

    ul:not(:last-child), p:not(:last-child) {
      margin-bottom: 0.75rem;
    }

    h2 {
      font-size: 0.7rem;
      color: #9d9d9d;
      font-weight: bold;
      margin-bottom: 0.6rem;
    }

    h2:not(:first-child) {
      margin-top: 2rem;
    }
  }
}

mat-icon.rkt-Icon[svgIcon="rl-assistant-circled"] {
  width: 2.25rem;
  height: 2.25rem;
  line-height: 2.25rem;
  filter: drop-shadow(0px 0px 12px rgba(0, 0, 0, 0.20));
}

.summary {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 20rem;
}

.mat-icon-button-purple {
  --mdc-icon-button-icon-color: #5B3C9C;
}

.feedback {
  background: #fff;
}

mat-icon[svgIcon="rl-assistant-sparkle"] {
  color: $rkt-purple-bright-500;
}

// Mimics look of RDS expansion panel
.sos-content {
  background: var(--mat-expansion-panel-background-color);
  border-radius: 12px;
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.06);
  padding: 1rem;
}

@property --rotate {
  syntax: '<angle>';
  initial-value: 0deg;
  inherits: false;
}

.active-call {
  position: relative;
}

.active-call::after, .active-call::before {
  position: absolute;
  content: '';
  width: 100%;
  height: 100%;
  top: 49.75%;
  left: 50%;
  translate: -50% -50%;
  z-index: -1;
  box-sizing: content-box;

  padding: 5px;
  animation: rotate-gradient 3s linear infinite;
  background-image: conic-gradient(from var(--rotate), transparent, #aa80e7);
  border-radius: 12px;
}
.active-call::before {
  filter: blur(1.5rem);
  opacity: 0.5;
}
@keyframes rotate-gradient {
  from {
    --rotate: 0deg;
  }
  to {
    --rotate: 360deg;
  }
}

:host-context(.rkt-DarkMode) {
  mat-icon[svgIcon="rl-assistant-sparkle"] {
    // Override overly-specific RDS rule
    color: $rkt-purple-bright-50 !important;
  }

  .summary {
    &__text {
      color: #D3D3D3;
    }
  }

  .feedback {
    background: #303030;
  }

  // Override overly-specific RDS rules (I hate this library)
  mat-expansion-panel {
    background-color: #313132 !important;

    &:hover {
      background-color: #474747 !important;
    }
  }
}
