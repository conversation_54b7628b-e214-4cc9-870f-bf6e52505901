import { AssetType } from '@rocket-logic/rl-xp-bff-models';
import { RecursivePartial } from '../../util/recursive-partial';
import { ExtractedGroupMatchStatus } from './extracted-group-match-status';
import { ExtractedValue } from './extracted-value';
import { TranscriptionExtractedAsset, TranscriptionExtractedClient, TranscriptionExtractedIncome } from './transcription-extracted-info';

export enum ExtractedGroupType {
  Client = 'CLIENT',
  Income = 'INCOME',
  Loan = 'LOAN',
  Asset = 'ASSET',
}

type BaseExtractedGroup = {
  label: string;
  status: ExtractedGroupMatchStatus;
  values: ExtractedValue[];
  tooltip?: string;
};

export type IncomeExtractedGroup = BaseExtractedGroup & {
  type: ExtractedGroupType.Income;
  context: {
    extractedIncome: RecursivePartial<TranscriptionExtractedIncome>;
    clientId: string;
  },
};

export type ClientExtractedGroup = BaseExtractedGroup & {
  type: ExtractedGroupType.Client;
  context: {
    extractedClient: RecursivePartial<TranscriptionExtractedClient>;
  },
};

export type LoanExtractedGroup = BaseExtractedGroup & {
  type: ExtractedGroupType.Loan;
};

export type AssetExtractedGroup = BaseExtractedGroup & {
  type: ExtractedGroupType.Asset;
  context: {
    assetType: AssetType,
    extractedAsset: RecursivePartial<TranscriptionExtractedAsset>;
  },
};

export type ExtractedGroup = IncomeExtractedGroup | ClientExtractedGroup | LoanExtractedGroup | AssetExtractedGroup;
