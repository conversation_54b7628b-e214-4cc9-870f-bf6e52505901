import { AssetType, IncomePaymentFrequency, IncomeType, MaritalStatus, MilitaryBranch, MilitaryComponent, MilitaryStatus, OccupancyType, OwnedPropertyDispositionStatus, PhoneNumberType, ResidencyType, SellerRelationship, State, Suffix, VADisabilityBenefitsStatus } from '@rocket-logic/rl-xp-bff-models';
import { RecursivePartial } from '../../util/recursive-partial';

export type TranscriptionExtractedInfo = RecursivePartial<{
  loanPurpose: string;
  sellerRelationshipToBorrower: SellerRelationship;
  hasSignedPurchaseAgreement: boolean;
  downPaymentAmount: number;
  downPaymentAmountType: string;
  sellerConcessionsAmount: number;
  contractClosingDate: string;
  earnestMoneyDeposit: number;
  purchasePrice: number;
  clients: TranscriptionExtractedClient[];
  accountAssets: TranscriptionExtractedAsset[];
  subsidyAssets: TranscriptionExtractedAsset[];
  giftAssets: TranscriptionExtractedAsset[];
  miscellaneousAssets: TranscriptionExtractedAsset[];
}>;

export interface TranscriptionExtractedClient {
  isPrimary: boolean;
  firstName: string;
  middleName: string;
  lastName: string;
  suffix: Suffix;
  preferredName: string;
  phone: {
    phoneNumber: number;
    phoneType: PhoneNumberType;
  };
  email: string;
  maritalStatus: MaritalStatus;
  marriedTo: string;
  currentAddress: {
    street: string;
    unit: string;
    state: State;
    city: string;
    zipCode: number;
    yearsLivedAt: number;
    monthsLivedAt: number;
    taxesFiledAtThisAddress: boolean;
    ownershipStatus: ResidencyType;
    planForProperty: OwnedPropertyDispositionStatus;
    sellingPrice: number;
    occupancyType: OccupancyType;
    monthlyRent: number;
  };
  servedInArmedForcesOrEligibleForVaHomeLoan: boolean;
  militaryInfo: {
    branchOfService: MilitaryBranch;
    serviceType: MilitaryComponent;
    militaryStatus: MilitaryStatus;
    disabilityBenefits: VADisabilityBenefitsStatus;
    dischargedUnderConditionsOtherThanDishonorable: boolean;
    hadPriorVALoan: boolean;
    survivingSpouse: boolean;
  };
  income: TranscriptionExtractedIncome[];
}

export interface TranscriptionExtractedIncome {
  type: IncomeType;
  amount: number;
  frequency: IncomePaymentFrequency;
  employerName: string;
  jobTitle: string;
  startDate: string;
  endDate: string;
}

export interface TranscriptionExtractedAsset {
  assetType: AssetType;
  assetValue: number;

  // account asset fields
  accountIdentifier?: number;
  financialInstitution?: string;

  // subsidy asset fields
  nameOfGrantor?: string;
  source?: string;
  grantDestinationType?: string;

  // gift asset fields
  nameOfGiftGiver?: string;
  giftSource?: string;
  giftDestinationType?: string;
}
