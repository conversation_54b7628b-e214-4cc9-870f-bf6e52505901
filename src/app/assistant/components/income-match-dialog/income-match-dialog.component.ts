import { Component, inject } from '@angular/core';
import { <PERSON><PERSON><PERSON><PERSON>, Form<PERSON>uilder, ReactiveFormsModule } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatDivider } from '@angular/material/divider';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatOption, MatSelect } from '@angular/material/select';
import { IncomeType } from '@rocket-logic/rl-xp-bff-models';
import { IncomeTypeAutocompleteComponent } from "../../../income-info/income-type-autocomplete/income-type-autocomplete.component";
import { AllIncomeGroup } from '../../../services/entity-state/income-state/form-types';

export type IncomeMatchDialogData = {
  existingIncomes: FormArray<AllIncomeGroup>;
};

export type IncomeMatchDialogResult = {
  resultType: 'new';
  incomeType: IncomeType,
} | {
  resultType: 'existing';
  existingIncomeInForm: AllIncomeGroup,
};

@Component({
  selector: 'app-income-match-dialog',
  standalone: true,
  imports: [
    MatDialogModule,
    IncomeTypeAutocompleteComponent,
    ReactiveFormsModule,
    MatButton,
    MatDivider,
    MatFormField,
    MatSelect,
    MatOption,
    MatLabel,
  ],
  templateUrl: './income-match-dialog.component.html',
})
export class IncomeMatchDialogComponent {
  private data: IncomeMatchDialogData = inject(MAT_DIALOG_DATA);
  dialogRef: MatDialogRef<IncomeMatchDialogComponent, IncomeMatchDialogResult> = inject(MatDialogRef);
  private fb = inject(FormBuilder);

  incomeTypeCtrl = this.fb.control<IncomeType | null>(null);
  existingIncomeCtrl = this.fb.control<AllIncomeGroup | null>(null);

  existingIncomeOptions = this.data.existingIncomes.controls.map(c => {
    return {
      display: c.value.employment?.employerName ?? c.value.incomeType,
      value: c,
    };
  });
}
