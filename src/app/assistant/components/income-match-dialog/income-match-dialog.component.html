<h1 mat-dialog-title>Match Income</h1>

<mat-dialog-content>

  <h2>Associate with existing income</h2>
  <mat-form-field style="display:block"  class="rkt-FormField"   color="accent">
    <mat-label>Existing Income</mat-label>

    <mat-select class="rkt-Input" [formControl]="existingIncomeCtrl">
      <mat-option [value]="null">None</mat-option>
      @for (option of existingIncomeOptions; track $index) {
        <mat-option [value]="option.value">{{ option.display }}</mat-option>
      }
    </mat-select>
  </mat-form-field>
  <button
    mat-flat-button
    class="rkt-Button"
    [disabled]="!existingIncomeCtrl.value"
    
    (click)="dialogRef.close({ resultType: 'existing', existingIncomeInForm: existingIncomeCtrl.value! })"
  >Associate</button>

  <mat-divider style="margin: 1rem 0" />

  <h2>Create a new income</h2>
  <app-income-type-autocomplete [formControl]="incomeTypeCtrl" style="display:block; margin-top:0.5rem" />
  <button
    mat-flat-button
    class="rkt-Button"
    [disabled]="!incomeTypeCtrl.value"
    
    (click)="dialogRef.close({ resultType: 'new', incomeType: incomeTypeCtrl.value! })"
  >Create</button>

</mat-dialog-content>

<mat-dialog-actions align="end">
  <button
    mat-flat-button
    class="rkt-Button rkt-Button--secondary"
    (click)="dialogRef.close()"
  >Cancel</button>
</mat-dialog-actions>
