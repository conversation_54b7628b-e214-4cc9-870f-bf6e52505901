@use '@rocketcentral/rocket-design-system-styles/web/scss/_color.scss' as *;

.extraction-tile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0.75rem;
  box-sizing: border-box;

  padding: 0.5rem 1rem;
  border: 0.5px solid transparent;
  background: #FFF;
  text-align: left;

  &--unmatched {
    user-select: text;
  }

  &--matched {
    background: $rkt-purple-a100;
    border: 0.5px solid $rkt-purple-bright-500;
  }

  &--partial-matched {
    cursor: pointer;

    .extraction-tile__value--overridden {
      font-weight: 400;
      text-decoration: line-through;
    }

    &:hover {
      border: 0.5px solid $rkt-purple-bright-500;
    }
  }

  &__value {
    color: $rkt-purple-bright-500;
  }

  &__icon {
    color: $rkt-purple-bright-500;

    &--matched {
      color: $rkt-green-500;
    }
  }
}

:host-context(.rkt-DarkMode) {
  .extraction-tile {
    background: #180c1c;

    &--matched {
      background: #0a0a0a;
      border: 0.5px solid $rkt-purple-bright-50;
    }

    &--partial-matched {
      &:hover {
        border: 0.5px solid $rkt-purple-bright-50;
      }
    }

    &__value {
      color: $rkt-purple-bright-50;
    }

    &__icon {
      color: $rkt-purple-bright-500 !important;

      &--matched {
        color: $rkt-green-500 !important;
      }
    }
  }
}
