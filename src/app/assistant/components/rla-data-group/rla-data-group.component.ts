import { Component, computed, inject, input } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { TrackClickDirective } from '../../../analytics/track-click.directive';
import { ExtractedGroupMatchStatus } from '../../models/extracted-group-match-status';
import { AssistantService } from '../../services/assistant.service';

@Component({
  selector: 'app-rla-data-group',
  standalone: true,
  imports: [
    MatTooltipModule,
    MatIconModule,
    TrackClickDirective,
  ],
  styleUrl: './rla-data-group.component.scss',
  templateUrl: './rla-data-group.component.html',
})
export class RlaDataGroupComponent {
  assistantService = inject(AssistantService);

  groupLabelFilter = input<string>();
  direction = input<'horizontal' | 'vertical'>('vertical');

  readonly ExtractedDataMatchStatus = ExtractedGroupMatchStatus;

  groups = toSignal(this.assistantService.groups$, { initialValue: [] });
  filteredGroups = computed(() => {
    const filter = this.groupLabelFilter();
    if (!filter) {
      return this.groups();
    }
    return this.groups().filter((group) => group.label.toLowerCase().includes(filter.toLowerCase()));
  });
}
