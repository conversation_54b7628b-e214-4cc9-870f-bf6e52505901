<div class="flex gap-2" [class.flex-col]="direction() === 'vertical'">
  @for (group of filteredGroups(); track group) {
    <button
      type="button"
      class="extraction-tile"
      [class.extraction-tile--unmatched]="group.status === ExtractedDataMatchStatus.Unmatched"
      [class.extraction-tile--matched]="group.status === ExtractedDataMatchStatus.Matched"
      [class.extraction-tile--partial-matched]="group.status === ExtractedDataMatchStatus.PartialMatched"
      (click)="assistantService.handleGroupMatch(group)"
      [matTooltip]="group.tooltip ?? ''"
      matTooltipPosition="left"
      [disabled]="group.status === ExtractedDataMatchStatus.Matched || group.status === ExtractedDataMatchStatus.Unmatched"
      appTrackClick
      [description]="'RLA: Applied group'"
    >
      <div class="overflow-auto">
        <div class="rkt-Caption-12">{{ group.label }}</div>
        <div>
          @for (value of group.values; track value) {
            <div class="text-sm break-words">
              @if (value.label) {
                <span>{{ value.label }}: </span>
              }
              <span class="extraction-tile__value rkt-FontWeight--700">{{ value.formattedValue ?? value.value }}</span>
            </div>
          }
        </div>
      </div>

      <div class="flex-shrink-0 flex items-center justify-center">
        @switch (group.status) {
          @case (ExtractedDataMatchStatus.Matched) {
            <mat-icon class="rkt-Icon extraction-tile__icon extraction-tile__icon--matched" svgIcon="check_circle-two_tone"></mat-icon>
          } @case (ExtractedDataMatchStatus.Unmatched) {
            <div></div>
          } @default {
            <mat-icon class="rkt-Icon extraction-tile__icon" svgIcon="add_circle-two_tone"></mat-icon>
          }
        }
      </div>
    </button>
  }
</div>
