@if (suggestion(); as suggestion) {
  <div
    (mouseenter)="isOpenSubject.next(true)"
    (mouseleave)="isOpenSubject.next(false)"
    cdkOverlayOrigin
    #trigger="cdkOverlayOrigin"
  >
    @if (isOnLoadSuggestionPhase()) {
      <button
        type="button"
        mat-icon-button
        style="cursor:default; pointer-events:none"
        (click)="$event.stopPropagation()"
      >
        <mat-icon class="rkt-Icon" svgIcon="rl-assistant-sparkle"></mat-icon>
      </button>
    } @else if (suggestion.isEqualToControl) {
      <button
        type="button"
        mat-icon-button
        matTooltip="Value matches suggestion"
        [matTooltipPosition]="'above'"
        (click)="$event.stopPropagation()"
        class="matches-suggestion cursor-not-allowed"
      >
        <mat-icon class="rkt-Icon" svgIcon="rl-assistant-sparkle"></mat-icon>
      </button>
    } @else {
      <button
        type="button"
        mat-icon-button
        (click)="$event.stopPropagation(); applySuggestion();"
        matTooltip="Click to apply the suggestion"
        [matTooltipPosition]="'above'"
        (mouseenter)="setShouldPreviewSuggestion(true)"
        (mouseleave)="setShouldPreviewSuggestion(false)"
        aria-label="Apply suggestion"
      >
        <mat-icon class="rkt-Icon" svgIcon="rl-assistant-sparkle"></mat-icon>
      </button>
    }
  </div>

  @if (suggestionContext(); as context) {
    <ng-template
      cdkConnectedOverlay
      [cdkConnectedOverlayOrigin]="trigger"
      [cdkConnectedOverlayOpen]="isOpen()"
      [cdkConnectedOverlayPositions]="positions"
    >
      <div
        class="transcript-overlay rounded-md p-4 shadow-xl max-h-96 overflow-y-auto"
        (mouseenter)="isOpenSubject.next(true)"
        (mouseleave)="isOpenSubject.next(false)"
        [@overlayTransition]="'showing'"
      >
        <div [innerHTML]="context" class="transcript-overlay__text max-w-72"></div>
      </div>
    </ng-template>
  }
}
