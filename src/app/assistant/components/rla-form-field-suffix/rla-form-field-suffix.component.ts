import { animate, state, style, transition, trigger } from '@angular/animations';
import { ConnectedPosition, OverlayModule } from '@angular/cdk/overlay';
import { AfterRenderPhase, Component, inject, Injector, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, NgControl } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { BehaviorSubject, combineLatest, debounceTime, distinctUntilChanged, filter, map, Observable, of, shareReplay, switchMap, throwError, timeout } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { CONTROL_PROVIDER } from '../../../_shared/tokens/control-provider';
import { afterNextRender$ } from '../../../_shared/util/after-next-render';
import { SuggestionsService } from '../../../suggestions/suggestions.service';
import { RlaHighlightDirective } from '../../directives/rla-highlight.directive';
import { RlaDataService } from '../../services/rla-data.service';
import { escapeRegex } from './escape-regex';

@Component({
  selector: 'app-rla-form-field-suffix',
  standalone: true,
  imports: [
    MatIconModule,
    MatButtonModule,
    OverlayModule,
    MatTooltipModule,
    MatDividerModule,
  ],
  templateUrl: './rla-form-field-suffix.component.html',
  styleUrl: './rla-form-field-suffix.component.scss',
  animations: [
    trigger('overlayTransition', [
      state(
        'void',
        style({
          opacity: 0,
          transform: 'rotateX(-50deg)',
        })
      ),
      state(
        'showing',
        style({
          opacity: 1,
          transform: 'rotateX(0)',
        })
      ),
      transition('void <=> showing', animate('0.1s ease-in-out')),
    ]),
  ],
})
export class RlaFormFieldSuffixComponent {
  private suggestionsService = inject(SuggestionsService);
  private directive = inject(RlaHighlightDirective, { optional: true });
  private rlaDataService = inject(RlaDataService);
  private injector = inject(Injector);
  private control = inject(NgControl, { optional: true });
  private controlProvider = inject(CONTROL_PROVIDER, { optional: true })

  private resolvedControl$: Observable<FormControl> = afterNextRender$({ phase: AfterRenderPhase.Write, injector: this.injector }).pipe(
    switchMap(() => {
      if (this.control?.control) {
        return of(this.control.control as FormControl);
      }
      if (this.controlProvider) {
        return toObservable(this.controlProvider.control, { injector: this.injector }).pipe(
          filter((control): control is FormControl => !!control),
          timeout({
            first: 2_000,
            with: () => throwError(() => new Error(`${RlaHighlightDirective.name}: CONTROL_PROVIDER was provided but did not emit a value`)),
          }),
        );
      }
      throw new Error(`${RlaHighlightDirective.name}: Could not find NgControl or CONTROL_PROVIDER`);
    }),
  );

  positions: ConnectedPosition[] = [
    {
      originX: 'start',
      originY: 'bottom',
      overlayX: 'start',
      overlayY: 'top',
    },
    {
      originX: 'end',
      originY: 'bottom',
      overlayX: 'end',
      overlayY: 'top',
    },
    {
      originX: 'start',
      originY: 'top',
      overlayX: 'start',
      overlayY: 'bottom',
    },
    {
      originX: 'end',
      originY: 'top',
      overlayX: 'end',
      overlayY: 'bottom',
    },
  ];

  isOpenSubject = new BehaviorSubject<boolean>(false);
  isOpen = toSignal(this.isOpenSubject.pipe(
    // Add a delay to prevent overlay getting hidden immediately when hovering
    // from the origin to the overlay.
    debounceTime(1),
  ), { initialValue: false });

  isOnLoadSuggestionPhase = this.directive
    ? toSignal(this.directive.isOnLoadSuggestionPhase$, { initialValue: true })
    : signal(false);

  suggestion$ = this.resolvedControl$.pipe(
    switchMap(control => this.suggestionsService.suggestionsForControl$(control)),
    map(suggestions => suggestions.at(0)),
    takeUntilDestroyed(),
    shareReplay(1),
  );
  suggestion = toSignal(this.suggestion$);

  suggestionContext$ = combineLatest({
    callInfo: this.rlaDataService.callInfo$,
    suggestion: this.suggestion$,
  }).pipe(
    map(({ callInfo, suggestion }) => {
      if (!suggestion) {
        return null;
      }

      const transcription = environment.rla?.mockCombinedTranscript ?? callInfo.transcription;
      const parser = new DOMParser();
      const doc = parser.parseFromString(transcription, 'text/html');

      const errorNode = doc.querySelector('parsererror');
      if (errorNode) {
        console.error('There was an error parsing the transcription HTML', errorNode);
        return null;
      }

      const children = Array.from(doc.querySelectorAll('body > *'))
        .filter(child => child.tagName !== 'H2');

      const valueToFind = suggestion.formattedValue ?? String(suggestion.value);

      const foundChildIndex = children.findIndex(child => {
        return child.innerHTML.toLowerCase().includes(valueToFind.toLowerCase());
      });

      if (foundChildIndex === -1) {
        return null;
      }

      const foundChild = children[foundChildIndex];
      foundChild.innerHTML = foundChild.innerHTML.replace(
        new RegExp(`${escapeRegex(valueToFind)}`, 'i'),
        `<span class="bg-purple-100 dark:bg-purple-900">${valueToFind}</span>`
      );

      return children.map(child => child.outerHTML).join('');
    }),
  );
  suggestionContext = toSignal(this.suggestionContext$);

  constructor() {
    this.isOpenSubject
      .pipe(
        distinctUntilChanged(),
        filter(isOpen => isOpen),
        takeUntilDestroyed()
      )
      .subscribe(() => {
        setTimeout(() => {
          const el = document.querySelector('.transcript-overlay__text span');
          el?.scrollIntoView({ behavior: 'instant', block: 'center' });
        }, 50);
      });

    this.suggestion$
      .pipe(
        takeUntilDestroyed(),
      )
      .subscribe(suggestion => {
        if (!suggestion) {
          this.isOpenSubject.next(false);
        }
      });
  }

  setShouldPreviewSuggestion(shouldPreview: boolean) {
    this.directive?.manualShouldShowSuggestionIfExists$.next(shouldPreview);
  }

  applySuggestion() {
    this.directive?.applySuggestionIfExistsAction$.next({ applyType: 'manual' });
  }
}
