@use '@rocketcentral/rocket-design-system-styles/web/scss/_color.scss' as *;

.transcript-overlay {
  --transcription-overlay-bg-color: 255, 255, 255;
  background: rgb(var(--transcription-overlay-bg-color));

  .transcript-overlay__text {
    & ::ng-deep p:last-child {
      margin: 0;
    }
  }
}

mat-icon[svgIcon="rl-assistant-sparkle"] {
  color: $rkt-purple-bright-500;
}

button.matches-suggestion mat-icon[svgIcon="rl-assistant-sparkle"] {
  opacity: 0.5;
  filter: grayscale(1);
}

::ng-deep .rkt-DarkMode .transcript-overlay {
  --transcription-overlay-bg-color: 9, 9, 9;
  color: white;
}

:host-context(.rkt-DarkMode) {
  mat-icon[svgIcon="rl-assistant-sparkle"] {
    // Override overly-specific RDS rule
    color: $rkt-purple-bright-50 !important;
  }
}
