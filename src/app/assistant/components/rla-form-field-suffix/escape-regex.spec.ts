import {escapeRegex} from './escape-regex';


describe('escapeRegex', () => {
  it('should escape special characters in the string "*** East Ave"', () => {
    const input = '*** East Ave';
    const expectedOutput = '\\*\\*\\* East Ave';
    expect(escapeRegex(input)).toBe(expectedOutput);
    expect(() => new RegExp(escapeRegex(input))).not.toThrow();
  });
  it('should not escape regular characters in the string "East Ave"', () => {
    const input = 'East Ave';
    const expectedOutput = 'East Ave';
    expect(escapeRegex(input)).toBe(expectedOutput);
    expect(() => new RegExp(escapeRegex(input))).not.toThrow();
  });

  it('should escape special characters in the string "hello@world!"', () => {
    const input = 'hello@world!';
    const expectedOutput = 'hello@world!';
    expect(escapeRegex(input)).toBe(expectedOutput);
    expect(() => new RegExp(escapeRegex(input))).not.toThrow();
  });
  it('should escape special characters in the string "(hello){world}"', () => {
    const input = '(hello){world}';
    const expectedOutput = '\\(hello\\)\\{world\\}';
    expect(escapeRegex(input)).toBe(expectedOutput);
    expect(() => new RegExp(escapeRegex(input))).not.toThrow();
  });
});
