import { Component, inject } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatDivider } from '@angular/material/divider';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatOption, MatSelect } from '@angular/material/select';
import { ClientFormService } from '../../../services/entity-state/client-state/client-form.service';
import { ClientControls } from '../../../services/entity-state/client-state/form-types';
import { getClientName } from '../../../util/get-client-name';

export type ClientMatchDialogData = {
  existingClients: ClientFormService['clientsFormArray'],
};

export type ClientMatchDialogResult = {
  resultType: 'new';
} | {
  resultType: 'existing';
  existingClientInForm: FormGroup<ClientControls>,
};

@Component({
  selector: 'app-client-match-dialog',
  standalone: true,
  imports: [
    MatDialogModule,
    ReactiveFormsModule,
    MatButton,
    MatDivider,
    MatFormField,
    MatSelect,
    MatOption,
    MatLabel,
  ],
  templateUrl: './client-match-dialog.component.html',
})
export class ClientMatchDialogComponent {
  private data: ClientMatchDialogData = inject(MAT_DIALOG_DATA);
  dialogRef: MatDialogRef<ClientMatchDialogComponent, ClientMatchDialogResult> = inject(MatDialogRef);
  private fb = inject(FormBuilder);

  existingClientCtrl = this.fb.control<FormGroup<ClientControls> | null>(null);

  existingClientOptions = this.data.existingClients.controls.map(c => {
    return {
      display: getClientName(c.value),
      value: c,
    };
  });
}
