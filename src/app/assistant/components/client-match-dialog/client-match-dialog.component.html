<h1 mat-dialog-title>Match Client</h1>

<mat-dialog-content>

  <h2>Associate with existing client</h2>
  <mat-form-field style="display:block" class="rkt-FormField"   color="accent">
    <mat-label>Existing Client</mat-label>

    <mat-select class="rkt-Input" [formControl]="existingClientCtrl">
      <mat-option [value]="null">None</mat-option>
      @for (option of existingClientOptions; track $index) {
        <mat-option [value]="option.value">{{ option.display }}</mat-option>
      }
    </mat-select>
  </mat-form-field>
  <button
    mat-flat-button
    class="rkt-Button"
    [disabled]="!existingClientCtrl.value"
    
    (click)="dialogRef.close({ resultType: 'existing', existingClientInForm: existingClientCtrl.value! })"
  >Associate</button>

  <mat-divider style="margin: 1rem 0" />

  <h2>Create a new client</h2>
  <button
    mat-flat-button
    class="rkt-Button"
    
    (click)="dialogRef.close({ resultType: 'new' })"
  >Create</button>

</mat-dialog-content>

<mat-dialog-actions align="end">
  <button
    mat-flat-button
    class="rkt-Button rkt-Button--secondary"
    (click)="dialogRef.close()"
  >Cancel</button>
</mat-dialog-actions>
