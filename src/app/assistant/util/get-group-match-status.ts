import { ExtractedGroupMatchStatus } from '../models/extracted-group-match-status';
import { ExtractedValue } from '../models/extracted-value';

export function getGroupMatchStatus(values: ExtractedValue[]): ExtractedGroupMatchStatus {
  if (values.every(v => !v.control)) {
    return ExtractedGroupMatchStatus.Unmatched;
  }
  if (values.some((v) => !v?.matchesControl)) {
    return ExtractedGroupMatchStatus.PartialMatched;
  }
  if (values.every((v) => v.control?.value !== undefined && v.control?.value !== null)) {
    return ExtractedGroupMatchStatus.Matched;
  }
  return ExtractedGroupMatchStatus.Unmatched;
}
