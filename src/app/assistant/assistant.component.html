<div class="flex flex-col p-4 pt-0 h-full box-border">

  <div class="h-full flex flex-col gap-8">
    @if (loadableOverallSummaryResult().data; as data) {
      <div class="overflow-auto flex flex-col flex-shrink-0">
        <div class="py-3 flex justify-between items-center">
          <div class="rkt-Tracked-12 py-1">OVERALL SUMMARY</div>
          @if (loadableOverallSummaryResult().loading) {
            <mat-spinner
              class="rkt-ProgressSpinner rkt-ProgressSpinner--enterprise"
              diameter="16"
            />
          }
          <div>
            <button type="button" mat-icon-button class="!overflow-hidden" (click)="copyToClipboard(data)">
              <mat-icon>content_copy</mat-icon>
            </button>
          </div>
        </div>
        <div class="sos-content formatted-inner-html rkt-Body-14" [innerHTML]="data"></div>
      </div>
    } @else if (loadableOverallSummaryResult().loading) {
      <div>
        <span rktSkeleton variant="text" class="mb-2"></span>
        <span rktSkeleton variant="rectangular" class="!w-full !h-48 mb-8"></span>
      </div>
    }

    @if (loadableCallInfo().data; as data) {
      <div class="flex flex-col flex-shrink-0">
        <div class="py-3 flex justify-between items-center">
          <div class="rkt-Tracked-12 py-1">LIVE CALL SUMMARIES</div>
          @if (loadableCallInfo().loading) {
            <mat-spinner
              class="rkt-ProgressSpinner rkt-ProgressSpinner--enterprise"
              diameter="16"
            />
          }
        </div>
        <mat-accordion displayMode="flat">
          @for (call of data.calls; track call; let i = $index) {
            <div class="my-3" [class.active-call]="call.callId === inProgressCallId()">
              <mat-expansion-panel
                class="rkt-AccordionPanel"
                [expanded]="call.callId === expandedSummaryId()"
                (opened)="manualExpandedSummaryId$.next(call.callId)"
                (closed)="expandedSummaryId() === call.callId ? manualExpandedSummaryId$.next(undefined) : null"
              >
                <mat-expansion-panel-header class="!p-4">
                  <mat-panel-title class="text-sm">
                    Call on {{ call.startTime | date:'short' }}
                  </mat-panel-title>
                </mat-expansion-panel-header>

                <div class="formatted-inner-html rkt-Body-14" [innerHTML]="call.summary"></div>
              </mat-expansion-panel>
            </div>
          }
        </mat-accordion>
      </div>
    } @else if (loadableCallInfo().loading) {
      <div>
        <span rktSkeleton variant="text" class="mb-2"></span>
        <span rktSkeleton variant="rectangular" class="!w-full !h-48 mb-8"></span>
      </div>
    } @else {
      <div class="rkt-Caption-12 text-center">No call summary available.</div>
    }

    @if (loadableResult().data && groups().length > 0) {
      <div class="data">
        <div class="py-3 flex justify-between items-center">
          <div class="rkt-Tracked-12 py-1">EXTRACTED DATA</div>
          @if (loadableResult().loading) {
            <mat-spinner
              class="rkt-ProgressSpinner rkt-ProgressSpinner--enterprise"
              diameter="16"
            />
          }
        </div>

        <app-rla-data-group />
      </div>

      <div class="flex flex-col gap-4 mt-auto">
        <div class="flex gap-2 items-center">
          <mat-icon class="rkt-Icon" svgIcon="rl-assistant-sparkle"></mat-icon>
          <div class="rkt-Caption-12">AI responses can be wrong. Verify all data for accuracy.</div>
        </div>

        <div class="mb-2 feedback flex gap-2 items-center p-4 rounded-lg shadow-md">
          <div class="flex gap-1 items-center">
            <button
              [class.pointer-events-none]="feedbackIsHappy()"
              [class.mat-icon-button-purple]="feedbackIsHappy()"
              mat-icon-button
              (click)="feedbackUpdate$.next(true)"
              [disabled]="feedbackUpdateResponse()?.loading"
            >
              <mat-icon class="rkt-Icon" svgIcon="thumb_up_off_alt{{ feedbackIsHappy() ? '-two_tone' : '-outlined' }}"></mat-icon>
            </button>
            <button
              [class.pointer-events-none]="feedbackIsHappy() === false"
              [class.mat-icon-button-purple]="feedbackIsHappy() === false"
              mat-icon-button
              (click)="feedbackUpdate$.next(false)"
              [disabled]="feedbackUpdateResponse()?.loading"
            >
              <mat-icon class="rkt-Icon" svgIcon="thumb_down{{ feedbackIsHappy() === false ? '-two_tone' : '-outlined' }}"></mat-icon>
            </button>
          </div>
          <div class="rkt-Caption-12">Help us improve! Your feedback on this experience is essential in refining this new technology.</div>
        </div>
      </div>
    } @else if (!loadableResult().data && loadableResult().loading) {
      <div>
        <span rktSkeleton variant="text" class="mb-2"></span>
        <span rktSkeleton variant="rectangular" class="!w-full !h-16 mb-4"></span>
        <span rktSkeleton variant="rectangular" class="!w-full !h-16"></span>
      </div>
    }
  </div>


</div>
