import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { catchError, EMPTY, filter, map, merge, Observable, of, shareReplay, Subject, switchMap, take, tap } from 'rxjs';
import { environment } from '../../../environments/environment';
import { LoanIdService } from '../../services/loan-id/loan-id.service';
import { RlaFeedback } from '../models/feedback';

@Injectable()
export class RlaFeedbackService {
  private readonly BASE_URL = environment.assistantUrl;
  private loanIdService = inject(LoanIdService);
  private httpClient = inject(HttpClient);

  private manualFeedbackUpdate$ = new Subject<boolean | undefined>();

  feedbackIsHappy$: Observable<boolean | undefined> = merge(
    this.loanIdService.loanId$.pipe(
      filter((loanId): loanId is string => !!loanId),
      switchMap((loanId) => this.getResultFeedback$(loanId)),
      map((res) => res?.isHappy ?? undefined),
    ),
    this.manualFeedbackUpdate$,
  ).pipe(takeUntilDestroyed(), shareReplay(1));


  putFeedback$(isHappy: boolean) {
    return this.loanIdService.loanId$.pipe(
      take(1),
      switchMap((loanId) => {
        if (!loanId) return EMPTY;
        return this.httpClient.put<void>(`${this.BASE_URL}/assistant/results/${loanId}/feedback`, {
          isHappy,
        });
      }),
      tap(() => this.manualFeedbackUpdate$.next(isHappy)),
    );
  }

  private getResultFeedback$(loanId: string): Observable<RlaFeedback | null> {
    return this.httpClient
      .get<RlaFeedback>(`${this.BASE_URL}/assistant/results/${loanId}/feedback`)
      .pipe(
        catchError((error) => {
          if (!(error instanceof HttpErrorResponse) || ![404].includes(error.status)) {
            console.error(error);
          }
          return of(null);
        }),
      );
  }
}
