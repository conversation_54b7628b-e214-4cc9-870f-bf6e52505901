import { inject, Injectable, Injector } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { OccupancyType, State } from '@rocket-logic/rl-xp-bff-models';
import { map, merge, Observable, of, shareReplay, switchMap } from 'rxjs';
import { getPlanForPropertyLabel } from '../../clients-info/current-address/get-plan-for-property-label';
import { getResidencyOptionLabel } from '../../clients-info/current-address/get-residency-option-label';
import {
  getMilitaryBranchLabel,
  getMilitaryComponentLabel,
  getMilitaryStatusLabel,
} from '../../clients-info/va-eligibility/va-eligibility.component';
import { ClientFormService } from '../../services/entity-state/client-state/client-form.service';
import { ClientControls } from '../../services/entity-state/client-state/form-types';
import { AllIncomeGroup } from '../../services/entity-state/income-state/form-types';
import { IncomeFormService } from '../../services/entity-state/income-state/income-form.service';
import { mapIncomeTypeToString } from '../../services/entity-state/income-state/supported-income';
import { pascalCaseSplit, pascalCaseToSentence } from '../../util/formatting-helpers';
import { normalizedCompare } from '../../util/normalized-compare';
import { RecursivePartial } from '../../util/recursive-partial';
import {
  ExtractedGroup,
  ExtractedGroupType,
  IncomeExtractedGroup,
} from '../models/extracted-group';
import { ExtractedGroupMatchStatus } from '../models/extracted-group-match-status';
import { ExtractedValue } from '../models/extracted-value';
import {
  TranscriptionExtractedClient,
  TranscriptionExtractedIncome,
} from '../models/transcription-extracted-info';
import { getGroupMatchStatus } from '../util/get-group-match-status';
import { RlaDataService } from './rla-data.service';
import { RlaUtilService } from './rla-util.service';

@Injectable()
export class RlaClientService {
  private rlaDataService = inject(RlaDataService);
  private injector = inject(Injector);
  private clientFormService = inject(ClientFormService);
  private incomeFormService = inject(IncomeFormService);
  private splunkLoggerService = inject(SplunkLoggerService);
  private rlaUtil = inject(RlaUtilService);

  clientAndIncomeGroups$: Observable<ExtractedGroup[]> = this.rlaDataService.dataResult$.pipe(
    switchMap((result) => {
      const extractedClients = result?.clients;
      if (!extractedClients) return of([]);

      // Re-calculate groups when relevant forms change.
      return merge(
        of(null), // run on init
        toObservable(this.clientFormService.entityFormMap, { injector: this.injector }).pipe(
          switchMap((clientMap) => {
            const controls = Array.from(clientMap.values());
            const valueChanges = controls.map((control) => control.valueChanges);
            return merge(...valueChanges);
          }),
        ),
        toObservable(this.incomeFormService.clientIncomeMap, { injector: this.injector }).pipe(
          switchMap((clientToIncomeMap) => {
            const controls = Object.values(clientToIncomeMap).flatMap((incomeMap) =>
              Array.from(incomeMap.values()),
            );
            const valueChanges = controls.map((control) => control.valueChanges);
            return merge(...valueChanges);
          }),
        ),
      ).pipe(
        map(() => {
          const clientsInForm = this.clientFormService.entityValues();

          const groups: ExtractedGroup[] = extractedClients
            .map((extractedClient): ExtractedGroup[] => {
              const clientInForm = clientsInForm.find((clientInForm) => {
                // Find client in form based on heuristics.
                if (
                  !!extractedClient.isPrimary &&
                  clientInForm?.controls.isPrimaryBorrower.value === extractedClient.isPrimary
                ) {
                  return true;
                }
                if (
                  !!extractedClient.firstName &&
                  // @TODO Do fuzzy matching
                  normalizedCompare(
                    clientInForm?.controls.personalInformation.controls.firstName.value,
                    extractedClient.firstName,
                  )
                ) {
                  return true;
                }
                return false;
              });

              try {
                const clientGroups = this.getGroupsForClient(extractedClient, clientInForm);
                return [
                  ...clientGroups,
                  ...(clientInForm
                    ? this.getIncomeGroupsForClient(extractedClient, clientInForm)
                    : []),
                ];
              } catch (error) {
                console.error(`Error getting groups in ${RlaClientService.name}`, error, extractedClient);
                this.splunkLoggerService.error(
                  `RLA: Error getting groups in ${RlaClientService.name}`,
                  error as Error,
                  { extractedClient, messsage: (error as Error).message }
                );
                return [];
              }
            })
            .flat();

          return groups;
        }),
      );
    }),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  private readonly stateEntries = Object.entries(State);
  private readonly occupancyTypeEntries = Object.entries(OccupancyType);

  private getGroupsForClient(
    extractedClient: RecursivePartial<TranscriptionExtractedClient>,
    clientInForm?: FormGroup<ClientControls>,
  ): ExtractedGroup[] {
    const isPrimaryLabel = extractedClient.isPrimary ? 'Primary Client' : 'Co-Client';

    const groupedValues: Array<{ label: string; values: ExtractedValue[] }> = [
      {
        label: `${isPrimaryLabel} - Personal Info`,
        values: [
          this.rlaUtil.toExtractedValue(
            'First Name',
            clientInForm?.controls.personalInformation.controls.firstName,
            extractedClient.firstName,
          ),
          this.rlaUtil.toExtractedValue(
            'Middle Name',
            clientInForm?.controls.personalInformation.controls.middleName,
            extractedClient.middleName,
          ),
          this.rlaUtil.toExtractedValue(
            'Last Name',
            clientInForm?.controls.personalInformation.controls.lastName,
            extractedClient.lastName,
          ),
          this.rlaUtil.toExtractedValue(
            'Suffix',
            clientInForm?.controls.personalInformation.controls.suffix,
            extractedClient.suffix,
          ),
          this.rlaUtil.toExtractedValue(
            'Preferred Name',
            clientInForm?.controls.personalInformation.controls.preferredName,
            extractedClient.preferredName,
          ),
        ].filter((v): v is ExtractedValue => v !== null),
      },
      {
        label: `${isPrimaryLabel} - Contact Info`,
        values: [
          this.rlaUtil.toExtractedValue(
            'Phone Number',
            // Use the first phone number in the form.
            clientInForm?.controls.contactInformation.controls.phoneNumbers.at(0)?.controls.number,
            extractedClient.phone?.phoneNumber?.toString(),
          ),
          this.rlaUtil.toExtractedValue(
            'Phone Number Type',
            // Use the first phone number in the form.
            clientInForm?.controls.contactInformation.controls.phoneNumbers.at(0)?.controls.type,
            extractedClient.phone?.phoneType,
          ),
          this.rlaUtil.toExtractedValue(
            'Email',
            clientInForm?.controls.contactInformation.controls.emailAddress,
            extractedClient.email,
          ),
        ].filter((v): v is ExtractedValue => v !== null),
      },
      {
        label: `${isPrimaryLabel} - Marital`,
        values: [
          this.rlaUtil.toExtractedValue(
            'Marital Status',
            clientInForm?.controls.personalInformation.controls.maritalStatus,
            extractedClient.maritalStatus,
          ),
        ].filter((v): v is ExtractedValue => v !== null),
      },
      {
        label: `${isPrimaryLabel} - Address`,
        values: [
          this.rlaUtil.toExtractedValue(
            'Street',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls.address
              .controls.addressLine1,
            extractedClient.currentAddress?.street,
          ),
          this.rlaUtil.toExtractedValue(
            'Unit',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls.address
              .controls.addressLine2,
            extractedClient.currentAddress?.unit,
          ),
          this.rlaUtil.toExtractedValue(
            'State',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls.address
              .controls.state,
            extractedClient.currentAddress?.state,
            () => {
              const state = extractedClient.currentAddress?.state;
              if (!state) return undefined;
              const stateKey = this.stateEntries.find(([_, value]) => value === state)?.at(0);
              if (!stateKey) return undefined;
              return pascalCaseSplit(stateKey);
            },
          ),
          this.rlaUtil.toExtractedValue(
            'City',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls.address
              .controls.city,
            extractedClient.currentAddress?.city,
          ),
          this.rlaUtil.toExtractedValue(
            'Zip Code',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls.address
              .controls.zipCode,
            extractedClient.currentAddress?.zipCode?.toString(),
          ),
          this.rlaUtil.toExtractedValue(
            'Years Lived At',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls
              .durationAtResidence?.controls.years,
            extractedClient.currentAddress?.yearsLivedAt,
          ),
          this.rlaUtil.toExtractedValue(
            'Months Lived At',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls
              .durationAtResidence?.controls.months,
            extractedClient.currentAddress?.monthsLivedAt,
          ),
          this.rlaUtil.toExtractedValue(
            'Taxes Filed At This Address',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls
              .taxesFiledAtThisAddress,
            extractedClient.currentAddress?.taxesFiledAtThisAddress,
          ),
          this.rlaUtil.toExtractedValue(
            'Ownership Status',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls
              .residencyType,
            extractedClient.currentAddress?.ownershipStatus,
            () => extractedClient.currentAddress?.ownershipStatus
              ? getResidencyOptionLabel(extractedClient.currentAddress.ownershipStatus)
              : undefined,
          ),
          this.rlaUtil.toExtractedValue(
            'Plan for property',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls
              .planForProperty,
            extractedClient.currentAddress?.planForProperty,
            () => extractedClient.currentAddress?.planForProperty
              ? getPlanForPropertyLabel(extractedClient.currentAddress.planForProperty)
              : undefined,
          ),
          this.rlaUtil.toExtractedValue(
            'Selling Price',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls
              .sellingPrice,
            extractedClient.currentAddress?.sellingPrice,
          ),
          this.rlaUtil.toExtractedValue(
            'Occupancy Type',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls
              .occupancyType,
            extractedClient.currentAddress?.occupancyType,
            () => {
              const occupancyType = extractedClient.currentAddress?.occupancyType;
              if (!occupancyType) return undefined;
              const occupancyTypeKey = this.occupancyTypeEntries
                .find(([_, value]) => value === occupancyType)
                ?.at(0);
              if (!occupancyTypeKey) return undefined;
              return pascalCaseSplit(occupancyTypeKey);
            },
          ),
          this.rlaUtil.toExtractedValue(
            'Monthly Rent',
            clientInForm?.controls.residenceInformation.controls.currentResidence.controls
              .monthlyRent,
            extractedClient.currentAddress?.monthlyRent,
          ),
        ].filter((v): v is ExtractedValue => v !== null),
      },
      {
        label: `${isPrimaryLabel} - Military`,
        values: [
          this.rlaUtil.toExtractedValue(
            'Has Military Service',
            clientInForm?.controls.military.controls.hasMilitaryService,
            extractedClient.servedInArmedForcesOrEligibleForVaHomeLoan,
          ),
          this.rlaUtil.toExtractedValue(
            'Branch of Service',
            clientInForm?.controls.military.controls.militaryServices.controls.at(0)?.controls
              .branch,
            extractedClient.militaryInfo?.branchOfService,
            () => extractedClient.militaryInfo?.branchOfService
              ? getMilitaryBranchLabel(extractedClient.militaryInfo.branchOfService)
              : undefined,
          ),
          this.rlaUtil.toExtractedValue(
            'Military Service Type',
            clientInForm?.controls.military.controls.militaryServices.controls.at(0)?.controls
              .component,
            extractedClient.militaryInfo?.serviceType,
            () => extractedClient.militaryInfo?.serviceType
              ? getMilitaryComponentLabel(extractedClient.militaryInfo.serviceType)
              : undefined,
          ),
          this.rlaUtil.toExtractedValue(
            'Military Status',
            clientInForm?.controls.military.controls.militaryServices.controls.at(0)?.controls
              .status,
            extractedClient.militaryInfo?.militaryStatus,
            () => extractedClient.militaryInfo?.militaryStatus
              ? getMilitaryStatusLabel(extractedClient.militaryInfo.militaryStatus)
              : undefined,
          ),
          this.rlaUtil.toExtractedValue(
            'Disability Benefits',
            clientInForm?.controls.military.controls.militaryServices.controls.at(0)?.controls
              .vaDisabilityBenefitsStatus,
            extractedClient.militaryInfo?.disabilityBenefits,
            () => extractedClient.militaryInfo?.disabilityBenefits
              ? pascalCaseToSentence(extractedClient.militaryInfo.disabilityBenefits)
              : undefined,
          ),
          this.rlaUtil.toExtractedValue(
            'Discharged Under Conditions Other Than Desirable',
            clientInForm?.controls.military.controls.militaryServices.controls.at(0)?.controls
              .wasDischargedUnderConditionsOtherThanDishonorable,
            extractedClient.militaryInfo?.dischargedUnderConditionsOtherThanDishonorable,
          ),
          this.rlaUtil.toExtractedValue(
            'Had Prior VA Loan',
            clientInForm?.controls.military.controls.militaryServices.controls.at(0)?.controls
              .hasPriorVAHomeLoan,
            extractedClient.militaryInfo?.hadPriorVALoan,
          ),
          this.rlaUtil.toExtractedValue(
            'Surviving Spouse',
            clientInForm?.controls.military.controls.militaryServices.controls.at(0)?.controls
              .isSurvivingSpouseOfVeteran,
            extractedClient.militaryInfo?.survivingSpouse,
          ),
        ].filter((v): v is ExtractedValue => v !== null),
      },
    ].filter((g) => g.values.length > 0);

    const extractedGroups: ExtractedGroup[] = groupedValues.map(({ label, values }) => {
      const status = getGroupMatchStatus(values);
      return {
        type: ExtractedGroupType.Client,
        context: { extractedClient },
        label,
        values,
        status: getGroupMatchStatus(values),
        tooltip:
          status === ExtractedGroupMatchStatus.Unmatched
            ? 'Unable to associate this data to a client on the form. Try filling out some fields, and the Assistant will associate automatically.'
            : undefined,
      };
    });

    return extractedGroups;
  }

  getControlsForIncome(
    extractedIncome: RecursivePartial<TranscriptionExtractedIncome>,
    incomeInForm?: AllIncomeGroup,
  ): ExtractedValue[] {
    return [
      this.rlaUtil.toExtractedValue(
        'Type',
        incomeInForm?.controls.incomeType,
        extractedIncome.type,
        () => extractedIncome.type ? mapIncomeTypeToString(extractedIncome.type) : undefined,
      ),
      this.rlaUtil.toExtractedValue(
        'Amount',
        incomeInForm?.controls.base.controls.clientReportedAmount,
        extractedIncome.amount,
      ),
      this.rlaUtil.toExtractedValue(
        'Frequency',
        incomeInForm?.controls.base.controls.frequency,
        extractedIncome.frequency,
        () => extractedIncome.frequency ? pascalCaseSplit(extractedIncome.frequency) : undefined,
      ),
      this.rlaUtil.toExtractedValue(
        'Employer',
        incomeInForm?.controls.employment.controls.employerName,
        extractedIncome.employerName,
      ),
      this.rlaUtil.toExtractedValue(
        'Job Title',
        incomeInForm?.controls.employment.controls.jobTitle,
        extractedIncome.jobTitle,
      ),
      this.rlaUtil.toExtractedValue(
        'Start Date',
        incomeInForm?.controls.employment.controls.employmentStartDate,
        extractedIncome.startDate,
        () => extractedIncome.startDate
          ? new Date(extractedIncome.startDate).toLocaleString()
          : undefined,
        (a, b) => !!a && !!b && new Date(a).getTime() === new Date(b).getTime(),
      ),
      this.rlaUtil.toExtractedValue(
        'End Date',
        incomeInForm?.controls.employment.controls.employmentEndDate,
        extractedIncome.endDate,
        () => extractedIncome.endDate ? new Date(extractedIncome.endDate).toLocaleString() : undefined,
        (a, b) => !!a && !!b && new Date(a).getTime() === new Date(b).getTime(),
      ),
    ].filter((v): v is ExtractedValue => v !== null);
  }

  private getIncomeGroupsForClient(
    extractedClient: RecursivePartial<TranscriptionExtractedClient>,
    clientInForm: FormGroup<ClientControls>,
  ): ExtractedGroup[] {
    const clientId = clientInForm.controls.id.value;
    if (!clientId) return [];

    const incomeMapForClient: Map<string, AllIncomeGroup> | undefined =
      this.incomeFormService.clientIncomeMap()[clientId];
    const incomesInForm = incomeMapForClient ? Array.from(incomeMapForClient.values()) : [];
    const incomeGroups = (extractedClient.income ?? [])
      .map((extractedIncome) => {
        const incomeInForm = incomesInForm.find((incomeInForm) => {
          // Find income in form based on heuristics.

          if (
            extractedIncome.amount !== undefined &&
            incomeInForm?.controls.base.controls.clientReportedAmount.value ===
              extractedIncome.amount
          ) {
            return true;
          }
          if (
            !!extractedIncome.employerName &&
            // @TODO Do fuzzy matching
            normalizedCompare(
              incomeInForm?.controls.employment.controls.employerName.value,
              extractedIncome.employerName,
            )
          ) {
            return true;
          }
          if (
            !!extractedIncome.jobTitle &&
            // @TODO Do fuzzy matching
            normalizedCompare(
              incomeInForm?.controls.employment.controls.jobTitle.value,
              extractedIncome.jobTitle,
            )
          ) {
            return true;
          }
          if (
            !!extractedIncome.type &&
            // @TODO Do fuzzy matching
            normalizedCompare(incomeInForm?.controls.incomeType.value, extractedIncome.type)
          ) {
            return true;
          }
          return false;
        });

        const values = this.getControlsForIncome(extractedIncome, incomeInForm);

        if (values.length === 0) {
          return null;
        }

        return {
          type: ExtractedGroupType.Income,
          context: { clientId, extractedIncome },
          label: `Income for ${clientInForm.value.isPrimaryBorrower ? 'Primary ' : 'Co-'}Client`,
          status: incomeInForm
            ? getGroupMatchStatus(values)
            : ExtractedGroupMatchStatus.PartialMatched,
          values,
        } satisfies IncomeExtractedGroup;
      })
      .filter((x): x is IncomeExtractedGroup => !!x);
    return incomeGroups;
  }
}
