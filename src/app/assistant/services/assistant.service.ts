import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { DestroyRef, inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import {
  catchError,
  combineLatest,
  EMPTY,
  firstValueFrom,
  map,
  Observable,
  shareReplay,
  startWith,
  switchMap,
  take
} from 'rxjs';
import { environment } from '../../../environments/environment';
import { ActiveSidenavScreenService, SidenavScreen } from '../../services/active-sidenav-screen/active-sidenav-screen.service';
import { AssetFormService } from '../../services/entity-state/asset-state/asset-form.service';
import { ClientFormService } from '../../services/entity-state/client-state/client-form.service';
import { IncomeFormService } from '../../services/entity-state/income-state/income-form.service';
import { LoanIdService } from '../../services/loan-id/loan-id.service';
import { Suggestion, SuggestionGroup, SuggestionsService } from '../../suggestions/suggestions.service';
import { ExtractedGroup, ExtractedGroupType } from '../models/extracted-group';
import { ExtractedGroupMatchStatus } from '../models/extracted-group-match-status';
import { RlaAssetService } from './rla-asset.service';
import { RlaClientService } from './rla-client.service';
import { RlaLoanService } from './rla-loan.service';

@Injectable()
export class AssistantService {
  private httpClient = inject(HttpClient);
  private readonly BASE_URL = environment.assistantUrl;
  private loanIdService = inject(LoanIdService);
  private incomeFormService = inject(IncomeFormService);
  private assetFormService = inject(AssetFormService);
  private clientFormService = inject(ClientFormService);
  private rlaClientService = inject(RlaClientService);
  private rlaLoanService = inject(RlaLoanService);
  private rlaAssetService = inject(RlaAssetService);
  private suggestionsService = inject(SuggestionsService);
  private destroyRef = inject(DestroyRef);
  private activeSidenavScreenService = inject(ActiveSidenavScreenService);

  groups$: Observable<ExtractedGroup[]> = combineLatest({
    clientGroups: this.rlaClientService.clientAndIncomeGroups$.pipe(startWith([])),
    loanGroups: this.rlaLoanService.loanGroups$.pipe(startWith([])),
    assetGroups: this.rlaAssetService.assetGroups$.pipe(startWith([])),
  }).pipe(
    map(({ clientGroups, loanGroups, assetGroups }) => {
      return [...clientGroups, ...loanGroups, ...assetGroups];
    }),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  private rlaSuggestions$: Observable<Array<{ control: FormControl, suggestions: Suggestion[] }>> = this.groups$.pipe(
    map(groups => {
      const extractedValues = groups.flatMap(group => group.values);

      const suggestions: Array<{ control: FormControl, suggestions: Suggestion[] }> = extractedValues
        .map(extractedValue => {
          if (!extractedValue.control) {
            return null;
          }
          const suggestions: Suggestion[] = [{
            value: extractedValue.value,
            formattedValue: extractedValue.formattedValue,
            group: SuggestionGroup.Rla,
            isEqualToControl: extractedValue.matchesControl,
          }];
          return { control: extractedValue.control, suggestions };
        })
        .filter((s): s is { control: FormControl, suggestions: Suggestion[] } => s !== null);
      return suggestions;
    }),
  );



  constructor() {
    if (environment.rla?.enableSidebarAutoOpen) {
      this.activeSidenavScreenService.activate(SidenavScreen.RocketLogicAssistant);
    }

    const cleanupSuggestionRegistration = this.suggestionsService.register(SuggestionGroup.Rla, this.rlaSuggestions$);
    this.destroyRef.onDestroy(() => cleanupSuggestionRegistration());
  }

  async handleGroupMatch(group: ExtractedGroup): Promise<void> {
    const values = (() => {
      if (group.type === ExtractedGroupType.Income) {
        const shouldCreateIncomeInForm = group.values.every((v) => !v.control);
        if (shouldCreateIncomeInForm) {
          const incomeInForm = this.incomeFormService.addIncome(group.context.clientId);
          const values = this.rlaClientService.getControlsForIncome(
            group.context.extractedIncome,
            incomeInForm,
          );
          return values;
        }
      }

      if (group.type === ExtractedGroupType.Asset) {
        const shouldCreateAssetInForm = group.values.every((v) => !v.control);
        if (shouldCreateAssetInForm) {
          const assetInForm = this.assetFormService.addAsset(group.context.assetType);

          //#region Populate rocketLogicClientIds if there is only 1 client.
          const clientsInForm = this.clientFormService.entityValues();
          const firstClient = clientsInForm.at(0);
          const clientIdsCtrl = assetInForm.get('rocketLogicClientIds') as FormControl | undefined;
          if (firstClient && clientsInForm.length === 1 && clientIdsCtrl) {
            const clientId = firstClient.controls.id.value;
            if (clientId) {
              clientIdsCtrl.setValue([clientId]);
            }
          }
          //#endregion

          const values = this.rlaAssetService.getControlsForAsset(
            group.context.extractedAsset,
            assetInForm,
          );
          return values;
        }
      }

      const values = group.status === ExtractedGroupMatchStatus.PartialMatched ? group.values : [];
      return values;
    })();

    values.forEach(({ control, value }) => {
      control?.markAsDirty();
      control?.setValue(value);
    });

    if (values.length > 0) {
      await Promise.allSettled([
        firstValueFrom(this.recordUsage$()),
        firstValueFrom(this.recordMetrics$({ fieldsAppliedCount: values.length })),
      ]);
    }
  }

  recordUsage$(): Observable<void> {
    return this.loanIdService.loanId$.pipe(
      take(1),
      switchMap((loanId) => {
        return this.httpClient
          .put<void>(`${this.BASE_URL}/assistant/results/${loanId}/usage`, null)
          .pipe(
            catchError((error) => {
              if (!(error instanceof HttpErrorResponse) || ![404].includes(error.status)) {
                console.error(error);
              }
              return EMPTY;
            }),
          );
      })
    );
  }

  recordMetrics$(metrics: Record<string, number>): Observable<void> {
    return this.loanIdService.loanId$.pipe(
      take(1),
      switchMap((loanId) => {
        return this.httpClient
          .put<void>(`${this.BASE_URL}/assistant/results/${loanId}/metrics`, metrics)
          .pipe(
            catchError((error) => {
              if (!(error instanceof HttpErrorResponse) || ![404].includes(error.status)) {
                console.error(error);
              }
              return EMPTY;
            }),
          );
      })
    );
  }
}
