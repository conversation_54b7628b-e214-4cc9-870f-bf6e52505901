import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { DestroyRef, inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import {
  catchError,
  concat,
  EMPTY,
  filter,
  map,
  merge,
  mergeMap,
  Observable,
  of,
  scan,
  shareReplay,
  startWith,
  switchMap
} from 'rxjs';
import { environment } from '../../../environments/environment';
import { AuthorizationService, Pilot } from '../../services/authorization/authorization.service';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import { LoanIdService } from '../../services/loan-id/loan-id.service';
import { LoanNotificationService, PublishedMessage } from '../../services/notification/loan-notification.service';
import { Loadable, loadable, mapLoadableData } from '../../util/loadable';
import { CallInfo } from '../models/call-info';
import { ExperimentId } from '../models/experiment-id';
import { RlaFeedbackResultItem } from '../models/rla-result-item';
import { TranscriptionExtractedInfo } from '../models/transcription-extracted-info';

@Injectable()
export class RlaDataService {
  private httpClient = inject(HttpClient);
  private readonly BASE_URL = environment.assistantUrl;
  private loanIdService = inject(LoanIdService);
  private loanNotificationService = inject(LoanNotificationService);
  private splunkLogger = inject(SplunkLoggerService);
  private authorizationService = inject(AuthorizationService);
  private loanStateService = inject(LoanStateService);
  private destroyRef = inject(DestroyRef);

  // @TODO Use this to highlight the call in UI
  inProgressCallId$ = this.loanNotificationService.notifications$.pipe(
    filter((notification): notification is PublishedMessage<{ call_id: string, chunk: number | null }> => notification?.type === 'call-summary'),
    map(({ payload: { call_id, chunk } }) => {
      const callHasEnded = chunk === null;
      return callHasEnded ? null : call_id.replace('rtl:', '');
    }),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  isLoanInAssistantPilot$ = this.loanStateService.isInitialized$.pipe(
    filter((isInitialized) => isInitialized),
    switchMap(() => this.loanIdService.nonNullableLoanId$),
    switchMap((loanId) => this.authorizationService.getLoanAccess(loanId, [Pilot.Assistant])),
    map((access) => access[Pilot.Assistant] ?? false),
    takeUntilDestroyed(this.destroyRef),
    shareReplay(1),
  );

  loadableCallInfo$: Observable<Loadable<CallInfo | null>> = this.loanIdService.nonNullableLoanId$.pipe(
    switchMap((loanId) => {
      return concat(
        this.getCallInfo$(loanId).pipe(loadable()),
        this.loanNotificationService.notifications$.pipe(
          filter(notification => notification?.type === 'call-summary'),
          switchMap(() => this.getCallInfo$(loanId).pipe(loadable())),
        ),
      );
    }),
    startWith({ loading: true }),
    scan((acc, value) => ({ ...acc, ...value })),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  callInfo$: Observable<CallInfo> = this.loadableCallInfo$.pipe(
    map((loadableCallInfo) => loadableCallInfo.data ?? undefined),
    filter((data): data is CallInfo => data !== undefined),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  loadableDataResult$: Observable<Loadable<TranscriptionExtractedInfo | null>> = this.isLoanInAssistantPilot$.pipe(
    switchMap(isInPilot => {
      if (!isInPilot) {
        return of({ loading: false, data: null });
      }
      return this.loanIdService.nonNullableLoanId$.pipe(
        switchMap((loanId) => {
          return concat(
            this.putResult$<TranscriptionExtractedInfo>(loanId).pipe(loadable()),
            merge(
              // Make requests to generate results as transcript events come in.
              this.loanNotificationService.notifications$.pipe(
                filter(notification => notification?.type === 'call-transcript'),
                mergeMap(() => this.putResult$<TranscriptionExtractedInfo>(loanId)),
                switchMap(() => EMPTY),
              ),
              // Listen for result generated events.
              this.loanNotificationService.notifications$.pipe(
                filter((notification): notification is PublishedMessage<{ result: RlaFeedbackResultItem, isOutdated: boolean }> =>
                  notification?.type === 'rla-feedback-result-generated' &&
                  notification.payload?.result?.experimentId === ExperimentId.RlXpAssistant
                ),
                switchMap((notification) => {
                  const { payload } = notification;
                  try {
                    const parsed = JSON.parse(payload.result.response) as TranscriptionExtractedInfo;
                    return of({ loading: false, data: parsed });
                  } catch (error) {
                    console.error('Failed to parse JSON', error, payload.result);
                    return EMPTY;
                  }
                }),
              ),
            ),
          );
        }),
      );
    }),
    startWith({ loading: true }),
    scan((acc, value) => ({ ...acc, ...value })),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  dataResult$ = this.loadableDataResult$.pipe(
    filter((loadableResult) => loadableResult.data !== undefined),
    map((loadableResult) => loadableResult.data as TranscriptionExtractedInfo | null),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  loadableOverallSummaryResult$: Observable<Loadable<string | null>> = this.loanIdService.nonNullableLoanId$.pipe(
    switchMap((loanId) => {
      return concat(
        this.putResult$<{ overallSummary: string }>(loanId, ExperimentId.RlXpOverallSummary).pipe(loadable()),
        this.loanNotificationService.notifications$.pipe(
          filter((notification): notification is PublishedMessage<{ result: RlaFeedbackResultItem, isOutdated: boolean }> =>
            notification?.type === 'rla-feedback-result-generated' &&
            notification.payload?.result?.experimentId === ExperimentId.RlXpOverallSummary
          ),
          switchMap((notification) => {
            const { payload } = notification;
            try {
              const parsed = JSON.parse(payload.result.response) as { overallSummary: string } | null;
              return of({ loading: false, data: parsed });
            } catch (error) {
              console.error('Failed to parse JSON', error, payload.result);
              return EMPTY;
            }
          }),
        ),
      );
    }),
    startWith({ loading: true }),
    scan((acc, value) => ({ ...acc, ...value })),
    mapLoadableData(data => data?.overallSummary ?? null),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  overallSummaryResult$ = this.loadableOverallSummaryResult$.pipe(
    filter(loadableResult => loadableResult.data !== undefined),
    map((loadableResult) => loadableResult.data as string | null),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  constructor() {
    this.loanNotificationService.notifications$.pipe(
      filter(notification => notification?.type === 'call-transcript'),
      takeUntilDestroyed(),
    ).subscribe(notification => {
      this.splunkLogger.info('RLA: call-transcript event', { notification: notification.payload });
    });
  }

  private getCallInfo$(loanId: string): Observable<CallInfo | null> {
    return this.httpClient
      .get<CallInfo | null>(`${environment.rla.rlaFeedbackUrl}/experiments/${ExperimentId.RlXpAssistant}/results/${loanId}/lexical-scope`)
      .pipe(
        map(res => {
          // from newest to oldest
          res?.calls?.sort((a, b) => {
            const aDate = new Date(a.startTime);
            const bDate = new Date(b.startTime);
            return bDate.getTime() - aDate.getTime();
          });
          return res;
        }),
        catchError((error) => {
          if (!(error instanceof HttpErrorResponse) || ![404].includes(error.status)) {
            console.error(error);
          }
          return of(null);
        }),
      );
  }

  private putResult$<T>(
    loanId: string,
    experimentId: ExperimentId = ExperimentId.RlXpAssistant,
  ): Observable<T | null> {
    if (environment.rla?.mockExtractedData && experimentId === ExperimentId.RlXpAssistant) {
      return of(environment.rla.mockExtractedData as T);
    } else if (environment.rla?.mockOverallSummary && experimentId === ExperimentId.RlXpOverallSummary) {
      return of({ overallSummary: environment.rla.mockOverallSummary } as T);
    }

    return this.httpClient
      .put<{ response: string }>(
        `${environment.rla.rlaFeedbackUrl}/experiments/${experimentId}/results/${loanId}`,
        {}
      )
      .pipe(
        map((res) => {
          return JSON.parse(res.response) as T;
        }),
        catchError((error) => {
          if (!(error instanceof HttpErrorResponse) || ![404].includes(error.status)) {
            console.error(error);
          }
          return of(null);
        }),
      );
  }
}
