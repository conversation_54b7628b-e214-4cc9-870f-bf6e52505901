import { inject, Injectable } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormControl } from '@angular/forms';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { map, merge, Observable, of, shareReplay, switchMap } from 'rxjs';
import { LoanFormService } from '../../services/entity-state/loan-state/loan-form.service';
import { ExtractedGroup, ExtractedGroupType } from '../models/extracted-group';
import { ExtractedValue } from '../models/extracted-value';
import { TranscriptionExtractedInfo } from '../models/transcription-extracted-info';
import { getGroupMatchStatus } from '../util/get-group-match-status';
import { RlaDataService } from './rla-data.service';
import { RlaUtilService } from './rla-util.service';

@Injectable()
export class RlaLoanService {
  private rlaDataService = inject(RlaDataService);
  private loanFormService = inject(LoanFormService);
  private splunkLoggerService = inject(SplunkLoggerService);
  private rlaUtil = inject(RlaUtilService);

  loanGroups$: Observable<ExtractedGroup[]> = this.rlaDataService.dataResult$.pipe(
    switchMap((result) => {
      if (!result) return of([]);

      const loanControlValueChanges = this.getGroupsForLoan(result)
        .flatMap((g) =>
          g.values.map((v) => v.control).filter((control): control is FormControl => !!control),
        )
        .map((control) => control.valueChanges);

      return merge(
        of(null), // run on init
        ...loanControlValueChanges,
      ).pipe(
        map(() => {
          try {
            return this.getGroupsForLoan(result);
          } catch (error) {
            console.error(`Error getting groups in ${RlaLoanService.name}`, error, result);
            this.splunkLoggerService.error(
              'RLA: Error getting loan groups',
              error as Error,
              {
                message: (error as Error).message,
              }
            );
            return [];
          }
        }),
      );
    }),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  private getGroupsForLoan(extractedInfo: TranscriptionExtractedInfo | null) {
    if (!extractedInfo) return [];

    const miscLoanValues = [
      this.rlaUtil.toExtractedValue(
        'Purchase Price',
        this.loanFormService.loanForm.controls.purchasePrice,
        extractedInfo.purchasePrice,
      ),
    ].filter((v): v is ExtractedValue => !!v);

    const groups: ExtractedGroup[] = [];

    if (miscLoanValues.length > 0) {
      groups.push({
        type: ExtractedGroupType.Loan,
        label: 'Loan',
        values: miscLoanValues,
        status: getGroupMatchStatus(miscLoanValues),
      });
    }

    return groups;
  }
}
