import { inject, Injectable, Injector } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup } from '@angular/forms';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { map, merge, Observable, of, shareReplay, switchMap } from 'rxjs';
import { FieldLabel } from '../../_shared/models/field-label';
import {
  getAccountIdentifierLabel,
  getAssetValueLabel,
  getFinancialInstitutionLabel,
} from '../../assets/asset-label-lookup';
import { mapAssetTypeToString } from '../../assets/create-asset-button/create-asset-button.component';
import { AssetFormService } from '../../services/entity-state/asset-state/asset-form.service';
import { pascalCaseToSentence } from '../../util/formatting-helpers';
import { RecursivePartial } from '../../util/recursive-partial';
import { ExtractedGroup, ExtractedGroupType } from '../models/extracted-group';
import { ExtractedGroupMatchStatus } from '../models/extracted-group-match-status';
import { ExtractedValue } from '../models/extracted-value';
import { TranscriptionExtractedAsset } from '../models/transcription-extracted-info';
import { getGroupMatchStatus } from '../util/get-group-match-status';
import { RlaDataService } from './rla-data.service';
import { RlaUtilService } from './rla-util.service';

@Injectable()
export class RlaAssetService {
  private rlaDataService = inject(RlaDataService);
  private assetFormService = inject(AssetFormService);
  private injector = inject(Injector);
  private splunkLoggerService = inject(SplunkLoggerService);
  private rlaUtil = inject(RlaUtilService);

  assetGroups$: Observable<ExtractedGroup[]> = this.rlaDataService.dataResult$.pipe(
    switchMap((result) => {
      const extractedAssets = [
        ...(result?.accountAssets ?? []),
        ...(result?.subsidyAssets ?? []),
        ...(result?.giftAssets ?? []),
        ...(result?.miscellaneousAssets ?? []),
      ];
      if (extractedAssets.length === 0) return of([]);

      // Re-calculate groups when relevant forms change.
      return merge(
        of(null), // run on init
        toObservable(this.assetFormService.entityFormMap, { injector: this.injector }).pipe(
          switchMap((assetMap) => {
            const controls = Array.from(assetMap.values());
            const valueChanges = controls.map((control) => control.valueChanges);
            return merge(...valueChanges);
          }),
        ),
      ).pipe(
        map(() => {
          const assetsInForm = this.assetFormService.entityValues();

          const groups: ExtractedGroup[] = extractedAssets
            .map((extractedAsset): ExtractedGroup[] => {
              const assetType = extractedAsset.assetType;
              const assetValue = extractedAsset.assetValue;
              if (!assetType || !assetValue) return [];

              const assetInForm = assetsInForm.find((assetInForm) => {
                // Find asset in form based on heuristics.
                if (
                  assetInForm?.get('assetType')?.value === assetType &&
                  assetInForm?.get('assetValue')?.value === assetValue
                ) {
                  return true;
                }
                return false;
              });

              try {
                const values = this.getControlsForAsset(extractedAsset, assetInForm);
                return [
                  {
                    type: ExtractedGroupType.Asset,
                    label: 'Asset',
                    context: { assetType, extractedAsset },
                    status: assetInForm
                      ? getGroupMatchStatus(values)
                      : ExtractedGroupMatchStatus.PartialMatched,
                    values,
                  } satisfies ExtractedGroup,
                ];
              } catch (error) {
                console.error(`Error getting groups in ${RlaAssetService.name}`, error, extractedAsset);
                this.splunkLoggerService.error(
                  `RLA: Error getting groups in ${RlaAssetService.name}`,
                  error as Error,
                  { extractedAsset, messsage: (error as Error).message }
                );
                return [];
              }
            })
            .flat();

          return groups;
        }),
      );
    }),
    takeUntilDestroyed(),
    shareReplay(1),
  );

  getControlsForAsset(
    extractedAsset: RecursivePartial<TranscriptionExtractedAsset>,
    assetInForm?: FormGroup,
  ): ExtractedValue[] {
    const assetType = extractedAsset.assetType;
    if (!assetType) return [];
    return [
      this.rlaUtil.toExtractedValue(
        'Asset Type',
        (assetInForm?.get('assetType') ?? undefined) as FormControl | undefined,
        extractedAsset.assetType,
        () => extractedAsset.assetType ? mapAssetTypeToString(extractedAsset.assetType) : undefined,
      ),
      this.rlaUtil.toExtractedValue(
        getAssetValueLabel(assetType),
        (assetInForm?.get('assetValue') ?? undefined) as FormControl | undefined,
        extractedAsset.assetValue,
      ),
      this.rlaUtil.toExtractedValue(
        getAccountIdentifierLabel(assetType),
        (assetInForm?.get('accountIdentifier') ?? undefined) as FormControl | undefined,
        extractedAsset.accountIdentifier,
        undefined,
        (a, b) => a == b, // control converts number to string, so do loose compare
      ),
      this.rlaUtil.toExtractedValue(
        getFinancialInstitutionLabel(assetType),
        (assetInForm?.get('financialInstitution') ?? undefined) as FormControl | undefined,
        extractedAsset.financialInstitution,
      ),
      this.rlaUtil.toExtractedValue(
        FieldLabel.GrantorName,
        (assetInForm?.get('nameOfGrantor') ?? undefined) as FormControl | undefined,
        extractedAsset.nameOfGrantor,
      ),
      this.rlaUtil.toExtractedValue(
        FieldLabel.GrantType,
        (assetInForm?.get('source') ?? undefined) as FormControl | undefined,
        extractedAsset.source,
        () => extractedAsset.source ? pascalCaseToSentence(extractedAsset.source) : undefined,
      ),
      this.rlaUtil.toExtractedValue(
        FieldLabel.GrantDestination,
        (assetInForm?.get('grantDestinationType') ?? undefined) as FormControl | undefined,
        extractedAsset.grantDestinationType,
        () => extractedAsset.grantDestinationType
          ? pascalCaseToSentence(extractedAsset.grantDestinationType)
          : undefined,
      ),
      this.rlaUtil.toExtractedValue(
        FieldLabel.GiftSourceName,
        (assetInForm?.get('nameOfGiftGiver') ?? undefined) as FormControl | undefined,
        extractedAsset.nameOfGiftGiver,
      ),
      this.rlaUtil.toExtractedValue(
        FieldLabel.GiftDonorType,
        (assetInForm?.get('giftSource') ?? undefined) as FormControl | undefined,
        extractedAsset.giftSource,
        () => extractedAsset.giftSource ? pascalCaseToSentence(extractedAsset.giftSource) : undefined,
      ),
      this.rlaUtil.toExtractedValue(
        FieldLabel.GiftDestination,
        (assetInForm?.get('giftDestinationType') ?? undefined) as FormControl | undefined,
        extractedAsset.giftDestinationType,
        () => extractedAsset.giftDestinationType
          ? pascalCaseToSentence(extractedAsset.giftDestinationType)
          : undefined,
      ),
    ].filter((v): v is ExtractedValue => v !== null);
  }
}
