import { inject, Injectable } from '@angular/core';
import { FormControl } from '@angular/forms';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { ExtractedValue } from '../models/extracted-value';

@Injectable()
export class RlaUtilService {
  private splunkLoggerService = inject(SplunkLoggerService);

  toExtractedValue<T>(
    label?: string,
    control?: FormControl<T>,
    extractedValue?: T,
    formattedValue?: () => string | undefined,
    equals: (a: T, b: T) => boolean = (a, b) => a === b,
  ): ExtractedValue | null {
    if (extractedValue === undefined || extractedValue === null || control?.disabled) {
      return null;
    }
    try {
      return {
        label,
        value: extractedValue,
        matchesControl: control ? equals(control.value, extractedValue) : false,
        control,
        formattedValue: formattedValue?.(),
      } satisfies ExtractedValue;
    } catch (error) {
      console.error(`RLA: Error creating extracted value`, extractedValue, error);
      this.splunkLoggerService.error('RLA: Error creating extracted value', error as Error, { label, extractedValue, message: (error as Error)?.message });
      return null;
    }
  }

}
