import { DatePipe } from "@angular/common";
import { Component, inject } from '@angular/core';
import { toSignal } from "@angular/core/rxjs-interop";
import { ReactiveFormsModule } from '@angular/forms';
import { MatIconButton } from "@angular/material/button";
import { MatAccordion, MatExpansionPanel, MatExpansionPanelHeader, MatExpansionPanelTitle } from '@angular/material/expansion';
import { MatIcon } from "@angular/material/icon";
import { MatProgressSpinner } from '@angular/material/progress-spinner';
import { MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';
import { RktSkeletonModule } from '@rocketcentral/rocket-design-system-angular';
import { concat, filter, map, Subject, switchMap, take } from 'rxjs';
import { loadable } from '../util/loadable';
import { RlaDataGroupComponent } from "./components/rla-data-group/rla-data-group.component";
import { ExtractedGroup } from './models/extracted-group';
import { ExtractedGroupMatchStatus } from './models/extracted-group-match-status';
import { AssistantService } from './services/assistant.service';
import { RlaDataService } from './services/rla-data.service';
import { RlaFeedbackService } from './services/rla-feedback.service';

@Component({
  selector: 'app-assistant',
  standalone: true,
  imports: [
    MatIcon,
    MatIconButton,
    ReactiveFormsModule,
    RktSkeletonModule,
    MatTooltipModule,
    MatProgressSpinner,
    MatExpansionPanel,
    MatExpansionPanelTitle,
    MatExpansionPanelHeader,
    MatAccordion,
    DatePipe,
    RlaDataGroupComponent
],
  templateUrl: './assistant.component.html',
  styleUrl: './assistant.component.scss',
})
export class AssistantComponent {
  assistantService = inject(AssistantService);
  rlaDataService = inject(RlaDataService);
  rlaFeedbackService = inject(RlaFeedbackService);
  matSnackbar = inject(MatSnackBar);

  readonly ExtractedDataMatchStatus = ExtractedGroupMatchStatus;

  loadableResult = toSignal(this.rlaDataService.loadableDataResult$, { requireSync: true });
  loadableCallInfo = toSignal(this.rlaDataService.loadableCallInfo$, { requireSync: true });

  manualExpandedSummaryId$ = new Subject<string | undefined>();
  expandedSummaryId = toSignal(concat(
    this.rlaDataService.callInfo$.pipe(
      map(callInfo => callInfo.calls.at(0)?.callId),
      filter((callId): callId is string => callId !== undefined),
      take(1),
    ),
    this.manualExpandedSummaryId$.asObservable(),
  ));

  groups = toSignal(this.assistantService.groups$, { initialValue: [] });

  loadableOverallSummaryResult = toSignal(this.rlaDataService.loadableOverallSummaryResult$, { requireSync: true });

  feedbackIsHappy = toSignal(this.rlaFeedbackService.feedbackIsHappy$);
  feedbackUpdate$ = new Subject<boolean>();
  feedbackUpdateResponse$ = this.feedbackUpdate$.pipe(
    switchMap((isHappy) => this.rlaFeedbackService.putFeedback$(isHappy).pipe(
      loadable()
    )),
  );
  feedbackUpdateResponse = toSignal(this.feedbackUpdateResponse$);

  inProgressCallId = toSignal(this.rlaDataService.inProgressCallId$);

  makeDescription(group: ExtractedGroup) {
    return `Selected Extracted Data for "${group.label}"`;
  }

  copyToClipboard(text?: string | null) {
    if (text) {
      navigator.clipboard.writeText(text);
      this.matSnackbar.open('Copied to clipboard', 'Dismiss', { duration: 2000, panelClass: 'rkt-Snackbar', horizontalPosition: 'right' });
    }
  }
}
