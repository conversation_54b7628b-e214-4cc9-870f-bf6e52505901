@use '@rocketcentral/rocket-design-system-styles/web/scss/_color.scss' as *;

////////////////////////////////////////////////////////////////////////////////
/// mat-form-field styling

:root {
  --rla-has-suggestion-border-color: #{$rkt-purple-bright-50};
  --rla-has-suggestion-background: linear-gradient(315deg, rgba(240, 153, 199, 0.25) 0%, rgba(115, 146, 252, 0.11) 100%);
  --rla-has-suggestion-placeholder-color: #{$rkt-purple-bright-500};

  --rla-has-suggestion-hover-border-color: #{$rkt-purple-bright-500};
  --rla-has-suggestion-hover-background: linear-gradient(315deg, rgba(240, 153, 199, 0.25) 0%, rgba(115, 146, 252, 0.11) 100%);
}

.rla-show-suggestion mat-form-field {
  //#region Field background
  &:not(.active-input-section) {
    --rlxp-form-field-background-color: var(--rla-has-suggestion-background) !important;
  }
  //#endregion

  //#region Field border and label color
  --mdc-outlined-text-field-outline-color: var(--rla-has-suggestion-border-color) !important;
  --mdc-outlined-text-field-focus-outline-color: var(--rla-has-suggestion-border-color) !important;
  --mdc-outlined-text-field-hover-outline-color: var(--rla-has-suggestion-border-color) !important;
  --mdc-outlined-text-field-hover-label-text-color: var(--rla-has-suggestion-border-color) !important;
  --mdc-outlined-text-field-focus-label-text-color: var(--rla-has-suggestion-border-color) !important;
  --mdc-outlined-text-field-label-text-color: var(--rla-has-suggestion-border-color) !important;

  label {
    color: var(--rla-has-suggestion-border-color) !important;
  }
  //#endregion

  //#region Placeholder color
  --mdc-outlined-text-field-input-text-color: var(--rla-has-suggestion-placeholder-color) !important;
  //#endregion

  //#region Make "click to fill" seem like an action
  cursor: pointer;
  input {
    cursor: pointer;
  }
  //#endregion

  &:hover {
    --rla-has-suggestion-border-color: var(--rla-has-suggestion-hover-border-color);
    --rla-has-suggestion-background: var(--rla-has-suggestion-hover-background);
  }
}

.rkt-DarkMode {
  .rla-show-suggestion mat-form-field {
    --rla-has-suggestion-border-color: #{$rkt-purple-bright-50} !important;
    --rla-has-suggestion-hover-border-color: #{$rkt-purple-bright-50} !important;
    --rla-has-suggestion-placeholder-color: #e8e0ff !important;

    input::placeholder {
      color: #e8e0ff !important;
    }
  }
}

////////////////////////////////////////////////////////////////////////////////
/// mat-slide-toggle styling

.rla-show-suggestion mat-slide-toggle {
  .mdc-switch__track {
    &::before {
      background: #{$rkt-purple-bright-50} !important;
    }
    &::after {
      background: #{$rkt-purple-bright-50} !important;
    }
  }

  .rkt-SlideToggle__label {
    color: #{$rkt-purple-bright-500} !important;
  }
}

.rla-show-suggestion:has(mat-slide-toggle) {
  //#region Make "click to fill" seem like an action
  cursor: pointer;
  input {
    cursor: pointer;
  }
  //#endregion
}

.rkt-DarkMode {
  .rla-show-suggestion mat-slide-toggle {
    .rkt-SlideToggle__label {
      color: #{$rkt-purple-a100} !important;
    }
  }
}
