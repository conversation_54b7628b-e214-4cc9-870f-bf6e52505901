<h1 mat-dialog-title>Remove Client Reason</h1>
<mat-dialog-content [formGroup]="deactivationGroup">
  <mat-form-field class="rkt-FormField"   color="accent">
    <mat-label>Removal Type</mat-label>
    <mat-select formControlName="reason" class="rkt-Input">
      @for (reason of DeactivationReason; track reason) {
        <mat-option [value]="reason">{{ reason | pascalCaseSplit }}</mat-option>
      }
    </mat-select>
  </mat-form-field>
  <mat-form-field class="rkt-FormField"   color="accent">
    <mat-label>Additional Option</mat-label>
    <mat-select formControlName="subReason" class="rkt-Input">
      <!-- None option already in enum -->
      @for (subReason of DeactivationSubReasonType; track subReason) {
        <mat-option [value]="subReason">{{ subReason | pascalCaseSplit }}</mat-option>
      }
    </mat-select>
  </mat-form-field>
</mat-dialog-content>
<mat-dialog-actions>
  <button mat-stroked-button class="rkt-Button rkt-Button--secondary" color="accent" mat-dialog-close>
    Cancel
  </button>
  <button
    mat-flat-button
    color="primary"
    class="rkt-Button"
    [class.rkt-Button--is-disabled]="deactivationGroup.invalid || deactivationGroup.pristine"
    [disabled]="deactivationGroup.invalid || deactivationGroup.pristine"
    [mat-dialog-close]="deactivationGroup.value"
  >
    Confirm Removal
  </button>
</mat-dialog-actions>
