import { Component } from '@angular/core';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { DeactivationReasonType, DeactivationSubReasonType } from '@rocket-logic/rl-xp-bff-models';
import { PascalCaseSplitPipe } from '../../util/pascal-case-split.pipe';

@Component({
  selector: 'app-client-deactivation',
  standalone: true,
  imports: [
    MatDialogModule,
    FormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    ReactiveFormsModule,
    MatButtonModule,
    PascalCaseSplitPipe,
  ],
  templateUrl: './client-deactivation.component.html',
  styleUrl: './client-deactivation.component.scss',
})
export class ClientDeactivationComponent {
  deactivationGroup = new FormGroup({
    reason: new FormControl<DeactivationReasonType | null>(null),
    subReason: new FormControl<DeactivationSubReasonType | null>(null, Validators.required),
  });

  DeactivationReason = Object.values(DeactivationReasonType);
  DeactivationSubReasonType = Object.values(DeactivationSubReasonType);
}
