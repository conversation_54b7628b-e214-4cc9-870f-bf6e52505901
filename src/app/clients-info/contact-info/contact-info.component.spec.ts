import { signal } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder } from '@angular/forms';
import { provideAnimations } from '@angular/platform-browser/animations';
import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { AssistantService } from '../../assistant/services/assistant.service';
import { RlaDataService } from '../../assistant/services/rla-data.service';
import { PhoneNumberGroup } from '../../services/entity-state/client-state/form-types';
import { FormNavInputService } from '../../services/form-nav/form-nav-input.service';
import { FormNavSectionService } from '../../services/form-nav/form-nav-section.service';
import { SectionMilestoneService } from '../../services/form-nav/section-milestone.service';
import { LeadService } from '../../services/lead/lead.service';
import { SuggestionsService } from '../../suggestions/suggestions.service';
import { buildAddressForm } from '../../util/build-address-form';
import { buildPhoneNumberForm } from '../../util/build-phone-number-form';
import { ContactInfoComponent } from './contact-info.component';

describe('ContactInfoComponent', () => {
  let component: ContactInfoComponent;
  let fixture: ComponentFixture<ContactInfoComponent>;
  let formBuilder: FormBuilder;

  beforeEach(() =>
    MockBuilder()
      .keep(ContactInfoComponent)
      .provide(provideAnimations())
      .mock(FormNavInputService)
      .mock(SectionMilestoneService)
      .mock(LeadService, { lead$: NEVER })
      .mock(FormNavSectionService, { activeSection: signal(null) })
      .mock(SuggestionsService, { suggestionsForControl$: () => NEVER })
      .mock(RlaDataService, { callInfo$: NEVER })
      .mock(AssistantService, { })
  );

  beforeEach(() => {
    fixture = TestBed.createComponent(ContactInfoComponent);
    formBuilder = TestBed.inject(FormBuilder);
    component = fixture.componentInstance;
    fixture.componentRef.setInput(
      'contactInfoForm',
      formBuilder.group({
        emailAddress: [null],
        phoneNumbers: formBuilder.array<PhoneNumberGroup>([buildPhoneNumberForm(formBuilder)]),
        mailingAddress: buildAddressForm(formBuilder),
      }),
    );
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
