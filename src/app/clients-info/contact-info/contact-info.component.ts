import { Component, EventEmitter, Output, computed, effect, inject, input } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { FormArray, FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { PhoneNumberType } from '@rocket-logic/rl-xp-bff-models';
import { TextFieldComponent } from "../../_shared/components/text-field/text-field.component";
import { RlaFormFieldSuffixComponent } from "../../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component";
import { RlaHighlightDirective } from '../../assistant/directives/rla-highlight.directive';
import { PhoneNumberComponent } from '../../phone-number/phone-number.component';
import {
  ContactInfoControls,
  PhoneNumberGroup,
} from '../../services/entity-state/client-state/form-types';
import { LeadService } from '../../services/lead/lead.service';

@Component({
  selector: 'app-contact-info',
  standalone: true,
  imports: [
    MatIconModule,
    MatButtonModule,
    PhoneNumberComponent,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    TextFieldComponent,
  ],
  templateUrl: './contact-info.component.html',
  styleUrl: './contact-info.component.scss',
})
export class ContactInfoComponent {
  @Output() addPhone = new EventEmitter<void>();
  contactInfoForm = input.required<FormGroup<ContactInfoControls>>();
  isLoanEditingDisabled = input<boolean | undefined>(false);
  private leadService = inject(LeadService);
  private leadInformation = toSignal(this.leadService.lead$);
  private leadClientsNumbers = computed(() => {
    return (
      this.leadInformation()?.clients?.flatMap((client) => {
        const additionalPhoneNumbers = client?.additionalPhoneNumbers ?? [];
        return [
          ...additionalPhoneNumbers.map((number) => ({ number: number, type: null })),
          { number: client?.cellPhoneNumber, type: PhoneNumberType.Cell },
          { number: client?.homePhoneNumber, type: PhoneNumberType.Home },
          { number: client?.workPhoneNumber, type: PhoneNumberType.Work },
        ].filter((phone) => !!phone.number);
      }) ?? []
    );
  });

  phoneNumbersFormArray = computed(
    () => this.contactInfoForm().get('phoneNumbers') as FormArray<PhoneNumberGroup>,
  );

  phoneTypesLength = Object.values(PhoneNumberType).length;

  constructor() {
    effect(() => {
      const phoneNumbersArray = this.phoneNumbersFormArray() as FormArray;

      for (const phoneNumberGroup of phoneNumbersArray.controls) {
        const phoneNumber = phoneNumberGroup.get('number')?.value;
        const phoneType = phoneNumberGroup.get('type')?.value;

        const phoneNumberExists = this.leadClientsNumbers().some(
          (leadNumber) => leadNumber.number === phoneNumber && leadNumber.type === phoneType,
        );

        if (phoneNumberExists && !phoneNumberGroup.dirty) {
          phoneNumberGroup.disable();
        }
      }
    });
  }

  onAddPhone() {
    this.addPhone.emit();
  }

  onDeletePhone(index: number) {
    this.phoneNumbersFormArray().removeAt(index);
  }
}
