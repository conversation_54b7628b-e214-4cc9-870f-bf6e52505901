<p class="rkt-Label-14 rkt-FontWeight--500">Contact Info</p>
<div class="container">
  @for (phoneNumberForm of phoneNumbersFormArray().controls; track phoneNumberForm) {
    <app-phone-number
      [phoneNumberForm]="phoneNumberForm"
      [isLoanEditingDisabled]="isLoanEditingDisabled()"
      [shouldRegisterMilestone]="$first"
    />
    @if (
      phoneNumberForm.enabled && (!$last || phoneNumbersFormArray().length === phoneTypesLength)
    ) {
      <button
        mat-icon-button
        class="rkt-ButtonIcon"
        [class.rkt-ButtonIcon--is-disabled]="isLoanEditingDisabled()"
        [disabled]="isLoanEditingDisabled()"
        (click)="onDeletePhone($index)"
        aria-label="Delete Phone Number"
      >
        <mat-icon class="rkt-Icon" svgIcon="delete-outlined"></mat-icon>
      </button>
    }
  }
  @if (phoneNumbersFormArray().length < phoneTypesLength) {
    <button
      class="rkt-ButtonIcon"
      [class.rkt-ButtonIcon--is-disabled]="isLoanEditingDisabled()"
      mat-icon-button
      [disabled]="isLoanEditingDisabled()"
      (click)="onAddPhone()"
      aria-label="Add Phone Number"
    >
      <mat-icon class="rkt-Icon" svgIcon="add_circle-outlined"></mat-icon>
    </button>
  }

  <app-text-field
    id="email"
    [control]="contactInfoForm().controls.emailAddress"
    appRlaHighlight
    label="Email Address"
    customError="An email address must have only 1 '&#64;' sign and must not contain spaces."
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-text-field>

</div>
