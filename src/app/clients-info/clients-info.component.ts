import { CommonModule } from '@angular/common';
import { Component, computed, ElementRef, inject, viewChild, viewChildren } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog } from '@angular/material/dialog';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { MatTooltip } from '@angular/material/tooltip';
import {
  MaritalStatus,
  MilitaryBranch,
  MilitaryComponent,
  MilitaryStatus,
  PhoneNumberType,
  Suffix,
  VADisabilityBenefitsStatus,
} from '@rocket-logic/rl-xp-bff-models';
import { EMPTY, switchMap, take } from 'rxjs';
import { environment } from '../../environments/environment';
import { FormSectionComponent } from '../form-section/form-section.component';
import { MilestoneChipComponent } from '../form-section/milestone-chip/milestone-chip.component';
import { CreditService } from '../services/credit/credit.service';
import { ClientActivationHandlerService } from '../services/entity-state/client-state/client-activation-handler.service';
import { ClientFormService } from '../services/entity-state/client-state/client-form.service';
import { ClientStateService } from '../services/entity-state/client-state/client-state.service';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { TileSkeletonComponent } from '../tile/tile-skeleton/tile-skeleton.component';
import { TileComponent } from '../tile/tile.component';
import { ClientNamePipe } from '../util/client-name.pipe';
import { pascalCaseToSentence } from '../util/formatting-helpers';
import { openFormSection } from '../util/open-form-section';
import { ClientDeactivationComponent } from './client-deactivation/client-deactivation.component';
import { ClientInfoComponent } from './client-info/client-info.component';
import { ClientSkeletonComponent } from './client-skeleton/client-skeleton.component';
import { ReactivateClientButtonComponent } from './reactivate-client-button/reactivate-client-button.component';

@Component({
  selector: 'app-clients-info',
  standalone: true,
  imports: [
    FormSectionComponent,
    MatButtonModule,
    MatIconModule,
    ClientInfoComponent,
    ClientDeactivationComponent,
    ClientSkeletonComponent,
    TileComponent,
    TileSkeletonComponent,
    CommonModule,
    ClientNamePipe,
    MilestoneChipComponent,
    MatMenuModule,
    MatTooltip,
    ReactivateClientButtonComponent,
  ],
  templateUrl: './clients-info.component.html',
  styleUrl: './clients-info.component.scss',
})
export class ClientsInfoComponent {
  clientFormService = inject(ClientFormService);
  clientStateService = inject(ClientStateService);
  creditService = inject(CreditService);
  dialog = inject(MatDialog);
  formNavSectionService = inject(FormNavSectionService);
  readonly FormSection = FormSection;
  private clientActivationHandlerService = inject(ClientActivationHandlerService);

  clientInfoRefs = viewChildren(ClientInfoComponent, { read: ElementRef });
  formSectionComponentRef = viewChild.required(FormSectionComponent);
  lastClientInfoRef = computed(() => this.clientInfoRefs()[this.clientInfoRefs().length - 1]);
  isAddClientDisabled = computed(
    () => this.isLoanEditingDisabled() || !this.clientFormService.canAddNewClient(),
  );

  isFetching = this.clientStateService.isFetching;
  deactivatedClients = computed(
    () => this.clientActivationHandlerService.deactivatedClients() ?? [],
  );
  clients = computed(() => this.clientStateService.stateValues() ?? []);
  addClientLabel = computed(() => (this.clients().length > 0 ? 'Add Co-Client' : 'Add Client'));
  isLoanEditingDisabled = inject(LoanEditingState).isLoanEditingDisabled;
  suffixOptions = Object.values(Suffix);
  phoneTypeOptions = Object.values(PhoneNumberType);
  maritalStatusOptions = Object.values(MaritalStatus);
  branchOptions = Object.values(MilitaryBranch).map((branch) => {
    let display: string = '';

    switch (branch) {
      case MilitaryBranch.NOAA:
      case MilitaryBranch.USPHS:
        display = branch;
        break;
      default:
        display = pascalCaseToSentence(branch);
    }

    return { value: branch, display };
  });
  componentOptions = Object.values(MilitaryComponent).map((component) => ({
    value: component,
    display: pascalCaseToSentence(component),
  }));
  militaryStatusOptions = Object.values(MilitaryStatus).map((status) => ({
    value: status,
    display: pascalCaseToSentence(status),
  }));
  disabilityOptions = Object.values(VADisabilityBenefitsStatus).map((status) => ({
    value: status,
    display: pascalCaseToSentence(status),
  }));

  onAddClick() {
    if (this.clientFormService.canAddNewClient()) {
      this.openFormSection();
      this.clientFormService.addClient();
      this.scrollToLatestClient();
    }
  }

  onAddPhone(clientKey: string) {
    this.clientFormService.addPhoneNumber(clientKey);
  }

  onAddPreviousAddress(clientKey: string) {
    this.clientFormService.addPreviousAddress(clientKey);
  }

  onDelete(clientKey: string) {
    const client = this.clientStateService.state()?.data?.get(clientKey);

    if (this.creditService.clientHasCredit(client?.id ?? '')) {
      const dialogRef = this.dialog.open(ClientDeactivationComponent, {
        panelClass: 'rkt-Dialog',
        minWidth: '50%',
        minHeight: '50%',
        backdropClass: 'rkt-Backdrop',
      });

      dialogRef
        .afterClosed()
        .pipe(
          take(1),
          switchMap((value) =>
            value ? this.clientFormService.deactivateClient$(clientKey, value) : EMPTY,
          ),
        )
        .subscribe();
    } else {
      this.clientFormService.deleteClient$(clientKey).pipe(take(1)).subscribe();
    }
  }

  openFormSection() {
    openFormSection(() => this.formSectionComponentRef());
  }

  public scrollToLatestClient() {
    /* using set timeout to wait for the latest client to be rendered, 
    normally would use an effect on the clientInfoRefs but this logic 
    is simpler than preventing it from scrolling on page loads or other client events which is not desired.
    */
    setTimeout(() => {
      const latestClient = this.lastClientInfoRef().nativeElement;
      latestClient.scrollIntoView({ block: 'start', behavior: environment.scrollBehavior });
    }, 650);
  }

  spouseAddedToForm() {
    this.scrollToLatestClient();
  }
}
