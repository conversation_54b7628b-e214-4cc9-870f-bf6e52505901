import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { MockBuilder, MockInstance } from 'ng-mocks';
import { SelectFieldComponent } from '../../_shared/components/select-field/select-field.component';
import { ClientActivationHandlerService } from '../../services/entity-state/client-state/client-activation-handler.service';
import { ClientFormService } from '../../services/entity-state/client-state/client-form.service';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { MaritalStatusComponent } from './marital-status.component';

describe('MaritalStatusComponent', () => {
  let component: MaritalStatusComponent;
  let fixture: ComponentFixture<MaritalStatusComponent>;

  beforeEach(() =>
    MockBuilder(MaritalStatusComponent)
      .mock(ClientFormService)
      .mock(LoanEditingState, {
        isLoanEditingDisabled: signal(false),
      })
      .mock(ClientActivationHandlerService, { deactivatedClients: signal(undefined) }),
  );
  beforeEach(() => {
    MockInstance(SelectFieldComponent, 'matFormField', signal(undefined));
    fixture = TestBed.createComponent(MaritalStatusComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('isPrimaryBorrower', false);
    fixture.componentRef.setInput(
      'personalInfoForm',
      new FormGroup({ maritalStatus: new FormControl() }),
    );
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
