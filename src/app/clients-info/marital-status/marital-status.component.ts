import { Component, computed, inject, input, output } from '@angular/core';
import { FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { Client, MaritalStatus, PersonalInfo } from '@rocket-logic/rl-xp-bff-models';
import { environment } from '../../../environments/environment';
import { SelectFieldComponent } from "../../_shared/components/select-field/select-field.component";
import { RlaFormFieldSuffixComponent } from '../../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component';
import { RlaHighlightDirective } from '../../assistant/directives/rla-highlight.directive';
import { InputSectionDirective } from '../../form-nav/nav-section/input-section.directive';
import { ClientActivationHandlerService } from '../../services/entity-state/client-state/client-activation-handler.service';
import { ClientFormService } from '../../services/entity-state/client-state/client-form.service';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import { FormInput } from '../../services/form-nav/form-nav-input.service';
import { SelectOption } from '../../type-ahead-select/type-ahead-select.component';
import { ClientNamePipe } from '../../util/client-name.pipe';
import { Controlify } from '../../util/form-utility-types';
import { MARITAL_STATUS } from '../../util/marital-status';
import { toValueChangesSignal } from '../../util/value-changes-signal';

@Component({
  selector: 'app-marital-status',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    ClientNamePipe,
    InputSectionDirective,
    MatIconModule,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    SelectFieldComponent
],
  templateUrl: './marital-status.component.html',
  styleUrl: './marital-status.component.scss',
})
export class MaritalStatusComponent {
  spouseFlowEnabled = environment.featureFlags?.enableSpouseFlow;

  personalInfoForm = input.required<FormGroup<Controlify<PersonalInfo>>>();
  clients = input.required<Client[]>();
  isPrimaryBorrower = input.required<boolean>();

  clientFormService = inject(ClientFormService);
  clientActivationHandlerService = inject(ClientActivationHandlerService);

  isLoanEditingDisabled = inject(LoanEditingState).isLoanEditingDisabled;
  maritalStatus = toValueChangesSignal<MaritalStatus>(this.personalInfoForm, 'maritalStatus');

  isDisabled = computed(
    () => this.isLoanEditingDisabled() || !this.clientFormService.canAddNewClient(),
  );

  isMarried = computed(() => this.maritalStatus() === MaritalStatus.Married);
  deactivatedClients = computed(
    () => this.clientActivationHandlerService.deactivatedClients() ?? [],
  );

  subLabel = computed(() =>
    (this.deactivatedClients().length ?? 0) > 1
      ? 'Previously removed clients'
      : 'Previously removed client',
  );

  addSpouse = output<void>();
  readonly FormInput = FormInput;
  readonly maritalStatusOptions: SelectOption<MaritalStatus>[] = MARITAL_STATUS;
  spouseOnLoan = toValueChangesSignal<MaritalStatus>(this.personalInfoForm, 'spouseClientId');

  onAddClick() {
    if (this.clientFormService.canAddNewClient()) {
      this.addSpouse.emit();
    }
  }
}
