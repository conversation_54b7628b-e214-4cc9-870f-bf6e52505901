<p class="rkt-Label-14 rkt-FontWeight--500">Marital Status</p>
<div class="container" [formGroup]="personalInfoForm()">
  <app-select-field
    id="marital-status"
    label="Marital Status"
    [control]="personalInfoForm().controls.maritalStatus"
    appNavInput
    [inputSection]="FormInput.MaritalStatus"
    data-synthetic-monitor-id="marital-status-select"
    appRlaHighlight
    [options]="maritalStatusOptions"
    syntheticMonitorId="marital-status-select"
    optionSyntheticMonitorIdPrefix="marital-status-select-option_"
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-select-field>

  @if (isMarried()) {
    <mat-form-field id="married-to" floatLabel="always" class="rkt-FormField" color="accent">
      <mat-label>Married To</mat-label>
      <mat-select
        class="rkt-Input"
        placeholder="Spouse not on Loan"
        formControlName="spouseClientId"
      >
        <mat-option [value]="null">Spouse not on Loan</mat-option>
        @for (client of clients(); track client.id) {
          <mat-option [value]="client.id">{{ client | clientName }}</mat-option>
        }
        @if (deactivatedClients().length) {
          <mat-optgroup [label]="subLabel()">
            @for (deactivatedClient of deactivatedClients(); track deactivatedClient.id) {
              <mat-option [value]="deactivatedClient.id">{{
                deactivatedClient | clientName
              }}</mat-option>
            }
          </mat-optgroup>
        }
        @if (spouseOnLoan() === null && spouseFlowEnabled) {
          <mat-option
            [disabled]="isDisabled()"
            class="rkt-Button rkt-Button--large rkt-Button--tertiary rkt-Button--has-icon w-full no-select"
            color="accent"
            (click)="onAddClick(); $event.stopPropagation()"
            [class.rkt-Button--is-disabled]="isDisabled()"
          >
            <mat-icon class="rkt-Icon" color="primary" svgIcon="add_circle-outlined"></mat-icon>
            Add Spouse
          </mat-option>
        }
      </mat-select>
    </mat-form-field>
  }
</div>
