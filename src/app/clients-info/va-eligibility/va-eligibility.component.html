<p class="rkt-Label-14 rkt-FontWeight--500">VA Eligibility</p>
<div class="container" [formGroup]="militaryForm()">
  <app-toggle-field
    id="has-military"
    [control]="militaryForm().controls.hasMilitaryService"
    label="Served in armed forces or eligible for VA home loan"
    appRlaHighlight
  >
    <ng-container icon-slot>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-toggle-field>

  @if (hasMilitaryService()) {
    <ng-container [formGroup]="getMilitaryServiceForm()">
      <app-select-field
        id="branch"
        label="Branch of Service"
        [control]="getMilitaryServiceForm().controls.branch"
        [options]="branchOptions"
        appRlaHighlight
      >
        <ng-container form-field-suffix>
          <app-rla-form-field-suffix />
        </ng-container>
      </app-select-field>

      <app-select-field
        id="service-type"
        label="Service Type"
        [control]="getMilitaryServiceForm().controls.component"
        [options]="componentOptions"
        appRlaHighlight
      >
        <ng-container form-field-suffix>
          <app-rla-form-field-suffix />
        </ng-container>
      </app-select-field>

      <app-select-field
        id="military-status"
        label="Military Status"
        appRlaHighlight
        class="col-span-5"
        [control]="getMilitaryServiceForm().controls.status"
        [options]="militaryStatusOptions"
        panelWidth=""
        (selectionChange)="handleMilitaryStatusChange($event)"
      >
        <ng-container form-field-suffix>
          <app-rla-form-field-suffix />
        </ng-container>
      </app-select-field>

      <app-select-field
        id="disability-benefits"
        label="Disability Benefits"
        [control]="getMilitaryServiceForm().controls.vaDisabilityBenefitsStatus"
        [options]="disabilityOptions"
        appRlaHighlight
        class="col-span-5"
      >
        <ng-container form-field-suffix>
          <app-rla-form-field-suffix />
        </ng-container>
      </app-select-field>

      @if (isDischarged()) {
        <app-toggle-field
          id="discharge-status"
          [control]="getMilitaryServiceForm().controls.wasDischargedUnderConditionsOtherThanDishonorable"
          label="Discharged on condition other than dishonorable"
          appRlaHighlight
        >
          <ng-container icon-slot>
            <app-rla-form-field-suffix />
          </ng-container>
        </app-toggle-field>
      }

      <app-toggle-field
        id="prior-loan"
        [control]="getMilitaryServiceForm().controls.hasPriorVAHomeLoan"
        label="Prior VA Loan"
        appRlaHighlight
        class="col-span-3"
      >
        <ng-container icon-slot>
          <app-rla-form-field-suffix />
        </ng-container>
      </app-toggle-field>

      <app-toggle-field
        id="surviving-spouse"
        [control]="getMilitaryServiceForm().controls.isSurvivingSpouseOfVeteran"
        label="Surviving Spouse"
        appRlaHighlight
        class="col-span-4"
      >
        <ng-container icon-slot>
          <app-rla-form-field-suffix />
        </ng-container>
      </app-toggle-field>
    </ng-container>
  }
</div>
