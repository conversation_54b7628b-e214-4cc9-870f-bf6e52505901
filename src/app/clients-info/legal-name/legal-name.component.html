<p class="rkt-Label-14 rkt-FontWeight--500">Legal Name</p>
<div class="container">

  <app-text-field
    id="first-name"
    [control]="personalInfoForm().controls.firstName"
    label="First Name"
    appNavInput
    [inputSection]="FormInput.ClientFirstName"
    appRlaHighlight
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-text-field>

  <app-text-field
    id="middle-name"
    [control]="personalInfoForm().controls.middleName"
    label="Middle Name"
    appRlaHighlight
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-text-field>

  <app-text-field
    id="last-name"
    [control]="personalInfoForm().controls.lastName"
    label="Last Name"
    appNavInput
    [inputSection]="FormInput.ClientLastName"
    appRlaHighlight
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-text-field>

  <app-select-field
    id="suffix"
    label="Suffix"
    [control]="personalInfoForm().controls.suffix"
    [options]="suffixOptions"
    appRlaHighlight
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-select-field>

  <app-text-field
    id="preferred-name"
    label="Preferred Name"
    [control]="personalInfoForm().controls.preferredName"
    appRlaHighlight
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-text-field>

</div>
