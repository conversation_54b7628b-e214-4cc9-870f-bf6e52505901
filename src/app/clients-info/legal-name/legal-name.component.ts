import { Component, input } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MatIconModule } from '@angular/material/icon';
import { PersonalInfo, Suffix } from '@rocket-logic/rl-xp-bff-models';
import { SelectFieldComponent } from "../../_shared/components/select-field/select-field.component";
import { TextFieldComponent } from '../../_shared/components/text-field/text-field.component';
import { RlaFormFieldSuffixComponent } from "../../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component";
import { RlaHighlightDirective } from '../../assistant/directives/rla-highlight.directive';
import { InputSectionDirective } from '../../form-nav/nav-section/input-section.directive';
import { FormInput } from '../../services/form-nav/form-nav-input.service';
import { SelectOption } from '../../type-ahead-select/type-ahead-select.component';
import { Controlify } from '../../util/form-utility-types';
import { pascalCaseSplit } from '../../util/formatting-helpers';

@Component({
  selector: 'app-legal-name',
  standalone: true,
  imports: [
    InputSectionDirective,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    MatIconModule,
    TextFieldComponent,
    SelectFieldComponent,
  ],
  templateUrl: './legal-name.component.html',
  styleUrl: './legal-name.component.scss',
})
export class LegalNameComponent {
  personalInfoForm = input.required<FormGroup<Controlify<PersonalInfo>>>();
  isPrimaryBorrower = input.required<boolean>();
  readonly FormInput = FormInput;

  suffixOptions: SelectOption<Suffix>[] = Object.values(Suffix).map(value => ({
    value,
    display: pascalCaseSplit(value),
  }));
}
