import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MockBuilder, MockInstance } from 'ng-mocks';
import { SelectFieldComponent } from '../../_shared/components/select-field/select-field.component';
import { TextFieldComponent } from '../../_shared/components/text-field/text-field.component';
import { LegalNameComponent } from './legal-name.component';

describe('LegalNameComponent', () => {
  let component: LegalNameComponent;
  let fixture: ComponentFixture<LegalNameComponent>;

  beforeEach(() => MockBuilder(LegalNameComponent));
  beforeEach(() => {
    MockInstance(TextFieldComponent, 'matFormField', signal(undefined));
    MockInstance(SelectFieldComponent, 'matFormField', signal(undefined));
    fixture = TestBed.createComponent(LegalNameComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('personalInfoForm', new FormGroup({}));
    fixture.componentRef.setInput('isPrimaryBorrower', false);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
