import { OwnedPropertyDispositionStatus } from '@rocket-logic/rl-xp-bff-models';

export function getPlanForPropertyLabel(value: OwnedPropertyDispositionStatus): string {
  switch (value) {
    case OwnedPropertyDispositionStatus.PendingSale: return 'Selling';
    case OwnedPropertyDispositionStatus.Retained: return 'Keeping';
    case OwnedPropertyDispositionStatus.AwardedToExSpouse: return 'Awarded To Ex-Spouse';
    case OwnedPropertyDispositionStatus.Sold: return 'Sold';
  }
}

