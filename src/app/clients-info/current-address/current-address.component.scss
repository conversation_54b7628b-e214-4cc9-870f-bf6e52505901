.container {
  display: grid;
  grid-template: repeat(1, 1fr) / repeat(10, 1fr);
  column-gap: 16px;
  row-gap: 12px;
  align-items: center;
}

.title-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  > p {
    margin: 0;
  }
}
app-address {
  grid-column: span 10;
  grid-row: 1 / span 2;
}

#ownership-status {
  grid-column: span 3;
}

#plan-for-property,
#selling-price,
#monthly-rent,
#occupancy-type {
  grid-column: span 3;
}

mat-slide-toggle {
  margin-bottom: 20px;
}
