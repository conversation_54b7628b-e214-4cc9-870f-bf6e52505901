<div class="title-container" [formGroup]="currentResidenceForm()">
  <p class="rkt-Label-14 rkt-FontWeight--500">Current Address</p>
  @if (showSameAs()) {
    <app-select-field
      [control]="currentResidenceForm().controls.sameAsClientId"
      label="Same as"
      [options]="clientsWithFormsOptions()"
      subscriptSizing="dynamic"
    />
  }
</div>

<div class="container" [formGroup]="currentResidenceForm()">
  <app-address [addressSection]="addressSection()" [addressForm]="addressForm()" />

  <app-text-field
    label="Years Lived At"
    [control]="durationForm().controls.years"
    appRlaHighlight
    type="number"
    class="col-span-2"
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-text-field>

  <app-text-field
    label="Months"
    [control]="durationForm().controls.months"
    appRlaHighlight
    type="number"
    class="col-span-2"
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-text-field>

  <app-toggle-field
    [control]="currentResidenceForm().controls.taxesFiledAtThisAddress"
    label="Taxes Filed at this address"
    appRlaHighlight
    class="col-span-6 mb-5"
  >
    <ng-container icon-slot>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-toggle-field>

  @if (
    this.showResidencyType() && !this.currentResidenceForm().controls.useAsSubjectProperty.disabled
  ) {
    <app-select-field
      id="ownership-status"
      appRlaHighlight
      label="Ownership Status"
      [control]="currentResidenceForm().controls.residencyType"
      [options]="residencyOptions"
      appNavInput
      [inputSection]="ProductMilestones.OwnershipStatus"
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-select-field>
  }
  @if (isOwned() && isPurchase()) {
    <app-select-field
      id="plan-for-property"
      appRlaHighlight
      label="Plan for Property"
      [control]="currentResidenceForm().controls.planForProperty"
      [options]="planForPropertyOptions"
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-select-field>

    @if (isSelling()) {
      <app-formatted-number-input
        [allowNegative]="false"
        [control]="sellingPrice"
        id="selling-price"
        label="Selling Price"
        prefix="$"
        appRlaHighlight
      >
        <ng-container form-field-suffix>
          <app-rla-form-field-suffix />
        </ng-container>
      </app-formatted-number-input>
    } @else if (isKeeping()) {
      <app-select-field
        id="occupancy-type"
        data-synthetic-monitor-id="occupancy-type"
        appRlaHighlight
        label="Occupancy Type"
        [control]="currentResidenceForm().controls.occupancyType"
        [options]="occupancyTypeOptions"
        appNavInput
        [inputSection]="ProductMilestones.OccupancyCurrentAddress"
      >
        <ng-container form-field-suffix>
          <app-rla-form-field-suffix />
        </ng-container>
      </app-select-field>
    }
  }
  @if (isRented()) {
    <app-formatted-number-input
      [allowNegative]="false"
      [control]="monthlyRent"
      id="monthly-rent"
      label="Monthly Rent"
      prefix="$"
    />
  }
</div>
