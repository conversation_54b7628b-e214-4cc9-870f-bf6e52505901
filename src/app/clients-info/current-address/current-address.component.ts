import { Component, computed, inject, input } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import {
  Client,
  OwnedPropertyDispositionStatus,
  ResidencyType,
} from '@rocket-logic/rl-xp-bff-models';
import { SelectFieldComponent } from "../../_shared/components/select-field/select-field.component";
import { TextFieldComponent } from "../../_shared/components/text-field/text-field.component";
import { ToggleFieldComponent } from "../../_shared/components/toggle-field/toggle-field.component";
import { AddressComponent } from '../../address/address.component';
import { RlaF<PERSON>FieldSuffixComponent } from "../../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component";
import { RlaHighlightDirective } from '../../assistant/directives/rla-highlight.directive';
import { InputSectionDirective } from '../../form-nav/nav-section/input-section.directive';
import { FormattedNumberInputComponent } from '../../question-input/formatted-number-input/formatted-number-input.component';
import {
  ClientControls,
  CurrentResidenceControls,
} from '../../services/entity-state/client-state/form-types';
import { LoanInfoFormListenerService } from '../../services/entity-state/loan-state/loan-info-form-listener.service';
import {
  FormInput,
  ProductMilestoneFormInput,
} from '../../services/form-nav/form-nav-input.service';
import { getClientName } from '../../util/get-client-name';
import { OCCUPANCY } from '../../util/occupancy';
import { toValueChangesSignal } from '../../util/value-changes-signal';
import { getPlanForPropertyLabel } from './get-plan-for-property-label';
import { getResidencyOptionLabel } from './get-residency-option-label';

@Component({
  selector: 'app-current-address',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    AddressComponent,
    MatFormFieldModule,
    MatInputModule,
    MatSlideToggleModule,
    MatSelectModule,
    FormattedNumberInputComponent,
    InputSectionDirective,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    TextFieldComponent,
    SelectFieldComponent,
    ToggleFieldComponent,
  ],
  templateUrl: './current-address.component.html',
  styleUrl: './current-address.component.scss',
})
export class CurrentAddressComponent {
  loanInfoFormListener = inject(LoanInfoFormListenerService);

  readonly ProductMilestones = ProductMilestoneFormInput;

  currentResidenceForm = input.required<FormGroup<CurrentResidenceControls>>();
  showSameAs = input<boolean>(true);
  clientsWithForms =
    input.required<{ client: Client; form: FormGroup<ClientControls> | undefined }[]>();

  clientsWithFormsOptions = computed(() => {
    return this.clientsWithForms()
      .filter(clientObj => !clientObj.form?.value?.residenceInformation?.currentResidence?.sameAsClientId)
      .map((clientWithForm) => {
        return {
          value: clientWithForm.client.id,
          display: getClientName(clientWithForm.client),
        };
      });
  });

  addressForm = computed(() => this.currentResidenceForm().get('address') as FormGroup);

  durationForm = computed(
    () => this.currentResidenceForm().controls.durationAtResidence!,
  );
  residencyType = toValueChangesSignal<ResidencyType>(this.currentResidenceForm, 'residencyType');
  planForProperty = toValueChangesSignal<OwnedPropertyDispositionStatus>(
    this.currentResidenceForm,
    'planForProperty',
  );
  useAsSubjectProperty = toValueChangesSignal<boolean>(
    this.currentResidenceForm,
    'useAsSubjectProperty',
  );
  isOwned = computed(() => this.residencyType() === ResidencyType.Own);
  isRented = computed(() => this.residencyType() === ResidencyType.Rent);
  isPurchase = computed(() => this.loanInfoFormListener.purchaseSelected());
  isSelling = computed(() => this.planForProperty() === OwnedPropertyDispositionStatus.PendingSale);
  isKeeping = computed(() => this.planForProperty() === OwnedPropertyDispositionStatus.Retained);
  showResidencyType = computed(
    () => this.isPurchase() || (!this.isPurchase() && !this.useAsSubjectProperty()),
  );

  addressSection = computed(() => {
    return {
      state: FormInput.CurrentStateAddress,
      city: FormInput.CurrentCityAddress,
      street: FormInput.CurrentStreetAddress,
      zip: FormInput.CurrentZipAddress,
    };
  });

  residencyOptions = Object.values(ResidencyType).map(value => ({
    value,
    display: getResidencyOptionLabel(value),
  }));

  planForPropertyOptions = [
    { display: getPlanForPropertyLabel(OwnedPropertyDispositionStatus.PendingSale), value: OwnedPropertyDispositionStatus.PendingSale },
    { display: getPlanForPropertyLabel(OwnedPropertyDispositionStatus.Retained), value: OwnedPropertyDispositionStatus.Retained },
  ];
  occupancyTypeOptions = OCCUPANCY;

  get monthlyRent() {
    return this.currentResidenceForm().get('monthlyRent') as FormControl;
  }

  get sellingPrice() {
    return this.currentResidenceForm().get('sellingPrice') as FormControl;
  }
}
