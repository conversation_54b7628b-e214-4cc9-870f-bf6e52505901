<app-form-section [title]="'Client(s)'" [formSection]="FormSection.ClientInfo">
  <div addButton>
    <ng-container *ngTemplateOutlet="addButtonTemplate"></ng-container>
  </div>

  @if (!isFetching()) {
    @for (clientMap of clientFormService.sortedEntityForms(); track clientMap[0]) {
      <app-client-info
        #clientInfo
        [isLoanEditingDisabled]="isLoanEditingDisabled()"
        [clientForms]="clientFormService.entityValues()"
        [clientForm]="clientMap[1]"
        [clients]="clients()"
        (addPhone)="onAddPhone(clientMap[0])"
        (addPreviousAddress)="onAddPreviousAddress(clientMap[0])"
        (delete)="onDelete(clientMap[0])"
        (spouseAdded)="spouseAddedToForm()"
      />
    }

    <ng-container *ngTemplateOutlet="addButtonTemplate"></ng-container>
  } @else {
    <app-client-skeleton />
  }
  @if (!isFetching()) {
    <div section-summary class="row flex-wrap">
      @for (client of clients(); track client) {
        <app-tile
          [label]="client.isPrimaryBorrower ? 'Primary Client' : 'Co-Client'"
          [content]="client | clientName"
          (tileClick)="openFormSection()"
        ></app-tile>
      }
    </div>
  } @else {
    <div section-summary class="row flex-wrap">
      <app-tile-skeleton></app-tile-skeleton>
    </div>
  }
  @if (!isFetching()) {
    <div milestone-chip>
      <app-milestone-chip
        [formSection]="FormSection.ClientInfo"
        (chipClick)="openFormSection()"
      ></app-milestone-chip>
    </div>
  }
</app-form-section>

<ng-template #addButtonTemplate>
  @if (deactivatedClients().length) {
    <div>
      <button
        [matMenuTriggerFor]="menu"
        [disabled]="isAddClientDisabled()"
        class="rkt-Button rkt-Button--large rkt-Button--tertiary rkt-Button--has-icon"
        mat-button
        color="accent"     
        [class.rkt-Button--is-disabled]="isAddClientDisabled()"
        data-testid="add-client-menu-trigger"
      >
        <mat-icon class="rkt-Icon" color="primary" svgIcon="add_circle-outlined"></mat-icon>
        {{ addClientLabel() }}
      </button>

      <mat-menu #menu xPosition="after" data-testid="add-client-menu">
        @for (client of deactivatedClients(); track client.id) {
          <app-reactivate-client-button [client]="client" />
        }
        <ng-container *ngTemplateOutlet="addButtonMenuItemTemplate"></ng-container>
      </mat-menu>
    </div>
  } @else {
    <button
      addButton
      [disabled]="isAddClientDisabled()"
      class="rkt-Button rkt-Button--large rkt-Button--tertiary rkt-Button--has-icon"
      mat-button
      color="accent"
      (click)="onAddClick()"
      [class.rkt-Button--is-disabled]="isAddClientDisabled()"
      [attr.data-synthetic-monitor-id]="'add-client'"
      data-testid="add-client-button"
    > 
      <mat-icon class="rkt-Icon" color="primary" svgIcon="add_circle-outlined"></mat-icon>
      {{ addClientLabel() }}
    </button>
  }
</ng-template>

<ng-template #addButtonMenuItemTemplate>
  <button
    [disabled]="isAddClientDisabled()"
    mat-menu-item
    class="rkt-Menu__item"
    (click)="onAddClick()"
    [class.rkt-Button--is-disabled]="isAddClientDisabled()"
    data-testid="add-co-client-menu-item"
  >
    <mat-icon svgIcon="add_circle-outlined"></mat-icon>
    Add New Co-Client
  </button>
</ng-template>
