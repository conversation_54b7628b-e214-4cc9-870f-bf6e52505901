<div class="container">
  @for (
    previousResidenceForm of previousResidenceFormArray().controls;
    track previousResidenceForm
  ) {
    <div class="flex justify-between items-center w-full">
      <span class="rkt-Label-14 rkt-FontWeight--500">Previous Address {{$index + 1}}</span>
      <button
        [disabled]="isLoanEditingDisabled()"
        [class.rkt-ButtonIcon--is-disabled]="isLoanEditingDisabled()"
        mat-icon-button
        class="rkt-ButtonIcon app-ButtonIcon"
        (click)="onDeletePreviousAddress($index)"
      >
        <mat-icon class="rkt-Icon" svgIcon="delete-outlined"></mat-icon>
      </button>
    </div>
    <div class="item-container" [formGroup]="previousResidenceForm">
      <app-address [addressForm]="getAddressForm(previousResidenceForm)" />
      <div [formGroup]="getDurationForm(previousResidenceForm)" id="duration">
        <mat-form-field  class="rkt-FormField"   color="accent">
          <mat-label>Years Lived At</mat-label>
          <input class="rkt-Input" matInput formControlName="years" type="number" />
        </mat-form-field>
        <mat-form-field  class="rkt-FormField"   color="accent">
          <mat-label>Months</mat-label>
          <input class="rkt-Input" matInput formControlName="months" type="number" />
        </mat-form-field>
      </div>
    </div>
  }
  <button
    id="add-previous-button"
    class="rkt-Button rkt-Button--large rkt-Button--tertiary rkt-Button--has-icon"
    mat-button
    [disabled]="isLoanEditingDisabled()"
    [class.rkt-Button--is-disabled]="isLoanEditingDisabled()"
    color="accent"
    (click)="onAddPreviousAddress()"
  >
    <mat-icon class="rkt-Icon" color="primary" svgIcon="add_circle-outlined"></mat-icon>
    Add Previous Address
  </button>
</div>
