.container {
  display: flex;
  flex-direction: column;
  column-gap: 16px;
  row-gap: 12px;
  align-items: start;
}

.item-container {
  display: grid;
  grid-template: repeat(3, 1fr) / repeat(10, 1fr);
  column-gap: 16px;
  row-gap: 12px;
  align-items: center;
}

app-address {
  grid-column: span 10;
  grid-row: 1 / span 2;
}

#duration {
  display: contents;
  grid-row: 3;

  mat-form-field {
    grid-column: span 2;
  }
}

#add-previous-button {
  margin-bottom: 20px;
}
