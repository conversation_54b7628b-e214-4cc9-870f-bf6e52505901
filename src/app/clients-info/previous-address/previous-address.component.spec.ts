import { ComponentFixture, TestBed } from '@angular/core/testing';

import { FormArray } from '@angular/forms';
import { MockBuilder } from 'ng-mocks';
import { PreviousAddressComponent } from './previous-address.component';

describe('PreviousAddressComponent', () => {
  let component: PreviousAddressComponent;
  let fixture: ComponentFixture<PreviousAddressComponent>;

  beforeEach(() => MockBuilder(PreviousAddressComponent));
  beforeEach(() => {
    fixture = TestBed.createComponent(PreviousAddressComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('previousResidenceFormArray', new FormArray([]));
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
