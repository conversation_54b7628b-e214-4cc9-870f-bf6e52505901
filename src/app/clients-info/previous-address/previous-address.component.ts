import { Component, EventEmitter, Output, input } from '@angular/core';
import { FormArray, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { AddressComponent } from '../../address/address.component';
import { ResidenceDetailControls } from '../../services/entity-state/client-state/form-types';
import { STATES } from '../../util/states';

@Component({
  selector: 'app-previous-address',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    AddressComponent,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
  ],
  templateUrl: './previous-address.component.html',
  styleUrl: './previous-address.component.scss',
})
export class PreviousAddressComponent {
  @Output() addPreviousAddress = new EventEmitter<void>();
  previousResidenceFormArray = input.required<FormArray<FormGroup<ResidenceDetailControls>>>();
  isLoanEditingDisabled = input<boolean | undefined>(false);

  stateOptions = STATES;

  getAddressForm(residenceForm: FormGroup): FormGroup {
    return residenceForm.get('address') as FormGroup;
  }

  getDurationForm(residenceForm: FormGroup): FormGroup {
    return residenceForm.get('durationAtResidence') as FormGroup;
  }

  onAddPreviousAddress() {
    this.addPreviousAddress.emit();
  }

  onDeletePreviousAddress(index: number) {
    this.previousResidenceFormArray().markAsDirty();
    this.previousResidenceFormArray().removeAt(index);
  }
}
