import { Component, EventEmitter, Output, computed, inject, input, output } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';
import { Client, Income } from '@rocket-logic/rl-xp-bff-models';
import { CreditService } from '../../services/credit/credit.service';
import { ClientFormService } from '../../services/entity-state/client-state/client-form.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { ClientControls } from '../../services/entity-state/client-state/form-types';
import { IncomeStateService } from '../../services/entity-state/income-state/income-state.service';
import { toValueChangesSignal } from '../../util/value-changes-signal';
import { ContactInfoComponent } from '../contact-info/contact-info.component';
import { CurrentAddressComponent } from '../current-address/current-address.component';
import { LegalNameComponent } from '../legal-name/legal-name.component';
import { MaritalStatusComponent } from '../marital-status/marital-status.component';
import { PreviousAddressComponent } from '../previous-address/previous-address.component';
import { VaEligibilityComponent } from '../va-eligibility/va-eligibility.component';

@Component({
  selector: 'app-client-info',
  standalone: true,
  host: {
    '[attr.data-synthetic-monitor-id]': 'monitorId()',
  },
  imports: [
    LegalNameComponent,
    ContactInfoComponent,
    MaritalStatusComponent,
    CurrentAddressComponent,
    PreviousAddressComponent,
    VaEligibilityComponent,
    MatButtonModule,
    MatIconModule,
    MatMenuModule,
  ],
  templateUrl: './client-info.component.html',
  styleUrl: './client-info.component.scss',
})
export class ClientInfoComponent {
  @Output() addPhone = new EventEmitter<void>();
  @Output() addPreviousAddress = new EventEmitter<void>();
  @Output() delete = new EventEmitter<void>();
  private clientStateService = inject(ClientStateService);
  private incomeStateService = inject(IncomeStateService);
  private creditService = inject(CreditService);
  private clientFormService = inject(ClientFormService);
  clientForms = input.required<FormGroup<ClientControls>[]>();
  clientForm = input.required<FormGroup<ClientControls>>();
  clients = input.required<Client[]>();
  isLoanEditingDisabled = input<boolean | undefined>(false);
  spouseAdded = output<void>();
  clientHasCredit = computed(() =>
    this.creditService.clientHasCredit(this.clientForm().value?.id ?? ''),
  );

  protected readonly monitorId = computed(() =>{
    const borrowerType = this.isPrimaryBorrower() ? 'primary' : 'co-client';

    return `rlxp-client-info-${borrowerType}`;
  });

  saveInProgress = this.clientStateService.isUpdating;

  canDeleteClient = computed(() => {
    // Need to reference otherClients() at the top of this signal to re-run on change
    const otherClients = this.otherClients();
    const client = this.clientForm().value;

    // Client cannot have partial info
    if (client.personalInformation?.ssn && !client.personalInformation.dateOfBirth) {
      return false;
    }

    // If the client to delete doesn't have a gcid, they can be deleted
    if (!client.gcid) {
      return true;
    }

    // If any other clients dont have gcid, MC will throw
    // when creating income dictionary by GCID
    const anyOtherClientsMissingGcid = otherClients.some((client) => !client.gcid);
    if (anyOtherClientsMissingGcid) {
      return false;
    }

    const incomeData = this.incomeStateService.state()?.data ?? {};
    const currentClientIncome = incomeData[client?.id ?? ''] ?? new Map<string, Income>();

    // If current client doesnt have income, allow deletion
    if (currentClientIncome.size === 0) {
      return true;
    } else {
      // Check if at least one other client has income and gcid
      return otherClients.some((otherClient) => {
        const otherClientIncome = incomeData[otherClient?.id ?? ''] ?? new Map<string, Income>();
        return otherClientIncome.size > 0 && otherClient.gcid;
      });
    }
  });

  /**
   * List of clients excluding the current client
   */
  otherClients = computed(() =>
    this.clients().filter((client) => client.id !== this.clientForm().value.id),
  );

  /**
   * Tuple with other clients and their form object
   */
  otherClientsWithForms = computed(() =>
    this.otherClients().map((client) => {
      const form = this.clientForms().find((control) => control.value.id === client.id);
      return { client, form };
    }),
  );
  isPrimaryBorrower = toValueChangesSignal<boolean>(this.clientForm, 'isPrimaryBorrower');
  personalInfoForm = computed(() => this.clientForm().get('personalInformation') as FormGroup);
  contactInfoForm = computed(() => this.clientForm().get('contactInformation') as FormGroup);
  currentResidenceForm = computed(
    () => this.clientForm().get('residenceInformation.currentResidence') as FormGroup,
  );
  previousResidenceFormArray = computed(
    () => this.clientForm().get('residenceInformation.formerResidences') as FormArray,
  );
  militaryForm = computed(() => this.clientForm().get('military') as FormGroup);
  isClientSaved = computed(() =>
    this.clients().some((client) => client.id === this.clientForm().value.id),
  );
  showMenuButton = computed(
    () =>
      !this.isClientSaved() ||
      (this.clients().length > 1 && (this.canDeleteClient() || !this.isPrimaryBorrower())),
  );

  onAddPhone() {
    this.addPhone.emit();
  }

  onAddPreviousAddress() {
    this.addPreviousAddress.emit();
  }

  onDeleteClick() {
    this.delete.emit();
  }

  onMakePrimary() {
    this.clientStateService.makePrimary$(this.clientForm().value.id!).subscribe();
  }

  addSpouseForThisClient() {
    this.clientFormService.addClient({ spouse: <Client>this.clientForm().value });
    this.spouseAdded.emit();
  }
}
