.container {
  display: flex;
  flex-direction: column;

  > * {
    border-bottom: 1px solid var(--rlxp-default-divider-color);
    padding-top: 24px;
    padding-bottom: 4px;

    &:first-child {
      padding-top: 0;
    }
    &:last-child {
      border-bottom: none;
    }
  }
}

.title-container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  height: var(--mdc-icon-button-state-layer-size, 32px);

  > p {
    margin: 0;
  }
}