<div class="title-container">
  <p class="rkt-Label-16 rkt-FontWeight--700">
    {{ isPrimaryBorrower() ? 'Primary Client' : 'Co-Client' }}
  </p>
  @if (showMenuButton()) {
    <button
      mat-icon-button
      class="rkt-ButtonIcon"
      [disabled]="isLoanEditingDisabled()"
      [class.rkt-ButtonIcon--is-disabled]="isLoanEditingDisabled()"
      [matMenuTriggerFor]="menu"
    >
      <mat-icon class="rkt-Icon" svgIcon="more_vert-outlined"></mat-icon>
    </button>

    <mat-menu #menu="matMenu">
      @if (canDeleteClient()) {
        <button class="rkt-Menu__item" mat-menu-item (click)="onDeleteClick()" [disabled]="saveInProgress()">
          <mat-icon class="rkt-Icon" svgIcon="delete-outlined"></mat-icon>
          <span class="rkt-Menu__item-text">
            @if(saveInProgress()) {
                Save In Progress
            } 
            @else if (clientHasCredit()) {
              Deactivate
            } @else {
              Delete
            }
          </span>
        </button>
      }
      @if (!isPrimaryBorrower() && isClientSaved()) {
        <button class="rkt-Menu__item" mat-menu-item (click)="onMakePrimary()">
          <mat-icon class="rkt-Icon" svgIcon="groups-outlined"></mat-icon>
          <span class="rkt-Menu__item-text"> Make Primary </span>
        </button>
      }
    </mat-menu>
  }
</div>
<div class="container">
  <app-legal-name
    [isPrimaryBorrower]="isPrimaryBorrower()"
    [personalInfoForm]="personalInfoForm()"
  />
  <app-contact-info
    [contactInfoForm]="contactInfoForm()"
    [isLoanEditingDisabled]="isLoanEditingDisabled()"
    (addPhone)="onAddPhone()"
  />
  <app-marital-status
    [isPrimaryBorrower]="isPrimaryBorrower()"
    [personalInfoForm]="personalInfoForm()"
    [clients]="otherClients()"
    (addSpouse)="addSpouseForThisClient()"
  />
  <app-current-address
    [currentResidenceForm]="currentResidenceForm()"
    [clientsWithForms]="otherClientsWithForms()"
    [showSameAs]="!isPrimaryBorrower()"
  />
  <app-previous-address
    [previousResidenceFormArray]="previousResidenceFormArray()"
    [isLoanEditingDisabled]="isLoanEditingDisabled()"
    (addPreviousAddress)="onAddPreviousAddress()"
  />
  <app-va-eligibility [militaryForm]="militaryForm()" />
</div>
