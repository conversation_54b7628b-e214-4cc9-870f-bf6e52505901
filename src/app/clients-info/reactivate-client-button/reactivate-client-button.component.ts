import { Component, inject, input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatMenuModule } from '@angular/material/menu';
import { Client } from '@rocket-logic/rl-xp-bff-models';
import { ClientActivationHandlerService } from '../../services/entity-state/client-state/client-activation-handler.service';
import { ClientNamePipe } from '../../util/client-name.pipe';

@Component({
  selector: 'app-reactivate-client-button',
  templateUrl: './reactivate-client-button.component.html',
  styleUrl: './reactivate-client-button.component.scss',
  standalone: true,
  imports: [MatButtonModule, MatFormFieldModule, ClientNamePipe, MatMenuModule],
})
export class ReactivateClientButtonComponent {
  private clientActivationHandlerService = inject(ClientActivationHandlerService);

  public reactivateClient(clientId: string) {
    return this.clientActivationHandlerService.reactivateClient$(clientId).subscribe();
  }

  client = input.required<Client>();
}
