import { Directive, inject, input } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { NgControl } from '@angular/forms';

/**
 * A way to connect a form control value to a reactive value.
 */
@Directive({
  standalone: true,
  selector: '[appControlValue]',
})
export class ControlValueDirective {
  private ngControl = inject(NgControl);

  appControlValue = input<any>(null);

  constructor() {
    toObservable(this.appControlValue)
      .pipe(takeUntilDestroyed())
      .subscribe((value) => {
        const control = this.ngControl.control;
        if (control && value !== control.value) {
          control.setValue(value, { emitEvent: false });
        }
      });
  }
}
