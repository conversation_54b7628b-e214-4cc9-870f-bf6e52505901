<div class="flex gap-2 items-center">
  <div [class.pointer-events-none]="preview() !== null">
    <mat-slide-toggle
      class="rkt-SlideToggle"
      color="accent"
      [formControl]="control()"
      [class.!h-0]="preview() !== null"
      [class.!w-0]="preview() !== null"
      [class.!opacity-0]="preview() !== null"
      [class.!absolute]="preview() !== null"
    >
      <span class="rkt-SlideToggle__label rkt-Spacing--ml8">{{ label() }}</span>
    </mat-slide-toggle>

    @if (preview() !== null) {
      <mat-slide-toggle
        class="rkt-SlideToggle"
        color="accent"
        [ngModel]="!!preview()"
      >
        <span class="rkt-SlideToggle__label rkt-Spacing--ml8">{{ label() }}</span>
      </mat-slide-toggle>
    }
  </div>

  <div>
    <ng-content select="[icon-slot]" />
  </div>
</div>
