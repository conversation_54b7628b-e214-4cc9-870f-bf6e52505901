<mat-form-field
  class="rkt-FormField"
  [class.pointer-events-none]="preview()"
  color="accent"
  [floatLabel]="!!preview() ? 'always' : 'auto'"
  [subscriptSizing]="subscriptSizing()"
>
  <mat-label>{{ label() }}</mat-label>
  <mat-select
    class="rkt-Input"
    [formControl]="control()"
    [class.!h-0]="preview()"
    [class.!w-0]="preview()"
    [class.!opacity-0]="preview()"
    [class.!absolute]="preview()"
    [errorStateMatcher]="errorStateMatcher()"
    [attr.data-synthetic-monitor-id]="syntheticMonitorId()"
    (selectionChange)="selectionChange.emit($event)"
  >
    <mat-option [value]="null">None</mat-option>
    @for (option of options(); track option.value) {
      <mat-option
        [value]="option.value"
        [attr.data-synthetic-monitor-id]="toOptionSyntheticMonitorId(option)"
      >{{ option.display }}</mat-option>
    }
  </mat-select>
  @if (preview(); as preview) {
    <mat-select class="rkt-Input" [value]="preview">
      <mat-option [value]="preview">{{ preview }}</mat-option>
    </mat-select>
  }

  <div class="flex items-center" matSuffix>
    <ng-content select="[form-field-suffix]"></ng-content>
  </div>

  <mat-error>{{ customError() ?? getError() }}</mat-error>
</mat-form-field>
