<mat-form-field
  class="rkt-FormField"
  color="accent"
  [floatLabel]="!!preview() ? 'always' : 'auto'"
  [class.pointer-events-none]="preview()"
>
  <mat-label>{{ label() }}</mat-label>

  @if (mask(); as mask) {
    @switch (type()) {
      @case ('number') {
        <input
          matInput
          type="number"
          class="rkt-Input"
          autocomplete="disabled"
          [formControl]="control()"
          [class.!h-0]="preview()"
          [class.!w-0]="preview()"
          [class.!opacity-0]="preview()"
          [class.!absolute]="preview()"
          [mask]="mask"
          [keepCharacterPositions]="keepCharacterPositions()"
          [errorStateMatcher]="errorStateMatcher()"
        />
      }
      @case ('tel') {
        <input
          matInput
          type="tel"
          class="rkt-Input"
          autocomplete="disabled"
          [formControl]="control()"
          [class.!h-0]="preview()"
          [class.!w-0]="preview()"
          [class.!opacity-0]="preview()"
          [class.!absolute]="preview()"
          [mask]="mask"
          [keepCharacterPositions]="keepCharacterPositions()"
          [errorStateMatcher]="errorStateMatcher()"
        />
      }
      @default {
        <input
          matInput
          class="rkt-Input"
          autocomplete="disabled"
          [formControl]="control()"
          [class.!h-0]="preview()"
          [class.!w-0]="preview()"
          [class.!opacity-0]="preview()"
          [class.!absolute]="preview()"
          [mask]="mask"
          [keepCharacterPositions]="keepCharacterPositions()"
          [errorStateMatcher]="errorStateMatcher()"
        />
      }
    }
  } @else {
    @switch (type()) {
      @case ('number') {
        <input
          matInput
          type="number"
          class="rkt-Input"
          autocomplete="disabled"
          [formControl]="control()"
          [class.!h-0]="preview()"
          [class.!w-0]="preview()"
          [class.!opacity-0]="preview()"
          [class.!absolute]="preview()"
          [errorStateMatcher]="errorStateMatcher()"
        />
      }
      @case ('tel') {
        <input
          matInput
          type="tel"
          class="rkt-Input"
          autocomplete="disabled"
          [formControl]="control()"
          [class.!h-0]="preview()"
          [class.!w-0]="preview()"
          [class.!opacity-0]="preview()"
          [class.!absolute]="preview()"
          [errorStateMatcher]="errorStateMatcher()"
        />
      }
      @default {
        <input
          matInput
          class="rkt-Input"
          autocomplete="disabled"
          [formControl]="control()"
          [class.!h-0]="preview()"
          [class.!w-0]="preview()"
          [class.!opacity-0]="preview()"
          [class.!absolute]="preview()"
          [errorStateMatcher]="errorStateMatcher()"
        />
      }
    }
  }

  @if (preview(); as preview) {
    @if (mask(); as mask) {
      <input
        matInput
        [type]="type()"
        class="rkt-Input"
        autocomplete="disabled"
        [ngModel]="preview"
        [mask]="mask"
        [keepCharacterPositions]="keepCharacterPositions()"
      />
    } @else {
      <input matInput [type]="type()" class="rkt-Input" autocomplete="disabled" [value]="preview" />
    }
  }

  <div class="flex items-center" matSuffix>
    <ng-content select="[form-field-suffix]"></ng-content>
  </div>

  <mat-error>{{ getError() }}</mat-error>
</mat-form-field>
