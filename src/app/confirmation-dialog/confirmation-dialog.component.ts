import { Component, Inject } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { LoanPurpose } from '@rocket-logic/rl-xp-bff-models';
import { environment } from "../../environments/environment";

@Component({
  selector: 'app-confirmation-dialog',
  standalone: true,
  imports: [MatButtonModule, MatDialogModule],
  templateUrl: './confirmation-dialog.component.html',
  styleUrl: './confirmation-dialog.component.scss',
})
export class ConfirmationDialogComponent {
  LoanPurpose = LoanPurpose;
  allowRefiAccess = environment.featureFlags?.allowRefiAccess

  constructor(
    @Inject(MAT_DIALOG_DATA)
    public data: {
      oldLoanPurpose: LoanPurpose;
      newLoanPurpose: LoanPurpose;
    },
  ) {}
}
