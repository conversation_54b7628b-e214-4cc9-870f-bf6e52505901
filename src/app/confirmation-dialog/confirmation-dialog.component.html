<h1 mat-dialog-title>Change to <PERSON><PERSON>urpose</h1>
<mat-dialog-content>
  <p>
    The loan purpose will be changed from
    <span class="rkt-FontWeight--700">{{ data.oldLoanPurpose }}</span> to
    <span class="rkt-FontWeight--700">{{ data.newLoanPurpose }}</span
    >.
  </p>
  @if (data.newLoanPurpose === LoanPurpose.Refinance && !allowRefiAccess) {
    <p class="rkt-FontWeight--700">
      This will remove the loan from the Rocket Logic Experience pilot and redirect you to Rocket
      Logic Banking.
    </p>
  }
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-flat-button class="rkt-Button rkt-Button--secondary" [mat-dialog-close]="false">
    Cancel
  </button>
  <button mat-flat-button class="rkt-Button" color="primary" [mat-dialog-close]="true">
    Confirm Change
  </button>
</mat-dialog-actions>
