import { Component, computed, effect, inject } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { LoanPurpose } from '@rocket-logic/rl-xp-bff-models';
import { FormSectionComponent } from '../form-section/form-section.component';
import { HugeRadioGroupComponent } from '../huge-radio-group/huge-radio-group.component';
import { CreditService } from '../services/credit/credit.service';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import { LoanFormService } from '../services/entity-state/loan-state/loan-form.service';
import { LoanStateService } from '../services/entity-state/loan-state/loan-state.service';
import { FormNavInputService } from '../services/form-nav/form-nav-input.service';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { LoanInfoSkeletonComponent } from './loan-info-skeleton/loan-info-skeleton.component';

@Component({
  selector: 'app-loan-info',
  standalone: true,
  imports: [
    FormSectionComponent,
    HugeRadioGroupComponent,
    ReactiveFormsModule,
    MatProgressSpinnerModule,
    MatSlideToggleModule,
    LoanInfoSkeletonComponent,
  ],
  templateUrl: './loan-info.component.html',
  styleUrl: './loan-info.component.scss',
})
export class LoanInfoComponent {
  loanStateService = inject(LoanStateService);
  formService = inject(LoanFormService);
  formInputNavService = inject(FormNavInputService);
  formNavSectionService = inject(FormNavSectionService);
  creditService = inject(CreditService);
  isDisabled = inject(LoanEditingState).isLoanEditingDisabled;
  readonly FormSection = FormSection;

  constructor() {
    effect(
      () => {
        if (this.hasPulledCredit()) {
          this.loanPurposeControl?.disable();
        } else if (!this.hasPulledCredit() && !this.isDisabled()) {
          this.loanPurposeControl?.enable();
        }
      },
      { allowSignalWrites: true },
    );
  }

  loanPurposeControl = this.formService.loanForm?.controls.loanPurpose;

  hasPulledCredit = computed(
    () =>
      this.creditService.creditReports()?.data?.length ||
      this.creditService.creditReports()?.fetching,
  );

  readonly loanPurposeOptions = [LoanPurpose.Purchase, LoanPurpose.Refinance].map((option) => ({
    value: option,
    display: option,
  }));
}
