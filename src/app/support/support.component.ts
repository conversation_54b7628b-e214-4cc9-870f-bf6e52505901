import { Component, OnInit, inject } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import {
  ContentComponent,
  StepPrompt,
  TrailControllerService,
  TrailDefinitionProviderService,
  TrailReference,
  provideInstanceTrailServices,
} from '@rocket-logic/support';
import { Observable, map, startWith } from 'rxjs';

@Component({
  selector: 'app-support',
  standalone: true,
  imports: [ContentComponent],
  providers: [provideInstanceTrailServices()],
  template: `
    <div class="container">
      <lib-content [prompts]="prompts()" />
    </div>
  `,
  styles: `
    :host {
      ::ng-deep {
        .mat-icon {
          color: var(--rlxp-icon-dropdown-color);
        }
        .panel-sub {
          font-weight: 700;
        }
        lib-icon-button button {
          background: var(--rlxp-red-700);
        }
        .mat-icon {
          color: var(--rlxp-white);
        }
      }
    }

    :host-context(.rkt-DarkMode) {
      ::ng-deep {
        .mat-icon {
          color: var(--rlxp-black);
        }
      }
    }

    .container {
      height: 100%;
      padding: 1rem;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      gap: 1rem;
    }
  `,
})
export class SupportComponent implements OnInit {
  private trailControllerService = inject(TrailControllerService);
  private trailDefinitionProviderService = inject(TrailDefinitionProviderService);

  private prompts$: Observable<StepPrompt[]> = this.trailControllerService.prompt$.pipe(
    map(({ promptContainer }) => promptContainer?.questions ?? []),
    startWith([]),
  );
  prompts = toSignal(this.prompts$, { requireSync: true });

  async ngOnInit() {
    const ref = new TrailReference('RLXPSupportTrail', 0, 'Rocket Logic Banking XP Support');
    const trail = await this.trailDefinitionProviderService.getTrail(ref);
    this.trailControllerService.setActiveTrail(trail);
  }
}
