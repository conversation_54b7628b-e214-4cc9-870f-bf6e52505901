import { Pipe, PipeTransform } from '@angular/core';
import { FormInput, ProductMilestoneFormInput } from '../services/form-nav/form-nav-input.service';

@Pipe({
  name: 'milestoneDescription',
  standalone: true,
})
export class MilestoneDescriptionPipe implements PipeTransform {
  transform(value: FormInput | ProductMilestoneFormInput): string {
    switch (value) {
      case FormInput.ClientFirstName:
        return 'Client - First Name';
      case FormInput.ClientLastName:
        return 'Client - Last Name';
      case FormInput.ConfirmSSN:
        return 'Client - Confirm SSN';
      case FormInput.CurrentCityAddress:
        return 'Client - Current Address City';
      case FormInput.CurrentStateAddress:
        return 'Client - Current Address State';
      case FormInput.CurrentStreetAddress:
        return 'Client - Current Address Street';
      case FormInput.CurrentZipAddress:
        return 'Client - Current Address Zip Code';
      case FormInput.DateOfBirth:
        return 'Client - Date of Birth';
      case FormInput.MaritalStatus:
        return 'Client - Marital Status';
      case FormInput.RefinanceGoals:
        return 'Refinance Goals';
      case FormInput.SSN:
        return 'Client - SSN';
      case FormInput.SubjectPropertyStateAddress:
        return 'Subject Property - State';
      case FormInput.SubjectPropertyZipAddress:
        return 'Subject Property - Zip Code';
      case ProductMilestoneFormInput.AmortizationType:
        return 'Subject Property Lien - Amortization Type';
      case ProductMilestoneFormInput.CashOutAmount:
        return 'Cash Out Amount';
      case ProductMilestoneFormInput.ClosingDate:
        return 'Closing Date';
      case ProductMilestoneFormInput.DownPayment:
        return 'Down Payment';
      case FormInput.DrawAmount:
        return 'Draw Amount';
      case ProductMilestoneFormInput.EstimatedValue:
        return 'Subject Property - Estimated Value';
      case ProductMilestoneFormInput.HoaPaymentAmount:
        return 'Subject Property - HOA Payment Amount';
      case ProductMilestoneFormInput.Income:
        return 'Income';
      case FormInput.LienPosition:
        return 'Lien Position';
      case FormInput.LoanAmount:
        return 'Loan Amount';
      case ProductMilestoneFormInput.Occupancy:
        return 'Subject Property - Occupancy';
      case ProductMilestoneFormInput.OccupancyCurrentAddress:
        return 'Client - Current Address Occupancy';
      case ProductMilestoneFormInput.OwnershipStatus:
        return 'Client - Current Address Ownership Status';
      case ProductMilestoneFormInput.PhoneNumber:
        return 'Client - Phone Number';
      case ProductMilestoneFormInput.PropertyType:
        return 'Subject Property - Property Type';
      case ProductMilestoneFormInput.PurchasePrice:
        return 'Subject Property - Purchase Price';
      case ProductMilestoneFormInput.SubjectPropertyCityAddress:
        return 'Subject Property - City';
      case ProductMilestoneFormInput.SubjectPropertyCounty:
        return 'Subject Property - County';
      case ProductMilestoneFormInput.SubjectPropertyStreetAddress:
        return 'Subject Property - Street';
      case ProductMilestoneFormInput.SubjectPropertyREO:
        return 'Subject Property - Associate a lien or mark free and clear';
      case ProductMilestoneFormInput.InternationalCredit:
        return 'International Credit - Select a value';
    }
  }
}
