import { animate, state, style, transition, trigger } from '@angular/animations';
import { CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';
import { Component, computed, effect, inject, input, OnDestroy, untracked } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatCardModule } from '@angular/material/card';
import { BehaviorSubject, delay, iif, of, switchMap } from 'rxjs';
import { AppIslandAction } from '../application-island/app-island-action-button/app-island-action-button.component';
import { FormNavInputService } from '../services/form-nav/form-nav-input.service';
import { MilestoneDescriptionPipe } from './milestone-description.pipe';

@Component({
  selector: 'app-milestone-tooltip',
  standalone: true,
  imports: [OverlayModule, MatCardModule, MilestoneDescriptionPipe],
  templateUrl: './milestone-tooltip.component.html',
  styleUrl: './milestone-tooltip.component.scss',
  animations: [
    trigger('state', [
      state('initial, void, hidden', style({ opacity: 0, transform: 'scale(0.8)' })),
      state('visible', style({ transform: 'scale(1)' })),
      transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),
      transition('* => hidden, * => void', animate('75ms cubic-bezier(0.4, 0, 1, 1)')),
    ]),
  ],
})
export class MilestoneTooltipComponent implements OnDestroy {
  private abortController?: AbortController;
  private milestoneService = inject(FormNavInputService);
  overlayOrigin = input.required<CdkOverlayOrigin>();
  activeAction = input.required<AppIslandAction>();
  isTooltipOpenSubject = new BehaviorSubject(false);
  isTooltipOpen = toSignal(
    this.isTooltipOpenSubject.pipe(
      switchMap((isOpen) => iif(() => isOpen, of(isOpen), of(isOpen).pipe(delay(50)))),
    ),
    { initialValue: false },
  );

  outstandingMilestones = computed(() => {
    switch (this.activeAction()) {
      case AppIslandAction.CreditRequest:
        return this.milestoneService.outstandingCreditMilestones();
      case AppIslandAction.GetPricing:
        return this.milestoneService.outstandingProductMilestones();
    }
  });

  constructor() {
    effect(() => {
      const overlayOrigin = this.overlayOrigin();

      untracked(() => {
        if (this.abortController) {
          this.abortController.abort();
        }

        this.abortController = new AbortController();
        overlayOrigin.elementRef.nativeElement.addEventListener(
          'mouseenter',
          () => {
            this.isTooltipOpenSubject.next(true);
          },
          {
            signal: this.abortController.signal,
          },
        );

        overlayOrigin.elementRef.nativeElement.addEventListener(
          'mouseleave',
          () => {
            this.isTooltipOpenSubject.next(false);
          },
          { signal: this.abortController.signal },
        );
      });
    });
  }

  ngOnDestroy(): void {
    this.abortController?.abort();
  }
}
