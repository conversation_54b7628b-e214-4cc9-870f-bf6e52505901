<ng-template
  cdkConnectedOverlay
  [cdkConnectedOverlayOrigin]="overlayOrigin()"
  [cdkConnectedOverlayOpen]="outstandingMilestones().length > 0 && isTooltipOpen()"
>
  <mat-card
    class="rkt-Card rkt-Elevation-6 flex flex-col"
    (mouseenter)="isTooltipOpenSubject.next(true)"
    (mouseleave)="isTooltipOpenSubject.next(false)"
    [@state]="'visible'"
  >
    <span class="rkt-Caption-12 rkt-FontWeight--500">Missing the following items:</span>
    @for (item of outstandingMilestones(); track item) {
      <span class="rkt-Caption-12">• {{item | milestoneDescription}}</span>
    }
  </mat-card>
</ng-template>
