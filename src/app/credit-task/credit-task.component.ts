import { Component, DestroyRef, computed, inject, input, signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import {
  CompleteTaskRequest,
  CompletedStatus,
  PersonalInfo,
  TaskChoice,
  TaskStatus,
} from '@rocket-logic/rl-xp-bff-models';
import { RktAlertEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { delay, filter, switchMap, take } from 'rxjs';
import {
  ActiveSidenavScreenService,
  SidenavScreen,
} from '../services/active-sidenav-screen/active-sidenav-screen.service';
import { CreditService } from '../services/credit/credit.service';
import { SelectedCreditClientService } from '../services/credit/selected-credit-client.service';
import { ClientStateService } from '../services/entity-state/client-state/client-state.service';
import { TaskStateService } from '../services/task/task-state.service';
import { ClientNamePipe } from '../util/client-name.pipe';

export interface TaskDisplayData {
  personalInformation: PersonalInfo;
  taskResolutionDetails: string;
  taskId: string;
  taskResolutionData: TaskChoice;
  clientId: string;
}

@Component({
  selector: 'app-credit-task',
  templateUrl: './credit-task.component.html',
  styleUrl: './credit-task.component.scss',
  standalone: true,
  imports: [
    RktAlertEnterpriseModule,
    ClientNamePipe,
    MatIconModule,
    MatProgressSpinnerModule,
    MatIconModule,
    MatButtonModule,
  ],
})
export class CreditTaskComponent {
  private creditService = inject(CreditService);
  private taskService = inject(TaskStateService);
  private clientService = inject(ClientStateService);
  private selectedCreditClientService = inject(SelectedCreditClientService);
  private activeActionService = inject(ActiveSidenavScreenService);

  destroyRef = inject(DestroyRef);

  taskInfo = input.required<TaskDisplayData>();

  public isCompletingCreditTask = signal(false);
  creditPullType = toSignal(this.creditService.creditPullType$);

  private creditTasks$ = toObservable(this.creditService.creditTasks);

  disableTaskButtons = computed(
    () => this.creditService.isPullingCredit() || this.isCompletingCreditTask(),
  );

  pullOrRepullPrompt = computed(() =>
    this.taskInfo().taskResolutionData === undefined ? 'Pull' : 'Repull',
  );

  completeTask(taskid: string, taskResolutionData: any) {
    this.isCompletingCreditTask.set(true);
    const completedTaskRequest = {
      status: CompletedStatus.Succeeded,
      data: taskResolutionData,
    } as CompleteTaskRequest;
    this.taskService
      .completeTask$(taskid, completedTaskRequest)
      .pipe(
        take(1),
        switchMap(() => {
          return this.creditTasks$.pipe(
            filter(
              (polledTasks) =>
                polledTasks?.find((task) => task.taskId === taskid)?.status !== TaskStatus.Created,
            ),
            take(1),
            // Tasks can complete before the update to RLAPI is completed
            delay(1000),
          );
        }),
      )
      .subscribe(() => {
        this.clientService.refreshState();
        this.isCompletingCreditTask.set(false);
      });
  }

  openCrmScreen(clientId: string) {
    this.selectedCreditClientService.setSelectedClientGroup(clientId);
    this.activeActionService.activate(SidenavScreen.CreditReportManager);
  }
}
