<div>
  <div class="mb-6">
    The following information for client <b>{{ taskInfo() | clientName }}</b> does not match any
    credit report on file. Please {{ taskInfo().taskResolutionDetails }}
  </div>
  <div class="task-button-container">
    @if (taskInfo().taskResolutionData !== undefined) {
      <button
        class="rkt-Button rkt-Button--has-icon rkt-Button--secondary"
        mat-flat-button
        [disabled]="disableTaskButtons()"
        [class.rkt-Button--is-disabled]="disableTaskButtons()"
        [class.rkt-Button--is-spinning]="isCompletingCreditTask()"
        (click)="completeTask(taskInfo().taskId, taskInfo().taskResolutionData)"
      >
        @if (isCompletingCreditTask()) {
          <mat-icon class="rkt-Icon rkt-Spacing--mr8">
            <mat-spinner role="presentation" class="rkt-Spinner" diameter="20"></mat-spinner>
          </mat-icon>
        }
        Update Client Info
      </button>
    }
    <button
      class="rkt-Button rkt-Button--secondary"
      id="request-credit-button"
      mat-flat-button
      [disabled]="disableTaskButtons()"
      [class.rkt-Button--is-disabled]="disableTaskButtons()"
      (click)="openCrmScreen(taskInfo().clientId!)"
    >
      {{ pullOrRepullPrompt() }}
      {{ this.creditPullType()?.creditPullType }} Credit
    </button>
  </div>
</div>
