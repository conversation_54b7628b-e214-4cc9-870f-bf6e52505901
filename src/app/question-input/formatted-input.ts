import { Directive, ElementRef, inject } from '@angular/core';
import { ControlValueAccessor, NgControl } from '@angular/forms';

@Directive()
// eslint-disable-next-line @angular-eslint/directive-class-suffix
export abstract class FormattedInput implements ControlValueAccessor {
  elementRef = inject(ElementRef<HTMLInputElement>);

  onChange: (value: unknown) => void = () => { };
  onTouched = () => { };

  protected ngControl = inject(NgControl, { optional: true, self: true });

  get value(): any {
    return this.displayToValue(this.elementRef.nativeElement.value ?? '');
  }
  set value(value: any) {
    this.elementRef.nativeElement.value = this.formatValue(value?.toString() ?? '');
  }

  constructor() {
    if (this.ngControl) {
      this.ngControl.valueAccessor = this;
    }
  }

  writeValue(value: any): void {
    this.value = value;
  }

  registerOnChange(fn: (value: any) => void): void {
    if (fn) {
      this.onChange = fn;
    }
  }

  registerOnTouched(fn: () => void): void {
    if (fn) {
      this.onTouched = fn;
    }
  }

  processInput(input: HTMLInputElement) {
    const formattedValue = this.formatValue(input.value);
    const caretPosition = this.getUpdatedCaretPosition(
      input,
      input?.selectionStart ?? 0,
      formattedValue,
    );

    input.value = formattedValue;
    this.onChange(this.value);

    input.setSelectionRange(caretPosition, caretPosition);
  }

  abstract formatValue(inputString: string): string;
  abstract displayToValue(displayString: string): any;
  abstract getUpdatedCaretPosition(
    input: HTMLInputElement,
    currentPosition: number,
    formattedValue: string,
  ): number;
}