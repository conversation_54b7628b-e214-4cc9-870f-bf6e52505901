<mat-form-field [class.pointer-events-none]="preview()" [subscriptSizing]="subscriptSizing()" class="rkt-FormField" [floatLabel]="!!preview() ? 'always' : 'auto'">
  @if (label()) {
    <mat-label>{{ label() }}</mat-label>
  }
  @if (prefix()) {
    <span matTextPrefix>{{ prefix() }}&nbsp;</span>
  }
  <input
    class="rkt-Input"
    [formControl]="control()"
    matInput
    #input
    autocomplete="off"
    [errorStateMatcher]="errorStateMatcher()"
    [attr.aria-label]="ariaLabel()"
    appFormattedNumberInput
    [allowNegative]="allowNegative()"
    [maxLength]="maxLength()"
    [decimalLimit]="decimalLimit()"
    [class.!h-0]="preview()"
    [class.!w-0]="preview()"
    [class.!opacity-0]="preview()"
  />

  @if (preview(); as preview) {
    <input
      matInput
      class="rkt-Input"
      autocomplete="disabled"
      [value]="preview"
      appFormattedNumberInput
      [allowNegative]="allowNegative()"
      [maxLength]="maxLength()"
      [decimalLimit]="decimalLimit()"
    />
  }

  @if (suffix()) {
    <span matTextSuffix>{{ suffix() }}</span>
  }
  <div class="flex items-center" matSuffix>
    <ng-content select="[form-field-suffix]"></ng-content>
  </div>
  <mat-error>{{ getErrorMessage() }}</mat-error>
</mat-form-field>
