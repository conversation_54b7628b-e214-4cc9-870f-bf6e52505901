import { Directive, input } from '@angular/core';
import { MAT_INPUT_VALUE_ACCESSOR } from '@angular/material/input';
import { FormattedInput } from '../formatted-input';

@Directive({
  selector: 'input[appFormattedNumberInput]',
  standalone: true,
  providers: [{ provide: MAT_INPUT_VALUE_ACCESSOR, useExisting: FormattedNumberInputDirective }],
  host: {
    '(input)': 'processInput($event.target)',
    '(focus)': 'onFocus()',
  },
})
export class FormattedNumberInputDirective extends FormattedInput {
  displayToValue(displayString: string): number | null {
    const value = parseFloat(this.stripNumberString(displayString));
    return isNaN(value) ? null : value;
  }

  allowNegative = input<boolean>(false);
  maxLength = input<number>();
  decimalLimit = input<number>(2);

  onFocus() {
    this.onTouched?.();
  }

  getUpdatedCaretPosition(
    input: HTMLInputElement,
    currentPosition: number,
    formattedValue: string,
  ): number {
    return currentPosition + (formattedValue.length - input.value.length);
  }

  formatValue(inputString: string): string {
    const strippedValue = this.stripNumberString(inputString);
    let [whole, decimal] = strippedValue.split('.');

    if (!whole && decimal) {
      whole = '0';
    }

    if (this.maxLength() && whole.length > (this.maxLength() ?? 0)) {
      whole = whole.substr(0, this.maxLength());
    }

    if (this.decimalLimit() && decimal && decimal.length > (this.decimalLimit() ?? 0)) {
      decimal = decimal.substr(0, this.decimalLimit());
    }

    return whole === '-'
      ? whole
      : (whole ? parseFloat(whole).toLocaleString() : '') +
      (typeof decimal === 'string' ? '.' + decimal : '');
  }

  private stripNumberString(numberString: string): string {
    const stripRegex = this.allowNegative()
      ? new RegExp(/[^0-9.-]/, 'g')
      : new RegExp(/[^0-9.]/, 'g');
    return numberString.replace(stripRegex, '').replace(/(\..*)\./g, '$1');
  }
}
