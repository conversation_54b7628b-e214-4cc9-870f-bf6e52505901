import { Component, computed, forwardRef, input, signal, viewChild } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatFormField, MatFormFieldModule, SubscriptSizing } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { CONTROL_PROVIDER, ControlProvider } from '../../_shared/tokens/control-provider';
import { FIELD_VALUE_PREVIEWER, FieldValuePreviewer } from '../../_shared/tokens/field-value-previewer';
import {
  MAT_FORM_FIELD_PROVIDER,
  MatFormFieldProvider,
} from '../../_shared/tokens/mat-form-field-provider';
import {
  ProductMilestoneFormInput
} from '../../services/form-nav/form-nav-input.service';
import { UntouchedErrorStateMatcher } from '../../util/error-state-matchers/untouched-error-state-matcher';
import { getErrorMessage } from '../../util/get-error-message';
import { FormattedNumberInputDirective } from './formatted-number-input.directive';

@Component({
  selector: 'app-formatted-number-input',
  standalone: true,
  imports: [
    MatFormFieldModule,
    ReactiveFormsModule,
    MatInputModule,
    FormattedNumberInputDirective,
    FormsModule,
  ],
  templateUrl: './formatted-number-input.component.html',
  styleUrl: './formatted-number-input.component.scss',
  providers: [
    {
      provide: CONTROL_PROVIDER,
      useExisting: forwardRef(() => FormattedNumberInputComponent),
    },
    {
      provide: MAT_FORM_FIELD_PROVIDER,
      useExisting: forwardRef(() => FormattedNumberInputComponent),
    },
    {
      provide: FIELD_VALUE_PREVIEWER,
      useExisting: forwardRef(() => FormattedNumberInputComponent),
    }
  ],
  host: {
    '[class.cursor-pointer]': "preview()"
  },
})
export class FormattedNumberInputComponent implements MatFormFieldProvider, ControlProvider, FieldValuePreviewer {
  matFormField = viewChild(MatFormField);

  prefix = input<string>('');
  label = input<string>('');
  ariaLabel = input<string>('');
  suffix = input<string>('');
  allowNegative = input.required<boolean>();
  maxLength = input<number>();
  decimalLimit = input<number>(2);
  subscriptSizing = input<SubscriptSizing>('fixed');
  control = input.required<FormControl<number | null | undefined>>();
  showUntouchedError = input<boolean>(false);

  preview = signal<string | null>(null);

  readonly ProductMilestoneFormInput = ProductMilestoneFormInput;
  errorStateMatcher = computed(() => new UntouchedErrorStateMatcher(this.showUntouchedError()));

  getErrorMessage(): string {
    return getErrorMessage(this.control(), [
      [
        'min',
        (minError) =>
          `Field must be at least ${this.prefix() + minError.min.toLocaleString() + this.suffix()}`,
      ],
      [
        'max',
        (maxError) =>
          `Field must be at most ${this.prefix() + maxError.max.toLocaleString() + this.suffix()}`,
      ],
    ]);
  }
}
