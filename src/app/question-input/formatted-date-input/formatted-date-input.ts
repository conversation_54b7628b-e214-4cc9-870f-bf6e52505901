import { Directive, inject } from '@angular/core';
import { MatDatepickerInput } from '@angular/material/datepicker';

@Directive({
  selector: 'input[appFormattedDateInput]',
  standalone: true,
  providers: [],
  host: {
    '(input)': 'processInput($event, $event.target)',
  },
})
// eslint-disable-next-line @angular-eslint/directive-class-suffix
export class FormattedDateInputDirective {
  matDatePickerInput = inject(MatDatepickerInput);

  processInput(event: any, input: HTMLInputElement) {
    if (event.inputType !== 'deleteContentBackward') {
      input.value = this.formatValue(input.value);
    }
  }

  formatValue(inputString: string): string {
    const dateList = inputString.split('/').slice(0, 3);

    if (dateList.length > 0 && dateList.length < 3) {
      const endValue = dateList[dateList.length - 1];
      if (endValue.length > 2) {
        dateList[dateList.length - 1] = endValue.slice(0, 2);
        dateList[dateList.length] = endValue.slice(2, 4); // Update to take a maximum of 4 digits for the year
      } else if (endValue.length === 2) {
        dateList[dateList.length] = '';
      }
    }

    // Limit the year to a maximum of 4 digits
    if (dateList.length === 3) {
      dateList[2] = dateList[2].slice(0, 4);
    }

    // Limit the month to a maximum of 12
    if (dateList.length >= 1) {
      const month = parseInt(dateList[0], 10);
      if (month > 12) {
        dateList[0] = '12';
      }
    }

    if (dateList.length >= 2) {
      const day = parseInt(dateList[1], 10);
      if (day > 31) {
        dateList[1] = '31';
      }
    }
    const date = dateList.join('/');
    this.matDatePickerInput._onInput(date);
    return date;
  }
}
