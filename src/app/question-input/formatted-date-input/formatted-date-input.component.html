<mat-form-field [class.pointer-events-none]="preview()" class="rkt-FormField" color="accent" [floatLabel]="!!preview() ? 'always' : 'auto'">
  <mat-label> {{ label() }}</mat-label>

  <input
    matInput
    [matDatepicker]="picker"
    class="rkt-Input"
    [formControl]="control()"
    appFormattedDateInput
    [min]="minimumDate()"
    [max]="maximumDate()"
    [errorStateMatcher]="errorStateMatcher()"
    [class.!h-0]="preview()"
    [class.!w-0]="preview()"
    [class.!opacity-0]="preview()"
  />

  @if (preview(); as preview) {
    <input
      matInput
      class="rkt-Input"
      [value]="preview"
      appFormattedDateInput
      [matDatepicker]="picker"
    />
  }

  <div class="flex items-center" matSuffix>
    <mat-datepicker-toggle [for]="picker">
      <mat-icon
        matDatepickerToggleIcon
        svgIcon="calendar_month-outlined"
        class="rkt-Color--gray-600"
      ></mat-icon>
    </mat-datepicker-toggle>

    <ng-content select="[form-field-suffix]"></ng-content>
  </div>

  <mat-datepicker #picker panelClass="rkt-Datepicker"></mat-datepicker>
  <mat-error>{{ getErrorMessage() }}</mat-error>
</mat-form-field>
