import { formatDate } from '@angular/common';
import { Component, Injector, forwardRef, inject, input, signal, viewChild } from '@angular/core';
import { FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ErrorStateMatcher, MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatFormField, MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { CONTROL_PROVIDER, ControlProvider } from '../../_shared/tokens/control-provider';
import { FIELD_VALUE_PREVIEWER, FieldValuePreviewer } from '../../_shared/tokens/field-value-previewer';
import { MAT_FORM_FIELD_PROVIDER, MatFormFieldProvider } from '../../_shared/tokens/mat-form-field-provider';
import { UntouchedErrorStateMatcher } from '../../util/error-state-matchers/untouched-error-state-matcher';
import { getErrorMessage } from '../../util/get-error-message';
import { FormattedDateInputDirective } from './formatted-date-input';

@Component({
  selector: 'app-formatted-date-input',
  standalone: true,
  imports: [
    MatFormFieldModule,
    MatInputModule,
    MatDatepickerModule,
    MatIconModule,
    ReactiveFormsModule,
    FormattedDateInputDirective,
    MatNativeDateModule,
    FormsModule,
  ],
  templateUrl: './formatted-date-input.component.html',
  styleUrl: './formatted-date-input.component.scss',
  providers: [
    {
      provide: CONTROL_PROVIDER,
      useExisting: forwardRef(() => FormattedDateInputComponent),
    },
    {
      provide: MAT_FORM_FIELD_PROVIDER,
      useExisting: forwardRef(() => FormattedDateInputComponent),
    },
    {
      provide: FIELD_VALUE_PREVIEWER,
      useExisting: forwardRef(() => FormattedDateInputComponent),
    }
  ],
  host: {
    '[class.cursor-pointer]': "preview()"
  },
})
export class FormattedDateInputComponent implements ControlProvider, MatFormFieldProvider, FieldValuePreviewer {
  matFormField = viewChild(MatFormField);

  label = input<string>('');
  minimumDate = input<Date>(new Date('01/01/1900'));
  maximumDate = input<Date>();
  control = input.required<FormControl<Date | string | null | undefined>>();
  injector = inject(Injector);
  errorStateMatcher = input<ErrorStateMatcher>(new UntouchedErrorStateMatcher());

  preview = signal<string | null>(null);

  getErrorMessage(): string {
    return getErrorMessage(this.control(), [
      [
        'matDatepickerMin',
        (minError) => `Earliest Date Allowed: ${formatDate(minError.min, 'MM/dd/yyyy', 'en-US')}`,
      ],
      [
        'matDatepickerMax',
        (maxError) => `Latest Date Allowed: ${formatDate(maxError.max, 'MM/dd/yyyy', 'en-US')}`,
      ],
    ]);
  }
}
