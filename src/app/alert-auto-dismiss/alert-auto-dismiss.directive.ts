import { DestroyRef, Directive, InjectionToken, OnInit, inject, input } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { RktAlertEnterpriseComponent } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { NEVER, combineLatest, switchMap, timer } from 'rxjs';

export const ALERT_DISMISS_DELAY = new InjectionToken<number>('ALERT_DISMISS_DELAY');

@Directive({
  selector: 'rkt-alert-enterprise[appAlertAutoDismiss]',
  standalone: true,
})
export class AlertAutoDismissDirective implements OnInit {
  destroyRef = inject(DestroyRef);
  host = inject(RktAlertEnterpriseComponent);
  dismissDelay = input(inject(ALERT_DISMISS_DELAY, { optional: true }) ?? 5000);
  isDismissible = input(true);
  autoDismiss = input(true);
  private isDismissible$ = toObservable(this.isDismissible);
  private autoDismiss$ = toObservable(this.autoDismiss);

  ngOnInit() {
    combineLatest([this.isDismissible$, this.autoDismiss$])
      .pipe(
        switchMap(([isDismissible, autoDismiss]) => {
          if (!(isDismissible && autoDismiss)) {
            return NEVER;
          }
          return timer(this.dismissDelay());
        }),
        takeUntilDestroyed(this.destroyRef),
      )
      .subscribe(() => {
        this.host.alertDismiss();
      });
  }
}
