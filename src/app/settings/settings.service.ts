import { effect, Injectable, signal, WritableSignal } from '@angular/core';
import { FormSection } from '../services/form-nav/form-nav-section.service';

@Injectable({ providedIn: 'root' })
export class SettingsService {
    /**
     * Default order of form sections
     */
    defaultformSectionOrder: FormSection[] = [
        FormSection.LoanInfo,
        FormSection.ClientInfo,
        FormSection.PurchaseInfo,
        FormSection.RefinanceInfo,
        FormSection.SubjectProperty,
        FormSection.Credit,
        FormSection.Assets,
        FormSection.Income,
        FormSection.REO,
    ];


    /**
     * Sections not shown in current view. typically conditional sections like REO.
     */
    visibleSections = new Set<FormSection>();

    /*
    * Signal representing the order of form sections
    */
    formSectionOrder: WritableSignal<FormSection[]> = signal(
        this.loadFormSectionOrder() ?? this.defaultformSectionOrder
    );

    /*
    * Effect to save formSectionOrder to localStorage when it changes
    */
    saveToStorageEffect = effect(() => {
        const currentOrder = JSON.stringify(this.formSectionOrder());
        const storedOrder = localStorage.getItem('RLXP_Form_Section_Order');

        if (currentOrder !== storedOrder) {
            localStorage.setItem('RLXP_Form_Section_Order', currentOrder);
        }
    });

    /*
    * Load formSectionOrder from localStorage
    */
    loadFormSectionOrder(): FormSection[] | null {
        try {
            const storedValue = localStorage.getItem('RLXP_Form_Section_Order');
            if (storedValue) {
                const parsed = JSON.parse(storedValue) as FormSection[];
                if (Array.isArray(parsed) && parsed.every((item) => Object.values(FormSection).includes(item))) {
                    return parsed;
                }
            }
        } catch (error) {
            console.warn('Error loading formSectionOrder from localStorage:', error);
        }
        return null;
    }

    /*
    * Reset formSectionOrder to default
    */
    resetFormSectionOrder() {
        this.formSectionOrder.set([...this.defaultformSectionOrder]);
    }

    /*
     * Clear formSectionOrder from localStorage
     */
    clearFormSectionOrder() {
        localStorage.removeItem('RLXP_Form_Section_Order');
    }

    /*
    * Update formSectionOrder
    */
    updateFormSectionOrder(newOrder: FormSection[]) {
        this.formSectionOrder.set([...newOrder]); // Trigger reactivity with a new reference
    }
}
