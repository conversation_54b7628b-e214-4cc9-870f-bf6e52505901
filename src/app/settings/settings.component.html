<div class="flex flex-col settings-container">
  <span class="rkt-Label-16 rkt-FontWeight--700 mt-1 mb-1">Settings</span>
  <div class="flex flex-col gap-2 pb-3 pt-3">
    <span class="uppercase rkt-Tracked-12 rkt-FontWeight--500 pl-2">Appearance</span>
    <mat-card class="rkt-Card rkt-Card--enterprise settings-card">
      <div class="flex items-center justify-between">
        <span class="rkt-Label-14 rkt-FontWeight--400">Light Mode</span>
        <rkt-theme-toggle
          [isDark]="darkModeService.isDarkMode()"
          (onToggleChangeEvent)="
            darkModeService.darkModePreference.set(
              $event ? DarkModePreference.Dark : DarkModePreference.Light
            )
          "
        />
      </div>
    </mat-card>
  </div>

  <mat-divider class="rkt-HorizontalDivider" />

  <mat-expansion-panel
    class="flex-shrink-0"
    [expanded]="isScrollablePanelExpanded()"
    (expandedChange)="isScrollablePanelExpanded.set($event)"
    [class.expanded]="isScrollablePanelExpanded()"
  >
    <mat-expansion-panel-header>
      <mat-panel-title class="rkt-Tracked-12 rkt-FontWeight--500 uppercase">Form Order</mat-panel-title>
    </mat-expansion-panel-header>

    <div
      class="flex flex-col flex-auto w-full h-full gap-2"
      cdkDropList
      (cdkDropListDropped)="drop($event)"
    >
      @for (sectionName of sectionNames; track sectionName) {
        <mat-card class="rkt-Card rkt-Card--enterprise settings-card" cdkDrag>
          <div class="flex items-center gap-2">
            <mat-icon class="rkt-Icon">drag_handle</mat-icon>
            <span class="rkt-Label-14 rkt-FontWeight--400">{{sectionName | pascalCaseSplit}}</span>
          </div>
        </mat-card>
      }
    </div>
  </mat-expansion-panel>
</div>
