mat-expansion-panel {
  background-color: transparent;
  margin: 0 -1rem; // Need to offset padding
  --mat-expansion-header-indicator-color: var(--Mapped-Text-text-primary);
}

.mat-expansion-panel:not([class*='mat-elevation-z']) {
  box-shadow: none;
}

.cdk-drag {
  touch-action: none; /* Disables default touch behaviors during drag */
}

.static-panel {
  min-height: 50px;
}

.static-panel.expanded {
  min-height: 150px;
  transition: all 250ms ease-out;
}

.cdk-drag-preview {
  border: none;
  box-sizing: border-box;
  box-shadow:
    0 5px 5px -3px rgba(0, 0, 0, 0.2),
    0 8px 10px 1px rgba(0, 0, 0, 0.14),
    0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.settings-container {
  width: 375px;
  padding: 1rem;
  gap: 18px;
  --mat-expansion-header-expanded-state-height: 48px;
}

mat-card.mat-mdc-card.settings-card.rkt-Card {
  background-color: var(--rlxp-settings-card-background-color);
}