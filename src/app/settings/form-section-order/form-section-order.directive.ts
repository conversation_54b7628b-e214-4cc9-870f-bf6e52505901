import { Directive, effect, ElementRef, inject, input, <PERSON><PERSON><PERSON><PERSON>, OnInit, Renderer2 } from '@angular/core';
import { FormSection } from '../../services/form-nav/form-nav-section.service';
import { SettingsService } from '../settings.service';

@Directive({
    selector: '[appFormSectionOrder]',
    standalone: true,
})
export class FormSectionOrderDirective implements OnInit, OnDestroy {
    private el = inject(ElementRef);
    private renderer = inject(Renderer2);
    private settings = inject(SettingsService);

    formSection = input.required<FormSection>();

    ngOnInit() {
        this.settings.visibleSections.add(this.formSection());
    }

    // Reactively update the order whenever `name` or `formSectionOrder()` changes
    changeOrder = effect(() => {
        const section: FormSection = this.formSection();
        if (this.formSection()) {
            const order = this.settings.formSectionOrder().indexOf(section);
            this.renderer.setStyle(this.el.nativeElement, 'order', order);
        }
    });

    ngOnDestroy() {
        this.settings.visibleSections.delete(this.formSection());
    }
}
