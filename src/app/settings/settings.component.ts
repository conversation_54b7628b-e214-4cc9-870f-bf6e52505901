import {
  CdkDrag,
  CdkDragDrop,
  CdkDropList,
  DragDropModule,
  moveItemInArray,
} from '@angular/cdk/drag-drop';
import { Component, effect, inject, signal } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatDialogContent, MatDialogTitle } from '@angular/material/dialog';
import { MatDividerModule } from '@angular/material/divider';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { RktThemeToggleModule } from '@rocketcentral/rocket-design-system-angular';
import { DarkModePreference, DarkModeService } from '../services/dark-mode/dark-mode.service';
import { PascalCaseSplitPipe } from '../util/pascal-case-split.pipe';
import { SettingsService } from './settings.service';

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [
    MatDialogTitle,
    MatDialogContent,
    MatSelectModule,
    MatFormFieldModule,
    ReactiveFormsModule,
    RktThemeToggleModule,
    DragDropModule,
    MatCardModule,
    MatIconModule,
    MatExpansionModule,
    CdkDropList,
    CdkDrag,
    PascalCaseSplitPipe,
    MatDividerModule,
  ],
  providers: [],
  templateUrl: './settings.component.html',
  styleUrl: './settings.component.scss',
})
export class SettingsComponent {
  isBasicPanelExpanded = signal(true);
  isScrollablePanelExpanded = signal(true);
  settings = inject(SettingsService);
  sectionNames = this.settings.formSectionOrder();

  formOrderChange = effect(() => {
    this.sectionNames = this.settings.formSectionOrder();
  });

  public darkModeService = inject(DarkModeService);
  readonly DarkModePreference = DarkModePreference;

  drop(event: CdkDragDrop<string[]>) {
    moveItemInArray(this.sectionNames, event.previousIndex, event.currentIndex);
    this.settings.formSectionOrder.set([...this.sectionNames]);
  }
}
