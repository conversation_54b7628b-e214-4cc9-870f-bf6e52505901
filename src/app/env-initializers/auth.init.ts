import { inject } from "@angular/core";
import { AuthService } from '@auth0/auth0-angular';
import { EMPTY, switchMap, take, tap } from "rxjs";

export const authInitFn = () => {
    const auth = inject(AuthService);
    auth.isAuthenticated$
      .pipe(
        take(1),
        tap((isAuthenticated) => {
          if (!isAuthenticated) {
            auth.loginWithRedirect({
              appState: { postAuthNavigation: window.location.href },
            });
          }
        }),
        switchMap((isAuthenticated) => {
          if (!isAuthenticated) {
            return EMPTY;
          }
          return auth.appState$.pipe(take(1));
        }),
      )
      .subscribe((state) => {
        if (state.postAuthNavigation) {
          window.location.assign(state.postAuthNavigation);
        }
      });
};