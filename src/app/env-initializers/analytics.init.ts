import { LogLevel } from '@amplitude/analytics-types';
import ampSRSegmentWrapper from '@amplitude/segment-session-replay-wrapper';
import { inject, NgZone } from '@angular/core';
import { AuthService, User } from '@auth0/auth0-angular';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { AnalyticsBrowser } from '@segment/analytics-next';
import * as Inspector from 'avo-inspector';
import { filter, take } from 'rxjs';
import Avo, { AvoEnv, CustomDestination } from '../../../src/Avo';
import { environment } from '../../environments/environment';
import { AmplitudeLoggerService } from '../analytics/amplitude-logger.service';

const USERNAME = 'https://ql.custom.openid.com/samaccountname';
const COMMONID = 'https://ql.custom.openid.com/common_id';
const ROCK_HUMAN_ID = 'https://ql.custom.openid.com/rock_human_id';

export const analyticsInitFn = () => {
  const auth = inject(AuthService);
  const analytics = inject(AnalyticsBrowser, { optional: true });
  const amplitudeLogger = inject(AmplitudeLoggerService);
  const zone = inject(NgZone);
  const logger = inject(SplunkLoggerService);

  try {
    zone.runOutsideAngular(() => {
      const destinationInterface: CustomDestination = {
        identify: (userId: string) => analytics?.identify(userId),

        logEvent: (eventName: string, eventProperties: any) =>
          analytics?.track(eventName, eventProperties),

        setUserProperties: (userId: string, userProperties: any) =>
          analytics?.identify({ userId, traits: userProperties }),
      };

      let envForAvo = AvoEnv.Dev;
      if (environment.appEnvironment === 'prod') {
        envForAvo = AvoEnv.Prod;
      }

      const inspector = new Inspector.AvoInspector({
        apiKey: Avo.avoInspectorApiKey,
        env: envForAvo,
        version: '1.0.0',
        appName: 'RL-XP',
        suffix: 'rlxp', // optional, if you have more than 1 instance of Avo Inspector in same project
      });

      Avo.initAvo(
        { env: envForAvo, inspector, webDebugger: environment.avoWebDebugger },
        {
          appVersion: window.navigator.userAgent,
          deviceType: window.navigator.platform,
          browserVersion: window.navigator.userAgent,
          osVersion: window.navigator.userAgent,
        },
        {},
        destinationInterface,
      );

      if (analytics && environment.amplitudeApiKey) {
        ampSRSegmentWrapper({
          amplitudeApiKey: environment.amplitudeApiKey,
          segmentInstance: analytics,
          sessionReplayOptions: {
            sampleRate: 1,
            optOut: false,
            loggerProvider: amplitudeLogger,
            logLevel: LogLevel.Warn,
          },
        });
      }

      if (analytics) {
        auth.user$
          .pipe(
            filter((user): user is User => !!user),
            take(1),
          )
          .subscribe((user) => {
            const id = user[COMMONID] ?? user[ROCK_HUMAN_ID] ?? user.sub;
            analytics.identify(id, {
              name: user.name,
              email: user.email,
              username: user[USERNAME],
            });
          });
      }
    });
  } catch (err) {
    logger.error('Failed to initialize analytics', err as Error);
  }
};
