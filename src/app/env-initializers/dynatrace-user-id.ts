import { inject } from "@angular/core";
import { AuthService, User } from "@auth0/auth0-angular";
import { filter, take } from "rxjs";

const COMMONID = 'https://ql.custom.openid.com/common_id';
const ROCK_HUMAN_ID = 'https://ql.custom.openid.com/rock_human_id';

export const dynatraceUserSessionInitFn = () => {
  const auth = inject(AuthService);
  auth.user$
      .pipe(
        filter((user): user is User => !!user),
        take(1),
      )
      .subscribe((user) => {
        const id = user[COMMONID] ?? user[ROCK_HUMAN_ID] ?? user.sub;
        !!id && window.dtrum?.identifyUser(id);
      });
}