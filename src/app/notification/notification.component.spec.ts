import { ComponentFixture, TestBed } from '@angular/core/testing';

import { NotificationComponent } from './notification.component';
import { LoanIdService } from '../services/loan-id/loan-id.service';
import { MockBuilder } from 'ng-mocks';
import { AuthService } from '@auth0/auth0-angular';
import { NEVER } from 'rxjs';

describe('NotificationComponent', () => {
  let component: NotificationComponent;
  let fixture: ComponentFixture<NotificationComponent>;

  beforeEach(() => MockBuilder(NotificationComponent)
    .mock(LoanIdService, {loanId$: NEVER})
    .mock(AuthService));
  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [NotificationComponent]
    })
    .compileComponents();

    fixture = TestBed.createComponent(NotificationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
