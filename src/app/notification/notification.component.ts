import { Component, computed, inject } from '@angular/core';
import { LoanIdService } from '../services/loan-id/loan-id.service';
import { NotificationService } from '../services/notification/notification.service';
import { NEVER, filter, scan, switchMap } from 'rxjs';
import { EventSourceMessage } from '@microsoft/fetch-event-source';
import { toSignal } from '@angular/core/rxjs-interop';

@Component({
  selector: 'app-notification',
  standalone: true,
  templateUrl: './notification.component.html',
  styleUrl: './notification.component.scss',
})
export class NotificationComponent {
  protected loanIdService = inject(LoanIdService);
  protected notificationService = inject(NotificationService);

  messages = toSignal(
    this.loanIdService.loanId$.pipe(
      switchMap((loanId) => loanId ?
        this.notificationService.connectTo(loanId) :
        NEVER
      ),
      filter(message => !!message.event),
      scan((a, c) => [...a, c], [] as EventSourceMessage[])
    ),
  );
  messageCount = computed(() => this.messages()?.length || 'nothing')
}
