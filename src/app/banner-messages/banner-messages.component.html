@if (activeMessage()) {
  <app-banner-message
    [subject]="activeMessage().subject"
    [content]="activeMessage().content"
    [severity]="activeMessage().messageSeverity"
  >
    @if (bannerMessages().length > 1) {
      <div class="flex items-center gap-4">
        <span class="rkt-Label-14"
          >{{ messageNumber() }} of {{ bannerMessages().length }}</span
        >
        <div>
          <button
            mat-stroked-button
            class="rkt-Button rkt-Button--secondary rkt-Button--large rkt-Button--has-icon"
            [class.rkt-Button--is-disabled]="messageNumber() === 1"
            [disabled]="messageNumber() === 1"
            color="accent"
            (click)="back()"
          >
            <mat-icon class="rkt-Icon" svgIcon="arrow_back-outlined"></mat-icon>
          </button>
          <button
            mat-stroked-button
            class="rkt-Button rkt-Button--secondary rkt-Button--large rkt-Button--has-icon"
            [class.rkt-Button--is-disabled]="messageNumber() === bannerMessages().length"
            [disabled]="messageNumber() === bannerMessages().length"
            color="accent"
            (click)="next()"
          >
            <mat-icon class="rkt-Icon" svgIcon="arrow_forward-outlined"></mat-icon>
          </button>
        </div>
      </div>
    }
  </app-banner-message>
}
