button.rkt-Button.rkt-Button--secondary.mdc-button.mdc-button--outlined {
  min-width: unset;
  height: 48px;
  width: 48px;
  padding: 0;
  border-radius: 4px;
  
  &:not(:hover) {
    background-color: var(--rlxp-application-background-color);
  }

  mat-icon.mat-icon {
    margin: 0;
  }
  &:first-of-type {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right-width: 0;
  }
  &:last-of-type {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}