<rkt-alert-enterprise class="banner-message" [variant]="alertVariant()" [isDismissible]="false">
  <div class="flex-1 w-0">
    <div class="rkt-Alert__heading">
      {{subject()}}
    </div>
    <div class="rkt-Alert__content" >
      <div #contentElement class="overflow-hidden text-ellipsis" [class.text-nowrap]="!expanded()">
        {{content()}}
      </div>
      @if (!expanded() && textOverflow()) {
        <button (click)="expand()" rktLinkEnterprise class="rkt-Label-14 text-nowrap">Show More</button>
      }
    </div>
  </div>
  @if (expanded()) {
    <button mat-icon-button class="rkt-ButtonIcon" (click)="collapse()">
      <mat-icon class="rkt-Icon" svgIcon="expand_less-outlined"></mat-icon>
    </button>
  } @else {
    <ng-content></ng-content>
  }
</rkt-alert-enterprise>