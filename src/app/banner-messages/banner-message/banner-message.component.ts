import {
  Component,
  computed,
  ElementRef,
  HostListener,
  inject,
  input,
  NgZone,
  OnInit,
  signal,
  viewChild,
} from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MessageSeverity } from '@rocket-logic/rl-xp-bff-models';
import {
  RktAlertEnterpriseComponent,
  RktAlertEnterpriseModule,
  RktLinkEnterpriseModule,
} from '@rocketcentral/rocket-design-system-enterprise-angular';

@Component({
  selector: 'app-banner-message',
  standalone: true,
  imports: [MatIconModule, RktLinkEnterpriseModule, MatButtonModule, RktAlertEnterpriseModule],
  templateUrl: './banner-message.component.html',
  styleUrl: './banner-message.component.scss',
})
export class BannerMessageComponent implements OnInit {
  private zone = inject(NgZone);
  private elementRef = inject(ElementRef);
  subject = input.required<string>();
  content = input.required<string>();
  severity = input.required<MessageSeverity>();

  private resizeObserver = new ResizeObserver(() => this.zone.run(() => this.onResize()));
  private selfClick = false;
  contentElement = viewChild.required<ElementRef>('contentElement');
  alertVariant = computed<typeof RktAlertEnterpriseComponent.prototype.variant>(() => {
    switch (this.severity()) {
      case MessageSeverity.Information:
        return 'info';
      case MessageSeverity.Warn:
        return 'caution';
      case MessageSeverity.Error:
        return 'warn';
      case MessageSeverity.Resolution:
        return 'success';
    }
  });
  textOverflow = signal(false);
  expanded = signal(false);
  readonly MessageSeverity = MessageSeverity;

  ngOnInit() {
    this.resizeObserver.observe(this.contentElement().nativeElement);
  }

  ngOnDestroy() {
    this.resizeObserver.disconnect();
  }

  expand() {
    this.selfClick = true;
    this.expanded.set(true);
  }

  collapse() {
    this.expanded.set(false);
    this.checkTextOverflow();
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    if (this.selfClick || !this.expanded()) {
      this.selfClick = false;
      return;
    }

    if (!this.elementRef.nativeElement.contains(event.target)) {
      this.collapse();
    }
  }

  private onResize() {
    if (this.expanded()) {
      // Ignore any resizes when the message is expanded
      return;
    }

    this.checkTextOverflow();
  }

  private checkTextOverflow() {
    const scrollWidth = this.contentElement().nativeElement.scrollWidth;
    const clientWidth = this.contentElement().nativeElement.clientWidth;
    this.textOverflow.set(scrollWidth > clientWidth);
  }
}
