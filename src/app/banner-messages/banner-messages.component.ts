import { Component, computed, effect, inject, signal, untracked } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { map } from 'rxjs';
import { BannerMessageService } from '../services/banner-message/banner-message.service';
import { BannerMessageComponent } from './banner-message/banner-message.component';

@Component({
  selector: 'app-banner-messages',
  standalone: true,
  imports: [BannerMessageComponent, MatButtonModule, MatIconModule],
  templateUrl: './banner-messages.component.html',
  styleUrl: './banner-messages.component.scss',
})
export class BannerMessagesComponent {
  private bannerMessagesService = inject(BannerMessageService);

  public readonly bannerMessages = toSignal(
    this.bannerMessagesService.bannerMessages$.pipe(
      map((messages) => messages.filter((message) => message.isActive)),
    ),
    {
      initialValue: [],
    },
  );

  public messageIndex = signal<number>(0);
  public readonly messageNumber = computed(() => this.messageIndex() + 1);
  public readonly activeMessage = computed(() => this.bannerMessages()[this.messageIndex()]);

  constructor() {
    effect(() => {
      const bannerMessages = this.bannerMessages();
      untracked(() => {
        if (this.messageIndex() >= bannerMessages.length) {
          this.messageIndex.set(Math.max(0, bannerMessages.length - 1));
        }
      });
    });
  }

  back() {
    this.messageIndex.update((index) => Math.max(0, index - 1));
  }

  next() {
    this.messageIndex.update((index) => Math.min(this.bannerMessages().length - 1, index + 1));
  }
}
