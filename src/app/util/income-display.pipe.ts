import { Pipe, PipeTransform } from '@angular/core';
import { IncomeType } from '@rocket-logic/rl-xp-bff-models';
import { SUPPORTED_INCOME_OPTIONS } from '../services/entity-state/income-state/supported-income';

@Pipe({
  name: 'incomeDisplay',
  standalone: true,
})
export class IncomeDisplayPipe implements PipeTransform {
  transform(value: IncomeType | null | undefined): string {
    return SUPPORTED_INCOME_OPTIONS.find((option) => option.value === value)?.display ?? '';
  }
}
