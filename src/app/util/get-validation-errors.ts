import { FormArray, FormGroup, ValidationErrors } from '@angular/forms';

export function getValidationErrors(control: FormGroup | FormArray): {
  [path: string]: ValidationErrors;
} {
  const results: { [path: string]: ValidationErrors } = {};

  Object.entries(control.controls).forEach(([key, control]) => {
    if (control.valid) {
      return;
    }

    if (control.errors) {
      results[key] = control.errors;
    }

    if (control instanceof FormArray || control instanceof FormGroup) {
      Object.assign(
        results,
        Object.entries(getValidationErrors(control)).reduce(
          (acc, [path, errors]) => {
            acc[`${key}.${path}`] = errors;
            return acc;
          },
          {} as { [path: string]: ValidationErrors },
        ),
      );
    }
  });

  return results;
}
