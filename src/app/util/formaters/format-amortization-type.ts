import { AmortizationType } from '@rocket-logic/rl-xp-bff-models';
import { pascalCaseSplit } from '../formatting-helpers';

export function formatAmortizationType(value: AmortizationType | null | undefined): string {
  switch (value) {
    case AmortizationType.Fixed:
      return 'Fixed Rate';
    case AmortizationType.HomeEquityLineOfCredit:
      return 'HELOC';
    case AmortizationType.AdjustableRateBalloon:
      return 'ARM Balloon';
    default:
      if (!value) {
        return '';
      }
      return pascalCaseSplit(value);
  }
}
