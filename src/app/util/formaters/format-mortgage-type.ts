import { MortgageType } from '@rocket-logic/rl-xp-bff-models';

export function formatMortgageType(value: MortgageType | null | undefined): string {
  switch (value) {
    case MortgageType.Conventional:
      return 'Conventional';
    case MortgageType.FHA:
      return 'FHA';
    case MortgageType.VA:
      return 'VA';
    case MortgageType.USDARuralDevelopment:
      return 'USDA';
    case MortgageType.NotApplicable:
      return 'Not Applicable';
    default:
      return '';
  }
}
