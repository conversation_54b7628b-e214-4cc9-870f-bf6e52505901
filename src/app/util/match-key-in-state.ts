export function matchKeyInState<T extends { id?: string }>(state: T[], stateMap: Map<string, T>) {
  const newState = new Map<string, T>();

  state.forEach((value) => {
    const existingEntry = Array.from(stateMap.entries()).find(
      ([, currentValue]) => currentValue.id === value.id,
    );

    const key = existingEntry ? existingEntry[0] : crypto.randomUUID();
    newState.set(key, value);
  });

  return newState;
}
