import { ManufacturedTypes } from '@rocket-logic/rl-xp-bff-models/dist/subject-property/manufactured-types';
import { pascalCaseToDashSeparated } from './formatting-helpers';

export const MANUFACTURED_TYPES: {
  display: string;
  value: number | undefined;
}[] = Object.keys(ManufacturedTypes).map((key) => {
  return {
    value: translateManufacturedType(
      ManufacturedTypes[key as keyof typeof ManufacturedTypes]
    ),
    display: pascalCaseToDashSeparated(key),
  };
});

export function translateManufacturedType(key: string) {
  switch (key) {
    case ManufacturedTypes.SingleWide:
      return 1;
    case ManufacturedTypes.DoubleWide:
      return 2;
    case ManufacturedTypes.TripleWide:
      return 3;
    default:
      return undefined;
  }
}
