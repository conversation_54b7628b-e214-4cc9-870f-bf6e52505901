import { AbstractControl, ValidationErrors } from '@angular/forms';

export const DEFAULT_ERROR_MESSAGES: [string, (errorValue: any) => string][] = [
  [
    'loanNumber',
    (loanNumberError) => {
      if (loanNumberError.startsWith) {
        return `This field must start with a ${loanNumberError.startsWith}`;
      } else {
        return `This field does not match the required length of ${loanNumberError.length}`;
      }
    },
  ],
  ['required', () => 'This field is required'],
  [
    'min',
    (minError: { min: number }) =>
      `This field is less than the minimum ${minError.min.toLocaleString()}`,
  ],
  [
    'max',
    (maxError: { max: number }) =>
      `This field is greater than the maximum ${maxError.max.toLocaleString()}`,
  ],
  ['requireNonZero', () => 'This field must be greater than zero'],
];

export function getErrorMessage(
  errors: ValidationErrors,
  errorMessages?: [string, (errorValue: any) => string][],
  defaultMessage?: string,
  textToRemove?: string,
): string;
export function getErrorMessage(
  control: AbstractControl,
  errorMessages?: [string, (errorValue: any) => string][],
  defaultMessage?: string,
): string;
export function getErrorMessage(
  value: AbstractControl | ValidationErrors,
  errorMessages: [string, (errorValue: any) => string][] = [],
  defaultMessage = 'This field is invalid',
  textToRemove?: string,
) {
  const [errorKey, messageFn] =
    [...errorMessages, ...DEFAULT_ERROR_MESSAGES].find(([eKey]) => hasError(value, eKey)) ?? [];

  let message = defaultMessage;
  if (messageFn) {
    message = messageFn(getError(value, errorKey ?? ''));
  }

  return textToRemove ? message.replace(textToRemove, '') : message;
}

function hasError(value: AbstractControl | ValidationErrors, errorKey: string) {
  return value instanceof AbstractControl ? value.hasError(errorKey) : errorKey in value;
}

function getError(value: AbstractControl | ValidationErrors, errorKey: string) {
  return value instanceof AbstractControl ? value.getError(errorKey) : value[errorKey];
}
