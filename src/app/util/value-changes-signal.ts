import { Signal } from '@angular/core';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { EMPTY, startWith, switchMap } from 'rxjs';

export function toValueChangesSignal<T>(
  formSignal: Signal<FormGroup>,
  controlPath: string | string[],
): Signal<T> {
  return toSignal(
    toObservable(formSignal).pipe(
      switchMap((form) => {
        const control = form.get(controlPath);
        return control?.valueChanges.pipe(startWith(control.value)) ?? EMPTY;
      }),
    ),
  );
}
