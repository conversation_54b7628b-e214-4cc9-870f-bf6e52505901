import { formatCurrency, formatDate } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';
import { isValidDateString } from './is-valid-date-format';

@Pipe({
  name: 'preFillFormatValue',
  standalone: true
})
export class PreFillFormatValuePipe implements PipeTransform {
  transform(value: string | unknown, label: string): string{
    if (!value) {
      return '';
    }

    const stringValue = String(value).trim();
    const lowerLabel = label.toLowerCase();

    if (lowerLabel.includes('amount') || lowerLabel.includes('price')) {
      const numberValue = parseFloat(stringValue);
      return formatCurrency(numberValue, 'en-US', '$', 'USD');
    }

    if (isValidDateString(stringValue)) {
      return formatDate(stringValue, 'MM/dd/yyyy', 'en-US');
    }

    if (stringValue.toLowerCase() === 'true'  || stringValue.toLowerCase() === 'false') {
      return stringValue.toLowerCase() === 'true' ? 'Yes' : 'No';
    }

    return stringValue;
  }
}
