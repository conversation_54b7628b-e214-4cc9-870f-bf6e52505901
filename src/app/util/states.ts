import { State } from '@rocket-logic/rl-xp-bff-models';
import { SelectOption } from '../type-ahead-select/type-ahead-select.component';
import { pascalCaseSplit } from './formatting-helpers';

export const STATES: SelectOption<State>[] = Object.entries(State)
  .filter(([_, state]) => isSupportedState(state))
  .map(([key, state]) => {
    return {
      value: state,
      display: pascalCaseSplit(key),
    };
  });

function isSupportedState(state: State): boolean {
  const unsupportedStates = [
    State.AmericanSamoa,
    State.FederatedStatesOfMicronesia,
    State.Guam,
    State.MarshallIslands,
    State.NorthernMarianaIslands,
    State.Palau,
    State.PuertoRico,
    State.VirginIslands,
  ];
  return !unsupportedStates.includes(state);
}
