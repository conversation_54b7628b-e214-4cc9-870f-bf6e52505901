import { FormGroup } from '@angular/forms';
import { Address } from '@rocket-logic/rl-xp-bff-models';
import { SubjectPropertyControls } from '../services/entity-state/subject-property-state/form-types';
import { markDiffsDirty } from './mark-diffs-dirty';

export function updateSubjectPropertyForm(
  address: Address,
  subjectPropertyForm: FormGroup<SubjectPropertyControls>,
) {
  const addressForm = subjectPropertyForm.controls.address;
  delete address.county; // Skip resetting county from current residence
  markDiffsDirty(address, addressForm);
  addressForm.patchValue(address);
}
