import { FormBuilder, FormGroup, ValidatorFn, Validators } from '@angular/forms';
import { Address } from '@rocket-logic/rl-xp-bff-models';
import { Controlify, Formify } from './form-utility-types';

export function buildAddressForm(
  formBuilder: FormBuilder,
  options: {
    requireStreet?: boolean;
    requireCity?: boolean;
    requireState?: boolean;
    requireZipCode?: boolean;
    allowPOBoxes?: boolean;
    
  } = {
    requireStreet: false,
    requireCity: false,
    requireState: false,
    requireZipCode: false,
    allowPOBoxes: false,
  },
): FormGroup<Controlify<Address>> {

  const streetValidationBehavior: ValidatorFn[] = [];
  if (options?.requireStreet) {
    streetValidationBehavior.push(Validators.required)
  }
  if (!options?.allowPOBoxes) {
    streetValidationBehavior.push(Validators.pattern(/^(?!((po *box)|(post *office *box)|(box)))/i))
  }
  
  return formBuilder.group<Formify<Address>>({
    addressLine1: [null, streetValidationBehavior],
    addressLine2: [null],
    city: [null, options?.requireCity ? [Validators.required] : []],
    state: [null, options?.requireState ? [Validators.required] : []],
    county: [null],
    zipCode: [
      null,
      options?.requireZipCode
        ? [Validators.required, Validators.pattern(/^\d{5}$/)]
        : [Validators.pattern(/^\d{5}$/)],
    ],
    plus4: [null],
    propertyHubGuid: [null],
    censusTractCode: [null],
  });
}

