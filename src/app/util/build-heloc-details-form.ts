import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  Validators,
} from '@angular/forms';
import { HelocDetails } from '@rocket-logic/rl-xp-bff-models/dist/refinance/heloc-details';
import { Controlify, Formify } from './form-utility-types';

type HelocDetailsForm = FormGroup<Controlify<HelocDetails>>;

export function drawDownAmountValidator(control: AbstractControl): ValidationErrors | null {
  const loanAmount = control.get('loanAmount')?.value;
  const drawDownAmount = control.get('drawDownAmount')?.value;

  if (drawDownAmount > loanAmount) {
    return { drawDownAmountExceedsLoanAmount: true };
  }

  return null;
}

export function buildHelocDetailsForm(formBuilder: FormBuilder): HelocDetailsForm {
  return formBuilder.group<Formify<HelocDetails>>(
    {
      loanAmount: [null, [Validators.min(1_000), Validators.max(10_000_000)]],
      drawDownAmount: [null, [Validators.min(0)]],
      lienPosition: [null],
    },
    {
      validators: [drawDownAmountValidator],
    },
  );
}
