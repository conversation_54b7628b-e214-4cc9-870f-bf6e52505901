import { ValidatorFn, AsyncValidatorFn, FormControl } from "@angular/forms";

export type Formify<Type> = {
    [Property in keyof Required<Type>]: FormBuilderControl<Type[Property]>;
};

export type Controlify<Type> = {
    [Property in keyof Required<Type>]: FormControl<Type[Property] | null | undefined>;
};

type FormBuilderControl<Type> = [Type | {value: Type, disabled: boolean} | null, ValidatorFn[]?, AsyncValidatorFn[]?];
