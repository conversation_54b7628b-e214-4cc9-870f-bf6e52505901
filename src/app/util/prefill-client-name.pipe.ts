import { Pipe, PipeTransform } from '@angular/core';
import { PrefillDataPoint } from '../services/prefill-state/prefill-state.service';

@Pipe({
  name: 'preFillClientName',
  standalone: true
})
export class PreFillClientNamePipe implements PipeTransform {
  transform(client: (PrefillDataPoint[] | PrefillDataPoint)[], index: number): string {
    const topLevelInfo = client.filter(
      (dataPoint) => !Array.isArray(dataPoint),
    ) as PrefillDataPoint[];

    const firstName = topLevelInfo.find((dataPoint) => dataPoint.key === 'firstName')?.value;
    const lastName = topLevelInfo.find((dataPoint) => dataPoint.key === 'lastName')?.value;
    const name = `${firstName ?? ''} ${lastName ?? ''}`.trim();

    if (name.length > 0) {
      return name;
    }

    return `Client ${index + 1}`;
  }
}