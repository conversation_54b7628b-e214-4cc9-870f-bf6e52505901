import { AbstractControl } from '@angular/forms';
import { concat, defer, EMPTY, map, Observable, of } from 'rxjs';

export function rawValueChanges$<T>(
  control: AbstractControl<unknown, T>,
  startWithCurrentValue = true,
): Observable<T> {
  return concat(
    startWithCurrentValue ? defer(() => of(control.getRawValue())) : EMPTY,
    control.valueChanges.pipe(map(() => control.getRawValue())),
  );
}
