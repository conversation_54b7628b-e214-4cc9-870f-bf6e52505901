import { Address } from '@rocket-logic/rl-xp-bff-models';

export function formatAddress(address?: Address): string {
  if (!address) {
    return '';
  }
  
  const { addressLine1, addressLine2, city, state, zipCode } = address;
  const parts: [string | undefined, string][] = [
    [addressLine1, ', '],
    [addressLine2, ', '],
    [city, ', '],
    [state, ' '],
    [zipCode, ''],
  ];

  return parts.reduce((acc, [part, separator]) => (part ? `${acc}${part}${separator}` : acc), '');
}
