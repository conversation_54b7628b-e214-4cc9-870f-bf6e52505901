import { PaymentFrequency } from '@rocket-logic/rl-xp-bff-models/dist/enums/payment-frequency';
import { pascalCaseToSentence } from './formatting-helpers';

export const PAYMENT_FREQUENCY: { display: string; value: PaymentFrequency }[] =
  Object.keys(PaymentFrequency).map((key) => {
    return {
      value: PaymentFrequency[key as keyof typeof PaymentFrequency],
      display: pascalCaseToSentence(key),
    };
  });
