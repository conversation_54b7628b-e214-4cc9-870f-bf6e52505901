import { FormControl, FormGroupDirective, NgForm } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';

/**
 *  This will display the errors if a field is untouched
 *  Angular by default will not display validation errors if a field is untouched.
 * @param showUntouchedErrors whether to display errors if a field is untouched or use Angular's default behavior
 */
export class UntouchedErrorStateMatcher implements ErrorStateMatcher {
  private showUntouchedErrors = false;

  constructor(showUntouchedErrors: boolean = false) {
    this.showUntouchedErrors = showUntouchedErrors;
  }

  isErrorState(control: FormControl | null, form: FormGroupDirective | NgForm | null): boolean {
    if (this.showUntouchedErrors) {
      return !!control && control.invalid;
    }
    // Angular's default behavior for Error State Matcher, check if there is a control, if it's invalid and if it's dirty or touched.
    return !!(control && control.invalid && (control.dirty || control.touched));
  }
}
