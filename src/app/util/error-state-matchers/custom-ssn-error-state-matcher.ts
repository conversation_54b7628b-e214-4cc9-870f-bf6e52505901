import { FormControl, FormGroupDirective, NgForm } from '@angular/forms';
import { ErrorStateMatcher } from '@angular/material/core';
import { getSsnWarnings } from '../ssn-warning';

/**
 *  This will display the errors if a SSN's format is invalid while still retaining
 *  the ability to save the SSN.
 * 
 *  Invalid Formats include:
 *  - SSNs starting with 9
 *  - SSNs starting with 666
 *  - SSNs with a group of 0s i.e. *********** & ***********
 *  - SSNs with all identical digits
 *  - Dummy SSNs (*********** & ***********)
 */
export class SsnErrorStateMatcher implements ErrorStateMatcher {

  isErrorState(control: FormControl | null, form: FormGroupDirective | NgForm | null): boolean {
    
    if (control === null) {
        return false;
    }

    return !!getSsnWarnings(control) || control.invalid;
  }
}