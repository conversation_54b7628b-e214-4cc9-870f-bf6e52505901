import { PropertyType } from '@rocket-logic/rl-xp-bff-models/dist/enums/subject-property-type';

export function isAttachmentType(propertyType?: PropertyType): boolean {
  return (
    propertyType === PropertyType.Condominium ||
    propertyType === PropertyType.SiteCondo ||
    propertyType === PropertyType.ManufacturedCondominium ||
    propertyType === PropertyType.ManufacturedPUD ||
    propertyType === PropertyType.PUD
  );
}
