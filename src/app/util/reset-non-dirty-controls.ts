import { AbstractControl, FormArray, FormGroup } from '@angular/forms';

export function resetNonDirtyControls(formState: any, control: AbstractControl, emitEvent = true) {
  if (!control.dirty) {
    control.reset(formState, { emitEvent });
  } else if (control instanceof FormGroup) {
    Object.entries(control.controls).forEach(([key, childControl]) => {
      resetNonDirtyControls(formState?.[key], childControl, emitEvent);
    });
  } else if (control instanceof FormArray) {
    control.controls.forEach((childControl, index) => {
      resetNonDirtyControls(formState?.[index], childControl, emitEvent);
    });
  }
}
