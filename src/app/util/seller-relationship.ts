import { SellerRelationship } from '@rocket-logic/rl-xp-bff-models/dist/purchase/enums/seller-relationship';
import { pascalCaseToSentence } from './formatting-helpers';

export const SELLER_RELATIONSHIP: {
  display: string;
  value: SellerRelationship;
}[] = Object.keys(SellerRelationship).map((key) => {
  return {
    value: SellerRelationship[key as keyof typeof SellerRelationship],
    display: pascalCaseToSentence(key),
  };
});
