import { Client, CreditReport } from '@rocket-logic/rl-xp-bff-models';

export function jointReportValidCheck(creditReport: CreditReport, loanClients: Client[]) {
  if (creditReport.clients.length !== 2) {
    return true;
  }
  return creditReport.clients.some((creditClient) =>
    loanClients.find(
      (client) => client.personalInformation?.spouseClientId === creditClient.clientId,
    ),
  );
}
