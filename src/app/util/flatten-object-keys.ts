export function flattenObjectKeys(obj: Record<string, any> | any[], path: string[] = []): Record<string, any> {
  if (Array.isArray(obj)) {
    return obj.map((value, index) => flattenObjectKeys(value, [...path, `${index}`]))
      .reduce((acc, curr) => ({ ...acc, ...curr }));
  }

  return Object.entries(obj).map(([key, value]) => {
    if (typeof value === 'boolean' || typeof value === 'string' || typeof value === 'number' || value === undefined || value === null) {
      return {
        [path.concat(key).join('.')]: value,
      };
    }
    return flattenObjectKeys(value, [...path, key]);
  })
    .reduce((acc, curr) => ({ ...acc, ...curr }));
}

