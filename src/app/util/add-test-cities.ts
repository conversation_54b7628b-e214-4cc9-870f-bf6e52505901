import { State } from '@rocket-logic/rl-xp-bff-models';
import { environment } from '../../environments/environment';

const TEST_CITIES: { [state: string]: { [zip: string]: string[] } } = {
  [State.Massachusetts]: {
    '02723': ['Someplace'],
  },
};

export function addTestCities(cities: string[], state?: State, zipCode?: string) {
  if (environment.appEnvironment === 'prod') {
    return cities;
  }

  const testCities = [];
  if (state && zipCode) {
    testCities.push(...(TEST_CITIES[state]?.[zipCode] ?? []));
  } else if (state) {
    testCities.push(...Object.values(TEST_CITIES[state] ?? {}).flat());
  }

  return cities.concat(testCities);
}
