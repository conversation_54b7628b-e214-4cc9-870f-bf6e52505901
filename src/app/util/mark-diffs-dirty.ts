import { FormGroup } from '@angular/forms';

export function markDiffsDirty<
  T extends { [key: string]: any },
  ControlType extends { [K in keyof T]: any },
>(value: T, form: FormGroup<ControlType>) {
  Object.keys(value).forEach((key) => {
    const control = form.controls[key];

    if (control instanceof FormGroup) {
      markDiffsDirty(value[key], control as FormGroup<any>);
      return;
    }

    if (value[key] !== undefined && isDiff(control.getRawValue(), value[key])) {
      control.markAsDirty();
    }
  });
}

function isDiff<T>(a: T, b: T): boolean {
  if (a === b) {
    return false;
  }

  if (Array.isArray(a) && Array.isArray(b)) {
    return a.some((item, index) => isDiff(item, b[index]));
  }

  if (a instanceof Date && b instanceof Date) {
    return a.toISOString() !== b.toISOString();
  }

  if (a instanceof Object && b instanceof Object) {
    return Object.keys(a).some((key) => isDiff(a[key as keyof T], b[key as keyof T]));
  }

  return true;
}