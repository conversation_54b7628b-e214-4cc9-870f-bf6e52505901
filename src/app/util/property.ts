import { PropertyType } from '@rocket-logic/rl-xp-bff-models/dist/enums/subject-property-type';

export const PROPERTY: { display: string | undefined; value: PropertyType }[] = [
  { value: PropertyType.SingleFamily, display: 'Single Family' },
  { value: PropertyType.PUD, display: 'Planned Unit Development (PUD)' },
  { value: PropertyType.Condominium, display: 'Condominium' },
  { value: PropertyType.SiteCondo, display: 'Site Condominium' },
  { value: PropertyType.TwoToFourFamily, display: 'Multi-Family' },
  { value: PropertyType.Coop, display: 'Co-Op' },
  { value: PropertyType.Townhouse, display: 'Townhouse' },
  { value: PropertyType.ManufacturedSingleFamily, display: 'Manufactured Single Family' },
  { value: PropertyType.ManufacturedPUD, display: 'Manufactured PUD' },
  { value: PropertyType.ManufacturedCondominium, display: 'Manufactured Condominium' },
];
