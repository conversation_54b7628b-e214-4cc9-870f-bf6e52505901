import { Pipe, PipeTransform } from '@angular/core';
import { EntityStateName } from '../services/entity-state/abstract-entity-state.service';

@Pipe({
  name: 'entityName',
  standalone: true,
})
export class EntityNamePipe implements PipeTransform {
  transform(value?: EntityStateName): string {
    return getEntityName(value);
  }
}

export function getEntityName(value?: EntityStateName): string {
  switch (value) {
    case EntityStateName.Loan:
      return 'Loan information';
    case EntityStateName.Client:
      return 'Client information';
    case EntityStateName.SubjectProperty:
      return 'Subject Property information';
    case EntityStateName.Income:
      return 'Income information';
    case EntityStateName.Liability:
      return 'Liability information';
    case EntityStateName.Assets:
      return 'Asset information';
    case EntityStateName.OwnedProperty:
      return 'Owned Property information';
    case EntityStateName.Task:
      return 'Task information';
    default:
      return 'some information';
  }
}
