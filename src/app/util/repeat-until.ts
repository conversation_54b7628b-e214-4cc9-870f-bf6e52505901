import { MonoTypeOperatorFunction, Observable, filter, repeat, take, takeWhile } from 'rxjs';

export function repeatUntil<T>(
  predicate: (value: T, index: number) => boolean,
  options?: { delay?: number; retries?: number; emitValues?: boolean },
): MonoTypeOperatorFunction<T> {
  return (source: Observable<T>) => {
    const resultObs = source.pipe(
      repeat({
        count: options?.retries,
        delay: 10000,
      }),
    );

    if (options?.emitValues ?? false) {
      return resultObs.pipe(takeWhile((value, index) => !predicate(value, index), true));
    }

    return resultObs.pipe(
      filter((value, index) => predicate(value, index)),
      take(1),
    );
  };
}
