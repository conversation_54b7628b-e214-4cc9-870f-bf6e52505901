import { AbstractControl, FormArray } from '@angular/forms';

/**
 * Syncs FormArray controls with an array of data
 * @param formArray The FormArray to sync
 * @param controlBuilder Control builder function to create new controls when pushing into the FormArray
 * @param data The data to sync with the FormArray, defaults to an empty array
 * @param emitEvent Whether to emit events when adding or removing controls, defaults to false
 */
export function syncFormArray<T>(
  formArray: FormArray,
  controlBuilder: () => AbstractControl,
  data: T[] = [],
  emitEvent = false
) {

  // remove all non dirty controls that dont match server state
  for (let i = data.length; i < formArray.controls.length; i++) {
    if (!formArray.at(i)?.dirty) {
      formArray.removeAt(i, { emitEvent });
    }
  }

  // Add new controls for form arrays that are missing entries from server state
  const pristineControlCount = formArray.controls.filter((control) => !control?.dirty).length
  for (let i = pristineControlCount; i < data.length; i++) {
    formArray.push(controlBuilder(), { emitEvent });
  }
}
