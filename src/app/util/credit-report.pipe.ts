import { Pipe, PipeTransform, computed, inject } from '@angular/core';
import { ClientCredit, CreditReport } from '@rocket-logic/rl-xp-bff-models';
import { ClientStateService } from '../services/entity-state/client-state/client-state.service';
import { getClientName } from './get-client-name';

@Pipe({
  name: 'creditReport',
  standalone: true,
})
export class CreditReportPipe implements PipeTransform {
  clientStateService = inject(ClientStateService);
  clients = computed(() => this.clientStateService.stateValues());

  transform(value: CreditReport): string {
    return `${this.getClientNames(value.clients)}-${this.getReportType(value.clients)}-${value.vendor}`;
  }

  private getClientNames(creditClients: ClientCredit[]): string {
    return creditClients
      .map((creditClient) => {
        const client = this.clients().find((client) => client.id === creditClient.clientId);
        return client ? getClientName(client, true) : 'Unknown';
      })
      .join('-');
  }

  private getReportType(creditClients: ClientCredit[]): string {
    return creditClients.length === 2 ? 'Joint' : 'Individual';
  }
}
