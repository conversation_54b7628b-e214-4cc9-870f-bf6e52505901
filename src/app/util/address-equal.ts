import { Address } from '@rocket-logic/rl-xp-bff-models';
import { Nullable } from './nullable-type';

export function areAddressesEqual(
  address1: Nullable<Address>,
  address2: Nullable<Address>,
): boolean {
  if (!address1 || !address2) {
    return false;
  }

  return (
    address1.addressLine1?.toLowerCase() === address2.addressLine1?.toLowerCase() &&
    (address1.addressLine2?.toLowerCase() ?? '') === (address2.addressLine2?.toLowerCase() ?? '') &&
    address1.city?.toLowerCase() === address2.city?.toLowerCase() &&
    address1.state?.toLowerCase() === address2.state?.toLowerCase() &&
    address1.zipCode?.toLowerCase() === address2.zipCode?.toLowerCase()
  );
}
