import { catchError, map, Observable, of, pipe, scan, startWith, UnaryFunction } from 'rxjs';

export function loadable<T, E = any>(
  mapError?: (error: any) => E,
): UnaryFunction<Observable<T>, Observable<Loadable<T>>> {
  return pipe(
    map((response) => ({ data: response, loading: false })),
    catchError((error) =>
      of({
        error: mapError ? mapError(error) : error,
        loading: false,
      }),
    ),
    startWith({ loading: true }),
    scan((acc, obj) => ({ ...acc, ...obj })),
  );
}

export interface Loadable<T> {
  loading: boolean;
  data?: T;
  error?: any;
}

export function mapLoadableData<T, U>(
  mapper: (incoming: T) => U
): UnaryFunction<Observable<Loadable<T>>, Observable<Loadable<U>>> {
  const memo = singleArgMemo();
  return pipe(
    map((incoming) => {
      if (incoming.data === undefined) {
        return incoming as unknown as Loadable<U>;
      }
      // Don't re-map data if it hasn't changed
      const data = memo(mapper)(incoming.data);
      return {
        ...incoming,
        data,
      };
    }),
  );
}

function singleArgMemo() {
  const cache = new Map<any, any>();
  return <T, U>(fn: (arg: T) => U) => (arg: T): U => {
    if (cache.has(arg)) {
      return cache.get(arg);
    }
    const result = fn(arg);
    cache.set(arg, result);
    return result;
  };
}
