import { AbstractControl, FormArray, ValidatorFn } from '@angular/forms';

export function validateUniquePhoneFields(): ValidatorFn {
  return (control: AbstractControl): { [key: string]: any } | null => {
    let parentFormArray: FormArray | null = null;

    let currentControl: AbstractControl | null = control;
    while (currentControl && !(currentControl instanceof FormArray)) {
      currentControl = currentControl.parent;
    }

    if (currentControl instanceof FormArray) {
      parentFormArray = currentControl;
    }

    if (!parentFormArray) {
      return null;
    }

    const controlValues = parentFormArray.controls.map(
      (group: AbstractControl) => group.get('type')?.value,
    );

    const duplicates = controlValues.filter((value) => value === control.value).length > 1;
    return duplicates
      ? {
          duplicateTypes: {
            value: control.value,
          },
        }
      : null;
  };
}
