function isEmptyValue(value: any): boolean {
  return (
    value == null || value === '' || (value instanceof Object && Object.keys(value).length === 0)
  );
}

/**
 * Recursively deletes null values, empty strings, and empty objects from a given object.
 * @param obj Object to delete empty values from.
 * @returns The same object reference that was passed in.
 */
export function removeEmptyValues<T extends object>(obj: T): T {
  for (const [key, value] of Object.entries(obj)) {
    if (value instanceof Date) {
      continue;
    }

    if (value instanceof Object) {
      removeEmptyValues(value);
    }

    if (isEmptyValue(value)) {
      delete obj[key as keyof T];
    }

    if (value instanceof Array && value.length) {
      // @ts-expect-error: Arbitrary type error
      obj[key] = value.filter((item) => !isEmptyValue(item));
    }
  }

  return obj;
}
