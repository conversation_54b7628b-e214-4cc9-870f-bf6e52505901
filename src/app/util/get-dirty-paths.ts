import { FormArray, FormGroup } from '@angular/forms';

export function getDirtyPaths(control: FormGroup | FormArray, ignoredPaths: string[] = []) {
  const results: string[] = [];

  Object.entries(control.controls).forEach(([key, control]) => {
    if (!control.dirty) {
      return;
    }

    if (control instanceof FormArray || control instanceof FormGroup) {
      const newResults = getDirtyPaths(control).map((path) => `${key}.${path}`);

      if (newResults.length > 0) {
        results.push(...newResults);
        return;
      }
    }

    results.push(key);
  });

  return ignoredPaths.length ? results.filter((path) => !ignoredPaths.includes(path)) : results;
}
