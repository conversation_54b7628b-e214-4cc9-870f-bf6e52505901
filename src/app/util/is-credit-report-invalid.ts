import { Client, CreditReport, Task } from '@rocket-logic/rl-xp-bff-models';
import { allReportClientsOnLoan } from './all-report-clients-on-loan';
import { clientHasOutstandingTask } from './client-has-outstanding-task';
import { jointReportValidCheck } from './joint-report-valid-check';

export function isReportInvalid(
  creditReport: CreditReport,
  clients: Client[],
  creditTasks?: Task[],
): boolean {
  return (
    !allReportClientsOnLoan(creditReport, clients) ||
    !jointReportValidCheck(creditReport, clients) ||
    clientHasOutstandingTask(
      creditTasks,
      creditReport.clients.map((client) => client.clientId),
    )
  );
}
