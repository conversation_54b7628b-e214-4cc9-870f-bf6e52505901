import { FormBuilder } from '@angular/forms';
import { PhoneNumber } from '@rocket-logic/rl-xp-bff-models';
import { PhoneNumberGroup } from '../services/entity-state/client-state/form-types';
import { Formify } from './form-utility-types';
import { validateUniquePhoneFields } from './validate-unique-phone-fields';

export function buildPhoneNumberForm(
  formBuilder: FormBuilder,
  options: { validateUniqueTypes?: boolean } = { validateUniqueTypes: false },
): PhoneNumberGroup {
  return formBuilder.group<Formify<PhoneNumber>>({
    number: [null],
    type: [null, options?.validateUniqueTypes ? [validateUniquePhoneFields()] : []],
    extension: [null],
  });
}
