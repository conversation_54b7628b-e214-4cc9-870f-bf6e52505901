export function getClientName(
  client?: {
    personalInformation?: {
      firstName?: string | null;
      lastName?: string | null;
      preferredName?: string | null;
      suffix?: string | null;
    };
  },
  useLegalName?: boolean,
): string {
  if (!client) return '';
  const { firstName, lastName, preferredName, suffix } = client.personalInformation ?? {};

  let name = '';

  if (useLegalName) {
    if (firstName && lastName) name = `${firstName} ${lastName}`;
  } else {
    if ((preferredName || firstName) && lastName)
      name = `${preferredName ?? firstName} ${lastName}`;
  }

  if (suffix) name += ` ${suffix}`;

  return name;
}
