import { HttpErrorResponse } from "@angular/common/http";

export class ErrorUtilities {
  static httpErrorToMessage(error: HttpErrorResponse): string {
    let errorMessage = '';
    if (error?.error instanceof ErrorEvent) {
      // A client-side or network error occurred
      errorMessage = `Client-side error: ${error.error.message}`;
    } else {
      // The backend returned an unsuccessful response code
      switch (error.status) {
        case 400:
          errorMessage =
            'Bad Request: The server could not understand the request due to invalid syntax.';
          break;
        case 401:
          errorMessage = 'Unauthorized: Access is denied due to invalid credentials.';
          break;
        case 403:
          errorMessage = 'Forbidden: You do not have permission to access this resource.';
          break;
        case 404:
          errorMessage = 'Not Found: The requested resource could not be found.';
          break;
        case 500:
          errorMessage = 'Internal Server Error: The server encountered an unexpected condition.';
          break;
        case 503:
          errorMessage = 'Service Unavailable: The server is not ready to handle the request.';
          break;
        default:
          errorMessage = `Unexpected error: ${error.status} - ${error.message}`;
      }
    }

    return errorMessage;
  }
}
