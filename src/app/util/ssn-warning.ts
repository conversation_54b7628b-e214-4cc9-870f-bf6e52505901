import { AbstractControl } from "@angular/forms";
import { environment } from "../../environments/environment";


export function getSsnWarnings(ssnFormControl: AbstractControl<any, any> | null ) : string{

    if (environment.appEnvironment !== 'prod') {
      return '';
    }

    const ssn = ssnFormControl?.value;

    if (ssn?.startsWith('9')) {
      return 'SSNs starting with 9 are invalid';
    }

    if (ssn?.startsWith('666')) {
      return 'SSNs starting with 666 are invalid';
    } 

    if (ssn?.startsWith('000') || (ssn?.length === 5 && ssn?.endsWith('00')) || (ssn?.length === 9 && (ssn?.endsWith("0000") || ssn?.slice(3,5) === "00"))) {
      return "SSNs with a group of 0 are invalid";
    }

    if (ssn?.length === 9 && new Set(ssn).size === 1) {
      return "SSNs with all identical digits are invalid";
    }

    //Dummy SSNs
    if (ssn === "123456789" || ssn==="078051120") {
      return "Provided SSN is considered invalid";
    }

    return '';

  }