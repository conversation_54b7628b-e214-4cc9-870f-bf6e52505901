import { Task, TaskStatus } from '@rocket-logic/rl-xp-bff-models';
import { getClientIdFromTask } from './get-client-id-from-task';

export function clientHasOutstandingTask(
  tasks?: Task[],
  selectedClients?: string[] | null,
): boolean {
  const taskClientIds = tasks
    ?.filter((task) => task.status === TaskStatus.Created)
    ?.map((task) => getClientIdFromTask(task));

  return taskClientIds?.some((id) => selectedClients?.includes(id ?? '')) ?? false;
}
