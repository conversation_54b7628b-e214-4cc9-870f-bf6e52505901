import { Pipe, PipeTransform } from '@angular/core';
import { getClientName } from './get-client-name';

@Pipe({
  name: 'clientName',
  standalone: true,
})
export class ClientNamePipe implements PipeTransform {
  transform(
    value?: {
      personalInformation?: {
        firstName?: string | null;
        lastName?: string | null;
        preferredName?: string | null;
        suffix?: string | null;
      };
    },
    useLegalName?: boolean,
  ): string {
    return getClientName(value, useLegalName);
  }
}
