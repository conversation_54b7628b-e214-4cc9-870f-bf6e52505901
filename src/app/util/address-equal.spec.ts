import { Address, State } from '@rocket-logic/rl-xp-bff-models';
import { areAddressesEqual } from './address-equal';

describe('areAddressesEqual', () => {
  it('should return true for identical addresses', () => {
    const address1: Address = {
      addressLine1: '123 Main St',
      addressLine2: 'Apt 4',
      city: 'Anytown',
      state: State.California,
      zipCode: '12345',
    };
    const address2: Address = {
      addressLine1: '123 Main St',
      addressLine2: 'Apt 4',
      city: 'Anytown',
      state: State.California,
      zipCode: '12345',
    };
    expect(areAddressesEqual(address1, address2)).toBe(true);
  });

  it('should return false for different addresses', () => {
    const address1: Address = {
      addressLine1: '123 Main St',
      addressLine2: 'Apt 4',
      city: 'Anytown',
      state: State.California,
      zipCode: '12345',
    };
    const address2: Address = {
      addressLine1: '124 Main St',
      addressLine2: 'Apt 4',
      city: 'Anytown',
      state: State.California,
      zipCode: '12345',
    };
    expect(areAddressesEqual(address1, address2)).toBe(false);
  });

  it('should handle null and undefined values', () => {
    const address1: Address = {
      addressLine1: '123 Main St',
      addressLine2: undefined,
      city: 'Anytown',
      state: State.California,
      zipCode: '12345',
    };
    const address2: Address = {
      addressLine1: '123 Main St',
      addressLine2: undefined,
      city: 'Anytown',
      state: State.California,
      zipCode: '12345',
    };
    expect(areAddressesEqual(address1, address2)).toBe(true);
  });

  it('should return false if one address is null', () => {
    const address1: Address = {
      addressLine1: '123 Main St',
      addressLine2: 'Apt 4',
      city: 'Anytown',
      state: State.California,
      zipCode: '12345',
    };
    const address2: Address = null as unknown as Address;
    expect(areAddressesEqual(address1, address2)).toBe(false);
  });

  it('should return false if both addresses are null', () => {
    const address1: Address = null as unknown as Address;
    const address2: Address = null as unknown as Address;
    expect(areAddressesEqual(address1, address2)).toBe(false);
  });

  it('should return true for addresses with different cases', () => {
    const address1: Address = {
      addressLine1: '123 Main St',
      addressLine2: 'Apt 4',
      city: 'Anytown',
      state: State.California,
      zipCode: '12345',
    };
    const address2: Address = {
      addressLine1: '123 MAIN ST',
      addressLine2: 'APT 4',
      city: 'ANYTOWN',
      state: State.California,
      zipCode: '12345',
    };
    expect(areAddressesEqual(address1, address2)).toBe(true);
  });
});
