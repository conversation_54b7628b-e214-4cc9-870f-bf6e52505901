<div class="flex flex-col gap-2">
  @for (
    employmentInfos of prefillClientEmploymentData()?.[clientGcid()];
    track employmentInfos;
    let index = $index
  ) {
    @if (!isDisabled(employmentInfos)) {
      <button
        type="button"
        class="data-tile"
        [class.data-tile--matched]="allControlsMatch(employmentInfos)"
        (click)="prefillService.handlePrefillClientEmploymentInfo(clientGcid(), index)"
        appTrackClick
        description="PreFill-EmploymentInfo-Clicked"
      >
        <div>
          @for (employmentProperty of employmentInfos; track employmentProperty) {
            <div>
              <span class="rkt-Caption-12"
                >{{ employmentProperty.key | capitalizeFirst | pascalCaseSplit }}:</span
              >
              <span class="rkt-Body-14 rkt-FontWeight--700">
                {{ employmentProperty.value | preFillFormatValue: employmentProperty.key }}
              </span>
            </div>
          }
        </div>

        @if (allControlsMatch(employmentInfos)) {
          <mat-icon
            class="rkt-Icon data-tile__icon data-tile__icon--matched"
            svgIcon="check_circle-two_tone"
          ></mat-icon>
        } @else {
          <mat-icon class="rkt-Icon data-tile__icon" svgIcon="add_circle-two_tone"></mat-icon>
        }
      </button>
    }
  }
</div>
