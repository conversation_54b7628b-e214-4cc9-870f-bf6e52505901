import { ChangeDetectionStrategy, Component, inject, input } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatIcon } from '@angular/material/icon';
import { IncomeType } from '@rocket-logic/rl-xp-bff-models/dist/income/income';
import { TrackClickDirective } from '../../analytics/track-click.directive';
import {
  MAPPABLE_INCOME_TYPES,
  SUPPORTED_INCOME_TYPES
} from '../../services/entity-state/income-state/supported-income';
import { LoanEditingState } from '../../services/entity-state/loan-state/loan-editing-state.service';
import {
  PrefillDataPoint,
  PrefillStateService
} from '../../services/prefill-state/prefill-state.service';
import { CapitalizeFirstPipe } from '../../util/capitalize-first-letter.pipe';
import { PascalCaseSplitPipe } from '../../util/pascal-case-split.pipe';
import { PreFillFormatValuePipe } from '../../util/prefill-format-value.pipe';
import { allControlsMatch } from '../prefill.shared';

@Component({
  selector: 'app-prefill-employment',
  standalone: true,
  imports: [
    MatIcon,
    PascalCaseSplitPipe,
    CapitalizeFirstPipe,
    PreFillFormatValuePipe,
    TrackClickDirective
  ],
  templateUrl: './prefill-employment.component.html',
  styleUrl: './prefill-employment.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PrefillEmploymentComponent {
  isSavingDisabled = inject(LoanEditingState).isLoanEditingDisabled;

  clientGcid = input.required<string>();

  prefillService = inject(PrefillStateService);
  prefillClientEmploymentData = toSignal(this.prefillService.prefillClientEmploymentData$);

  allControlsMatch = allControlsMatch;

  public isDisabled(employmentInfos: PrefillDataPoint[] | undefined): boolean {
    const incomeType = employmentInfos?.find(
      (info) => info.key?.toLowerCase() === 'incometype',
    )?.value;
    const isSupported = SUPPORTED_INCOME_TYPES.includes(IncomeType[incomeType as IncomeType]);
    const isMappable = MAPPABLE_INCOME_TYPES.has(incomeType as IncomeType);
    return this.isSavingDisabled() || !(isSupported || isMappable);
  }
}
