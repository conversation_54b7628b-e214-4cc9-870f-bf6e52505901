import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';

import { MockBuilder } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import { PrefillStateService } from '../services/prefill-state/prefill-state.service';
import { PrefillComponent } from './prefill.component';

describe('PrefillComponent', () => {
  let component: PrefillComponent;
  let fixture: ComponentFixture<PrefillComponent>;

  beforeEach(() =>
    MockBuilder(PrefillComponent)
      .mock(PrefillStateService, {
        hasPrefillData$: NEVER,
        prefillLoanData$: NEVER,
        prefillClientData$: NEVER,
        prefillClientEmploymentData$: NEVER,
        prefillError$: NEVER,
      })
      .mock(LoanEditingState, {
        isLoanEditingDisabled: signal(false),
      }),
  );
  beforeEach(() => {
    fixture = TestBed.createComponent(PrefillComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
