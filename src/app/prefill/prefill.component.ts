import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { toSignal } from '@angular/core/rxjs-interop';
import { MatExpansionModule } from '@angular/material/expansion';
import { MatIcon } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { TrackClickDirective } from '../analytics/track-click.directive';
import { LoanEditingState } from '../services/entity-state/loan-state/loan-editing-state.service';
import { PrefillStateService } from '../services/prefill-state/prefill-state.service';
import { CapitalizeFirstPipe } from '../util/capitalize-first-letter.pipe';
import { PascalCaseSplitPipe } from '../util/pascal-case-split.pipe';
import { PreFillClientNamePipe } from '../util/prefill-client-name.pipe';
import { PreFillFormatValuePipe } from '../util/prefill-format-value.pipe';
import { PrefillEmploymentComponent } from './prefill-employment/prefill-employment.component';
import { allControlsMatch } from './prefill.shared';

@Component({
  selector: 'app-prefill',
  standalone: true,
  imports: [
    MatExpansionModule,
    MatIcon,
    MatProgressSpinnerModule,
    PascalCaseSplitPipe,
    CapitalizeFirstPipe,
    PreFillFormatValuePipe,
    PreFillClientNamePipe,
    TrackClickDirective,
    PrefillEmploymentComponent,
  ],
  templateUrl: './prefill.component.html',
  styleUrl: './prefill.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PrefillComponent {
  prefillService = inject(PrefillStateService);
  hasPrefillData = toSignal(this.prefillService.hasPrefillData$);
  prefillLoanData = toSignal(this.prefillService.prefillLoanData$);
  prefillClientData = toSignal(this.prefillService.prefillClientData$);
  prefillClientEmploymentData = toSignal(this.prefillService.prefillClientEmploymentData$);
  prefillError = toSignal(this.prefillService.prefillError$);
  isSavingDisabled = inject(LoanEditingState).isLoanEditingDisabled;

  panelStates = signal<Record<string, boolean>>({});

  openPanel(panelId: string) {
    this.panelStates.update((states) => ({ ...states, [panelId]: true }));
  }

  closePanel(panelId: string) {
    this.panelStates.update((states) => ({ ...states, [panelId]: false }));
  }

  isArray = Array.isArray;
  allControlsMatch = allControlsMatch;
}
