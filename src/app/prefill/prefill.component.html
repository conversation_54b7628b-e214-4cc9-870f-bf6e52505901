<div class="container" class="flex flex-col p-4 box-border">
  @if (hasPrefillData()) {
    @if (prefillLoanData()?.length) {
      <mat-expansion-panel
        class="rkt-AccordionPanel app-AccordionPanel-outlined"
        [expanded]="panelStates()['subjectProperty']"
        (opened)="openPanel('subjectProperty')"
        (closed)="closePanel('subjectProperty')"
        hideToggle
        #subjectPropertyPanel
      >
        <mat-expansion-panel-header>
          <mat-panel-title class="rkt-AccordionPanel__header-title">
            <div class="rkt-Label-14">Subject Property Data ({{ prefillLoanData()!.length }})</div>
          </mat-panel-title>
          <mat-icon
            svgIcon="expand_more-outlined"
            [class.rotate-180]="subjectPropertyPanel.expanded"
            class="transition-transform"
          />
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="flex flex-col gap-2">
            @for (loanInfo of prefillLoanData(); track loanInfo) {
              @if (isArray(loanInfo.value)) {
                <button
                  type="button"
                  class="data-tile"
                  [class.data-tile--matched]="allControlsMatch(loanInfo.value)"
                  (click)="prefillService.handlePrefillLoanData(loanInfo.group)"
                  appTrackClick
                  description="PreFill-{{ loanInfo.group }}-Clicked"
                  [disabled]="isSavingDisabled()"
                  [class.rkt-Button--is-disabled]="isSavingDisabled()"
                >
                  <div>
                    @for (groupInfo of loanInfo.value; track groupInfo) {
                      <div>
                        <span class="rkt-Caption-12"
                          >{{ groupInfo.key | capitalizeFirst | pascalCaseSplit }}:</span
                        >
                        <span class="rkt-Body-14 rkt-FontWeight--700">
                          {{ groupInfo.value | preFillFormatValue: groupInfo.key }}
                        </span>
                      </div>
                    }
                  </div>

                  @if (allControlsMatch(loanInfo.value)) {
                    <mat-icon
                      class="rkt-Icon data-tile__icon data-tile__icon--matched"
                      svgIcon="check_circle-outlined"
                    ></mat-icon>
                  } @else {
                    <mat-icon
                      class="rkt-Icon data-tile__icon"
                      svgIcon="add_circle-outlined"
                    ></mat-icon>
                  }
                </button>
              } @else {
                <button
                  type="button"
                  class="data-tile"
                  [class.data-tile--matched]="loanInfo.matchesControl"
                  (click)="prefillService.handlePrefillLoanData(loanInfo.key)"
                  appTrackClick
                  description="PreFill-{{ loanInfo.key }}-Clicked"
                  [disabled]="isSavingDisabled()"
                  [class.rkt-Button--is-disabled]="isSavingDisabled()"
                >
                  <div>
                    <span class="rkt-Caption-12"
                      >{{ loanInfo.key | capitalizeFirst | pascalCaseSplit }}:</span
                    >
                    <span class="rkt-Body-14 rkt-FontWeight--700">
                      {{ loanInfo.value | preFillFormatValue: loanInfo.key }}
                    </span>
                  </div>

                  @if (loanInfo.matchesControl) {
                    <mat-icon
                      class="rkt-Icon data-tile__icon data-tile__icon--matched"
                      svgIcon="check_circle-outlined"
                    ></mat-icon>
                  } @else {
                    <mat-icon
                      class="rkt-Icon data-tile__icon"
                      svgIcon="add_circle-outlined"
                    ></mat-icon>
                  }
                </button>
              }
            }
          </div>
        </ng-template>
      </mat-expansion-panel>
    }

    @for (client of prefillClientData(); track client; let index = $index) {
      <mat-expansion-panel
        class="rkt-AccordionPanel app-AccordionPanel-outlined"
        [expanded]="panelStates()['client' + index]"
        (opened)="openPanel('client' + index)"
        (closed)="closePanel('client' + index)"
        hideToggle
        #clientPanel
      >
        <mat-expansion-panel-header>
          <mat-panel-title>
            <div class="rkt-Tracked-12 rkt-Label-16 prefill-section-header">
              {{ client.clientProperties | preFillClientName: index }} ({{
                client.clientProperties.length +
                  (prefillClientEmploymentData()?.[client.gcid]?.length ?? 0)
              }})
            </div>
          </mat-panel-title>
          <mat-icon
            svgIcon="expand_more-outlined"
            [class.rotate-180]="clientPanel.expanded"
            class="transition-transform"
          />
        </mat-expansion-panel-header>
        <ng-template matExpansionPanelContent>
          <div class="flex flex-col gap-2 pl-3.5 pt-3.5 pr-3.5">
            @for (clientInfo of client.clientProperties; track clientInfo) {
              @if (isArray(clientInfo)) {
                <button
                  type="button"
                  class="data-tile"
                  [class.data-tile--matched]="allControlsMatch(clientInfo)"
                  (click)="
                    prefillService.handlePrefillClientData(clientInfo[0].gcid, 'homeAddress')
                  "
                  appTrackClick
                  description="PreFill-HomeAddress-Clicked"
                  [disabled]="isSavingDisabled()"
                  [class.rkt-Button--is-disabled]="isSavingDisabled()"
                >
                  <div>
                    @for (addressInfo of clientInfo; track addressInfo) {
                      <div>
                        <span class="rkt-Caption-12"
                          >{{ addressInfo.key | capitalizeFirst | pascalCaseSplit }}:</span
                        >
                        <span class="rkt-Body-14 rkt-FontWeight--700">
                          {{ addressInfo.value | preFillFormatValue: addressInfo.key }}
                        </span>
                      </div>
                    }
                  </div>

                  @if (allControlsMatch(clientInfo)) {
                    <mat-icon
                      class="rkt-Icon data-tile__icon data-tile__icon--matched"
                      svgIcon="check_circle-outlined"
                    ></mat-icon>
                  } @else {
                    <mat-icon
                      class="rkt-Icon data-tile__icon"
                      svgIcon="add_circle-outlined"
                    ></mat-icon>
                  }
                </button>
              } @else {
                <button
                  type="button"
                  class="data-tile"
                  [class.data-tile--matched]="clientInfo.matchesControl"
                  (click)="prefillService.handlePrefillClientData(clientInfo.gcid, clientInfo.key)"
                  appTrackClick
                  description="PreFill-{{ clientInfo.key }}-Clicked"
                  [disabled]="isSavingDisabled()"
                  [class.rkt-Button--is-disabled]="isSavingDisabled()"
                >
                  <div>
                    <span class="rkt-Caption-12"
                      >{{ clientInfo.key | capitalizeFirst | pascalCaseSplit }}:</span
                    >
                    <span class="rkt-Body-14 rkt-FontWeight--700">
                      {{ clientInfo.value | preFillFormatValue: clientInfo.key }}
                    </span>
                  </div>

                  @if (clientInfo.matchesControl) {
                    <mat-icon
                      class="rkt-Icon data-tile__icon data-tile__icon--matched"
                      svgIcon="check_circle-outlined"
                    ></mat-icon>
                  } @else {
                    <mat-icon
                      class="rkt-Icon data-tile__icon"
                      svgIcon="add_circle-outlined"
                    ></mat-icon>
                  }
                </button>
              }
            }
            <app-prefill-employment clientGcid="{{ client.gcid }}" />
          </div>
        </ng-template>
      </mat-expansion-panel>
    }
    <div class="pb-4"></div>
  } @else if (prefillError()?.hasError) {
    <div class="text-center my-3">
      <div class="rkt-Caption-12">Something went wrong trying to get serviced data.</div>
      <button
        class="rkt-Button rkt-Button--secondary my-3 inline-flex"
        mat-flat-button
        (click)="prefillError()?.retry()"
        appTrackClick
        description="Serviced-Data-Fetch-Retry"
        [disabled]="prefillError()?.isFetching"
        [class.rkt-Button--is-disabled]="prefillError()?.isFetching"
        [class.rkt-Button--has-icon]="prefillError()?.isFetching"
      >
        @if (prefillError()?.isFetching) {
          <mat-icon iconPositionStart class="rkt-Icon mr-1">
            <mat-spinner
              role="presentation"
              class="rkt-ProgressSpinner rkt-ProgressSpinner--enterprise"
              color="mat-secondary"
              diameter="16"
            ></mat-spinner>
          </mat-icon>
        }
        Try Again
      </button>
    </div>
  } @else {
    <div class="text-center my-3">
      <div class="rkt-Caption-12">No serviced loan data is available.</div>
    </div>
  }
</div>
