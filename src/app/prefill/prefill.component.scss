.data-tile {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0.75rem;
  padding: 0.5rem 1rem;
  text-align: left;
  background-color: var(--rlxp-tile-background-color);

  &--matched {
    background: var(--rlxp-tile-background-color-matched);
  }

  &:hover:not(.data-tile--matched) {
    background: var(--rlxp-tile-background-color-hover);
  }

  mat-icon.mat-icon.rkt-Icon.data-tile__icon {
    color: var(--rlxp-data-tile-icon-color);   
    &--matched {
      color: var(--rlxp-data-tile-icon-color-matched);
    }
  }
}