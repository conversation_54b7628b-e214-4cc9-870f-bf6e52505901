:host {
  --gap: 0.5rem;
  --logo-width: 60px;
  --logo-height: 50px;
  --left-menu-width: 76px;
  --banner-height: 50px;
  --main-content-padding: 0.5rem;
  --left-menu-top-offset: calc(var(--gap) + var(--logo-height));
  display: block;
  max-height: 100%;
  height: 100%;
}

.header {
  grid-area: header;
  display: flex;
  padding: 9px 11px;
  justify-content: space-between;
  align-items: center;
  align-self: stretch;
}

.grid-container {
  display: grid;
  grid-template-areas: 'banner banner' 'header header' 'left-menu main-content';
  grid-template-rows: min-content 50px 1fr;
  grid-template-columns: var(--left-menu-width) 1fr;
  width: 100vw;
  height: 100vh;
  column-gap: 0.5rem;
}

.left-menu {
  grid-area: left-menu;
  padding: 0.5rem 0 16px 16px;
  z-index: 4; // show above overlay container
}

.main-content {
  grid-area: main-content;
  display: flex;
  gap: 0.5rem;
  justify-content: space-between;
  overflow: hidden;
  padding-right: 1rem;
}

.right-menu {
  z-index: 2;
}

.scroll-container {
  overflow-x: auto;
  overflow-y: auto;
  padding-top: 0.5rem;
  padding-bottom: 1rem;
}

.banner {
  grid-area: banner;
}
