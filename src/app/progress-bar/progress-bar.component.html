<button 
  mat-flat-button
  class="rkt-ProgressBarContainer rkt-ProgressBarContainer--enterprise"
  [class.rkt-ProgressBarContainer--success]="itemCount() === 0"
  [disabled]="itemCount() === 0"
  (click)="navigate.emit()"
  cdkOverlayOrigin
  #trigger="cdkOverlayOrigin"
>
  <mat-progress-bar
    class="rkt-ProgressBar"
    mode="determinate"
    [value]="progressBarValue()"
    aria-labelledby="progressBarLabel"
  ></mat-progress-bar>
  <div id="progressBarLabel" class="app-ProgressBarLabel rkt-Caption-12 rkt-FontWeight--500">
    {{ itemCount() }} item{{itemCount() === 1 ? '' : 's'}} required {{ requiredToAction() }}
  </div>
</button>

<app-milestone-tooltip [activeAction]="activeAction()" [overlayOrigin]="trigger" />