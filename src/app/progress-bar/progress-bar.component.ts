import { OverlayModule } from '@angular/cdk/overlay';
import { Component, EventEmitter, Output, computed, input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { AppIslandAction } from '../application-island/app-island-action-button/app-island-action-button.component';
import { MilestoneTooltipComponent } from '../milestone-tooltip/milestone-tooltip.component';

@Component({
  selector: 'app-progress-bar',
  standalone: true,
  imports: [MatButtonModule, MatProgressBarModule, OverlayModule, MilestoneTooltipComponent],
  templateUrl: './progress-bar.component.html',
  styleUrl: './progress-bar.component.scss',
})
export class ProgressBarComponent {
  activeAction = input.required<AppIslandAction>();
  itemCount = input.required<number>();
  progressBarValue = input.required<number>();
  @Output() navigate = new EventEmitter();

  requiredToAction = computed(() => {
    switch (this.activeAction()) {
      case AppIslandAction.CreditRequest:
        return 'to Pull Credit';
      case AppIslandAction.GetPricing:
        return 'to Get Pricing';
    }
  });
}
