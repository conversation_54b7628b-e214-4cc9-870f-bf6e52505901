import { ENVIRONMENT_INITIALIZER, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Provider } from '@angular/core';
import { dynatraceUserSessionInitFn } from '../env-initializers/dynatrace-user-id';
import { DynatraceCustomErrorHandler } from './dynatrace-custom-error-handler';

export function provideDynatraceRum(): Provider[] {
  return [
    { provide: <PERSON>rrorHandler, useClass: DynatraceCustomErrorHandler },
    { provide: ENVIRONMENT_INITIALIZER, multi: true, useValue: dynatraceUserSessionInitFn }
  ];
}
