import { Error<PERSON><PERSON><PERSON>, Injectable } from "@angular/core";

declare global {
  interface Window {
    dtrum?: {
      reportError: (error: unknown) => void;
      identifyUser: (id: string) => void;
    };
  }
}

@Injectable({providedIn: 'root'})
export class DynatraceCustomErrorHandler extends ErrorHandler {
  override handleError(error: unknown): void {
    window.dtrum?.reportError(error);
    super.handleError(error);
  }
}