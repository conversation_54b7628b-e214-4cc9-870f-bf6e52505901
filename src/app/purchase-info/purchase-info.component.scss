@use '@angular/material' as mat;

.signed-pa-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  column-gap: 16px;
  row-gap: 12px;
  grid-template-rows: min-content;
  align-items: start;
}

#signed-pa-title {
  margin-right: 0.5rem;
}

#signed-pa-toggle {
  grid-column: span 8;
  display: flex;
  margin-bottom: 0.5rem;
}
#closing-date {
  grid-column: span 2;
}

#purchase-price {
  grid-column: span 3;
}

#down-payment {
  grid-column: span 4;
  grid-row-start: 3;
}

#seller-concessions {
  grid-column: span 4;
  max-height: 100%;
  align-self: start;
}

#fsbo {
  grid-column: span 4;
}

#max-purchase-price {
  grid-column: span 4;
}

#desired-down-payment {
  grid-column: span 4;
}

#emd-container {
  display: flex;
  justify-content: space-between;
  grid-column: span 8;
  align-items: center;
  margin-bottom: 12px;
}

#emd-toggle-text {
  margin-right: 0.5rem;
}

#emd-title {
  grid-column: span 4;
  margin: 0px;
}

#emd-amount {
  grid-column: span 8;
}
