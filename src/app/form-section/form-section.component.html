<div>
  <div class="form-section-header row">
    <p class="title rkt-Tracked-12 rkt-Spacing--mb12">
      <mat-icon class="rkt-Icon" color="primary" [svgIcon]="icon()"></mat-icon>
      {{ title() }}
    </p>
    <div class="row">
      <span class="flex rkt-Spacing--mb4">
        <ng-content select="[addButton]"></ng-content>
      </span>
      <app-section-toggle-button
        [isCollapsible]="isCollapsible()"
        (sectionToggled)="setSectionOpenState($event)"
        [isOpen]="sectionToggle()"
      ></app-section-toggle-button>
    </div>
  </div>
  <mat-card
    class="rkt-Card rkt-Card--enterprise section-container"
    [class.active-form-section]="isActiveSection()"
  >
    <div [@expandCollapse]="primaryExpandCollapseState()" class="inner-styling">
      <ng-content></ng-content>
    </div>

    <div class="row summary-section" [@expandCollapse]="summaryExpandCollapseState()">
      <ng-content select="[section-summary]"></ng-content>
      <div class="milestone-chip">
        <ng-content select="[milestone-chip]"></ng-content>
      </div>
    </div>
  </mat-card>
</div>
