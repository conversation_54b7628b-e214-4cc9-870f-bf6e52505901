import { Component, inject, input, output } from '@angular/core';
import { MatChipsModule } from '@angular/material/chips';
import { FormSection } from '../../services/form-nav/form-nav-section.service';
import { SectionMilestoneService } from '../../services/form-nav/section-milestone.service';

@Component({
  selector: 'app-milestone-chip',
  standalone: true,
  imports: [MatChipsModule],
  templateUrl: './milestone-chip.component.html',
  styleUrl: './milestone-chip.component.scss',
})
export class MilestoneChipComponent {
  private sectionMilestoneService = inject(SectionMilestoneService);
  formSection = input<FormSection>();
  chipClick = output<void>();

  getAvailableMilestoneCount(): number {
    return this.sectionMilestoneService.getMilestoneCountForSection(this.formSection()!);
  }
}
