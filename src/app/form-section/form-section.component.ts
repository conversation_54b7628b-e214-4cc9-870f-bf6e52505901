import { animate, state, style, transition, trigger } from '@angular/animations';
import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  ElementRef,
  inject,
  input,
  OnDestroy,
  OnInit,
  signal,
} from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { MatIconModule } from '@angular/material/icon';
import { FormNavSectionService, FormSection } from '../services/form-nav/form-nav-section.service';
import { SectionToggleButtonComponent } from './section-toggle-button/section-toggle-button.component';
export const SECTION_TOGGLE_DURATION = 350;
@Component({
  selector: 'app-form-section',
  standalone: true,
  imports: [MatCardModule, MatIconModule, SectionToggleButtonComponent, CommonModule],
  templateUrl: './form-section.component.html',
  styleUrl: './form-section.component.scss',
  animations: [
    trigger('expandCollapse', [
      state('expanded', style({ height: '*', overflow: 'hidden' })),
      state('collapsed', style({ height: '0px', overflow: 'hidden' })),
      transition('expanded <=> collapsed', [animate('{{ duration }}ms ease-in-out')], {
        params: { duration: SECTION_TOGGLE_DURATION },
      }),
    ]),
  ],
})
export class FormSectionComponent implements OnInit, OnDestroy {
  formNavSectionService = inject(FormNavSectionService);
  elementRef = inject(ElementRef);

  title = input.required<string>();
  isCollapsible = input<boolean>(true);
  formSection = input.required<FormSection>();
  sectionToggle = signal<boolean>(true);

  isActiveSection = computed(
    () => this.formNavSectionService.activeSection() === this.formSection(),
  );

  icon = computed(() => {
    switch (this.title()) {
      case 'Loan Purpose':
        return 'goal-outlined';
      case 'Client(s)':
        return 'group-outlined';
      case 'Purchase Info':
      case 'Refinance Info':
        return 'real_estate_agent-outlined';
      case 'Subject Property':
        return 'house-outlined';
      case 'Credit':
        return 'rl-crm';
      case 'Income':
        return 'rl-income';
      case 'Asset(s)':
        return 'rl-assets';
      case 'REO':
        return 'rl-reo';
      default:
        return '';
    }
  });

  primaryExpandCollapseState = computed(() => {
    if (!this.isCollapsible()) {
      return null;
    }

    return {
      value: this.sectionToggle() ? 'expanded' : 'collapsed',
      params: { duration: SECTION_TOGGLE_DURATION },
    };
  });

  summaryExpandCollapseState = computed(() => {
    if (!this.isCollapsible()) {
      return null;
    }

    return {
      value: !this.sectionToggle() ? 'expanded' : 'collapsed',
      params: { duration: SECTION_TOGGLE_DURATION },
    };
  });

  ngOnInit(): void {
    this.formNavSectionService.registerSection(
      this.formSection(),
      this.elementRef.nativeElement,
      this.onSectionActivateHook(),
    );
  }

  ngOnDestroy(): void {
    this.formNavSectionService.deregisterSection(this.elementRef.nativeElement);
  }

  setSectionOpenState(isOpen: boolean) {
    this.sectionToggle.set(isOpen);
  }

  onSectionActivateHook() {
    return () => this.setSectionOpenState(true);
  }
}
