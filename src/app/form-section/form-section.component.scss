.section-container {
  border-width: 1px;
  border-style: solid;
}

.inner-styling {
  ::ng-deep {
    > :not(:last-child) {
      border-bottom: 1px solid #d3d3d3;
    }

    > *:not(button) {
      padding-top: 24px;

      &:first-child {
        padding-top: 0;
      }
    }
  }
}

.form-section-header {
  justify-content: space-between;

  p.title {
    line-height: 1rem;
    letter-spacing: 0.06em;
    display: flex;
    gap: 0.5rem;
    align-items: center;
  }
}

mat-card.mat-mdc-card {
  box-shadow: none;
}

.summary-section {
  justify-content: space-between;

  .milestone-chip {
    align-self: flex-start;
  }
}
