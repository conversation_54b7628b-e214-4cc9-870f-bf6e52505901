import { Component, input, output } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-section-toggle-button',
  standalone: true,
  imports: [MatIconModule, MatButtonModule],
  templateUrl: './section-toggle-button.component.html',
  styleUrl: './section-toggle-button.component.scss',
})
export class SectionToggleButtonComponent {
  isCollapsible = input<boolean>(false);
  sectionToggled = output<boolean>();
  isOpen = input<boolean>(true);

  toggleSectionVisibility() {
    this.sectionToggled.emit(!this.isOpen());
  }
}
