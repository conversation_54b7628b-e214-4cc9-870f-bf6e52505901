@if (isCollapsible()) {
  <button
    class="rkt-Button rkt-Button--large rkt-Button--tertiary rkt-Button--has-icon row rkt-Spacing--mb4"
    (click)="toggleSectionVisibility()"
    mat-button
  >
    <mat-icon
      svgIcon="expand_less-outlined"
      [class.rotate-180]="!isOpen()"
      class="transition-transform"
    ></mat-icon>
    <span class="rkt-Body-14">Show {{ isOpen() ? 'Less' : 'More' }}</span>
  </button>
}
