.container {
  display: grid;
  grid-template: repeat(2, 1fr) / repeat(10, 1fr);
  column-gap: 16px;
  row-gap: 12px;
  align-items: center;
}

#street {
  grid-column: span 8;
}

#street .rkt-Input {
  width: 90%;
}

#unit {
  grid-column: span 2;
}

#state,
#zip {
  grid-column: span 3;
}

#city,
#county {
  grid-column: span 3;
}

#house-icon-button{
  background-color: transparent;
}

.icon-label {
  color: var(--rlxp-icon-label);
}

.icon-label-disabled {
  color: var(--rlxp-icon-label-disabled)
}

.app-ButtonIcon {
  margin-bottom: 0;
}

:host {
  ::ng-deep {

    rkt-tag-enterprise .rkt-Tag.rkt-Tag--enterprise.enabled {
      background-color: var(--rlxp-green-icon-bg-color);
    }

    rkt-tag-enterprise .rkt-Tag.rkt-Tag--enterprise:hover.enabled{
      background-color: var(--rlxp-green-icon-bg-color-hover);
    }

    rkt-tag-enterprise .rkt-Tag.rkt-Tag--enterprise.disabled {
      background-color: var(--rlxp-green-icon-bg-color-disabled);
    }

    #property-hub-tag .rkt-Tag--enterprise.disabled .rkt-Tag__icon{
      color:var(--rlxp-green-icon-color-disabled);
    }

  }
}

:host ::ng-deep .mat-mdc-form-field-error-wrapper {
  padding: 0 8px;
}
