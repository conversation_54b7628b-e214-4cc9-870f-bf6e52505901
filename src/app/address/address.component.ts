import { Component, Injector, Signal, computed, inject, input, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable, toSignal } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatTooltip } from '@angular/material/tooltip';
import { Address, County, State, isCounty } from '@rocket-logic/rl-xp-bff-models';
import { RktLinkModule } from '@rocketcentral/rocket-design-system-angular';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import {
  catchError,
  combineLatest,
  distinctUntilChanged,
  filter,
  finalize,
  map,
  of,
  switchMap,
  tap,
} from 'rxjs';
import { TextFieldComponent } from "../_shared/components/text-field/text-field.component";
import { RlaFormFieldSuffixComponent } from '../assistant/components/rla-form-field-suffix/rla-form-field-suffix.component';
import { RlaHighlightDirective } from '../assistant/directives/rla-highlight.directive';
import { InputSectionDirective } from '../form-nav/nav-section/input-section.directive';
import { AddressDataService } from '../services/address/address-data.service';
import { FormInput, ProductMilestoneFormInput } from '../services/form-nav/form-nav-input.service';
import {
  SelectOption,
  TypeAheadSelectComponent,
} from '../type-ahead-select/type-ahead-select.component';
import { addTestCities } from '../util/add-test-cities';
import { Controlify } from '../util/form-utility-types';
import { getErrorMessage } from '../util/get-error-message';
import { STATES } from '../util/states';
import { toValueChangesSignal } from '../util/value-changes-signal';

export enum AddressType {
  Current = 'current',
  Previous = 'previous',
  SubjectProperty = 'subjectProperty',
}

@Component({
  selector: 'app-address',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    MatFormFieldModule,
    MatSelectModule,
    MatInputModule,
    MatIconModule,
    MatButtonModule,
    InputSectionDirective,
    TypeAheadSelectComponent,
    MatTooltip,
    RktTagEnterpriseModule,
    RktLinkModule,
    RlaHighlightDirective,
    RlaFormFieldSuffixComponent,
    TextFieldComponent,
  ],
  templateUrl: './address.component.html',
  styleUrl: './address.component.scss',
})
export class AddressComponent {
  addressDataService = inject(AddressDataService);
  addressForm = input.required<FormGroup<Controlify<Address>>>();
  injector = inject(Injector);
  showCounty = input<boolean>(false);
  addressSection = input<{
    city?: FormInput | ProductMilestoneFormInput;
    street?: FormInput | ProductMilestoneFormInput;
    shouldRegisterStreet?: Signal<boolean>;
    state?: FormInput;
    zip?: FormInput;
    county?: FormInput | ProductMilestoneFormInput;
  }>();

  shouldRegister = computed(() => Object.keys(this.addressSection() ?? {}).length > 0);
  isLoading = signal({ city: false, county: false, zipCode: false, state: false });

  openInPropHub() {
    window.open(`https://propertyhub/p/${this.propertyHubGuidControl().value}`, '_blank');
  }

  stateControl = computed(() => this.addressForm().get('state') as FormControl);
  streetControl = computed(() => this.addressForm().get('addressLine1') as FormControl);
  cityControl = computed(() => this.addressForm().get('city') as FormControl);
  countyControl = computed(() => this.addressForm().get('county') as FormControl);
  zipCodeControl = computed(() => this.addressForm().get('zipCode') as FormControl);
  propertyHubGuidControl = computed(() => this.addressForm().get('propertyHubGuid') as FormControl);

  state = toValueChangesSignal<State>(this.addressForm, 'state');
  state$ = toObservable(this.state);
  street = toValueChangesSignal<string>(this.addressForm, 'street');
  city = toValueChangesSignal<string>(this.addressForm, 'city');
  county = toValueChangesSignal<County>(this.addressForm, 'county');
  zipCode = toValueChangesSignal<string>(this.addressForm, 'zipCode');
  zipCode$ = toObservable(this.zipCode);
  validZipCode$ = this.zipCode$.pipe(
    filter((zipCode) => !zipCode || zipCode.length === 5),
    distinctUntilChanged(),
  );

  getErrorMessage() {
    return getErrorMessage(this.streetControl(), [['pattern', () => 'PO Boxes are not allowed']]);
  }

  countyDisplayWith = (value: County | null) => value?.name ?? '';

  stateOptions = signal(STATES);

  cityOptions = toSignal(
    combineLatest([this.state$, this.validZipCode$]).pipe(
      tap(() => this.isLoading.update((curr) => ({ ...curr, city: true }))),
      switchMap(([state, zipCode]) => {
        return this.addressDataService.getCities$(state, undefined, zipCode).pipe(
          map((cities) => addTestCities(cities, state, zipCode)),
          catchError((_) => of([] as string[])),
          finalize(() => {
            this.isLoading.update((curr) => ({ ...curr, city: false }));
          }),
        );
      }),
      tap((cities) => {
        if (
          this.city() &&
          cities.length > 0 &&
          !cities.some((city) => city.toLowerCase() === this.city()?.toLowerCase())
        ) {
          this.cityControl().reset();
        }
        if (cities.length === 1 && cities[0].toLowerCase() !== this.city()?.toLowerCase()) {
          this.cityControl().markAsDirty();
          this.cityControl().setValue(cities[0]);
        }
      }),
      map((cities) => this.mapToSelectOption(cities)),
    ),
    { initialValue: [] },
  );

  zipCodeOptions = toSignal(
    combineLatest([this.state$]).pipe(
      tap(() => this.isLoading.set({ ...this.isLoading(), zipCode: true })),
      switchMap(([state]) =>
        this.addressDataService.getZipCodes$(state).pipe(
          catchError((_) => of([] as string[])),
          finalize(() => {
            this.isLoading.set({ ...this.isLoading(), zipCode: false });
          }),
        ),
      ),
      // Reset the zip code control if the selected zip code is not in the list of zip codes
      tap((zipcodes) => {
        if (this.zipCode() && zipcodes.length > 0 && !zipcodes.includes(this.zipCode())) {
          this.zipCodeControl().reset();
        }
        if (zipcodes.length === 1 && zipcodes[0] !== this.zipCode()) {
          this.zipCodeControl().markAsDirty();
          this.zipCodeControl().setValue(zipcodes[0]);
        }
      }),
      map((zipcodes) => this.mapToSelectOption(zipcodes)),
    ),
    { initialValue: [] },
  );

  countyOptions = toSignal(
    combineLatest([this.state$, this.validZipCode$]).pipe(
      tap(() => this.isLoading.set({ ...this.isLoading(), county: true })),
      // Reset the county control if the selected county is not in the list of counties
      switchMap(([state, zipCode]) =>
        this.addressDataService.getCounties$(state, undefined, zipCode).pipe(
          catchError((_) => of([] as County[])),
          finalize(() => {
            this.isLoading.set({ ...this.isLoading(), county: false });
          }),
        ),
      ),
      tap((counties) => {
        if (
          this.county() &&
          counties.length > 0 &&
          !counties.some(
            (county) =>
              county.name.toLowerCase() === this.county().name.toLowerCase() &&
              county.fipsCode === this.county().fipsCode,
          )
        ) {
          this.countyControl().reset();
        }

        if (
          this.showCounty() &&
          counties.length === 1 &&
          counties[0].name.toLowerCase() !== this.county()?.name.toLowerCase()
        ) {
          this.countyControl().markAsDirty();
          this.countyControl().setValue(counties[0]);
        }
      }),
      map((counties) => this.mapToSelectOption(counties)),
    ),
    { initialValue: [] },
  );

  constructor() {
    this.validZipCode$
      .pipe(
        filter((zipCode) => !!zipCode),
        tap(() => this.isLoading.update((curr) => ({ ...curr, state: true }))),
        switchMap((zipCode) =>
          this.addressDataService
            .getStates$(zipCode)
            .pipe(finalize(() => this.isLoading.update((curr) => ({ ...curr, state: false })))),
        ),
        takeUntilDestroyed(),
      )
      .subscribe((states) => {
        if (states.length > 0 && !states.includes(this.state())) {
          this.stateControl().markAsDirty();
          this.stateControl().setValue(states[0]);
        }
      });
  }

  private mapToSelectOption(valuesArray: any[]): SelectOption<any>[] {
    if (!valuesArray) {
      return [];
    }

    return valuesArray.map((item: any) => ({
      value: item,
      display: isCounty(item) ? item.name : item,
    }));
  }
}
