<div class="container">
  <app-text-field
    id="street"
    label="Street"
    [control]="addressForm().controls.addressLine1"
    appRlaHighlight
    appNavInput
    [inputSection]="addressSection()?.street"
    [shouldRegister]="addressSection()?.shouldRegisterStreet?.() ?? shouldRegister()!"
    [customError]="getErrorMessage()"
  >
    <ng-container form-field-suffix>
      <div class="flex items-center" matSuffix>
        @if (propertyHubGuidControl().value) {
          <button
            matSuffix
            rkt-tag-enterprise
            id="house-icon-button"
            class="rkt-ButtonIcon app-ButtonIcon mr-2"
            [class.rkt-ButtonIcon--is-disabled]="addressForm().disabled"
            (click)="openInPropHub()"
            [disabled]="addressForm().disabled"
            [matTooltip]="addressForm().disabled ? '' : 'View in Property Hub'"
            matTooltipPosition="below"
          >
            <rkt-tag-enterprise
              [additionalClasses]="addressForm().disabled ? 'disabled' : 'enabled'"
              id="property-hub-tag"
              variant="success"
              iconPosition="right"
              iconName="open_in_new-outlined"
            >
              <span [class]="addressForm().disabled ? 'icon-label-disabled' : 'icon-label'"
                >Property Hub</span
              >
            </rkt-tag-enterprise>
          </button>
        }
        <app-rla-form-field-suffix />
      </div>
    </ng-container>
  </app-text-field>

  <app-text-field
    id="unit"
    appRlaHighlight
    label="Unit"
    [control]="addressForm().controls.addressLine2"
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-text-field>

  <app-type-ahead-select
    [control]="stateControl()"
    [options]="stateOptions()"
    label="State"
    [requireSelection]="true"
    id="state"
    [isLoading]="isLoading().state"
    appRlaHighlight
    appNavInput
    [inputSection]="addressSection()?.state"
    [shouldRegister]="shouldRegister()"
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-type-ahead-select>
  <app-type-ahead-select
    [control]="cityControl()"
    [options]="cityOptions()"
    label="City"
    id="city"
    [isLoading]="isLoading().city"
    appRlaHighlight
    appNavInput
    [inputSection]="addressSection()?.city"
    [shouldRegister]="shouldRegister()"
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-type-ahead-select>
  <app-type-ahead-select
    [control]="zipCodeControl()"
    [options]="zipCodeOptions()"
    label="Zip Code"
    id="zip"
    [isLoading]="isLoading().zipCode"
    appRlaHighlight
    appNavInput
    [inputSection]="addressSection()?.zip"
    [shouldRegister]="shouldRegister()"
  >
    <ng-container form-field-suffix>
      <app-rla-form-field-suffix />
    </ng-container>
  </app-type-ahead-select>
  @if (showCounty()) {
    <app-type-ahead-select
      [control]="countyControl()"
      [options]="countyOptions()"
      [requireSelection]="true"
      [displayWith]="countyDisplayWith"
      label="County"
      id="county"
      [isLoading]="isLoading().county"
      appRlaHighlight
      appNavInput
      [inputSection]="addressSection()?.county"
      [shouldRegister]="shouldRegister()"
    >
      <ng-container form-field-suffix>
        <app-rla-form-field-suffix />
      </ng-container>
    </app-type-ahead-select>
  }
</div>
