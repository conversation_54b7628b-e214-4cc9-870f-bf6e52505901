import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { MockBuilder, MockInstance } from 'ng-mocks';
import { of } from 'rxjs';
import { SelectFieldComponent } from '../_shared/components/select-field/select-field.component';
import { TextFieldComponent } from '../_shared/components/text-field/text-field.component';
import { AddressDataService } from '../services/address/address-data.service';
import { TypeAheadSelectComponent } from '../type-ahead-select/type-ahead-select.component';
import { buildAddressForm } from '../util/build-address-form';
import { AddressComponent } from './address.component';

describe('AddressComponent', () => {
  let component: AddressComponent;
  let fixture: ComponentFixture<AddressComponent>;

  beforeEach(() =>
    MockBuilder(AddressComponent).mock(AddressDataService, {
      getZipCodes$: () => of([]),
      getCounties$: () => of([]),
      getCities$: () => of([]),
    }),
  );
  beforeEach(() => {
    MockInstance(TypeAheadSelectComponent, 'matFormField', signal(undefined));
    MockInstance(TextFieldComponent, 'matFormField', signal(undefined));
    MockInstance(SelectFieldComponent, 'matFormField', signal(undefined));
    fixture = TestBed.createComponent(AddressComponent);
    const formBuilder = TestBed.inject(FormBuilder);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('addressForm', buildAddressForm(formBuilder));
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
