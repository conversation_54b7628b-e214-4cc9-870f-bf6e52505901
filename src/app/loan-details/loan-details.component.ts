import { CommonModule } from '@angular/common';
import { Component, computed, inject } from '@angular/core';
import { MatTabsModule } from '@angular/material/tabs';
import { RktTagEnterpriseModule } from '@rocketcentral/rocket-design-system-enterprise-angular';
import { DarkModeService } from '../services/dark-mode/dark-mode.service';
import { FormNavContentService } from '../services/form-nav/form-nav-content.service';
import { FormSection } from '../services/form-nav/form-nav-section.service';
import { ApplicationInfoComponent } from './application-info/application-info.component';
import { LoanSummaryComponent } from './loan-summary/loan-summary.component';

@Component({
  selector: 'app-loan-details',
  standalone: true,
  imports: [
    CommonModule,
    MatTabsModule,
    RktTagEnterpriseModule,
    ApplicationInfoComponent,
    LoanSummaryComponent,
  ],
  templateUrl: './loan-details.component.html',
  styleUrls: ['./loan-details.component.scss'],
})
export class LoanDetailsComponent {
  darkModeService = inject(DarkModeService);

  protected readonly loanDetailsFacade = inject(FormNavContentService);

  protected readonly hasLoanNumber = computed(() => {
    const loanInfo = this.loanDetailsFacade.sectionContent.get(FormSection.LoanInfo)?.();
    return !!loanInfo && !!loanInfo[0];
  });
}
