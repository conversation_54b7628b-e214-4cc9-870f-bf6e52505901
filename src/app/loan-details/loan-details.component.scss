@use '../../app/[containers]/right-panel/right-panel-shared.component.scss';

.no-messages {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;

  mat-icon {
    width: 5.1875rem;
    height: 5.1875rem;
  }
}

:host {
  --spacing: 12px;
  --horizontal-padding: 16px;

  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  align-self: stretch;

  mat-tab-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing);
    width: 100%;
    height: 100%;
  }

  ::ng-deep {
    @include right-panel-shared.right-panel-shared-tab;

    .mat-mdc-tab-body-wrapper {
      flex-grow: 1;
    }

    .rkt-Tag--enterprise {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column-reverse;
    }
  }
}

app-application-info,
app-loan-summary {
  padding: var(--spacing) var(--horizontal-padding);
}
