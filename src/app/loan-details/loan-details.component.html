<mat-tab-group
  mat-stretch-tabs="false"
  disablePagination="true"
  animationDuration="0"
  selectedIndex="0"
  class="rkt-Tabs"
  [disableRipple]="true"
>
  <mat-tab label="Application Info">
    @if (hasLoanNumber()) {
      <app-application-info />
    } @else {
      <ng-container
        *ngTemplateOutlet="placeholder; context: { message: 'Application Information' }"
      />
    }
  </mat-tab>

  <mat-tab label="Loan Summary">
    <!-- Todo: Replace false with conditional value once loan summary is built out. -->
    @if (false) {
      <app-loan-summary />
    } @else {
      <ng-container *ngTemplateOutlet="placeholder; context: { message: 'loan summary' }" />
    }
  </mat-tab>
</mat-tab-group>

<ng-template #placeholder let-message="message">
  <div class="no-messages">
    <mat-icon
      [svgIcon]="darkModeService.isDarkMode() ? 'rl-loan-summary-dark' : 'rl-loan-summary'"
    />
    <p class="rkt-Label-14">No {{ message }} available</p>
  </div>
</ng-template>
