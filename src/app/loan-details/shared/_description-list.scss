@use '@rocketcentral/rocket-design-system-styles/web/scss/spacing' as rkt-spacing;
@use '@rocketcentral/rocket-design-system-styles/web/scss/color' as rkt-color;
@use '@rocketcentral/rocket-design-system-styles/base/typography' as rkt-typography;

%container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  gap: rkt-spacing.$rkt-spacing-16;
  align-self: stretch;
}

%header {
  @extend %rkt-Label-14;
  font-weight: 500;
  line-height: 20px;
}

%dl-style {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  gap: rkt-spacing.$rkt-spacing-8;
  align-self: stretch;
  width: 100%;
  padding-bottom: rkt-spacing.$rkt-spacing-16;

  .term-description-pair {
    display: flex;
    gap: rkt-spacing.$rkt-spacing-8;
    width: 100%;
  }

  .term-title-color {
    color: var(--rlxp-term-title-color);
  }

  .term-definition-color {
    color: var(--rlxp-term-value-color);
  }

  dt,
  dd {
    margin-top: 0;
    display: flex;
    align-items: center;
  }

  dt {
    @extend %rkt-Caption-12;
    text-align: left;
    color: rkt-color.$rkt-gray-600;
    font-weight: 400;
    line-height: 16px;
    flex: 2;
    align-items: start;
  }

  dd {
    @extend %rkt-Caption-12;
    color: rkt-color.$rkt-gray-800;
    gap: rkt-spacing.$rkt-spacing-8;
    font-weight: 500;
    text-align: right;
    line-height: 16px;
    flex: 1;
    display: flex;
    justify-content: flex-end;
  }

  .bold {
    dt {
      @extend %rkt-Label-14;
      color: var(--Mapped-Text-text-primary);
      font-size: 14px;
      font-weight: 500;
      line-height: 20px;

      .term-definition-color {
        color: var(--rlxp-term-title-color);
      }
    }

    dd {
      @extend %rkt-Label-14;
      color: var(--Mapped-Text-text-primary);
      font-size: 14px;
      font-weight: 700;
      line-height: 20px;
    }
  }

  .success {
    dd {
      color: rkt-color.$rkt-green-500;
    }
  }
}

app-description-terms:not(:last-child) {
  border-bottom: 1px solid rkt-color.$rkt-gray-200;
}
