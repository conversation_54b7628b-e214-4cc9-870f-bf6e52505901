import { Clipboard } from '@angular/cdk/clipboard';
import { Component, inject, input } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar } from '@angular/material/snack-bar';

@Component({
  selector: 'app-copy-button',
  standalone: true,
  template: `
    <mat-icon class="rkt-Icon" aria-hidden="true">content_copy</mat-icon>
  `,
  styleUrls: ['./copy-button.component.scss'],
  imports: [
    MatButtonModule,
    MatIconModule
  ],
  host: {
    '(click)': 'copyToClipboard()',
    '(keydown.enter)': 'copyToClipboard()',
    'tabindex': '0',
    'role': 'button',
    'aria-label': 'Copy value to clipboard',
  },
})
export class CopyButtonComponent {
  readonly value = input<string>('');
  readonly #clipboard = inject(Clipboard);
  readonly #snackBar = inject(MatSnackBar);

  protected copyToClipboard(): void {
    this.#clipboard.copy(this.value());
    this.#snackBar.open(`Copied ${this.value()} to clipboard`, 'Dismiss', {
      panelClass: 'rkt-Snackbar',
      duration: 1000,
    });
  }
}
