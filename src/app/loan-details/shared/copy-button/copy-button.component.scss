@use '@rocketcentral/rocket-design-system-styles/web/scss/color' as rkt-color;
@use '@rocketcentral/rocket-design-system-styles/web/scss/spacing' as rkt-spacing;
@use '@rocketcentral/rocket-design-system-styles/web/scss/typography' as rkt-typography;

:host {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: opacity 0.3s;

  &:hover {
    opacity: 0.8;
  }

  &:active {
    transform: scale(0.95);
  }

  mat-icon {
    font-size: rkt-typography.$rkt-typography-font-size-label-16;
    width: auto;
    height: auto;
    line-height: 18px;
  }
}
