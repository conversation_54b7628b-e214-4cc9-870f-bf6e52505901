<dl>
  @for (detail of details(); track $index) {
    <div class="term-description-pair" [class.bold]="detail.isBold">
      <dt class="term-title-color">{{ detail.title }}</dt>
      <dd class="rkt-Label-14 rkt-FontWeight--700 wrap term-definition-color">
        @for (item of detail.content(); track item) {
          {{ item }} <br />
        } @empty {
          --
        }
        @if (detail.canCopy) {
          <app-copy-button [value]="getCopyableContent(detail.content())" />
        }
      </dd>
    </div>
  }
</dl>
