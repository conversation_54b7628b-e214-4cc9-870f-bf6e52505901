import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { CopyButtonComponent } from '../copy-button/copy-button.component';
import { Content, SectionDetail } from '../models/section-details.type';

@Component({
  selector: 'app-description-terms',
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CopyButtonComponent],
  templateUrl: './description-terms.component.html',
  styleUrls: ['./description-terms.component.scss']
})
export class DescriptionTermComponent {
  readonly details = input.required<ReadonlyArray<SectionDetail>>();

  protected getCopyableContent(content: Readonly<Content>): string {
    return content.join(" ");
  }
}
