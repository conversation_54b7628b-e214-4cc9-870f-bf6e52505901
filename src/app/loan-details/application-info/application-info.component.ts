import { CommonModule } from '@angular/common';
import { Component, computed, inject, Signal, signal } from '@angular/core';
import { MatDividerModule } from '@angular/material/divider';
import { LoanInfoFormListenerService } from '../../services/entity-state/loan-state/loan-info-form-listener.service';
import { FormNavContentService } from '../../services/form-nav/form-nav-content.service';
import { FormSection } from '../../services/form-nav/form-nav-section.service';
import { pascalCaseSplit } from '../../util/formatting-helpers';
import { DescriptionTermComponent } from '../shared/description-terms/description-terms.component';
import { Content, SectionDetail } from '../shared/models/section-details.type';

@Component({
  selector: 'app-application-info',
  standalone: true,
  imports: [CommonModule, DescriptionTermComponent, MatDividerModule],
  templateUrl: './application-info.component.html',
  styleUrl: './application-info.component.scss',
})
export class ApplicationInfoComponent {
  protected readonly loanDetailsFacade = inject(FormNavContentService);
  private readonly loanFormListener = inject(LoanInfoFormListenerService);

  private readonly overviewSection = computed(() => {
    if (this.loanFormListener.purchaseSelected()) {
      return [
        FormSection.LoanInfo,
        FormSection.ClientInfo,
        FormSection.PurchaseInfo,
        FormSection.SubjectProperty,
      ];
    }
    return [
      FormSection.LoanInfo,
      FormSection.ClientInfo,
      FormSection.RefinanceInfo,
      FormSection.SubjectProperty,
    ];
  });

  private readonly sectionTitleMap: ReadonlyMap<FormSection, string> = new Map<FormSection, string>(
    [
      [FormSection.LoanInfo, 'Loan Number'],
      [FormSection.ClientInfo, 'Client(s)'],
    ],
  );

  private readonly copyableSections: ReadonlySet<FormSection> = new Set<FormSection>([
    FormSection.LoanInfo,
  ]);

  protected readonly overviewSectionDetails: Signal<SectionDetail[]> = computed(() => {
    return this.overviewSection().map((section) => {
      return {
        section,
        canCopy: this.copyableSections.has(section),
        title: this.sectionTitleMap.get(section) ?? pascalCaseSplit(section),
        content: this.getContent(section),
      };
    });
  });

  protected readonly assetsSectionDetails: Signal<ReadonlyArray<SectionDetail>> = computed(() => [
    {
      section: FormSection.Assets,
      isBold: true,
      title: 'Assets',
      content: this.getContent(FormSection.Assets),
    },
  ]);

  protected readonly creditSectionDetails: Signal<ReadonlyArray<SectionDetail>> = computed(() => [
    {
      section: FormSection.Credit,
      isBold: true,
      title: 'Qualifying Credit Score',
      content: this.getContent(FormSection.Credit),
    },
  ]);

  protected readonly incomeSectionDetails: Signal<ReadonlyArray<SectionDetail>> = computed(() => [
    {
      section: FormSection.Income,
      isBold: true,
      title: 'Income',
      content: this.getContent(FormSection.Income),
    },
  ]);

  private getContent(section: FormSection): Signal<Readonly<Content>> {
    return this.loanDetailsFacade.sectionContent.get(section) ?? signal([]);
  }
}
