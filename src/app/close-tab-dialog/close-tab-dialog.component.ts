import { Component, inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';

@Component({
  selector: 'app-close-tab-dialog',
  templateUrl: './close-tab-dialog.component.html',
  styleUrls: ['./close-tab-dialog.component.scss'],
  standalone: true,
  imports: [MatDialogModule],
})
export class CloseTabDialogComponent {
  private config = inject(MAT_DIALOG_DATA);

  public get tabLimit() {
    return this.config.tabLimit;
  }

  public get appName() {
    return this.config.appName;
  }

  public get message() {
    return this.tabLimit === 1
      ? `There is currently more than ${this.tabLimit} tab open for ${this.appName}, this tab is now inactive.`
      : `There are currently more than ${this.tabLimit} tabs open for ${this.appName}, this tab is now inactive.`;
  }
}
