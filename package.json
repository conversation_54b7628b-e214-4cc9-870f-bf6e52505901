{"name": "rl-xp-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --configuration=development --open", "lint": "ng lint", "build": "ng build", "build:test": "npm run build -- --configuration=test", "build:beta": "npm run build -- --configuration=beta", "build:prod": "npm run build -- --configuration=production", "build:train": "npm run build -- --configuration=train", "watch": "ng build --watch --configuration development", "test": "ng test", "test:headless": "ng test --browsers=ChromeHeadless", "test:ci": "ng test --watch=false --progress=false --browsers=ChromeHeadlessCI", "avo:login": "avo login", "avo:pull:rocket-logic": "avo pull --branch 'rocket-logic' #must disable vpn for this command to work"}, "private": true, "dependencies": {"@amplitude/segment-session-replay-wrapper": "^0.4.0", "@angular/animations": "^17.2.0", "@angular/cdk": "^17.2.2", "@angular/common": "^17.2.0", "@angular/compiler": "^17.2.0", "@angular/core": "^17.2.0", "@angular/forms": "^17.2.0", "@angular/material": "^17.2.2", "@angular/platform-browser": "^17.2.0", "@angular/platform-browser-dynamic": "^17.2.0", "@angular/router": "^17.2.0", "@auth0/auth0-angular": "^2.2.3", "@decision-services/angular-splunk-logger": "^17.0.6", "@decision-services/splunk-logger": "^1.0.1", "@microsoft/fetch-event-source": "^2.0.1", "@rocket-logic/ngx-web-vitals": "^1.1.0", "@rocket-logic/rl-xp-bff-models": "2.47.2", "@rocket-logic/support": "^17.2.1", "@rocket-trails/definition-orchestrator-apimodels-generated-ts": "^0.0.3", "@rocket-trails/execution-expressions": "^14.0.0", "@rocketcentral/rocket-design-system-enterprise-angular": "^3.1.0", "@segment/analytics-next": "^1.70.0", "avo-inspector": "^1.4.1", "html2canvas": "^1.4.1", "jspdf": "^2.5.2", "lodash-es": "^4.17.21", "ngx-mask": "17.1.8", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.3"}, "devDependencies": {"@amplitude/analytics-types": "^2.8.0", "@angular-devkit/build-angular": "^17.1.3", "@angular-eslint/builder": "17.2.1", "@angular-eslint/eslint-plugin": "17.2.1", "@angular-eslint/eslint-plugin-template": "17.2.1", "@angular-eslint/schematics": "17.2.1", "@angular-eslint/template-parser": "17.2.1", "@angular/cli": "^17.1.3", "@angular/compiler-cli": "^17.1.0", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "6.19.0", "@typescript-eslint/parser": "6.19.0", "autoprefixer": "^10.4.19", "avo": "^3.2.11", "eslint": "^8.56.0", "inquirer": "^9.3.7", "inquirer-autocomplete-prompt": "^3.0.1", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "ng-mocks": "^14.12.1", "postcss": "^8.4.38", "prettier": "^3.2.5", "tailwindcss": "^3.4.4", "typescript": "~5.3.2"}}