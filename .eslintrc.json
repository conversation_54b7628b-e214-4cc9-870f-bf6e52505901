{"root": true, "ignorePatterns": ["projects/**/*"], "overrides": [{"files": ["*.ts"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:@angular-eslint/recommended", "plugin:@angular-eslint/template/process-inline-templates"], "rules": {"@angular-eslint/directive-selector": ["error", {"type": "attribute", "prefix": "app", "style": "camelCase"}], "@angular-eslint/component-selector": ["error", {"type": "element", "prefix": "app", "style": "kebab-case"}], "@typescript-eslint/no-explicit-any": ["warn"], "@typescript-eslint/no-unused-vars": "off", "@angular-eslint/no-host-metadata-property": "off", "@angular-eslint/prefer-standalone-component": "warn", "@angular-eslint/use-component-view-encapsulation": "warn"}}, {"files": ["*.html"], "extends": ["plugin:@angular-eslint/template/recommended", "plugin:@angular-eslint/template/accessibility"], "rules": {"@angular-eslint/template/attributes-order": "warn", "@angular-eslint/template/no-any": "warn", "@angular-eslint/template/no-duplicate-attributes": "warn", "@angular-eslint/template/prefer-self-closing-tags": "warn", "@angular-eslint/template/label-has-associated-control": "warn", "@angular-eslint/template/no-call-expression": "warn"}}]}