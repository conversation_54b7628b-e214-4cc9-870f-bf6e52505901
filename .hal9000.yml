version: '2'

defaults:
    platform:
        name: 'linux'
        image: 'hal9000/circleci'

build:
    stages:
        default:
            artifact: .artifact
            runs:
                - steps:
                    - command: unpack-build-from-circleci .artifact

deploy:
    stages:
        after:
            runs:
                - platform:
                    image: hal9000/awscli
                  steps:
                    - name: 'Invalidate Cloudfront'
                      command: aws cloudfront create-invalidation --distribution-id ${HAL_METADATA_CLOUDFRONTID} --paths '/*'
