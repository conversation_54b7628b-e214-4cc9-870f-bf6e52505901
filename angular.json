{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"rl-xp-ui": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "scss"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/rl-xp-ui", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/tailwind.css", "src/styles.scss"], "scripts": [], "outputHashing": "all"}, "configurations": {"test": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.test.ts"}], "index": {"input": "src/index.test.html", "output": "index.html"}, "sourceMap": true}, "beta": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.beta.ts"}], "index": {"input": "src/index.beta.html", "output": "index.html"}, "sourceMap": true}, "production": {"budgets": [{"type": "initial", "maximumWarning": "500kb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "2kb", "maximumError": "10kb"}], "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "index": {"input": "src/index.prod.html", "output": "index.html"}, "outputHashing": "all"}, "train": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.train.ts"}]}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.local.ts"}]}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "rl-xp-ui:build:production"}, "development": {"buildTarget": "rl-xp-ui:build:development"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"buildTarget": "rl-xp-ui:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "inlineStyleLanguage": "scss", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/tailwind.css", "src/styles.scss"], "scripts": [], "karmaConfig": "karma.conf.js"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}}, "cli": {"schematicCollections": ["@angular-eslint/schematics"], "analytics": false}}